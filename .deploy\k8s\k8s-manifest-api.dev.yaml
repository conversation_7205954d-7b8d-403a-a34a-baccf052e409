---
kind: Service
apiVersion: v1
metadata:
    name: ever-cloc-dev-api-lb
spec:
    type: ClusterIP
    selector:
        app: ever-cloc-dev-api
    ports:
        - name: http
          protocol: TCP
          port: 80
          targetPort: 3000

---
kind: Ingress
apiVersion: networking.k8s.io/v1
metadata:
    name: ever-cloc-dev-api-ingress
    annotations:
        nginx.ingress.kubernetes.io/force-ssl-redirect: 'true'
        nginx.ingress.kubernetes.io/proxy-body-size: '20m'
spec:
    ingressClassName: nginx
    rules:
        - host: apidev.cloc.ai
          http:
              paths:
                  - path: /
                    pathType: Prefix
                    backend:
                        service:
                            name: ever-cloc-dev-api-lb
                            port:
                                number: 80
    tls:
        - hosts:
              - apidev.cloc.ai
          secretName: api.cloc.co-tls

---
kind: Deployment
apiVersion: apps/v1
metadata:
    name: ever-cloc-dev-api
spec:
    replicas: 1
    selector:
        matchLabels:
            app: ever-cloc-dev-api
    template:
        metadata:
            labels:
                app: ever-cloc-dev-api
        spec:
            containers:
                - name: ever-cloc-dev-api
                  image: registry.digitalocean.com/ever/gauzy-api-demo:latest
                  env:
                      - name: API_HOST
                        value: 0.0.0.0
                      - name: DEMO
                        value: 'true'
                      - name: CLOUD_PROVIDER
                        value: '$CLOUD_PROVIDER'
                      - name: NODE_ENV
                        value: 'development'
                      - name: ADMIN_PASSWORD_RESET
                        value: 'true'
                      - name: LOG_LEVEL
                        value: 'info'
                      - name: SENTRY_DSN
                        value: '$SENTRY_DSN'
                      - name: SENTRY_HTTP_TRACING_ENABLED
                        value: '$SENTRY_HTTP_TRACING_ENABLED'
                      - name: SENTRY_PROFILING_ENABLED
                        value: '$SENTRY_PROFILING_ENABLED'
                      - name: SENTRY_POSTGRES_TRACKING_ENABLED
                        value: '$SENTRY_POSTGRES_TRACKING_ENABLED'
                      - name: API_BASE_URL
                        value: 'https://apidev.cloc.ai'
                      - name: CLIENT_BASE_URL
                        value: 'https://demo.cloc.ai'
                      - name: EXPRESS_SESSION_SECRET
                        value: 'gauzy'
                      - name: JWT_SECRET
                        value: 'secretKey'
                      - name: JWT_REFRESH_TOKEN_SECRET
                        value: 'refreshSecretKey'
                      - name: JWT_REFRESH_TOKEN_EXPIRATION_TIME
                        value: '86400'
                      - name: OTEL_ENABLED
                        value: '$OTEL_ENABLED'
                      - name: OTEL_EXPORTER_OTLP_TRACES_ENDPOINT
                        value: '$OTEL_EXPORTER_OTLP_TRACES_ENDPOINT'
                      - name: OTEL_EXPORTER_OTLP_HEADERS
                        value: '$OTEL_EXPORTER_OTLP_HEADERS'
                      - name: FEATURE_OPEN_STATS
                        value: '$FEATURE_OPEN_STATS'
                  ports:
                      - containerPort: 3000
                        protocol: TCP
