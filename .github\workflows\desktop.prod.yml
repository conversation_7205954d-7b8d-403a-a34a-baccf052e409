name: Desktop App Build

on:
  workflow_run:
    workflows: ['Release Apps']
    branches: [apps]
    types:
      - completed

concurrency:
  group: ${{ github.ref }}-${{ github.workflow }}
  cancel-in-progress: true

jobs:
  release-linux:
    runs-on: ${{ matrix.os }}

    strategy:
      matrix:
        os: [ubicloud-standard-16]

    steps:
      - name: Check out Git repository
        uses: actions/checkout@v4
        with:
          repository: 'ever-co/ever-gauzy'
          ref: develop

      - name: Install Node.js, NPM and Yarn
        uses: buildjet/setup-node@v4
        with:
            node-version: '20.19.0'
            cache: 'yarn'

      - name: Change permissions
        run: 'sudo chown -R $(whoami) ./*'

      - name: Install system dependencies
        run: 'sudo apt-get update && sudo apt install -y curl gnupg git libappindicator3-1 ca-certificates binutils icnsutils graphicsmagick'

      - name: Fix node-gyp and Python
        run: python3 -m pip install packaging setuptools

      - name: Install latest version of NPM
        run: 'sudo npm install -g npm@9'

      - name: Install latest node-gyp package
        run: 'sudo npm install --quiet -g node-gyp@10.2.0'

      - name: Install Yarn dependencies
        run: 'yarn install --network-timeout 1000000 --frozen-lockfile'

      - name: Bootstrap Yarn
        run: 'yarn bootstrap'

      - name: Bump version desktop basic-timer app
        uses: actions/github-script@v7
        with:
          script: |
            const script = require('./.scripts/bump-version-electron.js')
            script.desktopTimer(true).then(console.log)
        env:
          PROJECT_REPO: 'https://github.com/cloc-co/ever-cloc.git'
          DESKTOP_TIMER_APP_NAME: 'ever-cloc-desktop'
          DESKTOP_TIMER_APP_REPO_NAME: 'ever-cloc-desktop'
          DESKTOP_TIMER_APP_REPO_OWNER: 'cloc-co'
          COMPANY_SITE_LINK: 'https://cloc.ai'
          DESKTOP_TIMER_APP_DESCRIPTION: 'Ever Cloc Desktop'
          DESKTOP_TIMER_APP_ID: 'com.cloc.desktop'

      - name: Build Desktop Timer App
        run: 'yarn build:desktop-timer:linux:release:gh'
        env:
          USE_HARD_LINKS: false
          GH_TOKEN: ${{ secrets.GH_TOKEN }}
          EP_GH_IGNORE_TIME: true
          SENTRY_DSN: ${{ secrets.SENTRY_DSN }}
          SENTRY_TRACES_SAMPLE_RATE: '${{ secrets.SENTRY_TRACES_SAMPLE_RATE }}'
          SENTRY_HTTP_TRACING_ENABLED: '${{ secrets.SENTRY_HTTP_TRACING_ENABLED }}'
          SENTRY_POSTGRES_TRACKING_ENABLED: '${{ secrets.SENTRY_POSTGRES_TRACKING_ENABLED }}'
          SENTRY_PROFILING_ENABLED: '${{ secrets.SENTRY_PROFILING_ENABLED }}'
          DO_KEY_ID: ${{ secrets.DO_KEY_ID }}
          DO_SECRET_KEY: ${{ secrets.DO_SECRET_KEY }}
          NX_NO_CLOUD: true
          COMPANY_SITE: 'Cloc'
          COMPANY_SITE_LINK: 'https://cloc.ai'
          COMPANY_FACEBOOK_LINK: 'https://www.facebook.com/cloc-ai'
          COMPANY_TWITTER_LINK: 'https://twitter.com/cloc_ai'
          COMPANY_LINKEDIN_LINK: 'https://www.linkedin.com/company/cloc-ai'
          PROJECT_REPO: 'https://github.com/cloc-co/ever-cloc.git'
          DESKTOP_TIMER_APP_NAME: 'ever-cloc-desktop'
          DESKTOP_TIMER_APP_DESCRIPTION: 'Ever Cloc Desktop'
          DESKTOP_TIMER_APP_ID: 'com.cloc.desktop'
          DESKTOP_TIMER_APP_REPO_NAME: 'ever-cloc-desktop'
          DESKTOP_TIMER_APP_REPO_OWNER: 'cloc-co'
          DESKTOP_TIMER_APP_WELCOME_TITLE: 'Welcome to Ever Cloc'
          DESKTOP_TIMER_APP_WELCOME_CONTENT: 'Ever Cloc - Open Productivity & Time Tracking Platform'
          I18N_FILES_URL: 'https://raw.githubusercontent.com/cloc-co/ever-cloc/develop/apps/desktop/i18n'
          PLATFORM_LOGO: 'https://cloc.ai/assets/ever-cloc-dark.png'
          GAUZY_DESKTOP_LOGO_512X512: 'https://cloc.ai/assets/ever-cloc-512x512.png'

  release-mac:
    runs-on: ${{ matrix.os }}

    strategy:
      matrix:
        os: [macos-latest]

    steps:
      - name: Check out Git repository
        uses: actions/checkout@v4
        with:
          repository: 'ever-co/ever-gauzy'
          ref: develop

      - name: Install Node.js, NPM and Yarn
        uses: actions/setup-node@v4
        with:
            node-version: '20.19.0'
            cache: 'yarn'

      - name: Install Python dependencies
        run: python3 -m pip install --break-system-packages packaging setuptools

      - name: Install latest version of NPM
        run: 'sudo npm install -g npm@9'

      - name: Install latest node-gyp package
        run: 'sudo npm install --quiet -g node-gyp@10.2.0'

      - name: Install Yarn dependencies
        run: 'yarn install --network-timeout 1000000 --frozen-lockfile'

      - name: Bootstrap Yarn
        run: 'yarn bootstrap'

      - name: Bump version desktop basic-timer app
        uses: actions/github-script@v7
        with:
          script: |
            const script = require('./.scripts/bump-version-electron.js')
            script.desktopTimer(true).then(console.log)
        env:
          PROJECT_REPO: 'https://github.com/cloc-co/ever-cloc.git'
          DESKTOP_TIMER_APP_NAME: 'ever-cloc-desktop'
          DESKTOP_TIMER_APP_REPO_NAME: 'ever-cloc-desktop'
          DESKTOP_TIMER_APP_REPO_OWNER: 'cloc-co'
          COMPANY_SITE_LINK: 'https://cloc.ai'
          DESKTOP_TIMER_APP_DESCRIPTION: 'Ever Cloc Desktop'
          DESKTOP_TIMER_APP_ID: 'com.cloc.desktop'

      - name: Build Desktop Timer App
        run: 'yarn build:desktop-timer:mac:release'
        env:
          USE_HARD_LINKS: false
          GH_TOKEN: ${{ secrets.GH_TOKEN }}
          EP_GH_IGNORE_TIME: true
          SENTRY_DSN: ${{ secrets.SENTRY_DSN }}
          SENTRY_TRACES_SAMPLE_RATE: '${{ secrets.SENTRY_TRACES_SAMPLE_RATE }}'
          SENTRY_HTTP_TRACING_ENABLED: '${{ secrets.SENTRY_HTTP_TRACING_ENABLED }}'
          SENTRY_POSTGRES_TRACKING_ENABLED: '${{ secrets.SENTRY_POSTGRES_TRACKING_ENABLED }}'
          SENTRY_PROFILING_ENABLED: '${{ secrets.SENTRY_PROFILING_ENABLED }}'
          DO_KEY_ID: ${{ secrets.DO_KEY_ID }}
          DO_SECRET_KEY: ${{ secrets.DO_SECRET_KEY }}
          NX_NO_CLOUD: true
          COMPANY_SITE: 'Cloc'
          COMPANY_SITE_LINK: 'https://cloc.ai'
          COMPANY_FACEBOOK_LINK: 'https://www.facebook.com/cloc-ai'
          COMPANY_TWITTER_LINK: 'https://twitter.com/cloc_ai'
          COMPANY_LINKEDIN_LINK: 'https://www.linkedin.com/company/cloc-ai'
          PROJECT_REPO: 'https://github.com/cloc-co/ever-cloc.git'
          DESKTOP_TIMER_APP_NAME: 'ever-cloc-desktop'
          DESKTOP_TIMER_APP_DESCRIPTION: 'Ever Cloc Desktop'
          DESKTOP_TIMER_APP_ID: 'com.cloc.desktop'
          DESKTOP_TIMER_APP_REPO_NAME: 'ever-cloc-desktop'
          DESKTOP_TIMER_APP_REPO_OWNER: 'cloc-co'
          DESKTOP_TIMER_APP_WELCOME_TITLE: 'Welcome to Ever Cloc'
          DESKTOP_TIMER_APP_WELCOME_CONTENT: 'Ever Cloc - Open Productivity & Time Tracking Platform'
          I18N_FILES_URL: 'https://raw.githubusercontent.com/cloc-co/ever-cloc/develop/apps/desktop/i18n'
          PLATFORM_LOGO: 'https://cloc.ai/assets/ever-cloc-dark.png'
          GAUZY_DESKTOP_LOGO_512X512: 'https://cloc.ai/assets/ever-cloc-512x512.png'

  release-windows:
    runs-on: ${{ matrix.os }}

    strategy:
      matrix:
        os: [windows-latest]

    steps:
      - name: Check out Git repository
        uses: actions/checkout@v4
        with:
          repository: 'ever-co/ever-gauzy'
          ref: develop

      - name: Install Node.js, NPM and Yarn
        uses: actions/setup-node@v4
        with:
            node-version: '20.19.0'
            cache: 'yarn'

      - name: Fix node-gyp and Python
        run: python3 -m pip install packaging setuptools

      - name: Install latest version of NPM
        run: 'npm install -g npm@9'

      - name: Install latest node-gyp package
        run: 'npm install --quiet -g node-gyp@10.2.0'

      - name: Install Yarn dependencies
        run: 'yarn install --network-timeout 1000000 --frozen-lockfile'

      - name: Bootstrap Yarn
        run: 'yarn bootstrap'

      - name: Bump version desktop basic-timer app
        uses: actions/github-script@v7
        with:
          script: |
            const script = require('./.scripts/bump-version-electron.js')
            script.desktopTimer(true).then(console.log)
        env:
          PROJECT_REPO: 'https://github.com/cloc-co/ever-cloc.git'
          DESKTOP_TIMER_APP_NAME: 'ever-cloc-desktop'
          DESKTOP_TIMER_APP_REPO_NAME: 'ever-cloc-desktop'
          DESKTOP_TIMER_APP_REPO_OWNER: 'cloc-co'
          COMPANY_SITE_LINK: 'https://cloc.ai'
          DESKTOP_TIMER_APP_DESCRIPTION: 'Ever Cloc Desktop'
          DESKTOP_TIMER_APP_ID: 'com.cloc.desktop'

      - name: Build Desktop Timer App
        run: 'yarn build:desktop-timer:windows:release:gh'
        env:
          USE_HARD_LINKS: false
          GH_TOKEN: ${{ secrets.GH_TOKEN }}
          EP_GH_IGNORE_TIME: true
          SENTRY_DSN: ${{ secrets.SENTRY_DSN }}
          SENTRY_TRACES_SAMPLE_RATE: '${{ secrets.SENTRY_TRACES_SAMPLE_RATE }}'
          SENTRY_PROFILE_SAMPLE_RATE: '${{ secrets.SENTRY_PROFILE_SAMPLE_RATE }}'
          SENTRY_HTTP_TRACING_ENABLED: '${{ secrets.SENTRY_HTTP_TRACING_ENABLED }}'
          SENTRY_POSTGRES_TRACKING_ENABLED: '${{ secrets.SENTRY_POSTGRES_TRACKING_ENABLED }}'
          SENTRY_PROFILING_ENABLED: '${{ secrets.SENTRY_PROFILING_ENABLED }}'
          DO_KEY_ID: ${{ secrets.DO_KEY_ID }}
          DO_SECRET_KEY: ${{ secrets.DO_SECRET_KEY }}
          NX_NO_CLOUD: true
          COMPANY_SITE: 'Cloc'
          COMPANY_SITE_LINK: 'https://cloc.ai'
          COMPANY_FACEBOOK_LINK: 'https://www.facebook.com/cloc-ai'
          COMPANY_TWITTER_LINK: 'https://twitter.com/cloc_ai'
          COMPANY_LINKEDIN_LINK: 'https://www.linkedin.com/company/cloc-ai'
          PROJECT_REPO: 'https://github.com/cloc-co/ever-cloc.git'
          DESKTOP_TIMER_APP_NAME: 'ever-cloc-desktop'
          DESKTOP_TIMER_APP_DESCRIPTION: 'Ever Cloc Desktop'
          DESKTOP_TIMER_APP_ID: 'com.cloc.desktop'
          DESKTOP_TIMER_APP_REPO_NAME: 'ever-cloc-desktop'
          DESKTOP_TIMER_APP_REPO_OWNER: 'cloc-co'
          DESKTOP_TIMER_APP_WELCOME_TITLE: 'Welcome to Ever Cloc'
          DESKTOP_TIMER_APP_WELCOME_CONTENT: 'Ever Cloc - Open Productivity & Time Tracking Platform'
          I18N_FILES_URL: 'https://raw.githubusercontent.com/cloc-co/ever-cloc/develop/apps/desktop/i18n'
          PLATFORM_LOGO: 'https://cloc.ai/assets/ever-cloc-dark.png'
          GAUZY_DESKTOP_LOGO_512X512: 'https://cloc.ai/assets/ever-cloc-512x512.png'
