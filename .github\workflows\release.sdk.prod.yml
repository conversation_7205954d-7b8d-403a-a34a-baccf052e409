name: Release Cloc SDK (Private) to npm

on:
  push:
    branches: [main]
  workflow_dispatch:

jobs:
  release:
    name: Version and SDK Publish with Changesets
    runs-on: ubicloud-standard-2

    permissions:
      contents: write # for changelog/version PRs
      packages: write
      id-token: write # for trusted publishing (future use)

    steps:
      - name: Checkout repository
        uses: actions/checkout@v4
        with:
          # Fetch full history for changesets
          fetch-depth: 0

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: 22
          registry-url: 'https://registry.npmjs.org'
          always-auth: true

      - name: Verify NPM Token
        run: |
          if [ -z "${{ secrets.NPM_TOKEN }}" ]; then
            echo "❌ NPM_TOKEN secret is not set"
            echo "Please configure a granular NPM access token in GitHub secrets"
            exit 1
          fi
          echo "✅ NPM_TOKEN secret is configured"

      - name: Configure NPM Authentication
        run: |
          echo "//registry.npmjs.org/:_authToken=${{ secrets.NPM_TOKEN }}" > ~/.npmrc
          echo "registry=https://registry.npmjs.org/" >> ~/.npmrc
          echo "always-auth=true" >> ~/.npmrc
          echo "@cloc:registry=https://registry.npmjs.org/" >> ~/.npmrc
          echo "✅ NPM authentication configured"

      - name: Install dependencies (Yarn)
        run: yarn install --frozen-lockfile

      - name: Build SDK packages
        run: |
          echo "Building atoms package..."
          yarn build:atoms
          echo "Building tracking package..."
          yarn build:tracking
          echo "✅ All SDK packages built successfully"

      - name: Create version bump commits if there are changesets
        run: |
          yarn version-packages
          git config user.name "github-actions[bot]"
          git config user.email "41898282+github-actions[bot]@users.noreply.github.com"
          git add .
          git commit -m "ci: sdk version bump via changeset" || echo "No changes to commit"
          git push

      - name: Publish changed packages using Changesets
        uses: changesets/action@v1
        with:
          publish: yarn publish-packages
        env:
          NPM_TOKEN: ${{ secrets.NPM_TOKEN }}
          NODE_AUTH_TOKEN: ${{ secrets.NPM_TOKEN }}
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
