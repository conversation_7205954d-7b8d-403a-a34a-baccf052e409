{"version": 3, "file": "1466.08369097.iframe.bundle.js", "mappings": ";;;;;;;;;;;;;;;;AAwDA;;ACzCA;AACA;AACA;AACA;;;;;;;;ACsLA;;;;;;;AAOA;;;;;AAKA;;;;;AAKA;;;;;;AAMA;AACA;AACA;;;;;;;ACnMA;;;AA2BA;;;;;AAAA;;;;AAAA;AAdA;;;AAcA;;;;;;;;;;;AAAA;;;;AASA", "sources": ["webpack://@cloc/storybook/../../node_modules/@storybook/blocks/dist/index.mjs", "webpack://@cloc/storybook/../../node_modules/@storybook/components/dist/chunk-V2RRM4BX.mjs", "webpack://@cloc/storybook/../../node_modules/@storybook/components/dist/index.mjs", "webpack://@cloc/storybook/../../node_modules/@storybook/theming/dist/index.mjs"], "sourcesContent": ["import { getControlSetterButtonId, getControlId } from './chunk-GWAJ4KRU.mjs';\nimport React17, { createContext, lazy, useState, useCallback, useRef, useEffect, Component, cloneElement, useMemo, Suspense, useContext, Children } from 'react';\nimport { styled, ignoreSsrWarning, useTheme, themes, ThemeProvider, convert, ensure } from '@storybook/theming';\nimport { withReset, SyntaxHighlighter, FlexBar, codeCommon, Form, IconButton, components, Zoom, ActionBar, Button, Link, ResetWrapper, Code, nameSpaceClassNames, H3, H2, Loader, EmptyTabContent, TabsState, ErrorFormatter, getStoryHref, WithTooltipPure } from '@storybook/components';\nimport { transparentize, darken, opacify, lighten, rgba } from 'polished';\nimport { global } from '@storybook/global';\nimport { ChevronSmallUpIcon, ChevronSmallDownIcon, AddIcon, SubtractIcon, ChevronDownIcon as ChevronDownIcon$1, ChevronRightIcon, ZoomIcon, ZoomOutIcon, ZoomResetIcon, EyeCloseIcon, EyeIcon, DocumentIcon, UndoIcon, VideoIcon, LinkIcon } from '@storybook/icons';\nimport pickBy from 'lodash/pickBy.js';\nimport { includeConditionalArg } from '@storybook/csf';\nimport { deprecate, once, logger } from '@storybook/client-logger';\nimport Markdown from 'markdown-to-jsx';\nimport memoize from 'memoizerific';\nimport uniq from 'lodash/uniq.js';\nimport cloneDeep from 'lodash/cloneDeep.js';\nimport { filterArgTypes, composeConfigs, Preview as Preview$1, DocsContext as DocsContext$1 } from '@storybook/preview-api';\nimport { SNIPPET_RENDERED, SourceType } from '@storybook/docs-tools';\nimport { stringify } from 'telejson';\nimport { GLOBALS_UPDATED, STORY_ARGS_UPDATED, UPDATE_STORY_ARGS, RESET_STORY_ARGS, NAVIGATE_URL } from '@storybook/core-events';\nimport dedent from 'ts-dedent';\nimport * as tocbot from 'tocbot';\nimport { Channel } from '@storybook/channels';\n\nvar Wrapper=styled.div(withReset,({theme})=>({backgroundColor:theme.base===\"light\"?\"rgba(0,0,0,.01)\":\"rgba(255,255,255,.01)\",borderRadius:theme.appBorderRadius,border:`1px dashed ${theme.appBorderColor}`,display:\"flex\",alignItems:\"center\",justifyContent:\"center\",padding:20,margin:\"25px 0 40px\",color:transparentize(.3,theme.color.defaultText),fontSize:theme.typography.size.s2})),EmptyBlock=props=>React17.createElement(Wrapper,{...props,className:\"docblock-emptyblock sb-unstyled\"});var StyledSyntaxHighlighter=styled(SyntaxHighlighter)(({theme})=>({fontSize:`${theme.typography.size.s2-1}px`,lineHeight:\"19px\",margin:\"25px 0 40px\",borderRadius:theme.appBorderRadius,boxShadow:theme.base===\"light\"?\"rgba(0, 0, 0, 0.10) 0 1px 3px 0\":\"rgba(0, 0, 0, 0.20) 0 2px 5px 0\",\"pre.prismjs\":{padding:20,background:\"inherit\"}}));var SourceSkeletonWrapper=styled.div(({theme})=>({background:theme.background.content,borderRadius:theme.appBorderRadius,border:`1px solid ${theme.appBorderColor}`,boxShadow:theme.base===\"light\"?\"rgba(0, 0, 0, 0.10) 0 1px 3px 0\":\"rgba(0, 0, 0, 0.20) 0 2px 5px 0\",margin:\"25px 0 40px\",padding:\"20px 20px 20px 22px\"})),SourceSkeletonPlaceholder=styled.div(({theme})=>({animation:`${theme.animation.glow} 1.5s ease-in-out infinite`,background:theme.appBorderColor,height:17,marginTop:1,width:\"60%\",[`&:first-child${ignoreSsrWarning}`]:{margin:0}})),SourceSkeleton=()=>React17.createElement(SourceSkeletonWrapper,null,React17.createElement(SourceSkeletonPlaceholder,null),React17.createElement(SourceSkeletonPlaceholder,{style:{width:\"80%\"}}),React17.createElement(SourceSkeletonPlaceholder,{style:{width:\"30%\"}}),React17.createElement(SourceSkeletonPlaceholder,{style:{width:\"80%\"}})),Source=({isLoading,error,language,code,dark,format:format2=!1,...rest})=>{let{typography}=useTheme();if(isLoading)return React17.createElement(SourceSkeleton,null);if(error)return React17.createElement(EmptyBlock,null,error);let syntaxHighlighter=React17.createElement(StyledSyntaxHighlighter,{bordered:!0,copyable:!0,format:format2,language,className:\"docblock-source sb-unstyled\",...rest},code);if(typeof dark>\"u\")return syntaxHighlighter;let overrideTheme=dark?themes.dark:themes.light;return React17.createElement(ThemeProvider,{theme:convert({...overrideTheme,fontCode:typography.fonts.mono,fontBase:typography.fonts.base})},syntaxHighlighter)};var toGlobalSelector=element=>`& :where(${element}:not(.sb-anchor, .sb-unstyled, .sb-unstyled ${element}))`,breakpoint=600,Title=styled.h1(withReset,({theme})=>({color:theme.color.defaultText,fontSize:theme.typography.size.m3,fontWeight:theme.typography.weight.bold,lineHeight:\"32px\",[`@media (min-width: ${breakpoint}px)`]:{fontSize:theme.typography.size.l1,lineHeight:\"36px\",marginBottom:\"16px\"}})),Subtitle=styled.h2(withReset,({theme})=>({fontWeight:theme.typography.weight.regular,fontSize:theme.typography.size.s3,lineHeight:\"20px\",borderBottom:\"none\",marginBottom:15,[`@media (min-width: ${breakpoint}px)`]:{fontSize:theme.typography.size.m1,lineHeight:\"28px\",marginBottom:24},color:transparentize(.25,theme.color.defaultText)})),DocsContent=styled.div(({theme})=>{let reset={fontFamily:theme.typography.fonts.base,fontSize:theme.typography.size.s3,margin:0,WebkitFontSmoothing:\"antialiased\",MozOsxFontSmoothing:\"grayscale\",WebkitTapHighlightColor:\"rgba(0, 0, 0, 0)\",WebkitOverflowScrolling:\"touch\"},headers={margin:\"20px 0 8px\",padding:0,cursor:\"text\",position:\"relative\",color:theme.color.defaultText,\"&:first-of-type\":{marginTop:0,paddingTop:0},\"&:hover a.anchor\":{textDecoration:\"none\"},\"& code\":{fontSize:\"inherit\"}},code={lineHeight:1,margin:\"0 2px\",padding:\"3px 5px\",whiteSpace:\"nowrap\",borderRadius:3,fontSize:theme.typography.size.s2-1,border:theme.base===\"light\"?`1px solid ${theme.color.mediumlight}`:`1px solid ${theme.color.darker}`,color:theme.base===\"light\"?transparentize(.1,theme.color.defaultText):transparentize(.3,theme.color.defaultText),backgroundColor:theme.base===\"light\"?theme.color.lighter:theme.color.border};return {maxWidth:1e3,width:\"100%\",[toGlobalSelector(\"a\")]:{...reset,fontSize:\"inherit\",lineHeight:\"24px\",color:theme.color.secondary,textDecoration:\"none\",\"&.absent\":{color:\"#cc0000\"},\"&.anchor\":{display:\"block\",paddingLeft:30,marginLeft:-30,cursor:\"pointer\",position:\"absolute\",top:0,left:0,bottom:0}},[toGlobalSelector(\"blockquote\")]:{...reset,margin:\"16px 0\",borderLeft:`4px solid ${theme.color.medium}`,padding:\"0 15px\",color:theme.color.dark,\"& > :first-of-type\":{marginTop:0},\"& > :last-child\":{marginBottom:0}},[toGlobalSelector(\"div\")]:reset,[toGlobalSelector(\"dl\")]:{...reset,margin:\"16px 0\",padding:0,\"& dt\":{fontSize:\"14px\",fontWeight:\"bold\",fontStyle:\"italic\",padding:0,margin:\"16px 0 4px\"},\"& dt:first-of-type\":{padding:0},\"& dt > :first-of-type\":{marginTop:0},\"& dt > :last-child\":{marginBottom:0},\"& dd\":{margin:\"0 0 16px\",padding:\"0 15px\"},\"& dd > :first-of-type\":{marginTop:0},\"& dd > :last-child\":{marginBottom:0}},[toGlobalSelector(\"h1\")]:{...reset,...headers,fontSize:`${theme.typography.size.l1}px`,fontWeight:theme.typography.weight.bold},[toGlobalSelector(\"h2\")]:{...reset,...headers,fontSize:`${theme.typography.size.m2}px`,paddingBottom:4,borderBottom:`1px solid ${theme.appBorderColor}`},[toGlobalSelector(\"h3\")]:{...reset,...headers,fontSize:`${theme.typography.size.m1}px`,fontWeight:theme.typography.weight.bold},[toGlobalSelector(\"h4\")]:{...reset,...headers,fontSize:`${theme.typography.size.s3}px`},[toGlobalSelector(\"h5\")]:{...reset,...headers,fontSize:`${theme.typography.size.s2}px`},[toGlobalSelector(\"h6\")]:{...reset,...headers,fontSize:`${theme.typography.size.s2}px`,color:theme.color.dark},[toGlobalSelector(\"hr\")]:{border:\"0 none\",borderTop:`1px solid ${theme.appBorderColor}`,height:4,padding:0},[toGlobalSelector(\"img\")]:{maxWidth:\"100%\"},[toGlobalSelector(\"li\")]:{...reset,fontSize:theme.typography.size.s2,color:theme.color.defaultText,lineHeight:\"24px\",\"& + li\":{marginTop:\".25em\"},\"& ul, & ol\":{marginTop:\".25em\",marginBottom:0},\"& code\":code},[toGlobalSelector(\"ol\")]:{...reset,margin:\"16px 0\",paddingLeft:30,\"& :first-of-type\":{marginTop:0},\"& :last-child\":{marginBottom:0}},[toGlobalSelector(\"p\")]:{...reset,margin:\"16px 0\",fontSize:theme.typography.size.s2,lineHeight:\"24px\",color:theme.color.defaultText,\"& code\":code},[toGlobalSelector(\"pre\")]:{...reset,fontFamily:theme.typography.fonts.mono,WebkitFontSmoothing:\"antialiased\",MozOsxFontSmoothing:\"grayscale\",lineHeight:\"18px\",padding:\"11px 1rem\",whiteSpace:\"pre-wrap\",color:\"inherit\",borderRadius:3,margin:\"1rem 0\",\"&:not(.prismjs)\":{background:\"transparent\",border:\"none\",borderRadius:0,padding:0,margin:0},\"& pre, &.prismjs\":{padding:15,margin:0,whiteSpace:\"pre-wrap\",color:\"inherit\",fontSize:\"13px\",lineHeight:\"19px\",code:{color:\"inherit\",fontSize:\"inherit\"}},\"& code\":{whiteSpace:\"pre\"},\"& code, & tt\":{border:\"none\"}},[toGlobalSelector(\"span\")]:{...reset,\"&.frame\":{display:\"block\",overflow:\"hidden\",\"& > span\":{border:`1px solid ${theme.color.medium}`,display:\"block\",float:\"left\",overflow:\"hidden\",margin:\"13px 0 0\",padding:7,width:\"auto\"},\"& span img\":{display:\"block\",float:\"left\"},\"& span span\":{clear:\"both\",color:theme.color.darkest,display:\"block\",padding:\"5px 0 0\"}},\"&.align-center\":{display:\"block\",overflow:\"hidden\",clear:\"both\",\"& > span\":{display:\"block\",overflow:\"hidden\",margin:\"13px auto 0\",textAlign:\"center\"},\"& span img\":{margin:\"0 auto\",textAlign:\"center\"}},\"&.align-right\":{display:\"block\",overflow:\"hidden\",clear:\"both\",\"& > span\":{display:\"block\",overflow:\"hidden\",margin:\"13px 0 0\",textAlign:\"right\"},\"& span img\":{margin:0,textAlign:\"right\"}},\"&.float-left\":{display:\"block\",marginRight:13,overflow:\"hidden\",float:\"left\",\"& span\":{margin:\"13px 0 0\"}},\"&.float-right\":{display:\"block\",marginLeft:13,overflow:\"hidden\",float:\"right\",\"& > span\":{display:\"block\",overflow:\"hidden\",margin:\"13px auto 0\",textAlign:\"right\"}}},[toGlobalSelector(\"table\")]:{...reset,margin:\"16px 0\",fontSize:theme.typography.size.s2,lineHeight:\"24px\",padding:0,borderCollapse:\"collapse\",\"& tr\":{borderTop:`1px solid ${theme.appBorderColor}`,backgroundColor:theme.appContentBg,margin:0,padding:0},\"& tr:nth-of-type(2n)\":{backgroundColor:theme.base===\"dark\"?theme.color.darker:theme.color.lighter},\"& tr th\":{fontWeight:\"bold\",color:theme.color.defaultText,border:`1px solid ${theme.appBorderColor}`,margin:0,padding:\"6px 13px\"},\"& tr td\":{border:`1px solid ${theme.appBorderColor}`,color:theme.color.defaultText,margin:0,padding:\"6px 13px\"},\"& tr th :first-of-type, & tr td :first-of-type\":{marginTop:0},\"& tr th :last-child, & tr td :last-child\":{marginBottom:0}},[toGlobalSelector(\"ul\")]:{...reset,margin:\"16px 0\",paddingLeft:30,\"& :first-of-type\":{marginTop:0},\"& :last-child\":{marginBottom:0},listStyle:\"disc\"}}}),DocsWrapper=styled.div(({theme})=>({background:theme.background.content,display:\"flex\",justifyContent:\"center\",padding:\"4rem 20px\",minHeight:\"100vh\",boxSizing:\"border-box\",gap:\"3rem\",[`@media (min-width: ${breakpoint}px)`]:{}})),DocsPageWrapper=({children,toc})=>React17.createElement(DocsWrapper,{className:\"sbdocs sbdocs-wrapper\"},React17.createElement(DocsContent,{className:\"sbdocs sbdocs-content\"},children),toc);var getBlockBackgroundStyle=theme=>({borderRadius:theme.appBorderRadius,background:theme.background.content,boxShadow:theme.base===\"light\"?\"rgba(0, 0, 0, 0.10) 0 1px 3px 0\":\"rgba(0, 0, 0, 0.20) 0 2px 5px 0\",border:`1px solid ${theme.appBorderColor}`});var Bar=styled(FlexBar)({position:\"absolute\",left:0,right:0,top:0,transition:\"transform .2s linear\"}),Wrapper2=styled.div({display:\"flex\",alignItems:\"center\",gap:4}),IconPlaceholder=styled.div(({theme})=>({width:14,height:14,borderRadius:2,margin:\"0 7px\",backgroundColor:theme.appBorderColor,animation:`${theme.animation.glow} 1.5s ease-in-out infinite`})),Toolbar=({isLoading,storyId,baseUrl,zoom,resetZoom,...rest})=>React17.createElement(Bar,{...rest},React17.createElement(Wrapper2,{key:\"left\"},isLoading?[1,2,3].map(key=>React17.createElement(IconPlaceholder,{key})):React17.createElement(React17.Fragment,null,React17.createElement(IconButton,{key:\"zoomin\",onClick:e=>{e.preventDefault(),zoom(.8);},title:\"Zoom in\"},React17.createElement(ZoomIcon,null)),React17.createElement(IconButton,{key:\"zoomout\",onClick:e=>{e.preventDefault(),zoom(1.25);},title:\"Zoom out\"},React17.createElement(ZoomOutIcon,null)),React17.createElement(IconButton,{key:\"zoomreset\",onClick:e=>{e.preventDefault(),resetZoom();},title:\"Reset zoom\"},React17.createElement(ZoomResetIcon,null)))));var ZoomContext=createContext({scale:1});var{window:globalWindow}=global,IFrame=class extends Component{constructor(){super(...arguments);this.iframe=null;}componentDidMount(){let{id}=this.props;this.iframe=globalWindow.document.getElementById(id);}shouldComponentUpdate(nextProps){let{scale}=nextProps;return scale!==this.props.scale&&this.setIframeBodyStyle({width:`${scale*100}%`,height:`${scale*100}%`,transform:`scale(${1/scale})`,transformOrigin:\"top left\"}),!1}setIframeBodyStyle(style){return Object.assign(this.iframe.contentDocument.body.style,style)}render(){let{id,title,src,allowFullScreen,scale,...rest}=this.props;return React17.createElement(\"iframe\",{id,title,src,...allowFullScreen?{allow:\"fullscreen\"}:{},loading:\"lazy\",...rest})}};var{PREVIEW_URL}=global,BASE_URL=PREVIEW_URL||\"iframe.html\",storyBlockIdFromId=({story,primary})=>`story--${story.id}${primary?\"--primary\":\"\"}`,InlineStory=props=>{let storyRef=useRef(),[showLoader,setShowLoader]=useState(!0),[error,setError]=useState(),{story,height,autoplay,forceInitialArgs,renderStoryToElement}=props;return useEffect(()=>{if(!(story&&storyRef.current))return ()=>{};let element=storyRef.current,cleanup=renderStoryToElement(story,element,{showMain:()=>{},showError:({title,description})=>setError(new Error(`${title} - ${description}`)),showException:err=>setError(err)},{autoplay,forceInitialArgs});return setShowLoader(!1),()=>{Promise.resolve().then(()=>cleanup());}},[autoplay,renderStoryToElement,story]),error?React17.createElement(\"pre\",null,React17.createElement(ErrorFormatter,{error})):React17.createElement(React17.Fragment,null,height?React17.createElement(\"style\",null,`#${storyBlockIdFromId(props)} { min-height: ${height}; transform: translateZ(0); overflow: auto }`):null,showLoader&&React17.createElement(StorySkeleton,null),React17.createElement(\"div\",{ref:storyRef,id:`${storyBlockIdFromId(props)}-inner`,\"data-name\":story.name}))},IFrameStory=({story,height=\"500px\"})=>React17.createElement(\"div\",{style:{width:\"100%\",height}},React17.createElement(ZoomContext.Consumer,null,({scale})=>React17.createElement(IFrame,{key:\"iframe\",id:`iframe--${story.id}`,title:story.name,src:getStoryHref(BASE_URL,story.id,{viewMode:\"story\"}),allowFullScreen:!0,scale,style:{width:\"100%\",height:\"100%\",border:\"0 none\"}}))),Story=props=>{let{inline}=props;return React17.createElement(\"div\",{id:storyBlockIdFromId(props),className:\"sb-story sb-unstyled\",\"data-story-block\":\"true\"},inline?React17.createElement(InlineStory,{...props}):React17.createElement(IFrameStory,{...props}))},StorySkeleton=()=>React17.createElement(Loader,null);var ChildrenContainer=styled.div(({isColumn,columns,layout})=>({display:isColumn||!columns?\"block\":\"flex\",position:\"relative\",flexWrap:\"wrap\",overflow:\"auto\",flexDirection:isColumn?\"column\":\"row\",\"& .innerZoomElementWrapper > *\":isColumn?{width:layout!==\"fullscreen\"?\"calc(100% - 20px)\":\"100%\",display:\"block\"}:{maxWidth:layout!==\"fullscreen\"?\"calc(100% - 20px)\":\"100%\",display:\"inline-block\"}}),({layout=\"padded\"})=>layout===\"centered\"||layout===\"padded\"?{padding:\"30px 20px\",\"& .innerZoomElementWrapper > *\":{width:\"auto\",border:\"10px solid transparent!important\"}}:{},({layout=\"padded\"})=>layout===\"centered\"?{display:\"flex\",justifyContent:\"center\",justifyItems:\"center\",alignContent:\"center\",alignItems:\"center\"}:{},({columns})=>columns&&columns>1?{\".innerZoomElementWrapper > *\":{minWidth:`calc(100% / ${columns} - 20px)`}}:{}),StyledSource=styled(Source)(({theme})=>({margin:0,borderTopLeftRadius:0,borderTopRightRadius:0,borderBottomLeftRadius:theme.appBorderRadius,borderBottomRightRadius:theme.appBorderRadius,border:\"none\",background:theme.base===\"light\"?\"rgba(0, 0, 0, 0.85)\":darken(.05,theme.background.content),color:theme.color.lightest,button:{background:theme.base===\"light\"?\"rgba(0, 0, 0, 0.85)\":darken(.05,theme.background.content)}})),PreviewContainer=styled.div(({theme,withSource,isExpanded})=>({position:\"relative\",overflow:\"hidden\",margin:\"25px 0 40px\",...getBlockBackgroundStyle(theme),borderBottomLeftRadius:withSource&&isExpanded&&0,borderBottomRightRadius:withSource&&isExpanded&&0,borderBottomWidth:isExpanded&&0,\"h3 + &\":{marginTop:\"16px\"}}),({withToolbar})=>withToolbar&&{paddingTop:40}),getSource=(withSource,expanded,setExpanded)=>{switch(!0){case!!(withSource&&withSource.error):return {source:null,actionItem:{title:\"No code available\",className:\"docblock-code-toggle docblock-code-toggle--disabled\",disabled:!0,onClick:()=>setExpanded(!1)}};case expanded:return {source:React17.createElement(StyledSource,{...withSource,dark:!0}),actionItem:{title:\"Hide code\",className:\"docblock-code-toggle docblock-code-toggle--expanded\",onClick:()=>setExpanded(!1)}};default:return {source:React17.createElement(StyledSource,{...withSource,dark:!0}),actionItem:{title:\"Show code\",className:\"docblock-code-toggle\",onClick:()=>setExpanded(!0)}}}};function getStoryId(children){if(Children.count(children)===1){let elt=children;if(elt.props)return elt.props.id}return null}var PositionedToolbar=styled(Toolbar)({position:\"absolute\",top:0,left:0,right:0,height:40}),Relative=styled.div({overflow:\"hidden\",position:\"relative\"}),Preview=({isLoading,isColumn,columns,children,withSource,withToolbar=!1,isExpanded=!1,additionalActions,className,layout=\"padded\",...props})=>{let[expanded,setExpanded]=useState(isExpanded),{source,actionItem}=getSource(withSource,expanded,setExpanded),[scale,setScale]=useState(1),previewClasses=[className].concat([\"sbdocs\",\"sbdocs-preview\",\"sb-unstyled\"]),defaultActionItems=withSource?[actionItem]:[],[additionalActionItems,setAdditionalActionItems]=useState(additionalActions?[...additionalActions]:[]),actionItems=[...defaultActionItems,...additionalActionItems],{window:globalWindow4}=global,copyToClipboard=useCallback(async text=>{let{createCopyToClipboardFunction}=await import('@storybook/components');createCopyToClipboardFunction();},[]),onCopyCapture=e=>{let selection=globalWindow4.getSelection();selection&&selection.type===\"Range\"||(e.preventDefault(),additionalActionItems.filter(item=>item.title===\"Copied\").length===0&&copyToClipboard(source.props.code).then(()=>{setAdditionalActionItems([...additionalActionItems,{title:\"Copied\",onClick:()=>{}}]),globalWindow4.setTimeout(()=>setAdditionalActionItems(additionalActionItems.filter(item=>item.title!==\"Copied\")),1500);}));};return React17.createElement(PreviewContainer,{withSource,withToolbar,...props,className:previewClasses.join(\" \")},withToolbar&&React17.createElement(PositionedToolbar,{isLoading,border:!0,zoom:z=>setScale(scale*z),resetZoom:()=>setScale(1),storyId:getStoryId(children),baseUrl:\"./iframe.html\"}),React17.createElement(ZoomContext.Provider,{value:{scale}},React17.createElement(Relative,{className:\"docs-story\",onCopyCapture:withSource&&onCopyCapture},React17.createElement(ChildrenContainer,{isColumn:isColumn||!Array.isArray(children),columns,layout},React17.createElement(Zoom.Element,{scale},Array.isArray(children)?children.map((child,i)=>React17.createElement(\"div\",{key:i},child)):React17.createElement(\"div\",null,children))),React17.createElement(ActionBar,{actionItems}))),withSource&&expanded&&source)};styled(Preview)(()=>({\".docs-story\":{paddingTop:32,paddingBottom:40}}));var Table=styled.table(({theme})=>({\"&&\":{borderCollapse:\"collapse\",borderSpacing:0,border:\"none\",tr:{border:\"none !important\",background:\"none\"},\"td, th\":{padding:0,border:\"none\",width:\"auto!important\"},marginTop:0,marginBottom:0,\"th:first-of-type, td:first-of-type\":{paddingLeft:0},\"th:last-of-type, td:last-of-type\":{paddingRight:0},td:{paddingTop:0,paddingBottom:4,\"&:not(:first-of-type)\":{paddingLeft:10,paddingRight:0}},tbody:{boxShadow:\"none\",border:\"none\"},code:codeCommon({theme}),div:{span:{fontWeight:\"bold\"}},\"& code\":{margin:0,display:\"inline-block\",fontSize:theme.typography.size.s1}}})),ArgJsDoc=({tags})=>{let params=(tags.params||[]).filter(x=>x.description),hasDisplayableParams=params.length!==0,hasDisplayableDeprecated=tags.deprecated!=null,hasDisplayableReturns=tags.returns!=null&&tags.returns.description!=null;return !hasDisplayableParams&&!hasDisplayableReturns&&!hasDisplayableDeprecated?null:React17.createElement(React17.Fragment,null,React17.createElement(Table,null,React17.createElement(\"tbody\",null,hasDisplayableDeprecated&&React17.createElement(\"tr\",{key:\"deprecated\"},React17.createElement(\"td\",{colSpan:2},React17.createElement(\"strong\",null,\"Deprecated\"),\": \",tags.deprecated.toString())),hasDisplayableParams&&params.map(x=>React17.createElement(\"tr\",{key:x.name},React17.createElement(\"td\",null,React17.createElement(\"code\",null,x.name)),React17.createElement(\"td\",null,x.description))),hasDisplayableReturns&&React17.createElement(\"tr\",{key:\"returns\"},React17.createElement(\"td\",null,React17.createElement(\"code\",null,\"Returns\")),React17.createElement(\"td\",null,tags.returns.description)))))};var ITEMS_BEFORE_EXPANSION=8,Summary=styled.div(({isExpanded})=>({display:\"flex\",flexDirection:isExpanded?\"column\":\"row\",flexWrap:\"wrap\",alignItems:\"flex-start\",marginBottom:\"-4px\",minWidth:100})),Text=styled.span(codeCommon,({theme,simple=!1})=>({flex:\"0 0 auto\",fontFamily:theme.typography.fonts.mono,fontSize:theme.typography.size.s1,wordBreak:\"break-word\",whiteSpace:\"normal\",maxWidth:\"100%\",margin:0,marginRight:\"4px\",marginBottom:\"4px\",paddingTop:\"2px\",paddingBottom:\"2px\",lineHeight:\"13px\",...simple&&{background:\"transparent\",border:\"0 none\",paddingLeft:0}})),ExpandButton=styled.button(({theme})=>({fontFamily:theme.typography.fonts.mono,color:theme.color.secondary,marginBottom:\"4px\",background:\"none\",border:\"none\"})),Expandable=styled.div(codeCommon,({theme})=>({fontFamily:theme.typography.fonts.mono,color:theme.color.secondary,fontSize:theme.typography.size.s1,margin:0,whiteSpace:\"nowrap\",display:\"flex\",alignItems:\"center\"})),Detail=styled.div(({theme,width})=>({width,minWidth:200,maxWidth:800,padding:15,fontFamily:theme.typography.fonts.mono,fontSize:theme.typography.size.s1,boxSizing:\"content-box\",\"& code\":{padding:\"0 !important\"}})),ChevronUpIcon=styled(ChevronSmallUpIcon)({marginLeft:4}),ChevronDownIcon=styled(ChevronSmallDownIcon)({marginLeft:4}),EmptyArg=()=>React17.createElement(\"span\",null,\"-\"),ArgText=({text,simple})=>React17.createElement(Text,{simple},text),calculateDetailWidth=memoize(1e3)(detail=>{let lines=detail.split(/\\r?\\n/);return `${Math.max(...lines.map(x=>x.length))}ch`}),getSummaryItems=summary=>{if(!summary)return [summary];let summaryItems=summary.split(\"|\").map(value2=>value2.trim());return uniq(summaryItems)},renderSummaryItems=(summaryItems,isExpanded=!0)=>{let items=summaryItems;return isExpanded||(items=summaryItems.slice(0,ITEMS_BEFORE_EXPANSION)),items.map(item=>React17.createElement(ArgText,{key:item,text:item===\"\"?'\"\"':item}))},ArgSummary=({value:value2,initialExpandedArgs})=>{let{summary,detail}=value2,[isOpen,setIsOpen]=useState(!1),[isExpanded,setIsExpanded]=useState(initialExpandedArgs||!1);if(summary==null)return null;let summaryAsString=typeof summary.toString==\"function\"?summary.toString():summary;if(detail==null){if(/[(){}[\\]<>]/.test(summaryAsString))return React17.createElement(ArgText,{text:summaryAsString});let summaryItems=getSummaryItems(summaryAsString),itemsCount=summaryItems.length;return itemsCount>ITEMS_BEFORE_EXPANSION?React17.createElement(Summary,{isExpanded},renderSummaryItems(summaryItems,isExpanded),React17.createElement(ExpandButton,{onClick:()=>setIsExpanded(!isExpanded)},isExpanded?\"Show less...\":`Show ${itemsCount-ITEMS_BEFORE_EXPANSION} more...`)):React17.createElement(Summary,null,renderSummaryItems(summaryItems))}return React17.createElement(WithTooltipPure,{closeOnOutsideClick:!0,placement:\"bottom\",visible:isOpen,onVisibleChange:isVisible=>{setIsOpen(isVisible);},tooltip:React17.createElement(Detail,{width:calculateDetailWidth(detail)},React17.createElement(SyntaxHighlighter,{language:\"jsx\",format:!1},detail))},React17.createElement(Expandable,{className:\"sbdocs-expandable\"},React17.createElement(\"span\",null,summaryAsString),isOpen?React17.createElement(ChevronUpIcon,null):React17.createElement(ChevronDownIcon,null)))},ArgValue=({value:value2,initialExpandedArgs})=>value2==null?React17.createElement(EmptyArg,null):React17.createElement(ArgSummary,{value:value2,initialExpandedArgs});var Label=styled.label(({theme})=>({lineHeight:\"18px\",alignItems:\"center\",marginBottom:8,display:\"inline-block\",position:\"relative\",whiteSpace:\"nowrap\",background:theme.boolean.background,borderRadius:\"3em\",padding:1,'&[aria-disabled=\"true\"]':{opacity:.5,input:{cursor:\"not-allowed\"}},input:{appearance:\"none\",width:\"100%\",height:\"100%\",position:\"absolute\",left:0,top:0,margin:0,padding:0,border:\"none\",background:\"transparent\",cursor:\"pointer\",borderRadius:\"3em\",\"&:focus\":{outline:\"none\",boxShadow:`${theme.color.secondary} 0 0 0 1px inset !important`}},span:{textAlign:\"center\",fontSize:theme.typography.size.s1,fontWeight:theme.typography.weight.bold,lineHeight:\"1\",cursor:\"pointer\",display:\"inline-block\",padding:\"7px 15px\",transition:\"all 100ms ease-out\",userSelect:\"none\",borderRadius:\"3em\",color:transparentize(.5,theme.color.defaultText),background:\"transparent\",\"&:hover\":{boxShadow:`${opacify(.3,theme.appBorderColor)} 0 0 0 1px inset`},\"&:active\":{boxShadow:`${opacify(.05,theme.appBorderColor)} 0 0 0 2px inset`,color:opacify(1,theme.appBorderColor)},\"&:first-of-type\":{paddingRight:8},\"&:last-of-type\":{paddingLeft:8}},\"input:checked ~ span:last-of-type, input:not(:checked) ~ span:first-of-type\":{background:theme.boolean.selectedBackground,boxShadow:theme.base===\"light\"?`${opacify(.1,theme.appBorderColor)} 0 0 2px`:`${theme.appBorderColor} 0 0 0 1px`,color:theme.color.defaultText,padding:\"7px 15px\"}})),parse=value2=>value2===\"true\",BooleanControl=({name,value:value2,onChange,onBlur,onFocus,argType})=>{let onSetFalse=useCallback(()=>onChange(!1),[onChange]),readonly=!!argType?.table?.readonly;if(value2===void 0)return React17.createElement(Button,{variant:\"outline\",size:\"medium\",id:getControlSetterButtonId(name),onClick:onSetFalse,disabled:readonly},\"Set boolean\");let controlId=getControlId(name),parsedValue=typeof value2==\"string\"?parse(value2):value2;return React17.createElement(Label,{\"aria-disabled\":readonly,htmlFor:controlId,\"aria-label\":name},React17.createElement(\"input\",{id:controlId,type:\"checkbox\",onChange:e=>onChange(e.target.checked),checked:parsedValue,role:\"switch\",disabled:readonly,name,onBlur,onFocus}),React17.createElement(\"span\",{\"aria-hidden\":\"true\"},\"False\"),React17.createElement(\"span\",{\"aria-hidden\":\"true\"},\"True\"))};var parseDate=value2=>{let[year,month,day]=value2.split(\"-\"),result=new Date;return result.setFullYear(parseInt(year,10),parseInt(month,10)-1,parseInt(day,10)),result},parseTime=value2=>{let[hours,minutes]=value2.split(\":\"),result=new Date;return result.setHours(parseInt(hours,10)),result.setMinutes(parseInt(minutes,10)),result},formatDate=value2=>{let date=new Date(value2),year=`000${date.getFullYear()}`.slice(-4),month=`0${date.getMonth()+1}`.slice(-2),day=`0${date.getDate()}`.slice(-2);return `${year}-${month}-${day}`},formatTime=value2=>{let date=new Date(value2),hours=`0${date.getHours()}`.slice(-2),minutes=`0${date.getMinutes()}`.slice(-2);return `${hours}:${minutes}`},FormInput=styled(Form.Input)(({readOnly})=>({opacity:readOnly?.5:1})),FlexSpaced=styled.div(({theme})=>({flex:1,display:\"flex\",input:{marginLeft:10,flex:1,height:32,\"&::-webkit-calendar-picker-indicator\":{opacity:.5,height:12,filter:theme.base===\"light\"?void 0:\"invert(1)\"}},\"input:first-of-type\":{marginLeft:0,flexGrow:4},\"input:last-of-type\":{flexGrow:3}})),DateControl=({name,value:value2,onChange,onFocus,onBlur,argType})=>{let[valid,setValid]=useState(!0),dateRef=useRef(),timeRef=useRef(),readonly=!!argType?.table?.readonly;useEffect(()=>{valid!==!1&&(dateRef&&dateRef.current&&(dateRef.current.value=formatDate(value2)),timeRef&&timeRef.current&&(timeRef.current.value=formatTime(value2)));},[value2]);let onDateChange=e=>{let parsed=parseDate(e.target.value),result=new Date(value2);result.setFullYear(parsed.getFullYear(),parsed.getMonth(),parsed.getDate());let time=result.getTime();time&&onChange(time),setValid(!!time);},onTimeChange=e=>{let parsed=parseTime(e.target.value),result=new Date(value2);result.setHours(parsed.getHours()),result.setMinutes(parsed.getMinutes());let time=result.getTime();time&&onChange(time),setValid(!!time);},controlId=getControlId(name);return React17.createElement(FlexSpaced,null,React17.createElement(FormInput,{type:\"date\",max:\"9999-12-31\",ref:dateRef,id:`${controlId}-date`,name:`${controlId}-date`,readOnly:readonly,onChange:onDateChange,onFocus,onBlur}),React17.createElement(FormInput,{type:\"time\",id:`${controlId}-time`,name:`${controlId}-time`,ref:timeRef,onChange:onTimeChange,readOnly:readonly,onFocus,onBlur}),valid?null:React17.createElement(\"div\",null,\"invalid\"))};var Wrapper3=styled.label({display:\"flex\"}),parse2=value2=>{let result=parseFloat(value2);return Number.isNaN(result)?void 0:result},format=value2=>value2!=null?String(value2):\"\",FormInput2=styled(Form.Input)(({readOnly})=>({opacity:readOnly?.5:1})),NumberControl=({name,value:value2,onChange,min,max,step,onBlur,onFocus,argType})=>{let[inputValue,setInputValue]=useState(typeof value2==\"number\"?value2:\"\"),[forceVisible,setForceVisible]=useState(!1),[parseError,setParseError]=useState(null),readonly=!!argType?.table?.readonly,handleChange=useCallback(event=>{setInputValue(event.target.value);let result=parseFloat(event.target.value);Number.isNaN(result)?setParseError(new Error(`'${event.target.value}' is not a number`)):(onChange(result),setParseError(null));},[onChange,setParseError]),onForceVisible=useCallback(()=>{setInputValue(\"0\"),onChange(0),setForceVisible(!0);},[setForceVisible]),htmlElRef=useRef(null);return useEffect(()=>{forceVisible&&htmlElRef.current&&htmlElRef.current.select();},[forceVisible]),useEffect(()=>{inputValue!==(typeof value2==\"number\"?value2:\"\")&&setInputValue(value2);},[value2]),value2===void 0?React17.createElement(Button,{variant:\"outline\",size:\"medium\",id:getControlSetterButtonId(name),onClick:onForceVisible,disabled:readonly},\"Set number\"):React17.createElement(Wrapper3,null,React17.createElement(FormInput2,{ref:htmlElRef,id:getControlId(name),type:\"number\",onChange:handleChange,size:\"flex\",placeholder:\"Edit number...\",value:inputValue,valid:parseError?\"error\":null,autoFocus:forceVisible,readOnly:readonly,name,min,max,step,onFocus,onBlur}))};var selectedKey=(value2,options)=>{let entry=options&&Object.entries(options).find(([_key,val])=>val===value2);return entry?entry[0]:void 0},selectedKeys=(value2,options)=>value2&&options?Object.entries(options).filter(entry=>value2.includes(entry[1])).map(entry=>entry[0]):[],selectedValues=(keys,options)=>keys&&options&&keys.map(key=>options[key]);var Wrapper4=styled.div(({isInline})=>isInline?{display:\"flex\",flexWrap:\"wrap\",alignItems:\"flex-start\",label:{display:\"inline-flex\",marginRight:15}}:{label:{display:\"flex\"}},props=>{if(props[\"aria-readonly\"]===\"true\")return {input:{cursor:\"not-allowed\"}}}),Text2=styled.span({\"[aria-readonly=true] &\":{opacity:.5}}),Label2=styled.label({lineHeight:\"20px\",alignItems:\"center\",marginBottom:8,\"&:last-child\":{marginBottom:0},input:{margin:0,marginRight:6}}),CheckboxControl=({name,options,value:value2,onChange,isInline,argType})=>{if(!options)return logger.warn(`Checkbox with no options: ${name}`),React17.createElement(React17.Fragment,null,\"-\");let initial=selectedKeys(value2,options),[selected,setSelected]=useState(initial),readonly=!!argType?.table?.readonly,handleChange=e=>{let option=e.target.value,updated=[...selected];updated.includes(option)?updated.splice(updated.indexOf(option),1):updated.push(option),onChange(selectedValues(updated,options)),setSelected(updated);};useEffect(()=>{setSelected(selectedKeys(value2,options));},[value2]);let controlId=getControlId(name);return React17.createElement(Wrapper4,{\"aria-readonly\":readonly,isInline},Object.keys(options).map((key,index)=>{let id=`${controlId}-${index}`;return React17.createElement(Label2,{key:id,htmlFor:id},React17.createElement(\"input\",{type:\"checkbox\",disabled:readonly,id,name:id,value:key,onChange:handleChange,checked:selected?.includes(key)}),React17.createElement(Text2,null,key))}))};var Wrapper5=styled.div(({isInline})=>isInline?{display:\"flex\",flexWrap:\"wrap\",alignItems:\"flex-start\",label:{display:\"inline-flex\",marginRight:15}}:{label:{display:\"flex\"}},props=>{if(props[\"aria-readonly\"]===\"true\")return {input:{cursor:\"not-allowed\"}}}),Text3=styled.span({\"[aria-readonly=true] &\":{opacity:.5}}),Label3=styled.label({lineHeight:\"20px\",alignItems:\"center\",marginBottom:8,\"&:last-child\":{marginBottom:0},input:{margin:0,marginRight:6}}),RadioControl=({name,options,value:value2,onChange,isInline,argType})=>{if(!options)return logger.warn(`Radio with no options: ${name}`),React17.createElement(React17.Fragment,null,\"-\");let selection=selectedKey(value2,options),controlId=getControlId(name),readonly=!!argType?.table?.readonly;return React17.createElement(Wrapper5,{\"aria-readonly\":readonly,isInline},Object.keys(options).map((key,index)=>{let id=`${controlId}-${index}`;return React17.createElement(Label3,{key:id,htmlFor:id},React17.createElement(\"input\",{type:\"radio\",id,name:id,disabled:readonly,value:key,onChange:e=>onChange(options[e.currentTarget.value]),checked:key===selection}),React17.createElement(Text3,null,key))}))};var styleResets={appearance:\"none\",border:\"0 none\",boxSizing:\"inherit\",display:\" block\",margin:\" 0\",background:\"transparent\",padding:0,fontSize:\"inherit\",position:\"relative\"},OptionsSelect=styled.select(styleResets,({theme})=>({boxSizing:\"border-box\",position:\"relative\",padding:\"6px 10px\",width:\"100%\",color:theme.input.color||\"inherit\",background:theme.input.background,borderRadius:theme.input.borderRadius,boxShadow:`${theme.input.border} 0 0 0 1px inset`,fontSize:theme.typography.size.s2-1,lineHeight:\"20px\",\"&:focus\":{boxShadow:`${theme.color.secondary} 0 0 0 1px inset`,outline:\"none\"},\"&[disabled]\":{cursor:\"not-allowed\",opacity:.5},\"::placeholder\":{color:theme.textMutedColor},\"&[multiple]\":{overflow:\"auto\",padding:0,option:{display:\"block\",padding:\"6px 10px\",marginLeft:1,marginRight:1}}})),SelectWrapper=styled.span(({theme})=>({display:\"inline-block\",lineHeight:\"normal\",overflow:\"hidden\",position:\"relative\",verticalAlign:\"top\",width:\"100%\",svg:{position:\"absolute\",zIndex:1,pointerEvents:\"none\",height:\"12px\",marginTop:\"-6px\",right:\"12px\",top:\"50%\",fill:theme.textMutedColor,path:{fill:theme.textMutedColor}}})),NO_SELECTION=\"Choose option...\",SingleSelect=({name,value:value2,options,onChange,argType})=>{let handleChange=e=>{onChange(options[e.currentTarget.value]);},selection=selectedKey(value2,options)||NO_SELECTION,controlId=getControlId(name),readonly=!!argType?.table?.readonly;return React17.createElement(SelectWrapper,null,React17.createElement(ChevronSmallDownIcon,null),React17.createElement(OptionsSelect,{disabled:readonly,id:controlId,value:selection,onChange:handleChange},React17.createElement(\"option\",{key:\"no-selection\",disabled:!0},NO_SELECTION),Object.keys(options).map(key=>React17.createElement(\"option\",{key,value:key},key))))},MultiSelect=({name,value:value2,options,onChange,argType})=>{let handleChange=e=>{let selection2=Array.from(e.currentTarget.options).filter(option=>option.selected).map(option=>option.value);onChange(selectedValues(selection2,options));},selection=selectedKeys(value2,options),controlId=getControlId(name),readonly=!!argType?.table?.readonly;return React17.createElement(SelectWrapper,null,React17.createElement(OptionsSelect,{disabled:readonly,id:controlId,multiple:!0,value:selection,onChange:handleChange},Object.keys(options).map(key=>React17.createElement(\"option\",{key,value:key},key))))},SelectControl=props=>{let{name,options}=props;return options?props.isMulti?React17.createElement(MultiSelect,{...props}):React17.createElement(SingleSelect,{...props}):(logger.warn(`Select with no options: ${name}`),React17.createElement(React17.Fragment,null,\"-\"))};var normalizeOptions=(options,labels)=>Array.isArray(options)?options.reduce((acc,item)=>(acc[labels?.[item]||String(item)]=item,acc),{}):options,Controls={check:CheckboxControl,\"inline-check\":CheckboxControl,radio:RadioControl,\"inline-radio\":RadioControl,select:SelectControl,\"multi-select\":SelectControl},OptionsControl=props=>{let{type=\"select\",labels,argType}=props,normalized={...props,argType,options:argType?normalizeOptions(argType.options,labels):{},isInline:type.includes(\"inline\"),isMulti:type.includes(\"multi\")},Control=Controls[type];if(Control)return React17.createElement(Control,{...normalized});throw new Error(`Unknown options type: ${type}`)};var VALUE=\"value\",KEY=\"key\";var ERROR=\"Error\",OBJECT=\"Object\",ARRAY=\"Array\",STRING=\"String\",NUMBER=\"Number\",BOOLEAN=\"Boolean\",DATE=\"Date\",NULL=\"Null\",UNDEFINED=\"Undefined\",FUNCTION=\"Function\",SYMBOL=\"Symbol\";var ADD_DELTA_TYPE=\"ADD_DELTA_TYPE\",REMOVE_DELTA_TYPE=\"REMOVE_DELTA_TYPE\",UPDATE_DELTA_TYPE=\"UPDATE_DELTA_TYPE\";function getObjectType(obj){return obj!==null&&typeof obj==\"object\"&&!Array.isArray(obj)&&typeof obj[Symbol.iterator]==\"function\"?\"Iterable\":Object.prototype.toString.call(obj).slice(8,-1)}function isComponentWillChange(oldValue,newValue){let oldType=getObjectType(oldValue),newType=getObjectType(newValue);return (oldType===\"Function\"||newType===\"Function\")&&newType!==oldType}var JsonAddValue=class extends Component{constructor(props){super(props),this.state={inputRefKey:null,inputRefValue:null},this.refInputValue=this.refInputValue.bind(this),this.refInputKey=this.refInputKey.bind(this),this.onKeydown=this.onKeydown.bind(this),this.onSubmit=this.onSubmit.bind(this);}componentDidMount(){let{inputRefKey,inputRefValue}=this.state,{onlyValue}=this.props;inputRefKey&&typeof inputRefKey.focus==\"function\"&&inputRefKey.focus(),onlyValue&&inputRefValue&&typeof inputRefValue.focus==\"function\"&&inputRefValue.focus(),document.addEventListener(\"keydown\",this.onKeydown);}componentWillUnmount(){document.removeEventListener(\"keydown\",this.onKeydown);}onKeydown(event){event.altKey||event.ctrlKey||event.metaKey||event.shiftKey||event.repeat||((event.code===\"Enter\"||event.key===\"Enter\")&&(event.preventDefault(),this.onSubmit()),(event.code===\"Escape\"||event.key===\"Escape\")&&(event.preventDefault(),this.props.handleCancel()));}onSubmit(){let{handleAdd,onlyValue,onSubmitValueParser,keyPath,deep}=this.props,{inputRefKey,inputRefValue}=this.state,result={};if(!onlyValue){if(!inputRefKey.value)return;result.key=inputRefKey.value;}result.newValue=onSubmitValueParser(!1,keyPath,deep,result.key,inputRefValue.value),handleAdd(result);}refInputKey(node){this.state.inputRefKey=node;}refInputValue(node){this.state.inputRefValue=node;}render(){let{handleCancel,onlyValue,addButtonElement,cancelButtonElement,inputElementGenerator,keyPath,deep}=this.props,addButtonElementLayout=cloneElement(addButtonElement,{onClick:this.onSubmit}),cancelButtonElementLayout=cloneElement(cancelButtonElement,{onClick:handleCancel}),inputElementValue=inputElementGenerator(VALUE,keyPath,deep),inputElementValueLayout=cloneElement(inputElementValue,{placeholder:\"Value\",ref:this.refInputValue}),inputElementKeyLayout=null;if(!onlyValue){let inputElementKey=inputElementGenerator(KEY,keyPath,deep);inputElementKeyLayout=cloneElement(inputElementKey,{placeholder:\"Key\",ref:this.refInputKey});}return React17.createElement(\"span\",{className:\"rejt-add-value-node\"},inputElementKeyLayout,inputElementValueLayout,cancelButtonElementLayout,addButtonElementLayout)}};JsonAddValue.defaultProps={onlyValue:!1,addButtonElement:React17.createElement(\"button\",null,\"+\"),cancelButtonElement:React17.createElement(\"button\",null,\"c\")};var JsonArray=class extends Component{constructor(props){super(props);let keyPath=[...props.keyPath,props.name];this.state={data:props.data,name:props.name,keyPath,deep:props.deep,nextDeep:props.deep+1,collapsed:props.isCollapsed(keyPath,props.deep,props.data),addFormVisible:!1},this.handleCollapseMode=this.handleCollapseMode.bind(this),this.handleRemoveItem=this.handleRemoveItem.bind(this),this.handleAddMode=this.handleAddMode.bind(this),this.handleAddValueAdd=this.handleAddValueAdd.bind(this),this.handleAddValueCancel=this.handleAddValueCancel.bind(this),this.handleEditValue=this.handleEditValue.bind(this),this.onChildUpdate=this.onChildUpdate.bind(this),this.renderCollapsed=this.renderCollapsed.bind(this),this.renderNotCollapsed=this.renderNotCollapsed.bind(this);}static getDerivedStateFromProps(props,state){return props.data!==state.data?{data:props.data}:null}onChildUpdate(childKey,childData){let{data,keyPath}=this.state;data[childKey]=childData,this.setState({data});let{onUpdate}=this.props,size=keyPath.length;onUpdate(keyPath[size-1],data);}handleAddMode(){this.setState({addFormVisible:!0});}handleCollapseMode(){this.setState(state=>({collapsed:!state.collapsed}));}handleRemoveItem(index){return ()=>{let{beforeRemoveAction,logger:logger4}=this.props,{data,keyPath,nextDeep:deep}=this.state,oldValue=data[index];beforeRemoveAction(index,keyPath,deep,oldValue).then(()=>{let deltaUpdateResult={keyPath,deep,key:index,oldValue,type:REMOVE_DELTA_TYPE};data.splice(index,1),this.setState({data});let{onUpdate,onDeltaUpdate}=this.props;onUpdate(keyPath[keyPath.length-1],data),onDeltaUpdate(deltaUpdateResult);}).catch(logger4.error);}}handleAddValueAdd({newValue}){let{data,keyPath,nextDeep:deep}=this.state,{beforeAddAction,logger:logger4}=this.props;beforeAddAction(data.length,keyPath,deep,newValue).then(()=>{let newData=[...data,newValue];this.setState({data:newData}),this.handleAddValueCancel();let{onUpdate,onDeltaUpdate}=this.props;onUpdate(keyPath[keyPath.length-1],newData),onDeltaUpdate({type:ADD_DELTA_TYPE,keyPath,deep,key:newData.length-1,newValue});}).catch(logger4.error);}handleAddValueCancel(){this.setState({addFormVisible:!1});}handleEditValue({key,value:value2}){return new Promise((resolve,reject)=>{let{beforeUpdateAction}=this.props,{data,keyPath,nextDeep:deep}=this.state,oldValue=data[key];beforeUpdateAction(key,keyPath,deep,oldValue,value2).then(()=>{data[key]=value2,this.setState({data});let{onUpdate,onDeltaUpdate}=this.props;onUpdate(keyPath[keyPath.length-1],data),onDeltaUpdate({type:UPDATE_DELTA_TYPE,keyPath,deep,key,newValue:value2,oldValue}),resolve(void 0);}).catch(reject);})}renderCollapsed(){let{name,data,keyPath,deep}=this.state,{handleRemove,readOnly,getStyle,dataType,minusMenuElement}=this.props,{minus,collapsed}=getStyle(name,data,keyPath,deep,dataType),isReadOnly=readOnly(name,data,keyPath,deep,dataType),removeItemButton=cloneElement(minusMenuElement,{onClick:handleRemove,className:\"rejt-minus-menu\",style:minus});return React17.createElement(\"span\",{className:\"rejt-collapsed\"},React17.createElement(\"span\",{className:\"rejt-collapsed-text\",style:collapsed,onClick:this.handleCollapseMode},\"[...] \",data.length,\" \",data.length===1?\"item\":\"items\"),!isReadOnly&&removeItemButton)}renderNotCollapsed(){let{name,data,keyPath,deep,addFormVisible,nextDeep}=this.state,{isCollapsed,handleRemove,onDeltaUpdate,readOnly,getStyle,dataType,addButtonElement,cancelButtonElement,editButtonElement,inputElementGenerator,textareaElementGenerator,minusMenuElement,plusMenuElement,beforeRemoveAction,beforeAddAction,beforeUpdateAction,logger:logger4,onSubmitValueParser}=this.props,{minus,plus,delimiter,ul,addForm}=getStyle(name,data,keyPath,deep,dataType),isReadOnly=readOnly(name,data,keyPath,deep,dataType),addItemButton=cloneElement(plusMenuElement,{onClick:this.handleAddMode,className:\"rejt-plus-menu\",style:plus}),removeItemButton=cloneElement(minusMenuElement,{onClick:handleRemove,className:\"rejt-minus-menu\",style:minus});return React17.createElement(\"span\",{className:\"rejt-not-collapsed\"},React17.createElement(\"span\",{className:\"rejt-not-collapsed-delimiter\",style:delimiter},\"[\"),!addFormVisible&&addItemButton,React17.createElement(\"ul\",{className:\"rejt-not-collapsed-list\",style:ul},data.map((item,index)=>React17.createElement(JsonNode,{key:index,name:index.toString(),data:item,keyPath,deep:nextDeep,isCollapsed,handleRemove:this.handleRemoveItem(index),handleUpdateValue:this.handleEditValue,onUpdate:this.onChildUpdate,onDeltaUpdate,readOnly,getStyle,addButtonElement,cancelButtonElement,editButtonElement,inputElementGenerator,textareaElementGenerator,minusMenuElement,plusMenuElement,beforeRemoveAction,beforeAddAction,beforeUpdateAction,logger:logger4,onSubmitValueParser}))),!isReadOnly&&addFormVisible&&React17.createElement(\"div\",{className:\"rejt-add-form\",style:addForm},React17.createElement(JsonAddValue,{handleAdd:this.handleAddValueAdd,handleCancel:this.handleAddValueCancel,onlyValue:!0,addButtonElement,cancelButtonElement,inputElementGenerator,keyPath,deep,onSubmitValueParser})),React17.createElement(\"span\",{className:\"rejt-not-collapsed-delimiter\",style:delimiter},\"]\"),!isReadOnly&&removeItemButton)}render(){let{name,collapsed,data,keyPath,deep}=this.state,{dataType,getStyle}=this.props,value2=collapsed?this.renderCollapsed():this.renderNotCollapsed(),style=getStyle(name,data,keyPath,deep,dataType);return React17.createElement(\"div\",{className:\"rejt-array-node\"},React17.createElement(\"span\",{onClick:this.handleCollapseMode},React17.createElement(\"span\",{className:\"rejt-name\",style:style.name},name,\" :\",\" \")),value2)}};JsonArray.defaultProps={keyPath:[],deep:0,minusMenuElement:React17.createElement(\"span\",null,\" - \"),plusMenuElement:React17.createElement(\"span\",null,\" + \")};var JsonFunctionValue=class extends Component{constructor(props){super(props);let keyPath=[...props.keyPath,props.name];this.state={value:props.value,name:props.name,keyPath,deep:props.deep,editEnabled:!1,inputRef:null},this.handleEditMode=this.handleEditMode.bind(this),this.refInput=this.refInput.bind(this),this.handleCancelEdit=this.handleCancelEdit.bind(this),this.handleEdit=this.handleEdit.bind(this),this.onKeydown=this.onKeydown.bind(this);}static getDerivedStateFromProps(props,state){return props.value!==state.value?{value:props.value}:null}componentDidUpdate(){let{editEnabled,inputRef,name,value:value2,keyPath,deep}=this.state,{readOnly,dataType}=this.props,readOnlyResult=readOnly(name,value2,keyPath,deep,dataType);editEnabled&&!readOnlyResult&&typeof inputRef.focus==\"function\"&&inputRef.focus();}componentDidMount(){document.addEventListener(\"keydown\",this.onKeydown);}componentWillUnmount(){document.removeEventListener(\"keydown\",this.onKeydown);}onKeydown(event){event.altKey||event.ctrlKey||event.metaKey||event.shiftKey||event.repeat||((event.code===\"Enter\"||event.key===\"Enter\")&&(event.preventDefault(),this.handleEdit()),(event.code===\"Escape\"||event.key===\"Escape\")&&(event.preventDefault(),this.handleCancelEdit()));}handleEdit(){let{handleUpdateValue,originalValue,logger:logger4,onSubmitValueParser,keyPath}=this.props,{inputRef,name,deep}=this.state;if(!inputRef)return;let newValue=onSubmitValueParser(!0,keyPath,deep,name,inputRef.value);handleUpdateValue({value:newValue,key:name}).then(()=>{isComponentWillChange(originalValue,newValue)||this.handleCancelEdit();}).catch(logger4.error);}handleEditMode(){this.setState({editEnabled:!0});}refInput(node){this.state.inputRef=node;}handleCancelEdit(){this.setState({editEnabled:!1});}render(){let{name,value:value2,editEnabled,keyPath,deep}=this.state,{handleRemove,originalValue,readOnly,dataType,getStyle,editButtonElement,cancelButtonElement,textareaElementGenerator,minusMenuElement,keyPath:comeFromKeyPath}=this.props,style=getStyle(name,originalValue,keyPath,deep,dataType),result=null,minusElement=null,resultOnlyResult=readOnly(name,originalValue,keyPath,deep,dataType);if(editEnabled&&!resultOnlyResult){let textareaElement=textareaElementGenerator(VALUE,comeFromKeyPath,deep,name,originalValue,dataType),editButtonElementLayout=cloneElement(editButtonElement,{onClick:this.handleEdit}),cancelButtonElementLayout=cloneElement(cancelButtonElement,{onClick:this.handleCancelEdit}),textareaElementLayout=cloneElement(textareaElement,{ref:this.refInput,defaultValue:originalValue});result=React17.createElement(\"span\",{className:\"rejt-edit-form\",style:style.editForm},textareaElementLayout,\" \",cancelButtonElementLayout,editButtonElementLayout),minusElement=null;}else {result=React17.createElement(\"span\",{className:\"rejt-value\",style:style.value,onClick:resultOnlyResult?null:this.handleEditMode},value2);let minusMenuLayout=cloneElement(minusMenuElement,{onClick:handleRemove,className:\"rejt-minus-menu\",style:style.minus});minusElement=resultOnlyResult?null:minusMenuLayout;}return React17.createElement(\"li\",{className:\"rejt-function-value-node\",style:style.li},React17.createElement(\"span\",{className:\"rejt-name\",style:style.name},name,\" :\",\" \"),result,minusElement)}};JsonFunctionValue.defaultProps={keyPath:[],deep:0,handleUpdateValue:()=>{},editButtonElement:React17.createElement(\"button\",null,\"e\"),cancelButtonElement:React17.createElement(\"button\",null,\"c\"),minusMenuElement:React17.createElement(\"span\",null,\" - \")};var JsonNode=class extends Component{constructor(props){super(props),this.state={data:props.data,name:props.name,keyPath:props.keyPath,deep:props.deep};}static getDerivedStateFromProps(props,state){return props.data!==state.data?{data:props.data}:null}render(){let{data,name,keyPath,deep}=this.state,{isCollapsed,handleRemove,handleUpdateValue,onUpdate,onDeltaUpdate,readOnly,getStyle,addButtonElement,cancelButtonElement,editButtonElement,inputElementGenerator,textareaElementGenerator,minusMenuElement,plusMenuElement,beforeRemoveAction,beforeAddAction,beforeUpdateAction,logger:logger4,onSubmitValueParser}=this.props,readOnlyTrue=()=>!0,dataType=getObjectType(data);switch(dataType){case ERROR:return React17.createElement(JsonObject,{data,name,isCollapsed,keyPath,deep,handleRemove,onUpdate,onDeltaUpdate,readOnly:readOnlyTrue,dataType,getStyle,addButtonElement,cancelButtonElement,editButtonElement,inputElementGenerator,textareaElementGenerator,minusMenuElement,plusMenuElement,beforeRemoveAction,beforeAddAction,beforeUpdateAction,logger:logger4,onSubmitValueParser});case OBJECT:return React17.createElement(JsonObject,{data,name,isCollapsed,keyPath,deep,handleRemove,onUpdate,onDeltaUpdate,readOnly,dataType,getStyle,addButtonElement,cancelButtonElement,editButtonElement,inputElementGenerator,textareaElementGenerator,minusMenuElement,plusMenuElement,beforeRemoveAction,beforeAddAction,beforeUpdateAction,logger:logger4,onSubmitValueParser});case ARRAY:return React17.createElement(JsonArray,{data,name,isCollapsed,keyPath,deep,handleRemove,onUpdate,onDeltaUpdate,readOnly,dataType,getStyle,addButtonElement,cancelButtonElement,editButtonElement,inputElementGenerator,textareaElementGenerator,minusMenuElement,plusMenuElement,beforeRemoveAction,beforeAddAction,beforeUpdateAction,logger:logger4,onSubmitValueParser});case STRING:return React17.createElement(JsonValue,{name,value:`\"${data}\"`,originalValue:data,keyPath,deep,handleRemove,handleUpdateValue,readOnly,dataType,getStyle,cancelButtonElement,editButtonElement,inputElementGenerator,minusMenuElement,logger:logger4,onSubmitValueParser});case NUMBER:return React17.createElement(JsonValue,{name,value:data,originalValue:data,keyPath,deep,handleRemove,handleUpdateValue,readOnly,dataType,getStyle,cancelButtonElement,editButtonElement,inputElementGenerator,minusMenuElement,logger:logger4,onSubmitValueParser});case BOOLEAN:return React17.createElement(JsonValue,{name,value:data?\"true\":\"false\",originalValue:data,keyPath,deep,handleRemove,handleUpdateValue,readOnly,dataType,getStyle,cancelButtonElement,editButtonElement,inputElementGenerator,minusMenuElement,logger:logger4,onSubmitValueParser});case DATE:return React17.createElement(JsonValue,{name,value:data.toISOString(),originalValue:data,keyPath,deep,handleRemove,handleUpdateValue,readOnly:readOnlyTrue,dataType,getStyle,cancelButtonElement,editButtonElement,inputElementGenerator,minusMenuElement,logger:logger4,onSubmitValueParser});case NULL:return React17.createElement(JsonValue,{name,value:\"null\",originalValue:\"null\",keyPath,deep,handleRemove,handleUpdateValue,readOnly,dataType,getStyle,cancelButtonElement,editButtonElement,inputElementGenerator,minusMenuElement,logger:logger4,onSubmitValueParser});case UNDEFINED:return React17.createElement(JsonValue,{name,value:\"undefined\",originalValue:\"undefined\",keyPath,deep,handleRemove,handleUpdateValue,readOnly,dataType,getStyle,cancelButtonElement,editButtonElement,inputElementGenerator,minusMenuElement,logger:logger4,onSubmitValueParser});case FUNCTION:return React17.createElement(JsonFunctionValue,{name,value:data.toString(),originalValue:data,keyPath,deep,handleRemove,handleUpdateValue,readOnly,dataType,getStyle,cancelButtonElement,editButtonElement,textareaElementGenerator,minusMenuElement,logger:logger4,onSubmitValueParser});case SYMBOL:return React17.createElement(JsonValue,{name,value:data.toString(),originalValue:data,keyPath,deep,handleRemove,handleUpdateValue,readOnly:readOnlyTrue,dataType,getStyle,cancelButtonElement,editButtonElement,inputElementGenerator,minusMenuElement,logger:logger4,onSubmitValueParser});default:return null}}};JsonNode.defaultProps={keyPath:[],deep:0};var JsonObject=class extends Component{constructor(props){super(props);let keyPath=props.deep===-1?[]:[...props.keyPath,props.name];this.state={name:props.name,data:props.data,keyPath,deep:props.deep,nextDeep:props.deep+1,collapsed:props.isCollapsed(keyPath,props.deep,props.data),addFormVisible:!1},this.handleCollapseMode=this.handleCollapseMode.bind(this),this.handleRemoveValue=this.handleRemoveValue.bind(this),this.handleAddMode=this.handleAddMode.bind(this),this.handleAddValueAdd=this.handleAddValueAdd.bind(this),this.handleAddValueCancel=this.handleAddValueCancel.bind(this),this.handleEditValue=this.handleEditValue.bind(this),this.onChildUpdate=this.onChildUpdate.bind(this),this.renderCollapsed=this.renderCollapsed.bind(this),this.renderNotCollapsed=this.renderNotCollapsed.bind(this);}static getDerivedStateFromProps(props,state){return props.data!==state.data?{data:props.data}:null}onChildUpdate(childKey,childData){let{data,keyPath}=this.state;data[childKey]=childData,this.setState({data});let{onUpdate}=this.props,size=keyPath.length;onUpdate(keyPath[size-1],data);}handleAddMode(){this.setState({addFormVisible:!0});}handleAddValueCancel(){this.setState({addFormVisible:!1});}handleAddValueAdd({key,newValue}){let{data,keyPath,nextDeep:deep}=this.state,{beforeAddAction,logger:logger4}=this.props;beforeAddAction(key,keyPath,deep,newValue).then(()=>{data[key]=newValue,this.setState({data}),this.handleAddValueCancel();let{onUpdate,onDeltaUpdate}=this.props;onUpdate(keyPath[keyPath.length-1],data),onDeltaUpdate({type:ADD_DELTA_TYPE,keyPath,deep,key,newValue});}).catch(logger4.error);}handleRemoveValue(key){return ()=>{let{beforeRemoveAction,logger:logger4}=this.props,{data,keyPath,nextDeep:deep}=this.state,oldValue=data[key];beforeRemoveAction(key,keyPath,deep,oldValue).then(()=>{let deltaUpdateResult={keyPath,deep,key,oldValue,type:REMOVE_DELTA_TYPE};delete data[key],this.setState({data});let{onUpdate,onDeltaUpdate}=this.props;onUpdate(keyPath[keyPath.length-1],data),onDeltaUpdate(deltaUpdateResult);}).catch(logger4.error);}}handleCollapseMode(){this.setState(state=>({collapsed:!state.collapsed}));}handleEditValue({key,value:value2}){return new Promise((resolve,reject)=>{let{beforeUpdateAction}=this.props,{data,keyPath,nextDeep:deep}=this.state,oldValue=data[key];beforeUpdateAction(key,keyPath,deep,oldValue,value2).then(()=>{data[key]=value2,this.setState({data});let{onUpdate,onDeltaUpdate}=this.props;onUpdate(keyPath[keyPath.length-1],data),onDeltaUpdate({type:UPDATE_DELTA_TYPE,keyPath,deep,key,newValue:value2,oldValue}),resolve();}).catch(reject);})}renderCollapsed(){let{name,keyPath,deep,data}=this.state,{handleRemove,readOnly,dataType,getStyle,minusMenuElement}=this.props,{minus,collapsed}=getStyle(name,data,keyPath,deep,dataType),keyList=Object.getOwnPropertyNames(data),isReadOnly=readOnly(name,data,keyPath,deep,dataType),removeItemButton=cloneElement(minusMenuElement,{onClick:handleRemove,className:\"rejt-minus-menu\",style:minus});return React17.createElement(\"span\",{className:\"rejt-collapsed\"},React17.createElement(\"span\",{className:\"rejt-collapsed-text\",style:collapsed,onClick:this.handleCollapseMode},\"{...}\",\" \",keyList.length,\" \",keyList.length===1?\"key\":\"keys\"),!isReadOnly&&removeItemButton)}renderNotCollapsed(){let{name,data,keyPath,deep,nextDeep,addFormVisible}=this.state,{isCollapsed,handleRemove,onDeltaUpdate,readOnly,getStyle,dataType,addButtonElement,cancelButtonElement,editButtonElement,inputElementGenerator,textareaElementGenerator,minusMenuElement,plusMenuElement,beforeRemoveAction,beforeAddAction,beforeUpdateAction,logger:logger4,onSubmitValueParser}=this.props,{minus,plus,addForm,ul,delimiter}=getStyle(name,data,keyPath,deep,dataType),keyList=Object.getOwnPropertyNames(data),isReadOnly=readOnly(name,data,keyPath,deep,dataType),addItemButton=cloneElement(plusMenuElement,{onClick:this.handleAddMode,className:\"rejt-plus-menu\",style:plus}),removeItemButton=cloneElement(minusMenuElement,{onClick:handleRemove,className:\"rejt-minus-menu\",style:minus}),list=keyList.map(key=>React17.createElement(JsonNode,{key,name:key,data:data[key],keyPath,deep:nextDeep,isCollapsed,handleRemove:this.handleRemoveValue(key),handleUpdateValue:this.handleEditValue,onUpdate:this.onChildUpdate,onDeltaUpdate,readOnly,getStyle,addButtonElement,cancelButtonElement,editButtonElement,inputElementGenerator,textareaElementGenerator,minusMenuElement,plusMenuElement,beforeRemoveAction,beforeAddAction,beforeUpdateAction,logger:logger4,onSubmitValueParser}));return React17.createElement(\"span\",{className:\"rejt-not-collapsed\"},React17.createElement(\"span\",{className:\"rejt-not-collapsed-delimiter\",style:delimiter},\"{\"),!isReadOnly&&addItemButton,React17.createElement(\"ul\",{className:\"rejt-not-collapsed-list\",style:ul},list),!isReadOnly&&addFormVisible&&React17.createElement(\"div\",{className:\"rejt-add-form\",style:addForm},React17.createElement(JsonAddValue,{handleAdd:this.handleAddValueAdd,handleCancel:this.handleAddValueCancel,addButtonElement,cancelButtonElement,inputElementGenerator,keyPath,deep,onSubmitValueParser})),React17.createElement(\"span\",{className:\"rejt-not-collapsed-delimiter\",style:delimiter},\"}\"),!isReadOnly&&removeItemButton)}render(){let{name,collapsed,data,keyPath,deep}=this.state,{getStyle,dataType}=this.props,value2=collapsed?this.renderCollapsed():this.renderNotCollapsed(),style=getStyle(name,data,keyPath,deep,dataType);return React17.createElement(\"div\",{className:\"rejt-object-node\"},React17.createElement(\"span\",{onClick:this.handleCollapseMode},React17.createElement(\"span\",{className:\"rejt-name\",style:style.name},name,\" :\",\" \")),value2)}};JsonObject.defaultProps={keyPath:[],deep:0,minusMenuElement:React17.createElement(\"span\",null,\" - \"),plusMenuElement:React17.createElement(\"span\",null,\" + \")};var JsonValue=class extends Component{constructor(props){super(props);let keyPath=[...props.keyPath,props.name];this.state={value:props.value,name:props.name,keyPath,deep:props.deep,editEnabled:!1,inputRef:null},this.handleEditMode=this.handleEditMode.bind(this),this.refInput=this.refInput.bind(this),this.handleCancelEdit=this.handleCancelEdit.bind(this),this.handleEdit=this.handleEdit.bind(this),this.onKeydown=this.onKeydown.bind(this);}static getDerivedStateFromProps(props,state){return props.value!==state.value?{value:props.value}:null}componentDidUpdate(){let{editEnabled,inputRef,name,value:value2,keyPath,deep}=this.state,{readOnly,dataType}=this.props,isReadOnly=readOnly(name,value2,keyPath,deep,dataType);editEnabled&&!isReadOnly&&typeof inputRef.focus==\"function\"&&inputRef.focus();}componentDidMount(){document.addEventListener(\"keydown\",this.onKeydown);}componentWillUnmount(){document.removeEventListener(\"keydown\",this.onKeydown);}onKeydown(event){event.altKey||event.ctrlKey||event.metaKey||event.shiftKey||event.repeat||((event.code===\"Enter\"||event.key===\"Enter\")&&(event.preventDefault(),this.handleEdit()),(event.code===\"Escape\"||event.key===\"Escape\")&&(event.preventDefault(),this.handleCancelEdit()));}handleEdit(){let{handleUpdateValue,originalValue,logger:logger4,onSubmitValueParser,keyPath}=this.props,{inputRef,name,deep}=this.state;if(!inputRef)return;let newValue=onSubmitValueParser(!0,keyPath,deep,name,inputRef.value);handleUpdateValue({value:newValue,key:name}).then(()=>{isComponentWillChange(originalValue,newValue)||this.handleCancelEdit();}).catch(logger4.error);}handleEditMode(){this.setState({editEnabled:!0});}refInput(node){this.state.inputRef=node;}handleCancelEdit(){this.setState({editEnabled:!1});}render(){let{name,value:value2,editEnabled,keyPath,deep}=this.state,{handleRemove,originalValue,readOnly,dataType,getStyle,editButtonElement,cancelButtonElement,inputElementGenerator,minusMenuElement,keyPath:comeFromKeyPath}=this.props,style=getStyle(name,originalValue,keyPath,deep,dataType),isReadOnly=readOnly(name,originalValue,keyPath,deep,dataType),isEditing=editEnabled&&!isReadOnly,inputElement=inputElementGenerator(VALUE,comeFromKeyPath,deep,name,originalValue,dataType),editButtonElementLayout=cloneElement(editButtonElement,{onClick:this.handleEdit}),cancelButtonElementLayout=cloneElement(cancelButtonElement,{onClick:this.handleCancelEdit}),inputElementLayout=cloneElement(inputElement,{ref:this.refInput,defaultValue:JSON.stringify(originalValue)}),minusMenuLayout=cloneElement(minusMenuElement,{onClick:handleRemove,className:\"rejt-minus-menu\",style:style.minus});return React17.createElement(\"li\",{className:\"rejt-value-node\",style:style.li},React17.createElement(\"span\",{className:\"rejt-name\",style:style.name},name,\" : \"),isEditing?React17.createElement(\"span\",{className:\"rejt-edit-form\",style:style.editForm},inputElementLayout,\" \",cancelButtonElementLayout,editButtonElementLayout):React17.createElement(\"span\",{className:\"rejt-value\",style:style.value,onClick:isReadOnly?null:this.handleEditMode},String(value2)),!isReadOnly&&!isEditing&&minusMenuLayout)}};JsonValue.defaultProps={keyPath:[],deep:0,handleUpdateValue:()=>Promise.resolve(),editButtonElement:React17.createElement(\"button\",null,\"e\"),cancelButtonElement:React17.createElement(\"button\",null,\"c\"),minusMenuElement:React17.createElement(\"span\",null,\" - \")};var object={minus:{color:\"red\"},plus:{color:\"green\"},collapsed:{color:\"grey\"},delimiter:{},ul:{padding:\"0px\",margin:\"0 0 0 25px\",listStyle:\"none\"},name:{color:\"#2287CD\"},addForm:{}},array={minus:{color:\"red\"},plus:{color:\"green\"},collapsed:{color:\"grey\"},delimiter:{},ul:{padding:\"0px\",margin:\"0 0 0 25px\",listStyle:\"none\"},name:{color:\"#2287CD\"},addForm:{}},value={minus:{color:\"red\"},editForm:{},value:{color:\"#7bba3d\"},li:{minHeight:\"22px\",lineHeight:\"22px\",outline:\"0px\"},name:{color:\"#2287CD\"}};function parse3(string){let result=string;if(result.indexOf(\"function\")===0)return (0, eval)(`(${result})`);try{result=JSON.parse(string);}catch{}return result}var JsonTree=class extends Component{constructor(props){super(props),this.state={data:props.data,rootName:props.rootName},this.onUpdate=this.onUpdate.bind(this),this.removeRoot=this.removeRoot.bind(this);}static getDerivedStateFromProps(props,state){return props.data!==state.data||props.rootName!==state.rootName?{data:props.data,rootName:props.rootName}:null}onUpdate(key,data){this.setState({data}),this.props.onFullyUpdate(data);}removeRoot(){this.onUpdate(null,null);}render(){let{data,rootName}=this.state,{isCollapsed,onDeltaUpdate,readOnly,getStyle,addButtonElement,cancelButtonElement,editButtonElement,inputElement,textareaElement,minusMenuElement,plusMenuElement,beforeRemoveAction,beforeAddAction,beforeUpdateAction,logger:logger4,onSubmitValueParser,fallback=null}=this.props,dataType=getObjectType(data),readOnlyFunction=readOnly;getObjectType(readOnly)===\"Boolean\"&&(readOnlyFunction=()=>readOnly);let inputElementFunction=inputElement;inputElement&&getObjectType(inputElement)!==\"Function\"&&(inputElementFunction=()=>inputElement);let textareaElementFunction=textareaElement;return textareaElement&&getObjectType(textareaElement)!==\"Function\"&&(textareaElementFunction=()=>textareaElement),dataType===\"Object\"||dataType===\"Array\"?React17.createElement(\"div\",{className:\"rejt-tree\"},React17.createElement(JsonNode,{data,name:rootName,deep:-1,isCollapsed,onUpdate:this.onUpdate,onDeltaUpdate,readOnly:readOnlyFunction,getStyle,addButtonElement,cancelButtonElement,editButtonElement,inputElementGenerator:inputElementFunction,textareaElementGenerator:textareaElementFunction,minusMenuElement,plusMenuElement,handleRemove:this.removeRoot,beforeRemoveAction,beforeAddAction,beforeUpdateAction,logger:logger4,onSubmitValueParser})):fallback}};JsonTree.defaultProps={rootName:\"root\",isCollapsed:(keyPath,deep)=>deep!==-1,getStyle:(keyName,data,keyPath,deep,dataType)=>{switch(dataType){case\"Object\":case\"Error\":return object;case\"Array\":return array;default:return value}},readOnly:()=>!1,onFullyUpdate:()=>{},onDeltaUpdate:()=>{},beforeRemoveAction:()=>Promise.resolve(),beforeAddAction:()=>Promise.resolve(),beforeUpdateAction:()=>Promise.resolve(),logger:{error:()=>{}},onSubmitValueParser:(isEditMode,keyPath,deep,name,rawValue)=>parse3(rawValue),inputElement:()=>React17.createElement(\"input\",null),textareaElement:()=>React17.createElement(\"textarea\",null),fallback:null};var {window:globalWindow2}=global,Wrapper6=styled.div(({theme})=>({position:\"relative\",display:\"flex\",'&[aria-readonly=\"true\"]':{opacity:.5},\".rejt-tree\":{marginLeft:\"1rem\",fontSize:\"13px\"},\".rejt-value-node, .rejt-object-node > .rejt-collapsed, .rejt-array-node > .rejt-collapsed, .rejt-object-node > .rejt-not-collapsed, .rejt-array-node > .rejt-not-collapsed\":{\"& > svg\":{opacity:0,transition:\"opacity 0.2s\"}},\".rejt-value-node:hover, .rejt-object-node:hover > .rejt-collapsed, .rejt-array-node:hover > .rejt-collapsed, .rejt-object-node:hover > .rejt-not-collapsed, .rejt-array-node:hover > .rejt-not-collapsed\":{\"& > svg\":{opacity:1}},\".rejt-edit-form button\":{display:\"none\"},\".rejt-add-form\":{marginLeft:10},\".rejt-add-value-node\":{display:\"inline-flex\",alignItems:\"center\"},\".rejt-name\":{lineHeight:\"22px\"},\".rejt-not-collapsed-delimiter\":{lineHeight:\"22px\"},\".rejt-plus-menu\":{marginLeft:5},\".rejt-object-node > span > *, .rejt-array-node > span > *\":{position:\"relative\",zIndex:2},\".rejt-object-node, .rejt-array-node\":{position:\"relative\"},\".rejt-object-node > span:first-of-type::after, .rejt-array-node > span:first-of-type::after, .rejt-collapsed::before, .rejt-not-collapsed::before\":{content:'\"\"',position:\"absolute\",top:0,display:\"block\",width:\"100%\",marginLeft:\"-1rem\",padding:\"0 4px 0 1rem\",height:22},\".rejt-collapsed::before, .rejt-not-collapsed::before\":{zIndex:1,background:\"transparent\",borderRadius:4,transition:\"background 0.2s\",pointerEvents:\"none\",opacity:.1},\".rejt-object-node:hover, .rejt-array-node:hover\":{\"& > .rejt-collapsed::before, & > .rejt-not-collapsed::before\":{background:theme.color.secondary}},\".rejt-collapsed::after, .rejt-not-collapsed::after\":{content:'\"\"',position:\"absolute\",display:\"inline-block\",pointerEvents:\"none\",width:0,height:0},\".rejt-collapsed::after\":{left:-8,top:8,borderTop:\"3px solid transparent\",borderBottom:\"3px solid transparent\",borderLeft:\"3px solid rgba(153,153,153,0.6)\"},\".rejt-not-collapsed::after\":{left:-10,top:10,borderTop:\"3px solid rgba(153,153,153,0.6)\",borderLeft:\"3px solid transparent\",borderRight:\"3px solid transparent\"},\".rejt-value\":{display:\"inline-block\",border:\"1px solid transparent\",borderRadius:4,margin:\"1px 0\",padding:\"0 4px\",cursor:\"text\",color:theme.color.defaultText},\".rejt-value-node:hover > .rejt-value\":{background:theme.color.lighter,borderColor:theme.appBorderColor}})),ButtonInline=styled.button(({theme,primary})=>({border:0,height:20,margin:1,borderRadius:4,background:primary?theme.color.secondary:\"transparent\",color:primary?theme.color.lightest:theme.color.dark,fontWeight:primary?\"bold\":\"normal\",cursor:\"pointer\",order:primary?\"initial\":9})),ActionAddIcon=styled(AddIcon)(({theme,disabled})=>({display:\"inline-block\",verticalAlign:\"middle\",width:15,height:15,padding:3,marginLeft:5,cursor:disabled?\"not-allowed\":\"pointer\",color:theme.textMutedColor,\"&:hover\":disabled?{}:{color:theme.color.ancillary},\"svg + &\":{marginLeft:0}})),ActionSubstractIcon=styled(SubtractIcon)(({theme,disabled})=>({display:\"inline-block\",verticalAlign:\"middle\",width:15,height:15,padding:3,marginLeft:5,cursor:disabled?\"not-allowed\":\"pointer\",color:theme.textMutedColor,\"&:hover\":disabled?{}:{color:theme.color.negative},\"svg + &\":{marginLeft:0}})),Input=styled.input(({theme,placeholder})=>({outline:0,margin:placeholder?1:\"1px 0\",padding:\"3px 4px\",color:theme.color.defaultText,background:theme.background.app,border:`1px solid ${theme.appBorderColor}`,borderRadius:4,lineHeight:\"14px\",width:placeholder===\"Key\"?80:120,\"&:focus\":{border:`1px solid ${theme.color.secondary}`}})),RawButton=styled(IconButton)(({theme})=>({position:\"absolute\",zIndex:2,top:2,right:2,height:21,padding:\"0 3px\",background:theme.background.bar,border:`1px solid ${theme.appBorderColor}`,borderRadius:3,color:theme.textMutedColor,fontSize:\"9px\",fontWeight:\"bold\",textDecoration:\"none\",span:{marginLeft:3,marginTop:1}})),RawInput=styled(Form.Textarea)(({theme})=>({flex:1,padding:\"7px 6px\",fontFamily:theme.typography.fonts.mono,fontSize:\"12px\",lineHeight:\"18px\",\"&::placeholder\":{fontFamily:theme.typography.fonts.base,fontSize:\"13px\"},\"&:placeholder-shown\":{padding:\"7px 10px\"}})),ENTER_EVENT={bubbles:!0,cancelable:!0,key:\"Enter\",code:\"Enter\",keyCode:13},dispatchEnterKey=event=>{event.currentTarget.dispatchEvent(new globalWindow2.KeyboardEvent(\"keydown\",ENTER_EVENT));},selectValue=event=>{event.currentTarget.select();},getCustomStyleFunction=theme=>()=>({name:{color:theme.color.secondary},collapsed:{color:theme.color.dark},ul:{listStyle:\"none\",margin:\"0 0 0 1rem\",padding:0},li:{outline:0}}),ObjectControl=({name,value:value2,onChange,argType})=>{let theme=useTheme(),data=useMemo(()=>value2&&cloneDeep(value2),[value2]),hasData=data!=null,[showRaw,setShowRaw]=useState(!hasData),[parseError,setParseError]=useState(null),readonly=!!argType?.table?.readonly,updateRaw=useCallback(raw=>{try{raw&&onChange(JSON.parse(raw)),setParseError(void 0);}catch(e){setParseError(e);}},[onChange]),[forceVisible,setForceVisible]=useState(!1),onForceVisible=useCallback(()=>{onChange({}),setForceVisible(!0);},[setForceVisible]),htmlElRef=useRef(null);if(useEffect(()=>{forceVisible&&htmlElRef.current&&htmlElRef.current.select();},[forceVisible]),!hasData)return React17.createElement(Button,{disabled:readonly,id:getControlSetterButtonId(name),onClick:onForceVisible},\"Set object\");let rawJSONForm=React17.createElement(RawInput,{ref:htmlElRef,id:getControlId(name),name,defaultValue:value2===null?\"\":JSON.stringify(value2,null,2),onBlur:event=>updateRaw(event.target.value),placeholder:\"Edit JSON string...\",autoFocus:forceVisible,valid:parseError?\"error\":null,readOnly:readonly}),isObjectOrArray=Array.isArray(value2)||typeof value2==\"object\"&&value2?.constructor===Object;return React17.createElement(Wrapper6,{\"aria-readonly\":readonly},isObjectOrArray&&React17.createElement(RawButton,{onClick:e=>{e.preventDefault(),setShowRaw(v=>!v);}},showRaw?React17.createElement(EyeCloseIcon,null):React17.createElement(EyeIcon,null),React17.createElement(\"span\",null,\"RAW\")),showRaw?rawJSONForm:React17.createElement(JsonTree,{readOnly:readonly||!isObjectOrArray,isCollapsed:isObjectOrArray?void 0:()=>!0,data,rootName:name,onFullyUpdate:onChange,getStyle:getCustomStyleFunction(theme),cancelButtonElement:React17.createElement(ButtonInline,{type:\"button\"},\"Cancel\"),editButtonElement:React17.createElement(ButtonInline,{type:\"submit\"},\"Save\"),addButtonElement:React17.createElement(ButtonInline,{type:\"submit\",primary:!0},\"Save\"),plusMenuElement:React17.createElement(ActionAddIcon,null),minusMenuElement:React17.createElement(ActionSubstractIcon,null),inputElement:(_,__,___,key)=>key?React17.createElement(Input,{onFocus:selectValue,onBlur:dispatchEnterKey}):React17.createElement(Input,null),fallback:rawJSONForm}))};var RangeInput=styled.input(({theme,min,max,value:value2,disabled})=>({\"&\":{width:\"100%\",backgroundColor:\"transparent\",appearance:\"none\"},\"&::-webkit-slider-runnable-track\":{background:theme.base===\"light\"?`linear-gradient(to right, \n            ${theme.color.green} 0%, ${theme.color.green} ${(value2-min)/(max-min)*100}%, \n            ${darken(.02,theme.input.background)} ${(value2-min)/(max-min)*100}%, \n            ${darken(.02,theme.input.background)} 100%)`:`linear-gradient(to right, \n            ${theme.color.green} 0%, ${theme.color.green} ${(value2-min)/(max-min)*100}%, \n            ${lighten(.02,theme.input.background)} ${(value2-min)/(max-min)*100}%, \n            ${lighten(.02,theme.input.background)} 100%)`,boxShadow:`${theme.appBorderColor} 0 0 0 1px inset`,borderRadius:6,width:\"100%\",height:6,cursor:disabled?\"not-allowed\":\"pointer\"},\"&::-webkit-slider-thumb\":{marginTop:\"-6px\",width:16,height:16,border:`1px solid ${rgba(theme.appBorderColor,.2)}`,borderRadius:\"50px\",boxShadow:`0 1px 3px 0px ${rgba(theme.appBorderColor,.2)}`,cursor:disabled?\"not-allowed\":\"grab\",appearance:\"none\",background:`${theme.input.background}`,transition:\"all 150ms ease-out\",\"&:hover\":{background:`${darken(.05,theme.input.background)}`,transform:\"scale3d(1.1, 1.1, 1.1) translateY(-1px)\",transition:\"all 50ms ease-out\"},\"&:active\":{background:`${theme.input.background}`,transform:\"scale3d(1, 1, 1) translateY(0px)\",cursor:disabled?\"not-allowed\":\"grab\"}},\"&:focus\":{outline:\"none\",\"&::-webkit-slider-runnable-track\":{borderColor:rgba(theme.color.secondary,.4)},\"&::-webkit-slider-thumb\":{borderColor:theme.color.secondary,boxShadow:`0 0px 5px 0px ${theme.color.secondary}`}},\"&::-moz-range-track\":{background:theme.base===\"light\"?`linear-gradient(to right, \n            ${theme.color.green} 0%, ${theme.color.green} ${(value2-min)/(max-min)*100}%, \n            ${darken(.02,theme.input.background)} ${(value2-min)/(max-min)*100}%, \n            ${darken(.02,theme.input.background)} 100%)`:`linear-gradient(to right, \n            ${theme.color.green} 0%, ${theme.color.green} ${(value2-min)/(max-min)*100}%, \n            ${lighten(.02,theme.input.background)} ${(value2-min)/(max-min)*100}%, \n            ${lighten(.02,theme.input.background)} 100%)`,boxShadow:`${theme.appBorderColor} 0 0 0 1px inset`,borderRadius:6,width:\"100%\",height:6,cursor:disabled?\"not-allowed\":\"pointer\",outline:\"none\"},\"&::-moz-range-thumb\":{width:16,height:16,border:`1px solid ${rgba(theme.appBorderColor,.2)}`,borderRadius:\"50px\",boxShadow:`0 1px 3px 0px ${rgba(theme.appBorderColor,.2)}`,cursor:disabled?\"not-allowed\":\"grap\",background:`${theme.input.background}`,transition:\"all 150ms ease-out\",\"&:hover\":{background:`${darken(.05,theme.input.background)}`,transform:\"scale3d(1.1, 1.1, 1.1) translateY(-1px)\",transition:\"all 50ms ease-out\"},\"&:active\":{background:`${theme.input.background}`,transform:\"scale3d(1, 1, 1) translateY(0px)\",cursor:\"grabbing\"}},\"&::-ms-track\":{background:theme.base===\"light\"?`linear-gradient(to right, \n            ${theme.color.green} 0%, ${theme.color.green} ${(value2-min)/(max-min)*100}%, \n            ${darken(.02,theme.input.background)} ${(value2-min)/(max-min)*100}%, \n            ${darken(.02,theme.input.background)} 100%)`:`linear-gradient(to right, \n            ${theme.color.green} 0%, ${theme.color.green} ${(value2-min)/(max-min)*100}%, \n            ${lighten(.02,theme.input.background)} ${(value2-min)/(max-min)*100}%, \n            ${lighten(.02,theme.input.background)} 100%)`,boxShadow:`${theme.appBorderColor} 0 0 0 1px inset`,color:\"transparent\",width:\"100%\",height:\"6px\",cursor:\"pointer\"},\"&::-ms-fill-lower\":{borderRadius:6},\"&::-ms-fill-upper\":{borderRadius:6},\"&::-ms-thumb\":{width:16,height:16,background:`${theme.input.background}`,border:`1px solid ${rgba(theme.appBorderColor,.2)}`,borderRadius:50,cursor:\"grab\",marginTop:0},\"@supports (-ms-ime-align:auto)\":{\"input[type=range]\":{margin:\"0\"}}})),RangeLabel=styled.span({paddingLeft:5,paddingRight:5,fontSize:12,whiteSpace:\"nowrap\",fontFeatureSettings:\"tnum\",fontVariantNumeric:\"tabular-nums\",\"[aria-readonly=true] &\":{opacity:.5}}),RangeCurrentAndMaxLabel=styled(RangeLabel)(({numberOFDecimalsPlaces,max})=>({width:`${numberOFDecimalsPlaces+max.toString().length*2+3}ch`,textAlign:\"right\",flexShrink:0})),RangeWrapper=styled.div({display:\"flex\",alignItems:\"center\",width:\"100%\"});function getNumberOfDecimalPlaces(number){let match=number.toString().match(/(?:\\.(\\d+))?(?:[eE]([+-]?\\d+))?$/);return match?Math.max(0,(match[1]?match[1].length:0)-(match[2]?+match[2]:0)):0}var RangeControl=({name,value:value2,onChange,min=0,max=100,step=1,onBlur,onFocus,argType})=>{let handleChange=event=>{onChange(parse2(event.target.value));},hasValue=value2!==void 0,numberOFDecimalsPlaces=useMemo(()=>getNumberOfDecimalPlaces(step),[step]),readonly=!!argType?.table?.readonly;return React17.createElement(RangeWrapper,{\"aria-readonly\":readonly},React17.createElement(RangeLabel,null,min),React17.createElement(RangeInput,{id:getControlId(name),type:\"range\",disabled:readonly,onChange:handleChange,name,value:value2,min,max,step,onFocus,onBlur}),React17.createElement(RangeCurrentAndMaxLabel,{numberOFDecimalsPlaces,max},hasValue?value2.toFixed(numberOFDecimalsPlaces):\"--\",\" / \",max))};var Wrapper7=styled.label({display:\"flex\"}),MaxLength=styled.div(({isMaxed})=>({marginLeft:\"0.75rem\",paddingTop:\"0.35rem\",color:isMaxed?\"red\":void 0})),TextControl=({name,value:value2,onChange,onFocus,onBlur,maxLength,argType})=>{let handleChange=event=>{onChange(event.target.value);},readonly=!!argType?.table?.readonly,[forceVisible,setForceVisible]=useState(!1),onForceVisible=useCallback(()=>{onChange(\"\"),setForceVisible(!0);},[setForceVisible]);if(value2===void 0)return React17.createElement(Button,{variant:\"outline\",size:\"medium\",disabled:readonly,id:getControlSetterButtonId(name),onClick:onForceVisible},\"Set string\");let isValid=typeof value2==\"string\";return React17.createElement(Wrapper7,null,React17.createElement(Form.Textarea,{id:getControlId(name),maxLength,onChange:handleChange,disabled:readonly,size:\"flex\",placeholder:\"Edit string...\",autoFocus:forceVisible,valid:isValid?null:\"error\",name,value:isValid?value2:\"\",onFocus,onBlur}),maxLength&&React17.createElement(MaxLength,{isMaxed:value2?.length===maxLength},value2?.length??0,\" / \",maxLength))};var FileInput=styled(Form.Input)({padding:10});function revokeOldUrls(urls){urls.forEach(url=>{url.startsWith(\"blob:\")&&URL.revokeObjectURL(url);});}var FilesControl=({onChange,name,accept=\"image/*\",value:value2,argType})=>{let inputElement=useRef(null),readonly=argType?.control?.readOnly;function handleFileChange(e){if(!e.target.files)return;let fileUrls=Array.from(e.target.files).map(file=>URL.createObjectURL(file));onChange(fileUrls),revokeOldUrls(value2);}return useEffect(()=>{value2==null&&inputElement.current&&(inputElement.current.value=null);},[value2,name]),React17.createElement(FileInput,{ref:inputElement,id:getControlId(name),type:\"file\",name,multiple:!0,disabled:readonly,onChange:handleFileChange,accept,size:\"flex\"})};var LazyColorControl=lazy(()=>import('./Color-PRSJMWNM.mjs')),ColorControl=props=>React17.createElement(Suspense,{fallback:React17.createElement(\"div\",null)},React17.createElement(LazyColorControl,{...props}));var Controls2={array:ObjectControl,object:ObjectControl,boolean:BooleanControl,color:ColorControl,date:DateControl,number:NumberControl,check:OptionsControl,\"inline-check\":OptionsControl,radio:OptionsControl,\"inline-radio\":OptionsControl,select:OptionsControl,\"multi-select\":OptionsControl,range:RangeControl,text:TextControl,file:FilesControl},NoControl=()=>React17.createElement(React17.Fragment,null,\"-\"),ArgControl=({row,arg,updateArgs,isHovered})=>{let{key,control}=row,[isFocused,setFocused]=useState(!1),[boxedValue,setBoxedValue]=useState({value:arg});useEffect(()=>{isFocused||setBoxedValue({value:arg});},[isFocused,arg]);let onChange=useCallback(argVal=>(setBoxedValue({value:argVal}),updateArgs({[key]:argVal}),argVal),[updateArgs,key]),onBlur=useCallback(()=>setFocused(!1),[]),onFocus=useCallback(()=>setFocused(!0),[]);if(!control||control.disable){let canBeSetup=control?.disable!==!0&&row?.type?.name!==\"function\";return isHovered&&canBeSetup?React17.createElement(Link,{href:\"https://storybook.js.org/docs/react/essentials/controls\",target:\"_blank\",withArrow:!0},\"Setup controls\"):React17.createElement(NoControl,null)}let props={name:key,argType:row,value:boxedValue.value,onChange,onBlur,onFocus},Control=Controls2[control.type]||NoControl;return React17.createElement(Control,{...props,...control,controlType:control.type})};var Name=styled.span({fontWeight:\"bold\"}),Required=styled.span(({theme})=>({color:theme.color.negative,fontFamily:theme.typography.fonts.mono,cursor:\"help\"})),Description=styled.div(({theme})=>({\"&&\":{p:{margin:\"0 0 10px 0\"},a:{color:theme.color.secondary}},code:{...codeCommon({theme}),fontSize:12,fontFamily:theme.typography.fonts.mono},\"& code\":{margin:0,display:\"inline-block\"},\"& pre > code\":{whiteSpace:\"pre-wrap\"}})),Type=styled.div(({theme,hasDescription})=>({color:theme.base===\"light\"?transparentize(.1,theme.color.defaultText):transparentize(.2,theme.color.defaultText),marginTop:hasDescription?4:0})),TypeWithJsDoc=styled.div(({theme,hasDescription})=>({color:theme.base===\"light\"?transparentize(.1,theme.color.defaultText):transparentize(.2,theme.color.defaultText),marginTop:hasDescription?12:0,marginBottom:12})),StyledTd=styled.td(({theme,expandable})=>({paddingLeft:expandable?\"40px !important\":\"20px !important\"})),toSummary=value2=>value2&&{summary:typeof value2==\"string\"?value2:value2.name},ArgRow=props=>{let[isHovered,setIsHovered]=useState(!1),{row,updateArgs,compact,expandable,initialExpandedArgs}=props,{name,description}=row,table=row.table||{},type=table.type||toSummary(row.type),defaultValue=table.defaultValue||row.defaultValue,required=row.type?.required,hasDescription=description!=null&&description!==\"\";return React17.createElement(\"tr\",{onMouseEnter:()=>setIsHovered(!0),onMouseLeave:()=>setIsHovered(!1)},React17.createElement(StyledTd,{expandable},React17.createElement(Name,null,name),required?React17.createElement(Required,{title:\"Required\"},\"*\"):null),compact?null:React17.createElement(\"td\",null,hasDescription&&React17.createElement(Description,null,React17.createElement(Markdown,null,description)),table.jsDocTags!=null?React17.createElement(React17.Fragment,null,React17.createElement(TypeWithJsDoc,{hasDescription},React17.createElement(ArgValue,{value:type,initialExpandedArgs})),React17.createElement(ArgJsDoc,{tags:table.jsDocTags})):React17.createElement(Type,{hasDescription},React17.createElement(ArgValue,{value:type,initialExpandedArgs}))),compact?null:React17.createElement(\"td\",null,React17.createElement(ArgValue,{value:defaultValue,initialExpandedArgs})),updateArgs?React17.createElement(\"td\",null,React17.createElement(ArgControl,{...props,isHovered})):null)};var ExpanderIconDown=styled(ChevronDownIcon$1)(({theme})=>({marginRight:8,marginLeft:-10,marginTop:-2,height:12,width:12,color:theme.base===\"light\"?transparentize(.25,theme.color.defaultText):transparentize(.3,theme.color.defaultText),border:\"none\",display:\"inline-block\"})),ExpanderIconRight=styled(ChevronRightIcon)(({theme})=>({marginRight:8,marginLeft:-10,marginTop:-2,height:12,width:12,color:theme.base===\"light\"?transparentize(.25,theme.color.defaultText):transparentize(.3,theme.color.defaultText),border:\"none\",display:\"inline-block\"})),FlexWrapper=styled.span(({theme})=>({display:\"flex\",lineHeight:\"20px\",alignItems:\"center\"})),Section=styled.td(({theme})=>({position:\"relative\",letterSpacing:\"0.35em\",textTransform:\"uppercase\",fontWeight:theme.typography.weight.bold,fontSize:theme.typography.size.s1-1,color:theme.base===\"light\"?transparentize(.4,theme.color.defaultText):transparentize(.6,theme.color.defaultText),background:`${theme.background.app} !important`,\"& ~ td\":{background:`${theme.background.app} !important`}})),Subsection=styled.td(({theme})=>({position:\"relative\",fontWeight:theme.typography.weight.bold,fontSize:theme.typography.size.s2-1,background:theme.background.app})),StyledTd2=styled.td(()=>({position:\"relative\"})),StyledTr=styled.tr(({theme})=>({\"&:hover > td\":{backgroundColor:`${lighten(.005,theme.background.app)} !important`,boxShadow:`${theme.color.mediumlight} 0 - 1px 0 0 inset`,cursor:\"row-resize\"}})),ClickIntercept=styled.button(()=>({background:\"none\",border:\"none\",padding:\"0\",font:\"inherit\",position:\"absolute\",top:0,bottom:0,left:0,right:0,height:\"100%\",width:\"100%\",color:\"transparent\",cursor:\"row-resize !important\"})),SectionRow=({level=\"section\",label,children,initialExpanded=!0,colSpan=3})=>{let[expanded,setExpanded]=useState(initialExpanded),Level=level===\"subsection\"?Subsection:Section,itemCount=children?.length||0,caption=level===\"subsection\"?`${itemCount} item${itemCount!==1?\"s\":\"\"}`:\"\",helperText=`${expanded?\"Hide\":\"Show\"} ${level===\"subsection\"?itemCount:label} item${itemCount!==1?\"s\":\"\"}`;return React17.createElement(React17.Fragment,null,React17.createElement(StyledTr,{title:helperText},React17.createElement(Level,{colSpan:1},React17.createElement(ClickIntercept,{onClick:e=>setExpanded(!expanded),tabIndex:0},helperText),React17.createElement(FlexWrapper,null,expanded?React17.createElement(ExpanderIconDown,null):React17.createElement(ExpanderIconRight,null),label)),React17.createElement(StyledTd2,{colSpan:colSpan-1},React17.createElement(ClickIntercept,{onClick:e=>setExpanded(!expanded),tabIndex:-1,style:{outline:\"none\"}},helperText),expanded?null:caption)),expanded?children:null)};var Row=styled.div(({theme})=>({display:\"flex\",gap:16,borderBottom:`1px solid ${theme.appBorderColor}`,\"&:last-child\":{borderBottom:0}})),Column=styled.div(({numColumn})=>({display:\"flex\",flexDirection:\"column\",flex:numColumn||1,gap:5,padding:\"12px 20px\"})),SkeletonText=styled.div(({theme,width,height})=>({animation:`${theme.animation.glow} 1.5s ease-in-out infinite`,background:theme.appBorderColor,width:width||\"100%\",height:height||16,borderRadius:3})),columnWidth=[2,4,2,2],Skeleton=()=>React17.createElement(React17.Fragment,null,React17.createElement(Row,null,React17.createElement(Column,{numColumn:columnWidth[0]},React17.createElement(SkeletonText,{width:\"60%\"})),React17.createElement(Column,{numColumn:columnWidth[1]},React17.createElement(SkeletonText,{width:\"30%\"})),React17.createElement(Column,{numColumn:columnWidth[2]},React17.createElement(SkeletonText,{width:\"60%\"})),React17.createElement(Column,{numColumn:columnWidth[3]},React17.createElement(SkeletonText,{width:\"60%\"}))),React17.createElement(Row,null,React17.createElement(Column,{numColumn:columnWidth[0]},React17.createElement(SkeletonText,{width:\"60%\"})),React17.createElement(Column,{numColumn:columnWidth[1]},React17.createElement(SkeletonText,{width:\"80%\"}),React17.createElement(SkeletonText,{width:\"30%\"})),React17.createElement(Column,{numColumn:columnWidth[2]},React17.createElement(SkeletonText,{width:\"60%\"})),React17.createElement(Column,{numColumn:columnWidth[3]},React17.createElement(SkeletonText,{width:\"60%\"}))),React17.createElement(Row,null,React17.createElement(Column,{numColumn:columnWidth[0]},React17.createElement(SkeletonText,{width:\"60%\"})),React17.createElement(Column,{numColumn:columnWidth[1]},React17.createElement(SkeletonText,{width:\"80%\"}),React17.createElement(SkeletonText,{width:\"30%\"})),React17.createElement(Column,{numColumn:columnWidth[2]},React17.createElement(SkeletonText,{width:\"60%\"})),React17.createElement(Column,{numColumn:columnWidth[3]},React17.createElement(SkeletonText,{width:\"60%\"}))),React17.createElement(Row,null,React17.createElement(Column,{numColumn:columnWidth[0]},React17.createElement(SkeletonText,{width:\"60%\"})),React17.createElement(Column,{numColumn:columnWidth[1]},React17.createElement(SkeletonText,{width:\"80%\"}),React17.createElement(SkeletonText,{width:\"30%\"})),React17.createElement(Column,{numColumn:columnWidth[2]},React17.createElement(SkeletonText,{width:\"60%\"})),React17.createElement(Column,{numColumn:columnWidth[3]},React17.createElement(SkeletonText,{width:\"60%\"}))));var Wrapper8=styled.div(({inAddonPanel,theme})=>({height:inAddonPanel?\"100%\":\"auto\",display:\"flex\",border:inAddonPanel?\"none\":`1px solid ${theme.appBorderColor}`,borderRadius:inAddonPanel?0:theme.appBorderRadius,padding:inAddonPanel?0:40,alignItems:\"center\",justifyContent:\"center\",flexDirection:\"column\",gap:15,background:theme.background.content,boxShadow:\"rgba(0, 0, 0, 0.10) 0 1px 3px 0\"})),Links=styled.div(({theme})=>({display:\"flex\",fontSize:theme.typography.size.s2-1,gap:25})),Divider=styled.div(({theme})=>({width:1,height:16,backgroundColor:theme.appBorderColor})),Empty=({inAddonPanel})=>{let[isLoading,setIsLoading]=useState(!0);return useEffect(()=>{let load=setTimeout(()=>{setIsLoading(!1);},100);return ()=>clearTimeout(load)},[]),isLoading?null:React17.createElement(Wrapper8,{inAddonPanel},React17.createElement(EmptyTabContent,{title:inAddonPanel?\"Interactive story playground\":\"Args table with interactive controls couldn't be auto-generated\",description:React17.createElement(React17.Fragment,null,\"Controls give you an easy to use interface to test your components. Set your story args and you'll see controls appearing here automatically.\"),footer:React17.createElement(Links,null,inAddonPanel&&React17.createElement(React17.Fragment,null,React17.createElement(Link,{href:\"https://youtu.be/0gOfS6K0x0E\",target:\"_blank\",withArrow:!0},React17.createElement(VideoIcon,null),\" Watch 5m video\"),React17.createElement(Divider,null),React17.createElement(Link,{href:\"https://storybook.js.org/docs/essentials/controls\",target:\"_blank\",withArrow:!0},React17.createElement(DocumentIcon,null),\" Read docs\")),!inAddonPanel&&React17.createElement(Link,{href:\"https://storybook.js.org/docs/essentials/controls\",target:\"_blank\",withArrow:!0},React17.createElement(DocumentIcon,null),\" Learn how to set that up\"))}))};var TableWrapper=styled.table(({theme,compact,inAddonPanel})=>({\"&&\":{borderSpacing:0,color:theme.color.defaultText,\"td, th\":{padding:0,border:\"none\",verticalAlign:\"top\",textOverflow:\"ellipsis\"},fontSize:theme.typography.size.s2-1,lineHeight:\"20px\",textAlign:\"left\",width:\"100%\",marginTop:inAddonPanel?0:25,marginBottom:inAddonPanel?0:40,\"thead th:first-of-type, td:first-of-type\":{width:\"25%\"},\"th:first-of-type, td:first-of-type\":{paddingLeft:20},\"th:nth-of-type(2), td:nth-of-type(2)\":{...compact?null:{width:\"35%\"}},\"td:nth-of-type(3)\":{...compact?null:{width:\"15%\"}},\"th:last-of-type, td:last-of-type\":{paddingRight:20,...compact?null:{width:\"25%\"}},th:{color:theme.base===\"light\"?transparentize(.25,theme.color.defaultText):transparentize(.45,theme.color.defaultText),paddingTop:10,paddingBottom:10,paddingLeft:15,paddingRight:15},td:{paddingTop:\"10px\",paddingBottom:\"10px\",\"&:not(:first-of-type)\":{paddingLeft:15,paddingRight:15},\"&:last-of-type\":{paddingRight:20}},marginLeft:inAddonPanel?0:1,marginRight:inAddonPanel?0:1,tbody:{...inAddonPanel?null:{filter:theme.base===\"light\"?\"drop-shadow(0px 1px 3px rgba(0, 0, 0, 0.10))\":\"drop-shadow(0px 1px 3px rgba(0, 0, 0, 0.20))\"},\"> tr > *\":{background:theme.background.content,borderTop:`1px solid ${theme.appBorderColor}`},...inAddonPanel?null:{\"> tr:first-of-type > *\":{borderBlockStart:`1px solid ${theme.appBorderColor}`},\"> tr:last-of-type > *\":{borderBlockEnd:`1px solid ${theme.appBorderColor}`},\"> tr > *:first-of-type\":{borderInlineStart:`1px solid ${theme.appBorderColor}`},\"> tr > *:last-of-type\":{borderInlineEnd:`1px solid ${theme.appBorderColor}`},\"> tr:first-of-type > td:first-of-type\":{borderTopLeftRadius:theme.appBorderRadius},\"> tr:first-of-type > td:last-of-type\":{borderTopRightRadius:theme.appBorderRadius},\"> tr:last-of-type > td:first-of-type\":{borderBottomLeftRadius:theme.appBorderRadius},\"> tr:last-of-type > td:last-of-type\":{borderBottomRightRadius:theme.appBorderRadius}}}}})),StyledIconButton=styled(IconButton)(({theme})=>({margin:\"-4px -12px -4px 0\"})),ControlHeadingWrapper=styled.span({display:\"flex\",justifyContent:\"space-between\"});var sortFns={alpha:(a,b)=>a.name.localeCompare(b.name),requiredFirst:(a,b)=>+!!b.type?.required-+!!a.type?.required||a.name.localeCompare(b.name),none:void 0},groupRows=(rows,sort)=>{let sections={ungrouped:[],ungroupedSubsections:{},sections:{}};if(!rows)return sections;Object.entries(rows).forEach(([key,row])=>{let{category,subcategory}=row?.table||{};if(category){let section=sections.sections[category]||{ungrouped:[],subsections:{}};if(!subcategory)section.ungrouped.push({key,...row});else {let subsection=section.subsections[subcategory]||[];subsection.push({key,...row}),section.subsections[subcategory]=subsection;}sections.sections[category]=section;}else if(subcategory){let subsection=sections.ungroupedSubsections[subcategory]||[];subsection.push({key,...row}),sections.ungroupedSubsections[subcategory]=subsection;}else sections.ungrouped.push({key,...row});});let sortFn=sortFns[sort],sortSubsection=record=>sortFn?Object.keys(record).reduce((acc,cur)=>({...acc,[cur]:record[cur].sort(sortFn)}),{}):record;return {ungrouped:sections.ungrouped.sort(sortFn),ungroupedSubsections:sortSubsection(sections.ungroupedSubsections),sections:Object.keys(sections.sections).reduce((acc,cur)=>({...acc,[cur]:{ungrouped:sections.sections[cur].ungrouped.sort(sortFn),subsections:sortSubsection(sections.sections[cur].subsections)}}),{})}},safeIncludeConditionalArg=(row,args,globals)=>{try{return includeConditionalArg(row,args,globals)}catch(err){return once.warn(err.message),!1}},ArgsTable=props=>{let{updateArgs,resetArgs,compact,inAddonPanel,initialExpandedArgs,sort=\"none\",isLoading}=props;if(\"error\"in props){let{error}=props;return React17.createElement(EmptyBlock,null,error,\"\\xA0\",React17.createElement(Link,{href:\"http://storybook.js.org/docs/\",target:\"_blank\",withArrow:!0},React17.createElement(DocumentIcon,null),\" Read the docs\"))}if(isLoading)return React17.createElement(Skeleton,null);let{rows,args,globals}=\"rows\"in props&&props,groups=groupRows(pickBy(rows,row=>!row?.table?.disable&&safeIncludeConditionalArg(row,args||{},globals||{})),sort),hasNoUngrouped=groups.ungrouped.length===0,hasNoSections=Object.entries(groups.sections).length===0,hasNoUngroupedSubsections=Object.entries(groups.ungroupedSubsections).length===0;if(hasNoUngrouped&&hasNoSections&&hasNoUngroupedSubsections)return React17.createElement(Empty,{inAddonPanel});let colSpan=1;updateArgs&&(colSpan+=1),compact||(colSpan+=2);let expandable=Object.keys(groups.sections).length>0,common={updateArgs,compact,inAddonPanel,initialExpandedArgs};return React17.createElement(ResetWrapper,null,React17.createElement(TableWrapper,{compact,inAddonPanel,className:\"docblock-argstable sb-unstyled\"},React17.createElement(\"thead\",{className:\"docblock-argstable-head\"},React17.createElement(\"tr\",null,React17.createElement(\"th\",null,React17.createElement(\"span\",null,\"Name\")),compact?null:React17.createElement(\"th\",null,React17.createElement(\"span\",null,\"Description\")),compact?null:React17.createElement(\"th\",null,React17.createElement(\"span\",null,\"Default\")),updateArgs?React17.createElement(\"th\",null,React17.createElement(ControlHeadingWrapper,null,\"Control\",\" \",!isLoading&&resetArgs&&React17.createElement(StyledIconButton,{onClick:()=>resetArgs(),title:\"Reset controls\"},React17.createElement(UndoIcon,{\"aria-hidden\":!0})))):null)),React17.createElement(\"tbody\",{className:\"docblock-argstable-body\"},groups.ungrouped.map(row=>React17.createElement(ArgRow,{key:row.key,row,arg:args&&args[row.key],...common})),Object.entries(groups.ungroupedSubsections).map(([subcategory,subsection])=>React17.createElement(SectionRow,{key:subcategory,label:subcategory,level:\"subsection\",colSpan},subsection.map(row=>React17.createElement(ArgRow,{key:row.key,row,arg:args&&args[row.key],expandable,...common})))),Object.entries(groups.sections).map(([category,section])=>React17.createElement(SectionRow,{key:category,label:category,level:\"section\",colSpan},section.ungrouped.map(row=>React17.createElement(ArgRow,{key:row.key,row,arg:args&&args[row.key],...common})),Object.entries(section.subsections).map(([subcategory,subsection])=>React17.createElement(SectionRow,{key:subcategory,label:subcategory,level:\"subsection\",colSpan},subsection.map(row=>React17.createElement(ArgRow,{key:row.key,row,arg:args&&args[row.key],expandable,...common})))))))))};var TabbedArgsTable=({tabs,...props})=>{let entries=Object.entries(tabs);return entries.length===1?React17.createElement(ArgsTable,{...entries[0][1],...props}):React17.createElement(TabsState,null,entries.map((entry,index)=>{let[label,table]=entry,id=`prop_table_div_${label}`,Component4=\"div\",argsTableProps=index===0?props:{sort:props.sort};return React17.createElement(Component4,{key:id,id,title:label},({active})=>active?React17.createElement(ArgsTable,{key:`prop_table_${label}`,...table,...argsTableProps}):null)}))};var Label4=styled.div(({theme})=>({marginRight:30,fontSize:`${theme.typography.size.s1}px`,color:theme.base===\"light\"?transparentize(.4,theme.color.defaultText):transparentize(.6,theme.color.defaultText)})),Sample=styled.div({overflow:\"hidden\",whiteSpace:\"nowrap\",textOverflow:\"ellipsis\"}),TypeSpecimen=styled.div({display:\"flex\",flexDirection:\"row\",alignItems:\"baseline\",\"&:not(:last-child)\":{marginBottom:\"1rem\"}}),Wrapper9=styled.div(withReset,({theme})=>({...getBlockBackgroundStyle(theme),margin:\"25px 0 40px\",padding:\"30px 20px\"})),Typeset=({fontFamily,fontSizes,fontWeight,sampleText,...props})=>React17.createElement(Wrapper9,{...props,className:\"docblock-typeset sb-unstyled\"},fontSizes.map(size=>React17.createElement(TypeSpecimen,{key:size},React17.createElement(Label4,null,size),React17.createElement(Sample,{style:{fontFamily,fontSize:size,fontWeight,lineHeight:1.2}},sampleText||\"Was he a beast if music could move him so?\"))));var ItemTitle=styled.div(({theme})=>({fontWeight:theme.typography.weight.bold,color:theme.color.defaultText})),ItemSubtitle=styled.div(({theme})=>({color:theme.base===\"light\"?transparentize(.2,theme.color.defaultText):transparentize(.6,theme.color.defaultText)})),ItemDescription=styled.div({flex:\"0 0 30%\",lineHeight:\"20px\",marginTop:5}),SwatchLabel=styled.div(({theme})=>({flex:1,textAlign:\"center\",fontFamily:theme.typography.fonts.mono,fontSize:theme.typography.size.s1,lineHeight:1,overflow:\"hidden\",color:theme.base===\"light\"?transparentize(.4,theme.color.defaultText):transparentize(.6,theme.color.defaultText),\"> div\":{display:\"inline-block\",overflow:\"hidden\",maxWidth:\"100%\",textOverflow:\"ellipsis\"},span:{display:\"block\",marginTop:2}})),SwatchLabels=styled.div({display:\"flex\",flexDirection:\"row\"}),Swatch=styled.div(({background})=>({position:\"relative\",flex:1,\"&::before\":{position:\"absolute\",top:0,left:0,width:\"100%\",height:\"100%\",background,content:'\"\"'}})),SwatchColors=styled.div(({theme})=>({...getBlockBackgroundStyle(theme),display:\"flex\",flexDirection:\"row\",height:50,marginBottom:5,overflow:\"hidden\",backgroundColor:\"white\",backgroundImage:\"repeating-linear-gradient(-45deg, #ccc, #ccc 1px, #fff 1px, #fff 16px)\",backgroundClip:\"padding-box\"})),SwatchSpecimen=styled.div({display:\"flex\",flexDirection:\"column\",flex:1,position:\"relative\",marginBottom:30}),Swatches=styled.div({flex:1,display:\"flex\",flexDirection:\"row\"}),Item=styled.div({display:\"flex\",alignItems:\"flex-start\"}),ListName=styled.div({flex:\"0 0 30%\"}),ListSwatches=styled.div({flex:1}),ListHeading=styled.div(({theme})=>({display:\"flex\",flexDirection:\"row\",alignItems:\"center\",paddingBottom:20,fontWeight:theme.typography.weight.bold,color:theme.base===\"light\"?transparentize(.4,theme.color.defaultText):transparentize(.6,theme.color.defaultText)})),List=styled.div(({theme})=>({fontSize:theme.typography.size.s2,lineHeight:\"20px\",display:\"flex\",flexDirection:\"column\"}));function renderSwatch(color,index){return React17.createElement(Swatch,{key:`${color}-${index}`,title:color,background:color})}function renderSwatchLabel(color,index,colorDescription){return React17.createElement(SwatchLabel,{key:`${color}-${index}`,title:color},React17.createElement(\"div\",null,color,colorDescription&&React17.createElement(\"span\",null,colorDescription)))}function renderSwatchSpecimen(colors){if(Array.isArray(colors))return React17.createElement(SwatchSpecimen,null,React17.createElement(SwatchColors,null,colors.map((color,index)=>renderSwatch(color,index))),React17.createElement(SwatchLabels,null,colors.map((color,index)=>renderSwatchLabel(color,index))));let swatchElements=[],labelElements=[];for(let colorKey in colors){let colorValue=colors[colorKey];swatchElements.push(renderSwatch(colorValue,swatchElements.length)),labelElements.push(renderSwatchLabel(colorKey,labelElements.length,colorValue));}return React17.createElement(SwatchSpecimen,null,React17.createElement(SwatchColors,null,swatchElements),React17.createElement(SwatchLabels,null,labelElements))}var ColorItem=({title,subtitle,colors})=>React17.createElement(Item,null,React17.createElement(ItemDescription,null,React17.createElement(ItemTitle,null,title),React17.createElement(ItemSubtitle,null,subtitle)),React17.createElement(Swatches,null,renderSwatchSpecimen(colors))),ColorPalette=({children,...props})=>React17.createElement(ResetWrapper,null,React17.createElement(List,{...props,className:\"docblock-colorpalette sb-unstyled\"},React17.createElement(ListHeading,null,React17.createElement(ListName,null,\"Name\"),React17.createElement(ListSwatches,null,\"Swatches\")),children));var ItemLabel=styled.div(({theme})=>({fontFamily:theme.typography.fonts.base,fontSize:theme.typography.size.s2,color:theme.color.defaultText,marginLeft:10,lineHeight:1.2})),ItemSpecimen=styled.div(({theme})=>({...getBlockBackgroundStyle(theme),overflow:\"hidden\",height:40,width:40,display:\"flex\",alignItems:\"center\",justifyContent:\"center\",flex:\"none\",\"> img, > svg\":{width:20,height:20}})),Item2=styled.div({display:\"inline-flex\",flexDirection:\"row\",alignItems:\"center\",flex:\"0 1 calc(20% - 10px)\",minWidth:120,margin:\"0px 10px 30px 0\"}),List2=styled.div({display:\"flex\",flexFlow:\"row wrap\"}),IconItem=({name,children})=>React17.createElement(Item2,null,React17.createElement(ItemSpecimen,null,children),React17.createElement(ItemLabel,null,name)),IconGallery=({children,...props})=>React17.createElement(ResetWrapper,null,React17.createElement(List2,{...props,className:\"docblock-icongallery sb-unstyled\"},children));var anchorBlockIdFromId=storyId=>`anchor--${storyId}`,Anchor=({storyId,children})=>React17.createElement(\"div\",{id:anchorBlockIdFromId(storyId),className:\"sb-anchor\"},children);global&&global.__DOCS_CONTEXT__===void 0&&(global.__DOCS_CONTEXT__=createContext(null),global.__DOCS_CONTEXT__.displayName=\"DocsContext\");var DocsContext=global?global.__DOCS_CONTEXT__:createContext(null);var useOf=(moduleExportOrType,validTypes)=>useContext(DocsContext).resolveOf(moduleExportOrType,validTypes);var titleCase=str=>str.split(\"-\").map(part=>part.charAt(0).toUpperCase()+part.slice(1)).join(\"\"),getComponentName=component=>{if(component)return typeof component==\"string\"?component.includes(\"-\")?titleCase(component):component:component.__docgenInfo&&component.__docgenInfo.displayName?component.__docgenInfo.displayName:component.name};function scrollToElement(element,block=\"start\"){element.scrollIntoView({behavior:\"smooth\",block,inline:\"nearest\"});}function extractComponentArgTypes(component,parameters){let{extractArgTypes}=parameters.docs||{};if(!extractArgTypes)throw new Error(\"Args unsupported. See Args documentation for your framework.\");return extractArgTypes(component)}function getArgTypesFromResolved(resolved){if(resolved.type===\"component\"){let{component:component2,projectAnnotations:{parameters:parameters2}}=resolved;return {argTypes:extractComponentArgTypes(component2,parameters2),parameters:parameters2,component:component2}}if(resolved.type===\"meta\"){let{preparedMeta:{argTypes:argTypes2,parameters:parameters2,component:component2,subcomponents:subcomponents2}}=resolved;return {argTypes:argTypes2,parameters:parameters2,component:component2,subcomponents:subcomponents2}}let{story:{argTypes,parameters,component,subcomponents}}=resolved;return {argTypes,parameters,component,subcomponents}}var ArgTypes=props=>{let{of}=props;if(\"of\"in props&&of===void 0)throw new Error(\"Unexpected `of={undefined}`, did you mistype a CSF file reference?\");let resolved=useOf(of||\"meta\"),{argTypes,parameters,component,subcomponents}=getArgTypesFromResolved(resolved),argTypesParameters=parameters.docs?.argTypes||{},include=props.include??argTypesParameters.include,exclude=props.exclude??argTypesParameters.exclude,sort=props.sort??argTypesParameters.sort,filteredArgTypes=filterArgTypes(argTypes,include,exclude);if(!(!!subcomponents&&Object.keys(subcomponents).length>0))return React17.createElement(ArgsTable,{rows:filteredArgTypes,sort});let mainComponentName=getComponentName(component),subcomponentTabs=Object.fromEntries(Object.entries(subcomponents).map(([key,comp])=>[key,{rows:filterArgTypes(extractComponentArgTypes(comp,parameters),include,exclude),sort}])),tabs={[mainComponentName]:{rows:filteredArgTypes,sort},...subcomponentTabs};return React17.createElement(TabbedArgsTable,{tabs,sort})};function argsHash(args){return stringify(args,{allowFunction:!1})}var SourceContext=createContext({sources:{}}),UNKNOWN_ARGS_HASH=\"--unknown--\",SourceContainer=({children,channel})=>{let[sources,setSources]=useState({});return useEffect(()=>{let handleSnippetRendered=(idOrEvent,inputSource=null,inputFormat=!1)=>{let{id,args=void 0,source,format:format2}=typeof idOrEvent==\"string\"?{id:idOrEvent,source:inputSource,format:inputFormat}:idOrEvent,hash=args?argsHash(args):UNKNOWN_ARGS_HASH;setSources(current=>({...current,[id]:{...current[id],[hash]:{code:source,format:format2}}}));};return channel.on(SNIPPET_RENDERED,handleSnippetRendered),()=>channel.off(SNIPPET_RENDERED,handleSnippetRendered)},[]),React17.createElement(SourceContext.Provider,{value:{sources}},children)};var getStorySource=(storyId,args,sourceContext)=>{let{sources}=sourceContext,sourceMap=sources?.[storyId];return sourceMap?.[argsHash(args)]||sourceMap?.[UNKNOWN_ARGS_HASH]||{code:\"\"}},getSnippet=({snippet,storyContext,typeFromProps,transformFromProps})=>{let{__isArgsStory:isArgsStory}=storyContext.parameters,sourceParameters=storyContext.parameters.docs?.source||{},type=typeFromProps||sourceParameters.type||SourceType.AUTO;if(sourceParameters.code!==void 0)return sourceParameters.code;let code=type===SourceType.DYNAMIC||type===SourceType.AUTO&&snippet&&isArgsStory?snippet:sourceParameters.originalSource||\"\";return (transformFromProps??sourceParameters.transform)?.(code,storyContext)||code},useSourceProps=(props,docsContext,sourceContext)=>{let story,{of}=props;if(\"of\"in props&&of===void 0)throw new Error(\"Unexpected `of={undefined}`, did you mistype a CSF file reference?\");if(of)story=docsContext.resolveOf(of,[\"story\"]).story;else try{story=docsContext.storyById();}catch{}let sourceParameters=story?.parameters?.docs?.source||{},{code}=props,format2=props.format??sourceParameters.format,language=props.language??sourceParameters.language??\"jsx\",dark=props.dark??sourceParameters.dark??!1;if(!code&&!story)return {error:\"Oh no! The source is not available.\"};if(code)return {code,format:format2,language,dark};let storyContext=docsContext.getStoryContext(story),argsForSource=props.__forceInitialArgs?storyContext.initialArgs:storyContext.unmappedArgs,source=getStorySource(story.id,argsForSource,sourceContext);return format2=source.format??story.parameters.docs?.source?.format??!1,{code:getSnippet({snippet:source.code,storyContext:{...storyContext,args:argsForSource},typeFromProps:props.type,transformFromProps:props.transform}),format:format2,language,dark}},Source2=props=>{let sourceContext=useContext(SourceContext),docsContext=useContext(DocsContext),sourceProps=useSourceProps(props,docsContext,sourceContext);return React17.createElement(Source,{...sourceProps})};function useStory(storyId,context){let stories=useStories([storyId],context);return stories&&stories[0]}function useStories(storyIds,context){let[storiesById,setStories]=useState({});return useEffect(()=>{Promise.all(storyIds.map(async storyId=>{let story=await context.loadStory(storyId);setStories(current=>current[storyId]===story?current:{...current,[storyId]:story});}));}),storyIds.map(storyId=>{if(storiesById[storyId])return storiesById[storyId];try{return context.storyById(storyId)}catch{return null}})}var getStoryId2=(props,context)=>{let{of,meta}=props;if(\"of\"in props&&of===void 0)throw new Error(\"Unexpected `of={undefined}`, did you mistype a CSF file reference?\");return meta&&context.referenceMeta(meta,!1),context.resolveOf(of||\"story\",[\"story\"]).story.id},getStoryProps=(props,story,context)=>{let{parameters={}}=story||{},{docs={}}=parameters,storyParameters=docs.story||{};if(docs.disable)return null;if(props.inline??storyParameters.inline??!1){let height2=props.height??storyParameters.height,autoplay=props.autoplay??storyParameters.autoplay??!1;return {story,inline:!0,height:height2,autoplay,forceInitialArgs:!!props.__forceInitialArgs,primary:!!props.__primary,renderStoryToElement:context.renderStoryToElement}}let height=props.height??storyParameters.height??storyParameters.iframeHeight??\"100px\";return {story,inline:!1,height,primary:!!props.__primary}},Story2=(props={__forceInitialArgs:!1,__primary:!1})=>{let context=useContext(DocsContext),storyId=getStoryId2(props,context),story=useStory(storyId,context);if(!story)return React17.createElement(StorySkeleton,null);let storyProps=getStoryProps(props,story,context);return storyProps?React17.createElement(Story,{...storyProps}):null};var Canvas=props=>{let docsContext=useContext(DocsContext),sourceContext=useContext(SourceContext),{of,source}=props;if(\"of\"in props&&of===void 0)throw new Error(\"Unexpected `of={undefined}`, did you mistype a CSF file reference?\");let{story}=useOf(of||\"story\",[\"story\"]),sourceProps=useSourceProps({...source,...of&&{of}},docsContext,sourceContext),layout=props.layout??story.parameters.layout??story.parameters.docs?.canvas?.layout??\"padded\",withToolbar=props.withToolbar??story.parameters.docs?.canvas?.withToolbar??!1,additionalActions=props.additionalActions??story.parameters.docs?.canvas?.additionalActions,sourceState=props.sourceState??story.parameters.docs?.canvas?.sourceState??\"hidden\",className=props.className??story.parameters.docs?.canvas?.className;return React17.createElement(Preview,{withSource:sourceState===\"none\"?void 0:sourceProps,isExpanded:sourceState===\"shown\",withToolbar,additionalActions,className,layout},React17.createElement(Story2,{of:of||story.moduleExport,meta:props.meta,...props.story}))};var useGlobals=(story,context)=>{let storyContext=context.getStoryContext(story),[globals,setGlobals]=useState(storyContext.globals);return useEffect(()=>{let onGlobalsUpdated=changed=>{setGlobals(changed.globals);};return context.channel.on(GLOBALS_UPDATED,onGlobalsUpdated),()=>context.channel.off(GLOBALS_UPDATED,onGlobalsUpdated)},[context.channel]),[globals]};var useArgs=(story,context)=>{let result=useArgsIfDefined(story,context);if(!result)throw new Error(\"No result when story was defined\");return result},useArgsIfDefined=(story,context)=>{let storyContext=story?context.getStoryContext(story):{args:{}},{id:storyId}=story||{id:\"none\"},[args,setArgs]=useState(storyContext.args);useEffect(()=>{let onArgsUpdated=changed=>{changed.storyId===storyId&&setArgs(changed.args);};return context.channel.on(STORY_ARGS_UPDATED,onArgsUpdated),()=>context.channel.off(STORY_ARGS_UPDATED,onArgsUpdated)},[storyId,context.channel]);let updateArgs=useCallback(updatedArgs=>context.channel.emit(UPDATE_STORY_ARGS,{storyId,updatedArgs}),[storyId,context.channel]),resetArgs=useCallback(argNames=>context.channel.emit(RESET_STORY_ARGS,{storyId,argNames}),[storyId,context.channel]);return story&&[args,updateArgs,resetArgs]};function extractComponentArgTypes2(component,parameters){let{extractArgTypes}=parameters.docs||{};if(!extractArgTypes)throw new Error(\"Args unsupported. See Args documentation for your framework.\");return extractArgTypes(component)}var Controls3=props=>{let{of}=props;if(\"of\"in props&&of===void 0)throw new Error(\"Unexpected `of={undefined}`, did you mistype a CSF file reference?\");let context=useContext(DocsContext),{story}=context.resolveOf(of||\"story\",[\"story\"]),{parameters,argTypes,component,subcomponents}=story,controlsParameters=parameters.docs?.controls||{},include=props.include??controlsParameters.include,exclude=props.exclude??controlsParameters.exclude,sort=props.sort??controlsParameters.sort,[args,updateArgs,resetArgs]=useArgs(story,context),[globals]=useGlobals(story,context),filteredArgTypes=filterArgTypes(argTypes,include,exclude);if(!(!!subcomponents&&Object.keys(subcomponents).length>0))return Object.keys(filteredArgTypes).length>0||Object.keys(args).length>0?React17.createElement(ArgsTable,{rows:filteredArgTypes,sort,args,globals,updateArgs,resetArgs}):null;let mainComponentName=getComponentName(component),subcomponentTabs=Object.fromEntries(Object.entries(subcomponents).map(([key,comp])=>[key,{rows:filterArgTypes(extractComponentArgTypes2(comp,parameters),include,exclude),sort}])),tabs={[mainComponentName]:{rows:filteredArgTypes,sort},...subcomponentTabs};return React17.createElement(TabbedArgsTable,{tabs,sort,args,globals,updateArgs,resetArgs})};var {document:document2}=global,assertIsFn=val=>{if(typeof val!=\"function\")throw new Error(`Expected story function, got: ${val}`);return val},AddContext=props=>{let{children,...rest}=props,parentContext=React17.useContext(DocsContext);return React17.createElement(DocsContext.Provider,{value:{...parentContext,...rest}},children)},CodeOrSourceMdx=({className,children,...rest})=>{if(typeof className!=\"string\"&&(typeof children!=\"string\"||!children.match(/[\\n\\r]/g)))return React17.createElement(Code,null,children);let language=className&&className.split(\"-\");return React17.createElement(Source,{language:language&&language[1]||\"text\",format:!1,code:children,...rest})};function navigate(context,url){context.channel.emit(NAVIGATE_URL,url);}var A=components.a,AnchorInPage=({hash,children})=>{let context=useContext(DocsContext);return React17.createElement(A,{href:hash,target:\"_self\",onClick:event=>{let id=hash.substring(1);document2.getElementById(id)&&navigate(context,hash);}},children)},AnchorMdx=props=>{let{href,target,children,...rest}=props,context=useContext(DocsContext);return !href||target===\"_blank\"||/^https?:\\/\\//.test(href)?React17.createElement(A,{...props}):href.startsWith(\"#\")?React17.createElement(AnchorInPage,{hash:href},children):React17.createElement(A,{href,onClick:event=>{event.button===0&&!event.altKey&&!event.ctrlKey&&!event.metaKey&&!event.shiftKey&&(event.preventDefault(),navigate(context,event.currentTarget.getAttribute(\"href\")));},target,...rest},children)},SUPPORTED_MDX_HEADERS=[\"h1\",\"h2\",\"h3\",\"h4\",\"h5\",\"h6\"],OcticonHeaders=SUPPORTED_MDX_HEADERS.reduce((acc,headerType)=>({...acc,[headerType]:styled(headerType)({\"& svg\":{position:\"relative\",top:\"-0.1em\",visibility:\"hidden\"},\"&:hover svg\":{visibility:\"visible\"}})}),{}),OcticonAnchor=styled.a(()=>({float:\"left\",lineHeight:\"inherit\",paddingRight:\"10px\",marginLeft:\"-24px\",color:\"inherit\"})),HeaderWithOcticonAnchor=({as,id,children,...rest})=>{let context=useContext(DocsContext),OcticonHeader=OcticonHeaders[as],hash=`#${id}`;return React17.createElement(OcticonHeader,{id,...rest},React17.createElement(OcticonAnchor,{\"aria-hidden\":\"true\",href:hash,tabIndex:-1,target:\"_self\",onClick:event=>{document2.getElementById(id)&&navigate(context,hash);}},React17.createElement(LinkIcon,null)),children)},HeaderMdx=props=>{let{as,id,children,...rest}=props;if(id)return React17.createElement(HeaderWithOcticonAnchor,{as,id,...rest},children);let Component4=as,{as:omittedAs,...withoutAs}=props;return React17.createElement(Component4,{...nameSpaceClassNames(withoutAs,as)})},HeadersMdx=SUPPORTED_MDX_HEADERS.reduce((acc,headerType)=>({...acc,[headerType]:props=>React17.createElement(HeaderMdx,{as:headerType,...props})}),{});var Markdown2=props=>{if(!props.children)return null;if(typeof props.children!=\"string\")throw new Error(dedent`The Markdown block only accepts children as a single string, but children were of type: '${typeof props.children}'\n        This is often caused by not wrapping the child in a template string.\n        \n        This is invalid:\n        <Markdown>\n          # Some heading\n          A paragraph\n        </Markdown>\n\n        Instead do:\n        <Markdown>\n        {\\`\n          # Some heading\n          A paragraph\n        \\`}\n        </Markdown>\n      `);return React17.createElement(Markdown,{...props,options:{forceBlock:!0,overrides:{code:CodeOrSourceMdx,a:AnchorMdx,...HeadersMdx,...props?.options?.overrides},...props?.options}})};var DescriptionType=(DescriptionType2=>(DescriptionType2.INFO=\"info\",DescriptionType2.NOTES=\"notes\",DescriptionType2.DOCGEN=\"docgen\",DescriptionType2.AUTO=\"auto\",DescriptionType2))(DescriptionType||{}),getDescriptionFromResolvedOf=resolvedOf=>{switch(resolvedOf.type){case\"story\":return resolvedOf.story.parameters.docs?.description?.story||null;case\"meta\":{let{parameters,component}=resolvedOf.preparedMeta,metaDescription=parameters.docs?.description?.component;return metaDescription||parameters.docs?.extractComponentDescription?.(component,{component,parameters})||null}case\"component\":{let{component,projectAnnotations:{parameters}}=resolvedOf;return parameters.docs?.extractComponentDescription?.(component,{component,parameters})||null}default:throw new Error(`Unrecognized module type resolved from 'useOf', got: ${resolvedOf.type}`)}},DescriptionContainer=props=>{let{of}=props;if(\"of\"in props&&of===void 0)throw new Error(\"Unexpected `of={undefined}`, did you mistype a CSF file reference?\");let resolvedOf=useOf(of||\"meta\"),markdown=getDescriptionFromResolvedOf(resolvedOf);return markdown?React17.createElement(Markdown2,null,markdown):null};var Wrapper10=styled.div(({theme})=>({width:\"10rem\",\"@media (max-width: 768px)\":{display:\"none\"}})),Content=styled.div(({theme})=>({position:\"fixed\",bottom:0,top:0,width:\"10rem\",paddingTop:\"4rem\",paddingBottom:\"2rem\",overflowY:\"auto\",fontFamily:theme.typography.fonts.base,fontSize:theme.typography.size.s2,WebkitFontSmoothing:\"antialiased\",MozOsxFontSmoothing:\"grayscale\",WebkitTapHighlightColor:\"rgba(0, 0, 0, 0)\",WebkitOverflowScrolling:\"touch\",\"& *\":{boxSizing:\"border-box\"},\"& > .toc-wrapper > .toc-list\":{paddingLeft:0,borderLeft:`solid 2px ${theme.color.mediumlight}`,\".toc-list\":{paddingLeft:0,borderLeft:`solid 2px ${theme.color.mediumlight}`,\".toc-list\":{paddingLeft:0,borderLeft:`solid 2px ${theme.color.mediumlight}`}}},\"& .toc-list-item\":{position:\"relative\",listStyleType:\"none\",marginLeft:20,paddingTop:3,paddingBottom:3},\"& .toc-list-item::before\":{content:'\"\"',position:\"absolute\",height:\"100%\",top:0,left:0,transform:\"translateX(calc(-2px - 20px))\",borderLeft:`solid 2px ${theme.color.mediumdark}`,opacity:0,transition:\"opacity 0.2s\"},\"& .toc-list-item.is-active-li::before\":{opacity:1},\"& .toc-list-item > a\":{color:theme.color.defaultText,textDecoration:\"none\"},\"& .toc-list-item.is-active-li > a\":{fontWeight:600,color:theme.color.secondary,textDecoration:\"none\"}})),Heading=styled.p(({theme})=>({fontWeight:600,fontSize:\"0.875em\",color:theme.textColor,textTransform:\"uppercase\",marginBottom:10})),OptionalTitle=({title})=>title===null?null:typeof title==\"string\"?React17.createElement(Heading,null,title):title,TableOfContents=({title,disable,headingSelector,contentsSelector,ignoreSelector,unsafeTocbotOptions})=>(useEffect(()=>{let configuration={tocSelector:\".toc-wrapper\",contentSelector:contentsSelector??\".sbdocs-content\",headingSelector:headingSelector??\"h3\",ignoreSelector:ignoreSelector??\".docs-story *, .skip-toc\",headingsOffset:40,scrollSmoothOffset:-40,orderedList:!1,onClick:()=>!1,...unsafeTocbotOptions},timeout=setTimeout(()=>tocbot.init(configuration),100);return ()=>{clearTimeout(timeout),tocbot.destroy();}},[disable]),React17.createElement(React17.Fragment,null,React17.createElement(Wrapper10,null,disable?null:React17.createElement(Content,null,React17.createElement(OptionalTitle,{title:title||null}),React17.createElement(\"div\",{className:\"toc-wrapper\"})))));var {document:document3,window:globalWindow3}=global,DocsContainer=({context,theme,children})=>{let toc;try{toc=context.resolveOf(\"meta\",[\"meta\"]).preparedMeta.parameters?.docs?.toc;}catch{toc=context?.projectAnnotations?.parameters?.docs?.toc;}return useEffect(()=>{let url;try{if(url=new URL(globalWindow3.parent.location.toString()),url.hash){let element=document3.getElementById(url.hash.substring(1));element&&setTimeout(()=>{scrollToElement(element);},200);}}catch{}}),React17.createElement(DocsContext.Provider,{value:context},React17.createElement(SourceContainer,{channel:context.channel},React17.createElement(ThemeProvider,{theme:ensure(theme)},React17.createElement(DocsPageWrapper,{toc:toc?React17.createElement(TableOfContents,{className:\"sbdocs sbdocs-toc--custom\",...toc}):null},children))))};var STORY_KIND_PATH_SEPARATOR=/\\s*\\/\\s*/,extractTitle=title=>{let groups=title.trim().split(STORY_KIND_PATH_SEPARATOR);return groups?.[groups?.length-1]||title},Title2=props=>{let{children,of}=props;if(\"of\"in props&&of===void 0)throw new Error(\"Unexpected `of={undefined}`, did you mistype a CSF file reference?\");let preparedMeta;try{preparedMeta=useOf(of||\"meta\",[\"meta\"]).preparedMeta;}catch(error){if(children&&!error.message.includes(\"did you forget to use <Meta of={} />?\"))throw error}let content=children||extractTitle(preparedMeta?.title);return content?React17.createElement(Title,{className:\"sbdocs-title sb-unstyled\"},content):null};var DEPRECATION_MIGRATION_LINK=\"https://github.com/storybookjs/storybook/blob/next/MIGRATION.md#subtitle-block-and-parameterscomponentsubtitle\",Subtitle2=props=>{let{of,children}=props;if(\"of\"in props&&of===void 0)throw new Error(\"Unexpected `of={undefined}`, did you mistype a CSF file reference?\");let preparedMeta;try{preparedMeta=useOf(of||\"meta\",[\"meta\"]).preparedMeta;}catch(error){if(children&&!error.message.includes(\"did you forget to use <Meta of={} />?\"))throw error}let{componentSubtitle,docs}=preparedMeta?.parameters||{};componentSubtitle&&deprecate(`Using 'parameters.componentSubtitle' property to subtitle stories is deprecated. See ${DEPRECATION_MIGRATION_LINK}`);let content=children||docs?.subtitle||componentSubtitle;return content?React17.createElement(Subtitle,{className:\"sbdocs-subtitle sb-unstyled\"},content):null};var Subheading=({children,disableAnchor})=>{if(disableAnchor||typeof children!=\"string\")return React17.createElement(H3,null,children);let tagID=globalThis.encodeURIComponent(children.toLowerCase());return React17.createElement(HeaderMdx,{as:\"h3\",id:tagID},children)};var DocsStory=({of,expanded=!0,withToolbar:withToolbarProp=!1,__forceInitialArgs=!1,__primary=!1})=>{let{story}=useOf(of||\"story\",[\"story\"]),withToolbar=story.parameters.docs?.canvas?.withToolbar??withToolbarProp;return React17.createElement(Anchor,{storyId:story.id},expanded&&React17.createElement(React17.Fragment,null,React17.createElement(Subheading,null,story.name),React17.createElement(DescriptionContainer,{of})),React17.createElement(Canvas,{of,withToolbar,story:{__forceInitialArgs,__primary},source:{__forceInitialArgs}}))};var Primary=props=>{let{of}=props;if(\"of\"in props&&of===void 0)throw new Error(\"Unexpected `of={undefined}`, did you mistype a CSF file reference?\");let{csfFile}=useOf(of||\"meta\",[\"meta\"]),primaryStory=useContext(DocsContext).componentStoriesFromCSFFile(csfFile)[0];return primaryStory?React17.createElement(DocsStory,{of:primaryStory.moduleExport,expanded:!1,__primary:!0,withToolbar:!0}):null};var Heading2=({children,disableAnchor,...props})=>{if(disableAnchor||typeof children!=\"string\")return React17.createElement(H2,null,children);let tagID=children.toLowerCase().replace(/[^a-z0-9]/gi,\"-\");return React17.createElement(HeaderMdx,{as:\"h2\",id:tagID,...props},children)};var StyledHeading=styled(Heading2)(({theme})=>({fontSize:`${theme.typography.size.s2-1}px`,fontWeight:theme.typography.weight.bold,lineHeight:\"16px\",letterSpacing:\"0.35em\",textTransform:\"uppercase\",color:theme.textMutedColor,border:0,marginBottom:\"12px\",\"&:first-of-type\":{marginTop:\"56px\"}})),Stories=({title=\"Stories\",includePrimary=!0})=>{let{componentStories,projectAnnotations,getStoryContext}=useContext(DocsContext),stories=componentStories(),{stories:{filter}={filter:void 0}}=projectAnnotations.parameters?.docs||{};return filter&&(stories=stories.filter(story=>filter(story,getStoryContext(story)))),stories.some(story=>story.tags?.includes(\"autodocs\"))&&(stories=stories.filter(story=>story.tags?.includes(\"autodocs\"))),includePrimary||(stories=stories.slice(1)),!stories||stories.length===0?null:React17.createElement(React17.Fragment,null,React17.createElement(StyledHeading,null,title),stories.map(story=>story&&React17.createElement(DocsStory,{key:story.id,of:story.moduleExport,expanded:!0,__forceInitialArgs:!0})))};var DocsPage=()=>{let resolvedOf=useOf(\"meta\",[\"meta\"]),{stories}=resolvedOf.csfFile,isSingleStory=Object.keys(stories).length===1;return React17.createElement(React17.Fragment,null,React17.createElement(Title2,null),React17.createElement(Subtitle2,null),React17.createElement(DescriptionContainer,{of:\"meta\"}),isSingleStory?React17.createElement(DescriptionContainer,{of:\"story\"}):null,React17.createElement(Primary,null),React17.createElement(Controls3,null),isSingleStory?null:React17.createElement(Stories,null))};function Docs({context,docsParameter}){let Container=docsParameter.container||DocsContainer,Page=docsParameter.page||DocsPage;return React17.createElement(Container,{context,theme:docsParameter.theme},React17.createElement(Page,null))}var ExternalDocsContext=class extends DocsContext$1{constructor(channel,store,renderStoryToElement,processMetaExports){super(channel,store,renderStoryToElement,[]);this.channel=channel;this.store=store;this.renderStoryToElement=renderStoryToElement;this.processMetaExports=processMetaExports;this.referenceMeta=(metaExports,attach)=>{let csfFile=this.processMetaExports(metaExports);this.referenceCSFFile(csfFile),super.referenceMeta(metaExports,attach);};}};var ConstantMap=class{constructor(prefix){this.prefix=prefix;this.entries=new Map;}get(key){return this.entries.has(key)||this.entries.set(key,`${this.prefix}${this.entries.size}`),this.entries.get(key)}},ExternalPreview=class extends Preview$1{constructor(projectAnnotations){super(path=>Promise.resolve(this.moduleExportsByImportPath[path]),()=>composeConfigs([{parameters:{docs:{story:{inline:!0}}}},this.projectAnnotations]),new Channel({}));this.projectAnnotations=projectAnnotations;this.importPaths=new ConstantMap(\"./importPath/\");this.titles=new ConstantMap(\"title-\");this.storyIndex={v:5,entries:{}};this.moduleExportsByImportPath={};this.processMetaExports=metaExports=>{let importPath=this.importPaths.get(metaExports);this.moduleExportsByImportPath[importPath]=metaExports;let title=metaExports.default.title||this.titles.get(metaExports),csfFile=this.storyStoreValue.processCSFFileWithCache(metaExports,importPath,title);return Object.values(csfFile.stories).forEach(({id,name})=>{this.storyIndex.entries[id]={id,importPath,title,name,type:\"story\"};}),this.onStoriesChanged({storyIndex:this.storyIndex}),csfFile};this.docsContext=()=>new ExternalDocsContext(this.channel,this.storyStoreValue,this.renderStoryToElement.bind(this),this.processMetaExports.bind(this));}async getStoryIndexFromServer(){return this.storyIndex}};function usePreview(projectAnnotations){let previewRef=useRef();return previewRef.current||(previewRef.current=new ExternalPreview(projectAnnotations)),previewRef.current}function ExternalDocs({projectAnnotationsList,children}){let projectAnnotations=composeConfigs(projectAnnotationsList),preview2=usePreview(projectAnnotations),docsParameter={...projectAnnotations.parameters?.docs,page:()=>children};return React17.createElement(Docs,{docsParameter,context:preview2.docsContext()})}var preview,ExternalDocsContainer=({projectAnnotations,children})=>(preview||(preview=new ExternalPreview(projectAnnotations)),React17.createElement(DocsContext.Provider,{value:preview.docsContext()},React17.createElement(ThemeProvider,{theme:ensure(themes.light)},children)));var Meta=({of})=>{let context=useContext(DocsContext);of&&context.referenceMeta(of,!0);try{let primary=context.storyById();return React17.createElement(Anchor,{storyId:primary.id})}catch{return null}};var Unstyled=props=>React17.createElement(\"div\",{...props,className:\"sb-unstyled\"});var Wrapper11=({children})=>React17.createElement(\"div\",{style:{fontFamily:\"sans-serif\"}},children);var PRIMARY_STORY=\"^\";\n\nexport { AddContext, Anchor, AnchorMdx, ArgTypes, BooleanControl, Canvas, CodeOrSourceMdx, ColorControl, ColorItem, ColorPalette, Controls3 as Controls, DateControl, DescriptionContainer as Description, DescriptionType, Docs, DocsContainer, DocsContext, DocsPage, DocsStory, ExternalDocs, ExternalDocsContainer, FilesControl, HeaderMdx, HeadersMdx, Heading2 as Heading, IconGallery, IconItem, Markdown2 as Markdown, Meta, NumberControl, ObjectControl, OptionsControl, PRIMARY_STORY, Primary, ArgsTable as PureArgsTable, RangeControl, Source2 as Source, SourceContainer, SourceContext, Stories, Story2 as Story, Subheading, Subtitle2 as Subtitle, TextControl, Title2 as Title, Typeset, UNKNOWN_ARGS_HASH, Unstyled, Wrapper11 as Wrapper, anchorBlockIdFromId, argsHash, assertIsFn, extractTitle, format, formatDate, formatTime, getStoryId2 as getStoryId, getStoryProps, parse2 as parse, parseDate, parseTime, useOf, useSourceProps };\n", "import { _extends, _objectWithoutPropertiesLoose } from './chunk-FD4M6EBV.mjs';\nimport { __commonJS, __toESM } from './chunk-GN5PWX3D.mjs';\nimport * as React3 from 'react';\nimport React3__default, { useState, useCallback, useEffect } from 'react';\nimport * as ReactDOM from 'react-dom';\nimport ReactDOM__default from 'react-dom';\nimport { styled, lighten } from '@storybook/theming';\nimport { global } from '@storybook/global';\nimport memoize from 'memoizerific';\n\nvar require_react_fast_compare=__commonJS({\"../../node_modules/react-fast-compare/index.js\"(exports,module){var hasElementType=typeof Element<\"u\",hasMap=typeof Map==\"function\",hasSet=typeof Set==\"function\",hasArrayBuffer=typeof ArrayBuffer==\"function\"&&!!ArrayBuffer.isView;function equal(a,b){if(a===b)return !0;if(a&&b&&typeof a==\"object\"&&typeof b==\"object\"){if(a.constructor!==b.constructor)return !1;var length,i,keys;if(Array.isArray(a)){if(length=a.length,length!=b.length)return !1;for(i=length;i--!==0;)if(!equal(a[i],b[i]))return !1;return !0}var it;if(hasMap&&a instanceof Map&&b instanceof Map){if(a.size!==b.size)return !1;for(it=a.entries();!(i=it.next()).done;)if(!b.has(i.value[0]))return !1;for(it=a.entries();!(i=it.next()).done;)if(!equal(i.value[1],b.get(i.value[0])))return !1;return !0}if(hasSet&&a instanceof Set&&b instanceof Set){if(a.size!==b.size)return !1;for(it=a.entries();!(i=it.next()).done;)if(!b.has(i.value[0]))return !1;return !0}if(hasArrayBuffer&&ArrayBuffer.isView(a)&&ArrayBuffer.isView(b)){if(length=a.length,length!=b.length)return !1;for(i=length;i--!==0;)if(a[i]!==b[i])return !1;return !0}if(a.constructor===RegExp)return a.source===b.source&&a.flags===b.flags;if(a.valueOf!==Object.prototype.valueOf&&typeof a.valueOf==\"function\"&&typeof b.valueOf==\"function\")return a.valueOf()===b.valueOf();if(a.toString!==Object.prototype.toString&&typeof a.toString==\"function\"&&typeof b.toString==\"function\")return a.toString()===b.toString();if(keys=Object.keys(a),length=keys.length,length!==Object.keys(b).length)return !1;for(i=length;i--!==0;)if(!Object.prototype.hasOwnProperty.call(b,keys[i]))return !1;if(hasElementType&&a instanceof Element)return !1;for(i=length;i--!==0;)if(!((keys[i]===\"_owner\"||keys[i]===\"__v\"||keys[i]===\"__o\")&&a.$$typeof)&&!equal(a[keys[i]],b[keys[i]]))return !1;return !0}return a!==a&&b!==b}module.exports=function(a,b){try{return equal(a,b)}catch(error){if((error.message||\"\").match(/stack|recursion/i))return console.warn(\"react-fast-compare cannot handle circular refs\"),!1;throw error}};}});var fromEntries=function(entries){return entries.reduce(function(acc,_ref){var key=_ref[0],value=_ref[1];return acc[key]=value,acc},{})},useIsomorphicLayoutEffect=typeof window<\"u\"&&window.document&&window.document.createElement?React3.useLayoutEffect:React3.useEffect;var top=\"top\",bottom=\"bottom\",right=\"right\",left=\"left\",auto=\"auto\",basePlacements=[top,bottom,right,left],start=\"start\",end=\"end\",clippingParents=\"clippingParents\",viewport=\"viewport\",popper=\"popper\",reference=\"reference\",variationPlacements=basePlacements.reduce(function(acc,placement){return acc.concat([placement+\"-\"+start,placement+\"-\"+end])},[]),placements=[].concat(basePlacements,[auto]).reduce(function(acc,placement){return acc.concat([placement,placement+\"-\"+start,placement+\"-\"+end])},[]),beforeRead=\"beforeRead\",read=\"read\",afterRead=\"afterRead\",beforeMain=\"beforeMain\",main=\"main\",afterMain=\"afterMain\",beforeWrite=\"beforeWrite\",write=\"write\",afterWrite=\"afterWrite\",modifierPhases=[beforeRead,read,afterRead,beforeMain,main,afterMain,beforeWrite,write,afterWrite];function getNodeName(element){return element?(element.nodeName||\"\").toLowerCase():null}function getWindow(node){if(node==null)return window;if(node.toString()!==\"[object Window]\"){var ownerDocument=node.ownerDocument;return ownerDocument&&ownerDocument.defaultView||window}return node}function isElement(node){var OwnElement=getWindow(node).Element;return node instanceof OwnElement||node instanceof Element}function isHTMLElement(node){var OwnElement=getWindow(node).HTMLElement;return node instanceof OwnElement||node instanceof HTMLElement}function isShadowRoot(node){if(typeof ShadowRoot>\"u\")return !1;var OwnElement=getWindow(node).ShadowRoot;return node instanceof OwnElement||node instanceof ShadowRoot}function applyStyles(_ref){var state=_ref.state;Object.keys(state.elements).forEach(function(name){var style=state.styles[name]||{},attributes=state.attributes[name]||{},element=state.elements[name];!isHTMLElement(element)||!getNodeName(element)||(Object.assign(element.style,style),Object.keys(attributes).forEach(function(name2){var value=attributes[name2];value===!1?element.removeAttribute(name2):element.setAttribute(name2,value===!0?\"\":value);}));});}function effect(_ref2){var state=_ref2.state,initialStyles={popper:{position:state.options.strategy,left:\"0\",top:\"0\",margin:\"0\"},arrow:{position:\"absolute\"},reference:{}};return Object.assign(state.elements.popper.style,initialStyles.popper),state.styles=initialStyles,state.elements.arrow&&Object.assign(state.elements.arrow.style,initialStyles.arrow),function(){Object.keys(state.elements).forEach(function(name){var element=state.elements[name],attributes=state.attributes[name]||{},styleProperties=Object.keys(state.styles.hasOwnProperty(name)?state.styles[name]:initialStyles[name]),style=styleProperties.reduce(function(style2,property){return style2[property]=\"\",style2},{});!isHTMLElement(element)||!getNodeName(element)||(Object.assign(element.style,style),Object.keys(attributes).forEach(function(attribute){element.removeAttribute(attribute);}));});}}var applyStyles_default={name:\"applyStyles\",enabled:!0,phase:\"write\",fn:applyStyles,effect,requires:[\"computeStyles\"]};function getBasePlacement(placement){return placement.split(\"-\")[0]}var max=Math.max,min=Math.min,round=Math.round;function getUAString(){var uaData=navigator.userAgentData;return uaData!=null&&uaData.brands&&Array.isArray(uaData.brands)?uaData.brands.map(function(item){return item.brand+\"/\"+item.version}).join(\" \"):navigator.userAgent}function isLayoutViewport(){return !/^((?!chrome|android).)*safari/i.test(getUAString())}function getBoundingClientRect(element,includeScale,isFixedStrategy){includeScale===void 0&&(includeScale=!1),isFixedStrategy===void 0&&(isFixedStrategy=!1);var clientRect=element.getBoundingClientRect(),scaleX=1,scaleY=1;includeScale&&isHTMLElement(element)&&(scaleX=element.offsetWidth>0&&round(clientRect.width)/element.offsetWidth||1,scaleY=element.offsetHeight>0&&round(clientRect.height)/element.offsetHeight||1);var _ref=isElement(element)?getWindow(element):window,visualViewport=_ref.visualViewport,addVisualOffsets=!isLayoutViewport()&&isFixedStrategy,x=(clientRect.left+(addVisualOffsets&&visualViewport?visualViewport.offsetLeft:0))/scaleX,y=(clientRect.top+(addVisualOffsets&&visualViewport?visualViewport.offsetTop:0))/scaleY,width=clientRect.width/scaleX,height=clientRect.height/scaleY;return {width,height,top:y,right:x+width,bottom:y+height,left:x,x,y}}function getLayoutRect(element){var clientRect=getBoundingClientRect(element),width=element.offsetWidth,height=element.offsetHeight;return Math.abs(clientRect.width-width)<=1&&(width=clientRect.width),Math.abs(clientRect.height-height)<=1&&(height=clientRect.height),{x:element.offsetLeft,y:element.offsetTop,width,height}}function contains(parent,child){var rootNode=child.getRootNode&&child.getRootNode();if(parent.contains(child))return !0;if(rootNode&&isShadowRoot(rootNode)){var next=child;do{if(next&&parent.isSameNode(next))return !0;next=next.parentNode||next.host;}while(next)}return !1}function getComputedStyle(element){return getWindow(element).getComputedStyle(element)}function isTableElement(element){return [\"table\",\"td\",\"th\"].indexOf(getNodeName(element))>=0}function getDocumentElement(element){return ((isElement(element)?element.ownerDocument:element.document)||window.document).documentElement}function getParentNode(element){return getNodeName(element)===\"html\"?element:element.assignedSlot||element.parentNode||(isShadowRoot(element)?element.host:null)||getDocumentElement(element)}function getTrueOffsetParent(element){return !isHTMLElement(element)||getComputedStyle(element).position===\"fixed\"?null:element.offsetParent}function getContainingBlock(element){var isFirefox=/firefox/i.test(getUAString()),isIE=/Trident/i.test(getUAString());if(isIE&&isHTMLElement(element)){var elementCss=getComputedStyle(element);if(elementCss.position===\"fixed\")return null}var currentNode=getParentNode(element);for(isShadowRoot(currentNode)&&(currentNode=currentNode.host);isHTMLElement(currentNode)&&[\"html\",\"body\"].indexOf(getNodeName(currentNode))<0;){var css=getComputedStyle(currentNode);if(css.transform!==\"none\"||css.perspective!==\"none\"||css.contain===\"paint\"||[\"transform\",\"perspective\"].indexOf(css.willChange)!==-1||isFirefox&&css.willChange===\"filter\"||isFirefox&&css.filter&&css.filter!==\"none\")return currentNode;currentNode=currentNode.parentNode;}return null}function getOffsetParent(element){for(var window2=getWindow(element),offsetParent=getTrueOffsetParent(element);offsetParent&&isTableElement(offsetParent)&&getComputedStyle(offsetParent).position===\"static\";)offsetParent=getTrueOffsetParent(offsetParent);return offsetParent&&(getNodeName(offsetParent)===\"html\"||getNodeName(offsetParent)===\"body\"&&getComputedStyle(offsetParent).position===\"static\")?window2:offsetParent||getContainingBlock(element)||window2}function getMainAxisFromPlacement(placement){return [\"top\",\"bottom\"].indexOf(placement)>=0?\"x\":\"y\"}function within(min2,value,max2){return max(min2,min(value,max2))}function withinMaxClamp(min2,value,max2){var v=within(min2,value,max2);return v>max2?max2:v}function getFreshSideObject(){return {top:0,right:0,bottom:0,left:0}}function mergePaddingObject(paddingObject){return Object.assign({},getFreshSideObject(),paddingObject)}function expandToHashMap(value,keys){return keys.reduce(function(hashMap,key){return hashMap[key]=value,hashMap},{})}var toPaddingObject=function(padding,state){return padding=typeof padding==\"function\"?padding(Object.assign({},state.rects,{placement:state.placement})):padding,mergePaddingObject(typeof padding!=\"number\"?padding:expandToHashMap(padding,basePlacements))};function arrow(_ref){var _state$modifiersData$,state=_ref.state,name=_ref.name,options=_ref.options,arrowElement=state.elements.arrow,popperOffsets2=state.modifiersData.popperOffsets,basePlacement=getBasePlacement(state.placement),axis=getMainAxisFromPlacement(basePlacement),isVertical=[left,right].indexOf(basePlacement)>=0,len=isVertical?\"height\":\"width\";if(!(!arrowElement||!popperOffsets2)){var paddingObject=toPaddingObject(options.padding,state),arrowRect=getLayoutRect(arrowElement),minProp=axis===\"y\"?top:left,maxProp=axis===\"y\"?bottom:right,endDiff=state.rects.reference[len]+state.rects.reference[axis]-popperOffsets2[axis]-state.rects.popper[len],startDiff=popperOffsets2[axis]-state.rects.reference[axis],arrowOffsetParent=getOffsetParent(arrowElement),clientSize=arrowOffsetParent?axis===\"y\"?arrowOffsetParent.clientHeight||0:arrowOffsetParent.clientWidth||0:0,centerToReference=endDiff/2-startDiff/2,min2=paddingObject[minProp],max2=clientSize-arrowRect[len]-paddingObject[maxProp],center=clientSize/2-arrowRect[len]/2+centerToReference,offset2=within(min2,center,max2),axisProp=axis;state.modifiersData[name]=(_state$modifiersData$={},_state$modifiersData$[axisProp]=offset2,_state$modifiersData$.centerOffset=offset2-center,_state$modifiersData$);}}function effect2(_ref2){var state=_ref2.state,options=_ref2.options,_options$element=options.element,arrowElement=_options$element===void 0?\"[data-popper-arrow]\":_options$element;arrowElement!=null&&(typeof arrowElement==\"string\"&&(arrowElement=state.elements.popper.querySelector(arrowElement),!arrowElement)||contains(state.elements.popper,arrowElement)&&(state.elements.arrow=arrowElement));}var arrow_default={name:\"arrow\",enabled:!0,phase:\"main\",fn:arrow,effect:effect2,requires:[\"popperOffsets\"],requiresIfExists:[\"preventOverflow\"]};function getVariation(placement){return placement.split(\"-\")[1]}var unsetSides={top:\"auto\",right:\"auto\",bottom:\"auto\",left:\"auto\"};function roundOffsetsByDPR(_ref,win){var x=_ref.x,y=_ref.y,dpr=win.devicePixelRatio||1;return {x:round(x*dpr)/dpr||0,y:round(y*dpr)/dpr||0}}function mapToStyles(_ref2){var _Object$assign2,popper2=_ref2.popper,popperRect=_ref2.popperRect,placement=_ref2.placement,variation=_ref2.variation,offsets=_ref2.offsets,position=_ref2.position,gpuAcceleration=_ref2.gpuAcceleration,adaptive=_ref2.adaptive,roundOffsets=_ref2.roundOffsets,isFixed=_ref2.isFixed,_offsets$x=offsets.x,x=_offsets$x===void 0?0:_offsets$x,_offsets$y=offsets.y,y=_offsets$y===void 0?0:_offsets$y,_ref3=typeof roundOffsets==\"function\"?roundOffsets({x,y}):{x,y};x=_ref3.x,y=_ref3.y;var hasX=offsets.hasOwnProperty(\"x\"),hasY=offsets.hasOwnProperty(\"y\"),sideX=left,sideY=top,win=window;if(adaptive){var offsetParent=getOffsetParent(popper2),heightProp=\"clientHeight\",widthProp=\"clientWidth\";if(offsetParent===getWindow(popper2)&&(offsetParent=getDocumentElement(popper2),getComputedStyle(offsetParent).position!==\"static\"&&position===\"absolute\"&&(heightProp=\"scrollHeight\",widthProp=\"scrollWidth\")),offsetParent=offsetParent,placement===top||(placement===left||placement===right)&&variation===end){sideY=bottom;var offsetY=isFixed&&offsetParent===win&&win.visualViewport?win.visualViewport.height:offsetParent[heightProp];y-=offsetY-popperRect.height,y*=gpuAcceleration?1:-1;}if(placement===left||(placement===top||placement===bottom)&&variation===end){sideX=right;var offsetX=isFixed&&offsetParent===win&&win.visualViewport?win.visualViewport.width:offsetParent[widthProp];x-=offsetX-popperRect.width,x*=gpuAcceleration?1:-1;}}var commonStyles=Object.assign({position},adaptive&&unsetSides),_ref4=roundOffsets===!0?roundOffsetsByDPR({x,y},getWindow(popper2)):{x,y};if(x=_ref4.x,y=_ref4.y,gpuAcceleration){var _Object$assign;return Object.assign({},commonStyles,(_Object$assign={},_Object$assign[sideY]=hasY?\"0\":\"\",_Object$assign[sideX]=hasX?\"0\":\"\",_Object$assign.transform=(win.devicePixelRatio||1)<=1?\"translate(\"+x+\"px, \"+y+\"px)\":\"translate3d(\"+x+\"px, \"+y+\"px, 0)\",_Object$assign))}return Object.assign({},commonStyles,(_Object$assign2={},_Object$assign2[sideY]=hasY?y+\"px\":\"\",_Object$assign2[sideX]=hasX?x+\"px\":\"\",_Object$assign2.transform=\"\",_Object$assign2))}function computeStyles(_ref5){var state=_ref5.state,options=_ref5.options,_options$gpuAccelerat=options.gpuAcceleration,gpuAcceleration=_options$gpuAccelerat===void 0?!0:_options$gpuAccelerat,_options$adaptive=options.adaptive,adaptive=_options$adaptive===void 0?!0:_options$adaptive,_options$roundOffsets=options.roundOffsets,roundOffsets=_options$roundOffsets===void 0?!0:_options$roundOffsets,commonStyles={placement:getBasePlacement(state.placement),variation:getVariation(state.placement),popper:state.elements.popper,popperRect:state.rects.popper,gpuAcceleration,isFixed:state.options.strategy===\"fixed\"};state.modifiersData.popperOffsets!=null&&(state.styles.popper=Object.assign({},state.styles.popper,mapToStyles(Object.assign({},commonStyles,{offsets:state.modifiersData.popperOffsets,position:state.options.strategy,adaptive,roundOffsets})))),state.modifiersData.arrow!=null&&(state.styles.arrow=Object.assign({},state.styles.arrow,mapToStyles(Object.assign({},commonStyles,{offsets:state.modifiersData.arrow,position:\"absolute\",adaptive:!1,roundOffsets})))),state.attributes.popper=Object.assign({},state.attributes.popper,{\"data-popper-placement\":state.placement});}var computeStyles_default={name:\"computeStyles\",enabled:!0,phase:\"beforeWrite\",fn:computeStyles,data:{}};var passive={passive:!0};function effect3(_ref){var state=_ref.state,instance=_ref.instance,options=_ref.options,_options$scroll=options.scroll,scroll=_options$scroll===void 0?!0:_options$scroll,_options$resize=options.resize,resize=_options$resize===void 0?!0:_options$resize,window2=getWindow(state.elements.popper),scrollParents=[].concat(state.scrollParents.reference,state.scrollParents.popper);return scroll&&scrollParents.forEach(function(scrollParent){scrollParent.addEventListener(\"scroll\",instance.update,passive);}),resize&&window2.addEventListener(\"resize\",instance.update,passive),function(){scroll&&scrollParents.forEach(function(scrollParent){scrollParent.removeEventListener(\"scroll\",instance.update,passive);}),resize&&window2.removeEventListener(\"resize\",instance.update,passive);}}var eventListeners_default={name:\"eventListeners\",enabled:!0,phase:\"write\",fn:function(){},effect:effect3,data:{}};var hash={left:\"right\",right:\"left\",bottom:\"top\",top:\"bottom\"};function getOppositePlacement(placement){return placement.replace(/left|right|bottom|top/g,function(matched){return hash[matched]})}var hash2={start:\"end\",end:\"start\"};function getOppositeVariationPlacement(placement){return placement.replace(/start|end/g,function(matched){return hash2[matched]})}function getWindowScroll(node){var win=getWindow(node),scrollLeft=win.pageXOffset,scrollTop=win.pageYOffset;return {scrollLeft,scrollTop}}function getWindowScrollBarX(element){return getBoundingClientRect(getDocumentElement(element)).left+getWindowScroll(element).scrollLeft}function getViewportRect(element,strategy){var win=getWindow(element),html=getDocumentElement(element),visualViewport=win.visualViewport,width=html.clientWidth,height=html.clientHeight,x=0,y=0;if(visualViewport){width=visualViewport.width,height=visualViewport.height;var layoutViewport=isLayoutViewport();(layoutViewport||!layoutViewport&&strategy===\"fixed\")&&(x=visualViewport.offsetLeft,y=visualViewport.offsetTop);}return {width,height,x:x+getWindowScrollBarX(element),y}}function getDocumentRect(element){var _element$ownerDocumen,html=getDocumentElement(element),winScroll=getWindowScroll(element),body=(_element$ownerDocumen=element.ownerDocument)==null?void 0:_element$ownerDocumen.body,width=max(html.scrollWidth,html.clientWidth,body?body.scrollWidth:0,body?body.clientWidth:0),height=max(html.scrollHeight,html.clientHeight,body?body.scrollHeight:0,body?body.clientHeight:0),x=-winScroll.scrollLeft+getWindowScrollBarX(element),y=-winScroll.scrollTop;return getComputedStyle(body||html).direction===\"rtl\"&&(x+=max(html.clientWidth,body?body.clientWidth:0)-width),{width,height,x,y}}function isScrollParent(element){var _getComputedStyle=getComputedStyle(element),overflow=_getComputedStyle.overflow,overflowX=_getComputedStyle.overflowX,overflowY=_getComputedStyle.overflowY;return /auto|scroll|overlay|hidden/.test(overflow+overflowY+overflowX)}function getScrollParent(node){return [\"html\",\"body\",\"#document\"].indexOf(getNodeName(node))>=0?node.ownerDocument.body:isHTMLElement(node)&&isScrollParent(node)?node:getScrollParent(getParentNode(node))}function listScrollParents(element,list){var _element$ownerDocumen;list===void 0&&(list=[]);var scrollParent=getScrollParent(element),isBody=scrollParent===((_element$ownerDocumen=element.ownerDocument)==null?void 0:_element$ownerDocumen.body),win=getWindow(scrollParent),target=isBody?[win].concat(win.visualViewport||[],isScrollParent(scrollParent)?scrollParent:[]):scrollParent,updatedList=list.concat(target);return isBody?updatedList:updatedList.concat(listScrollParents(getParentNode(target)))}function rectToClientRect(rect){return Object.assign({},rect,{left:rect.x,top:rect.y,right:rect.x+rect.width,bottom:rect.y+rect.height})}function getInnerBoundingClientRect(element,strategy){var rect=getBoundingClientRect(element,!1,strategy===\"fixed\");return rect.top=rect.top+element.clientTop,rect.left=rect.left+element.clientLeft,rect.bottom=rect.top+element.clientHeight,rect.right=rect.left+element.clientWidth,rect.width=element.clientWidth,rect.height=element.clientHeight,rect.x=rect.left,rect.y=rect.top,rect}function getClientRectFromMixedType(element,clippingParent,strategy){return clippingParent===viewport?rectToClientRect(getViewportRect(element,strategy)):isElement(clippingParent)?getInnerBoundingClientRect(clippingParent,strategy):rectToClientRect(getDocumentRect(getDocumentElement(element)))}function getClippingParents(element){var clippingParents2=listScrollParents(getParentNode(element)),canEscapeClipping=[\"absolute\",\"fixed\"].indexOf(getComputedStyle(element).position)>=0,clipperElement=canEscapeClipping&&isHTMLElement(element)?getOffsetParent(element):element;return isElement(clipperElement)?clippingParents2.filter(function(clippingParent){return isElement(clippingParent)&&contains(clippingParent,clipperElement)&&getNodeName(clippingParent)!==\"body\"}):[]}function getClippingRect(element,boundary,rootBoundary,strategy){var mainClippingParents=boundary===\"clippingParents\"?getClippingParents(element):[].concat(boundary),clippingParents2=[].concat(mainClippingParents,[rootBoundary]),firstClippingParent=clippingParents2[0],clippingRect=clippingParents2.reduce(function(accRect,clippingParent){var rect=getClientRectFromMixedType(element,clippingParent,strategy);return accRect.top=max(rect.top,accRect.top),accRect.right=min(rect.right,accRect.right),accRect.bottom=min(rect.bottom,accRect.bottom),accRect.left=max(rect.left,accRect.left),accRect},getClientRectFromMixedType(element,firstClippingParent,strategy));return clippingRect.width=clippingRect.right-clippingRect.left,clippingRect.height=clippingRect.bottom-clippingRect.top,clippingRect.x=clippingRect.left,clippingRect.y=clippingRect.top,clippingRect}function computeOffsets(_ref){var reference2=_ref.reference,element=_ref.element,placement=_ref.placement,basePlacement=placement?getBasePlacement(placement):null,variation=placement?getVariation(placement):null,commonX=reference2.x+reference2.width/2-element.width/2,commonY=reference2.y+reference2.height/2-element.height/2,offsets;switch(basePlacement){case top:offsets={x:commonX,y:reference2.y-element.height};break;case bottom:offsets={x:commonX,y:reference2.y+reference2.height};break;case right:offsets={x:reference2.x+reference2.width,y:commonY};break;case left:offsets={x:reference2.x-element.width,y:commonY};break;default:offsets={x:reference2.x,y:reference2.y};}var mainAxis=basePlacement?getMainAxisFromPlacement(basePlacement):null;if(mainAxis!=null){var len=mainAxis===\"y\"?\"height\":\"width\";switch(variation){case start:offsets[mainAxis]=offsets[mainAxis]-(reference2[len]/2-element[len]/2);break;case end:offsets[mainAxis]=offsets[mainAxis]+(reference2[len]/2-element[len]/2);break;}}return offsets}function detectOverflow(state,options){options===void 0&&(options={});var _options=options,_options$placement=_options.placement,placement=_options$placement===void 0?state.placement:_options$placement,_options$strategy=_options.strategy,strategy=_options$strategy===void 0?state.strategy:_options$strategy,_options$boundary=_options.boundary,boundary=_options$boundary===void 0?clippingParents:_options$boundary,_options$rootBoundary=_options.rootBoundary,rootBoundary=_options$rootBoundary===void 0?viewport:_options$rootBoundary,_options$elementConte=_options.elementContext,elementContext=_options$elementConte===void 0?popper:_options$elementConte,_options$altBoundary=_options.altBoundary,altBoundary=_options$altBoundary===void 0?!1:_options$altBoundary,_options$padding=_options.padding,padding=_options$padding===void 0?0:_options$padding,paddingObject=mergePaddingObject(typeof padding!=\"number\"?padding:expandToHashMap(padding,basePlacements)),altContext=elementContext===popper?reference:popper,popperRect=state.rects.popper,element=state.elements[altBoundary?altContext:elementContext],clippingClientRect=getClippingRect(isElement(element)?element:element.contextElement||getDocumentElement(state.elements.popper),boundary,rootBoundary,strategy),referenceClientRect=getBoundingClientRect(state.elements.reference),popperOffsets2=computeOffsets({reference:referenceClientRect,element:popperRect,strategy:\"absolute\",placement}),popperClientRect=rectToClientRect(Object.assign({},popperRect,popperOffsets2)),elementClientRect=elementContext===popper?popperClientRect:referenceClientRect,overflowOffsets={top:clippingClientRect.top-elementClientRect.top+paddingObject.top,bottom:elementClientRect.bottom-clippingClientRect.bottom+paddingObject.bottom,left:clippingClientRect.left-elementClientRect.left+paddingObject.left,right:elementClientRect.right-clippingClientRect.right+paddingObject.right},offsetData=state.modifiersData.offset;if(elementContext===popper&&offsetData){var offset2=offsetData[placement];Object.keys(overflowOffsets).forEach(function(key){var multiply=[right,bottom].indexOf(key)>=0?1:-1,axis=[top,bottom].indexOf(key)>=0?\"y\":\"x\";overflowOffsets[key]+=offset2[axis]*multiply;});}return overflowOffsets}function computeAutoPlacement(state,options){options===void 0&&(options={});var _options=options,placement=_options.placement,boundary=_options.boundary,rootBoundary=_options.rootBoundary,padding=_options.padding,flipVariations=_options.flipVariations,_options$allowedAutoP=_options.allowedAutoPlacements,allowedAutoPlacements=_options$allowedAutoP===void 0?placements:_options$allowedAutoP,variation=getVariation(placement),placements2=variation?flipVariations?variationPlacements:variationPlacements.filter(function(placement2){return getVariation(placement2)===variation}):basePlacements,allowedPlacements=placements2.filter(function(placement2){return allowedAutoPlacements.indexOf(placement2)>=0});allowedPlacements.length===0&&(allowedPlacements=placements2);var overflows=allowedPlacements.reduce(function(acc,placement2){return acc[placement2]=detectOverflow(state,{placement:placement2,boundary,rootBoundary,padding})[getBasePlacement(placement2)],acc},{});return Object.keys(overflows).sort(function(a,b){return overflows[a]-overflows[b]})}function getExpandedFallbackPlacements(placement){if(getBasePlacement(placement)===auto)return [];var oppositePlacement=getOppositePlacement(placement);return [getOppositeVariationPlacement(placement),oppositePlacement,getOppositeVariationPlacement(oppositePlacement)]}function flip(_ref){var state=_ref.state,options=_ref.options,name=_ref.name;if(!state.modifiersData[name]._skip){for(var _options$mainAxis=options.mainAxis,checkMainAxis=_options$mainAxis===void 0?!0:_options$mainAxis,_options$altAxis=options.altAxis,checkAltAxis=_options$altAxis===void 0?!0:_options$altAxis,specifiedFallbackPlacements=options.fallbackPlacements,padding=options.padding,boundary=options.boundary,rootBoundary=options.rootBoundary,altBoundary=options.altBoundary,_options$flipVariatio=options.flipVariations,flipVariations=_options$flipVariatio===void 0?!0:_options$flipVariatio,allowedAutoPlacements=options.allowedAutoPlacements,preferredPlacement=state.options.placement,basePlacement=getBasePlacement(preferredPlacement),isBasePlacement=basePlacement===preferredPlacement,fallbackPlacements=specifiedFallbackPlacements||(isBasePlacement||!flipVariations?[getOppositePlacement(preferredPlacement)]:getExpandedFallbackPlacements(preferredPlacement)),placements2=[preferredPlacement].concat(fallbackPlacements).reduce(function(acc,placement2){return acc.concat(getBasePlacement(placement2)===auto?computeAutoPlacement(state,{placement:placement2,boundary,rootBoundary,padding,flipVariations,allowedAutoPlacements}):placement2)},[]),referenceRect=state.rects.reference,popperRect=state.rects.popper,checksMap=new Map,makeFallbackChecks=!0,firstFittingPlacement=placements2[0],i=0;i<placements2.length;i++){var placement=placements2[i],_basePlacement=getBasePlacement(placement),isStartVariation=getVariation(placement)===start,isVertical=[top,bottom].indexOf(_basePlacement)>=0,len=isVertical?\"width\":\"height\",overflow=detectOverflow(state,{placement,boundary,rootBoundary,altBoundary,padding}),mainVariationSide=isVertical?isStartVariation?right:left:isStartVariation?bottom:top;referenceRect[len]>popperRect[len]&&(mainVariationSide=getOppositePlacement(mainVariationSide));var altVariationSide=getOppositePlacement(mainVariationSide),checks=[];if(checkMainAxis&&checks.push(overflow[_basePlacement]<=0),checkAltAxis&&checks.push(overflow[mainVariationSide]<=0,overflow[altVariationSide]<=0),checks.every(function(check){return check})){firstFittingPlacement=placement,makeFallbackChecks=!1;break}checksMap.set(placement,checks);}if(makeFallbackChecks)for(var numberOfChecks=flipVariations?3:1,_loop=function(_i2){var fittingPlacement=placements2.find(function(placement2){var checks2=checksMap.get(placement2);if(checks2)return checks2.slice(0,_i2).every(function(check){return check})});if(fittingPlacement)return firstFittingPlacement=fittingPlacement,\"break\"},_i=numberOfChecks;_i>0;_i--){var _ret=_loop(_i);if(_ret===\"break\")break}state.placement!==firstFittingPlacement&&(state.modifiersData[name]._skip=!0,state.placement=firstFittingPlacement,state.reset=!0);}}var flip_default={name:\"flip\",enabled:!0,phase:\"main\",fn:flip,requiresIfExists:[\"offset\"],data:{_skip:!1}};function getSideOffsets(overflow,rect,preventedOffsets){return preventedOffsets===void 0&&(preventedOffsets={x:0,y:0}),{top:overflow.top-rect.height-preventedOffsets.y,right:overflow.right-rect.width+preventedOffsets.x,bottom:overflow.bottom-rect.height+preventedOffsets.y,left:overflow.left-rect.width-preventedOffsets.x}}function isAnySideFullyClipped(overflow){return [top,right,bottom,left].some(function(side){return overflow[side]>=0})}function hide(_ref){var state=_ref.state,name=_ref.name,referenceRect=state.rects.reference,popperRect=state.rects.popper,preventedOffsets=state.modifiersData.preventOverflow,referenceOverflow=detectOverflow(state,{elementContext:\"reference\"}),popperAltOverflow=detectOverflow(state,{altBoundary:!0}),referenceClippingOffsets=getSideOffsets(referenceOverflow,referenceRect),popperEscapeOffsets=getSideOffsets(popperAltOverflow,popperRect,preventedOffsets),isReferenceHidden=isAnySideFullyClipped(referenceClippingOffsets),hasPopperEscaped=isAnySideFullyClipped(popperEscapeOffsets);state.modifiersData[name]={referenceClippingOffsets,popperEscapeOffsets,isReferenceHidden,hasPopperEscaped},state.attributes.popper=Object.assign({},state.attributes.popper,{\"data-popper-reference-hidden\":isReferenceHidden,\"data-popper-escaped\":hasPopperEscaped});}var hide_default={name:\"hide\",enabled:!0,phase:\"main\",requiresIfExists:[\"preventOverflow\"],fn:hide};function distanceAndSkiddingToXY(placement,rects,offset2){var basePlacement=getBasePlacement(placement),invertDistance=[left,top].indexOf(basePlacement)>=0?-1:1,_ref=typeof offset2==\"function\"?offset2(Object.assign({},rects,{placement})):offset2,skidding=_ref[0],distance=_ref[1];return skidding=skidding||0,distance=(distance||0)*invertDistance,[left,right].indexOf(basePlacement)>=0?{x:distance,y:skidding}:{x:skidding,y:distance}}function offset(_ref2){var state=_ref2.state,options=_ref2.options,name=_ref2.name,_options$offset=options.offset,offset2=_options$offset===void 0?[0,0]:_options$offset,data=placements.reduce(function(acc,placement){return acc[placement]=distanceAndSkiddingToXY(placement,state.rects,offset2),acc},{}),_data$state$placement=data[state.placement],x=_data$state$placement.x,y=_data$state$placement.y;state.modifiersData.popperOffsets!=null&&(state.modifiersData.popperOffsets.x+=x,state.modifiersData.popperOffsets.y+=y),state.modifiersData[name]=data;}var offset_default={name:\"offset\",enabled:!0,phase:\"main\",requires:[\"popperOffsets\"],fn:offset};function popperOffsets(_ref){var state=_ref.state,name=_ref.name;state.modifiersData[name]=computeOffsets({reference:state.rects.reference,element:state.rects.popper,strategy:\"absolute\",placement:state.placement});}var popperOffsets_default={name:\"popperOffsets\",enabled:!0,phase:\"read\",fn:popperOffsets,data:{}};function getAltAxis(axis){return axis===\"x\"?\"y\":\"x\"}function preventOverflow(_ref){var state=_ref.state,options=_ref.options,name=_ref.name,_options$mainAxis=options.mainAxis,checkMainAxis=_options$mainAxis===void 0?!0:_options$mainAxis,_options$altAxis=options.altAxis,checkAltAxis=_options$altAxis===void 0?!1:_options$altAxis,boundary=options.boundary,rootBoundary=options.rootBoundary,altBoundary=options.altBoundary,padding=options.padding,_options$tether=options.tether,tether=_options$tether===void 0?!0:_options$tether,_options$tetherOffset=options.tetherOffset,tetherOffset=_options$tetherOffset===void 0?0:_options$tetherOffset,overflow=detectOverflow(state,{boundary,rootBoundary,padding,altBoundary}),basePlacement=getBasePlacement(state.placement),variation=getVariation(state.placement),isBasePlacement=!variation,mainAxis=getMainAxisFromPlacement(basePlacement),altAxis=getAltAxis(mainAxis),popperOffsets2=state.modifiersData.popperOffsets,referenceRect=state.rects.reference,popperRect=state.rects.popper,tetherOffsetValue=typeof tetherOffset==\"function\"?tetherOffset(Object.assign({},state.rects,{placement:state.placement})):tetherOffset,normalizedTetherOffsetValue=typeof tetherOffsetValue==\"number\"?{mainAxis:tetherOffsetValue,altAxis:tetherOffsetValue}:Object.assign({mainAxis:0,altAxis:0},tetherOffsetValue),offsetModifierState=state.modifiersData.offset?state.modifiersData.offset[state.placement]:null,data={x:0,y:0};if(popperOffsets2){if(checkMainAxis){var _offsetModifierState$,mainSide=mainAxis===\"y\"?top:left,altSide=mainAxis===\"y\"?bottom:right,len=mainAxis===\"y\"?\"height\":\"width\",offset2=popperOffsets2[mainAxis],min2=offset2+overflow[mainSide],max2=offset2-overflow[altSide],additive=tether?-popperRect[len]/2:0,minLen=variation===start?referenceRect[len]:popperRect[len],maxLen=variation===start?-popperRect[len]:-referenceRect[len],arrowElement=state.elements.arrow,arrowRect=tether&&arrowElement?getLayoutRect(arrowElement):{width:0,height:0},arrowPaddingObject=state.modifiersData[\"arrow#persistent\"]?state.modifiersData[\"arrow#persistent\"].padding:getFreshSideObject(),arrowPaddingMin=arrowPaddingObject[mainSide],arrowPaddingMax=arrowPaddingObject[altSide],arrowLen=within(0,referenceRect[len],arrowRect[len]),minOffset=isBasePlacement?referenceRect[len]/2-additive-arrowLen-arrowPaddingMin-normalizedTetherOffsetValue.mainAxis:minLen-arrowLen-arrowPaddingMin-normalizedTetherOffsetValue.mainAxis,maxOffset=isBasePlacement?-referenceRect[len]/2+additive+arrowLen+arrowPaddingMax+normalizedTetherOffsetValue.mainAxis:maxLen+arrowLen+arrowPaddingMax+normalizedTetherOffsetValue.mainAxis,arrowOffsetParent=state.elements.arrow&&getOffsetParent(state.elements.arrow),clientOffset=arrowOffsetParent?mainAxis===\"y\"?arrowOffsetParent.clientTop||0:arrowOffsetParent.clientLeft||0:0,offsetModifierValue=(_offsetModifierState$=offsetModifierState?.[mainAxis])!=null?_offsetModifierState$:0,tetherMin=offset2+minOffset-offsetModifierValue-clientOffset,tetherMax=offset2+maxOffset-offsetModifierValue,preventedOffset=within(tether?min(min2,tetherMin):min2,offset2,tether?max(max2,tetherMax):max2);popperOffsets2[mainAxis]=preventedOffset,data[mainAxis]=preventedOffset-offset2;}if(checkAltAxis){var _offsetModifierState$2,_mainSide=mainAxis===\"x\"?top:left,_altSide=mainAxis===\"x\"?bottom:right,_offset=popperOffsets2[altAxis],_len=altAxis===\"y\"?\"height\":\"width\",_min=_offset+overflow[_mainSide],_max=_offset-overflow[_altSide],isOriginSide=[top,left].indexOf(basePlacement)!==-1,_offsetModifierValue=(_offsetModifierState$2=offsetModifierState?.[altAxis])!=null?_offsetModifierState$2:0,_tetherMin=isOriginSide?_min:_offset-referenceRect[_len]-popperRect[_len]-_offsetModifierValue+normalizedTetherOffsetValue.altAxis,_tetherMax=isOriginSide?_offset+referenceRect[_len]+popperRect[_len]-_offsetModifierValue-normalizedTetherOffsetValue.altAxis:_max,_preventedOffset=tether&&isOriginSide?withinMaxClamp(_tetherMin,_offset,_tetherMax):within(tether?_tetherMin:_min,_offset,tether?_tetherMax:_max);popperOffsets2[altAxis]=_preventedOffset,data[altAxis]=_preventedOffset-_offset;}state.modifiersData[name]=data;}}var preventOverflow_default={name:\"preventOverflow\",enabled:!0,phase:\"main\",fn:preventOverflow,requiresIfExists:[\"offset\"]};function getHTMLElementScroll(element){return {scrollLeft:element.scrollLeft,scrollTop:element.scrollTop}}function getNodeScroll(node){return node===getWindow(node)||!isHTMLElement(node)?getWindowScroll(node):getHTMLElementScroll(node)}function isElementScaled(element){var rect=element.getBoundingClientRect(),scaleX=round(rect.width)/element.offsetWidth||1,scaleY=round(rect.height)/element.offsetHeight||1;return scaleX!==1||scaleY!==1}function getCompositeRect(elementOrVirtualElement,offsetParent,isFixed){isFixed===void 0&&(isFixed=!1);var isOffsetParentAnElement=isHTMLElement(offsetParent),offsetParentIsScaled=isHTMLElement(offsetParent)&&isElementScaled(offsetParent),documentElement=getDocumentElement(offsetParent),rect=getBoundingClientRect(elementOrVirtualElement,offsetParentIsScaled,isFixed),scroll={scrollLeft:0,scrollTop:0},offsets={x:0,y:0};return (isOffsetParentAnElement||!isOffsetParentAnElement&&!isFixed)&&((getNodeName(offsetParent)!==\"body\"||isScrollParent(documentElement))&&(scroll=getNodeScroll(offsetParent)),isHTMLElement(offsetParent)?(offsets=getBoundingClientRect(offsetParent,!0),offsets.x+=offsetParent.clientLeft,offsets.y+=offsetParent.clientTop):documentElement&&(offsets.x=getWindowScrollBarX(documentElement))),{x:rect.left+scroll.scrollLeft-offsets.x,y:rect.top+scroll.scrollTop-offsets.y,width:rect.width,height:rect.height}}function order(modifiers){var map=new Map,visited=new Set,result=[];modifiers.forEach(function(modifier){map.set(modifier.name,modifier);});function sort(modifier){visited.add(modifier.name);var requires=[].concat(modifier.requires||[],modifier.requiresIfExists||[]);requires.forEach(function(dep){if(!visited.has(dep)){var depModifier=map.get(dep);depModifier&&sort(depModifier);}}),result.push(modifier);}return modifiers.forEach(function(modifier){visited.has(modifier.name)||sort(modifier);}),result}function orderModifiers(modifiers){var orderedModifiers=order(modifiers);return modifierPhases.reduce(function(acc,phase){return acc.concat(orderedModifiers.filter(function(modifier){return modifier.phase===phase}))},[])}function debounce(fn2){var pending;return function(){return pending||(pending=new Promise(function(resolve){Promise.resolve().then(function(){pending=void 0,resolve(fn2());});})),pending}}function mergeByName(modifiers){var merged=modifiers.reduce(function(merged2,current){var existing=merged2[current.name];return merged2[current.name]=existing?Object.assign({},existing,current,{options:Object.assign({},existing.options,current.options),data:Object.assign({},existing.data,current.data)}):current,merged2},{});return Object.keys(merged).map(function(key){return merged[key]})}var DEFAULT_OPTIONS={placement:\"bottom\",modifiers:[],strategy:\"absolute\"};function areValidElements(){for(var _len=arguments.length,args=new Array(_len),_key=0;_key<_len;_key++)args[_key]=arguments[_key];return !args.some(function(element){return !(element&&typeof element.getBoundingClientRect==\"function\")})}function popperGenerator(generatorOptions){generatorOptions===void 0&&(generatorOptions={});var _generatorOptions=generatorOptions,_generatorOptions$def=_generatorOptions.defaultModifiers,defaultModifiers2=_generatorOptions$def===void 0?[]:_generatorOptions$def,_generatorOptions$def2=_generatorOptions.defaultOptions,defaultOptions=_generatorOptions$def2===void 0?DEFAULT_OPTIONS:_generatorOptions$def2;return function(reference2,popper2,options){options===void 0&&(options=defaultOptions);var state={placement:\"bottom\",orderedModifiers:[],options:Object.assign({},DEFAULT_OPTIONS,defaultOptions),modifiersData:{},elements:{reference:reference2,popper:popper2},attributes:{},styles:{}},effectCleanupFns=[],isDestroyed=!1,instance={state,setOptions:function(setOptionsAction){var options2=typeof setOptionsAction==\"function\"?setOptionsAction(state.options):setOptionsAction;cleanupModifierEffects(),state.options=Object.assign({},defaultOptions,state.options,options2),state.scrollParents={reference:isElement(reference2)?listScrollParents(reference2):reference2.contextElement?listScrollParents(reference2.contextElement):[],popper:listScrollParents(popper2)};var orderedModifiers=orderModifiers(mergeByName([].concat(defaultModifiers2,state.options.modifiers)));return state.orderedModifiers=orderedModifiers.filter(function(m){return m.enabled}),runModifierEffects(),instance.update()},forceUpdate:function(){if(!isDestroyed){var _state$elements=state.elements,reference3=_state$elements.reference,popper3=_state$elements.popper;if(areValidElements(reference3,popper3)){state.rects={reference:getCompositeRect(reference3,getOffsetParent(popper3),state.options.strategy===\"fixed\"),popper:getLayoutRect(popper3)},state.reset=!1,state.placement=state.options.placement,state.orderedModifiers.forEach(function(modifier){return state.modifiersData[modifier.name]=Object.assign({},modifier.data)});for(var index=0;index<state.orderedModifiers.length;index++){if(state.reset===!0){state.reset=!1,index=-1;continue}var _state$orderedModifie=state.orderedModifiers[index],fn2=_state$orderedModifie.fn,_state$orderedModifie2=_state$orderedModifie.options,_options=_state$orderedModifie2===void 0?{}:_state$orderedModifie2,name=_state$orderedModifie.name;typeof fn2==\"function\"&&(state=fn2({state,options:_options,name,instance})||state);}}}},update:debounce(function(){return new Promise(function(resolve){instance.forceUpdate(),resolve(state);})}),destroy:function(){cleanupModifierEffects(),isDestroyed=!0;}};if(!areValidElements(reference2,popper2))return instance;instance.setOptions(options).then(function(state2){!isDestroyed&&options.onFirstUpdate&&options.onFirstUpdate(state2);});function runModifierEffects(){state.orderedModifiers.forEach(function(_ref){var name=_ref.name,_ref$options=_ref.options,options2=_ref$options===void 0?{}:_ref$options,effect4=_ref.effect;if(typeof effect4==\"function\"){var cleanupFn=effect4({state,name,instance,options:options2}),noopFn=function(){};effectCleanupFns.push(cleanupFn||noopFn);}});}function cleanupModifierEffects(){effectCleanupFns.forEach(function(fn2){return fn2()}),effectCleanupFns=[];}return instance}}var defaultModifiers=[eventListeners_default,popperOffsets_default,computeStyles_default,applyStyles_default,offset_default,flip_default,preventOverflow_default,arrow_default,hide_default],createPopper=popperGenerator({defaultModifiers});var import_react_fast_compare=__toESM(require_react_fast_compare());var EMPTY_MODIFIERS=[],usePopper=function(referenceElement,popperElement,options){options===void 0&&(options={});var prevOptions=React3.useRef(null),optionsWithDefaults={onFirstUpdate:options.onFirstUpdate,placement:options.placement||\"bottom\",strategy:options.strategy||\"absolute\",modifiers:options.modifiers||EMPTY_MODIFIERS},_React$useState=React3.useState({styles:{popper:{position:optionsWithDefaults.strategy,left:\"0\",top:\"0\"},arrow:{position:\"absolute\"}},attributes:{}}),state=_React$useState[0],setState=_React$useState[1],updateStateModifier=React3.useMemo(function(){return {name:\"updateState\",enabled:!0,phase:\"write\",fn:function(_ref){var state2=_ref.state,elements=Object.keys(state2.elements);ReactDOM.flushSync(function(){setState({styles:fromEntries(elements.map(function(element){return [element,state2.styles[element]||{}]})),attributes:fromEntries(elements.map(function(element){return [element,state2.attributes[element]]}))});});},requires:[\"computeStyles\"]}},[]),popperOptions=React3.useMemo(function(){var newOptions={onFirstUpdate:optionsWithDefaults.onFirstUpdate,placement:optionsWithDefaults.placement,strategy:optionsWithDefaults.strategy,modifiers:[].concat(optionsWithDefaults.modifiers,[updateStateModifier,{name:\"applyStyles\",enabled:!1}])};return (0, import_react_fast_compare.default)(prevOptions.current,newOptions)?prevOptions.current||newOptions:(prevOptions.current=newOptions,newOptions)},[optionsWithDefaults.onFirstUpdate,optionsWithDefaults.placement,optionsWithDefaults.strategy,optionsWithDefaults.modifiers,updateStateModifier]),popperInstanceRef=React3.useRef();return useIsomorphicLayoutEffect(function(){popperInstanceRef.current&&popperInstanceRef.current.setOptions(popperOptions);},[popperOptions]),useIsomorphicLayoutEffect(function(){if(!(referenceElement==null||popperElement==null)){var createPopper2=options.createPopper||createPopper,popperInstance=createPopper2(referenceElement,popperElement,popperOptions);return popperInstanceRef.current=popperInstance,function(){popperInstance.destroy(),popperInstanceRef.current=null;}}},[referenceElement,popperElement,options.createPopper]),{state:popperInstanceRef.current?popperInstanceRef.current.state:null,styles:state.styles,attributes:state.attributes,update:popperInstanceRef.current?popperInstanceRef.current.update:null,forceUpdate:popperInstanceRef.current?popperInstanceRef.current.forceUpdate:null}};function useGetLatest(val){var ref=React3.useRef(val);return ref.current=val,React3.useCallback(function(){return ref.current},[])}var noop=function(){};function useControlledState(_ref){var initial=_ref.initial,value=_ref.value,_ref$onChange=_ref.onChange,onChange=_ref$onChange===void 0?noop:_ref$onChange;if(initial===void 0&&value===void 0)throw new TypeError('Either \"value\" or \"initial\" variable must be set. Now both are undefined');var _React$useState=React3.useState(initial),state=_React$useState[0],setState=_React$useState[1],getLatest=useGetLatest(state),set=React3.useCallback(function(updater){var state2=getLatest(),updatedState=typeof updater==\"function\"?updater(state2):updater;typeof updatedState.persist==\"function\"&&updatedState.persist(),setState(updatedState),typeof onChange==\"function\"&&onChange(updatedState);},[getLatest,onChange]),isControlled=value!==void 0;return [isControlled?value:state,isControlled?onChange:set]}function generateBoundingClientRect(x,y){return x===void 0&&(x=0),y===void 0&&(y=0),function(){return {width:0,height:0,top:y,right:x,bottom:y,left:x,x:0,y:0,toJSON:function(){return null}}}}var _excluded=[\"styles\",\"attributes\"],virtualElement={getBoundingClientRect:generateBoundingClientRect()},defaultConfig={closeOnOutsideClick:!0,closeOnTriggerHidden:!1,defaultVisible:!1,delayHide:0,delayShow:0,followCursor:!1,interactive:!1,mutationObserverOptions:{attributes:!0,childList:!0,subtree:!0},offset:[0,6],trigger:\"hover\"};function usePopperTooltip(config,popperOptions){var _popperProps$state,_popperProps$state$mo,_popperProps$state$mo2;config===void 0&&(config={}),popperOptions===void 0&&(popperOptions={});var finalConfig=Object.keys(defaultConfig).reduce(function(config2,key){var _extends2;return _extends({},config2,(_extends2={},_extends2[key]=config2[key]!==void 0?config2[key]:defaultConfig[key],_extends2))},config),defaultModifiers2=React3.useMemo(function(){return [{name:\"offset\",options:{offset:finalConfig.offset}}]},Array.isArray(finalConfig.offset)?finalConfig.offset:[]),finalPopperOptions=_extends({},popperOptions,{placement:popperOptions.placement||finalConfig.placement,modifiers:popperOptions.modifiers||defaultModifiers2}),_React$useState=React3.useState(null),triggerRef=_React$useState[0],setTriggerRef=_React$useState[1],_React$useState2=React3.useState(null),tooltipRef=_React$useState2[0],setTooltipRef=_React$useState2[1],_useControlledState=useControlledState({initial:finalConfig.defaultVisible,value:finalConfig.visible,onChange:finalConfig.onVisibleChange}),visible=_useControlledState[0],setVisible=_useControlledState[1],timer=React3.useRef();React3.useEffect(function(){return function(){return clearTimeout(timer.current)}},[]);var _usePopper=usePopper(finalConfig.followCursor?virtualElement:triggerRef,tooltipRef,finalPopperOptions),styles=_usePopper.styles,attributes=_usePopper.attributes,popperProps=_objectWithoutPropertiesLoose(_usePopper,_excluded),update=popperProps.update,getLatest=useGetLatest({visible,triggerRef,tooltipRef,finalConfig}),isTriggeredBy=React3.useCallback(function(trigger){return Array.isArray(finalConfig.trigger)?finalConfig.trigger.includes(trigger):finalConfig.trigger===trigger},Array.isArray(finalConfig.trigger)?finalConfig.trigger:[finalConfig.trigger]),hideTooltip=React3.useCallback(function(){clearTimeout(timer.current),timer.current=window.setTimeout(function(){return setVisible(!1)},finalConfig.delayHide);},[finalConfig.delayHide,setVisible]),showTooltip=React3.useCallback(function(){clearTimeout(timer.current),timer.current=window.setTimeout(function(){return setVisible(!0)},finalConfig.delayShow);},[finalConfig.delayShow,setVisible]),toggleTooltip=React3.useCallback(function(){getLatest().visible?hideTooltip():showTooltip();},[getLatest,hideTooltip,showTooltip]);React3.useEffect(function(){if(getLatest().finalConfig.closeOnOutsideClick){var handleClickOutside=function(event){var _event$composedPath,_getLatest=getLatest(),tooltipRef2=_getLatest.tooltipRef,triggerRef2=_getLatest.triggerRef,target=(event.composedPath==null||(_event$composedPath=event.composedPath())==null?void 0:_event$composedPath[0])||event.target;target instanceof Node&&tooltipRef2!=null&&triggerRef2!=null&&!tooltipRef2.contains(target)&&!triggerRef2.contains(target)&&hideTooltip();};return document.addEventListener(\"mousedown\",handleClickOutside),function(){return document.removeEventListener(\"mousedown\",handleClickOutside)}}},[getLatest,hideTooltip]),React3.useEffect(function(){if(!(triggerRef==null||!isTriggeredBy(\"click\")))return triggerRef.addEventListener(\"click\",toggleTooltip),function(){return triggerRef.removeEventListener(\"click\",toggleTooltip)}},[triggerRef,isTriggeredBy,toggleTooltip]),React3.useEffect(function(){if(!(triggerRef==null||!isTriggeredBy(\"double-click\")))return triggerRef.addEventListener(\"dblclick\",toggleTooltip),function(){return triggerRef.removeEventListener(\"dblclick\",toggleTooltip)}},[triggerRef,isTriggeredBy,toggleTooltip]),React3.useEffect(function(){if(!(triggerRef==null||!isTriggeredBy(\"right-click\"))){var preventDefaultAndToggle=function(event){event.preventDefault(),toggleTooltip();};return triggerRef.addEventListener(\"contextmenu\",preventDefaultAndToggle),function(){return triggerRef.removeEventListener(\"contextmenu\",preventDefaultAndToggle)}}},[triggerRef,isTriggeredBy,toggleTooltip]),React3.useEffect(function(){if(!(triggerRef==null||!isTriggeredBy(\"focus\")))return triggerRef.addEventListener(\"focus\",showTooltip),triggerRef.addEventListener(\"blur\",hideTooltip),function(){triggerRef.removeEventListener(\"focus\",showTooltip),triggerRef.removeEventListener(\"blur\",hideTooltip);}},[triggerRef,isTriggeredBy,showTooltip,hideTooltip]),React3.useEffect(function(){if(!(triggerRef==null||!isTriggeredBy(\"hover\")))return triggerRef.addEventListener(\"mouseenter\",showTooltip),triggerRef.addEventListener(\"mouseleave\",hideTooltip),function(){triggerRef.removeEventListener(\"mouseenter\",showTooltip),triggerRef.removeEventListener(\"mouseleave\",hideTooltip);}},[triggerRef,isTriggeredBy,showTooltip,hideTooltip]),React3.useEffect(function(){if(!(tooltipRef==null||!isTriggeredBy(\"hover\")||!getLatest().finalConfig.interactive))return tooltipRef.addEventListener(\"mouseenter\",showTooltip),tooltipRef.addEventListener(\"mouseleave\",hideTooltip),function(){tooltipRef.removeEventListener(\"mouseenter\",showTooltip),tooltipRef.removeEventListener(\"mouseleave\",hideTooltip);}},[tooltipRef,isTriggeredBy,showTooltip,hideTooltip,getLatest]);var isReferenceHidden=popperProps==null||(_popperProps$state=popperProps.state)==null||(_popperProps$state$mo=_popperProps$state.modifiersData)==null||(_popperProps$state$mo2=_popperProps$state$mo.hide)==null?void 0:_popperProps$state$mo2.isReferenceHidden;React3.useEffect(function(){finalConfig.closeOnTriggerHidden&&isReferenceHidden&&hideTooltip();},[finalConfig.closeOnTriggerHidden,hideTooltip,isReferenceHidden]),React3.useEffect(function(){if(!finalConfig.followCursor||triggerRef==null)return;function setMousePosition(_ref){var clientX=_ref.clientX,clientY=_ref.clientY;virtualElement.getBoundingClientRect=generateBoundingClientRect(clientX,clientY),update?.();}return triggerRef.addEventListener(\"mousemove\",setMousePosition),function(){return triggerRef.removeEventListener(\"mousemove\",setMousePosition)}},[finalConfig.followCursor,triggerRef,update]),React3.useEffect(function(){if(!(tooltipRef==null||update==null||finalConfig.mutationObserverOptions==null)){var observer=new MutationObserver(update);return observer.observe(tooltipRef,finalConfig.mutationObserverOptions),function(){return observer.disconnect()}}},[finalConfig.mutationObserverOptions,tooltipRef,update]);var getTooltipProps=function(args){return args===void 0&&(args={}),_extends({},args,{style:_extends({},args.style,styles.popper)},attributes.popper,{\"data-popper-interactive\":finalConfig.interactive})},getArrowProps=function(args){return args===void 0&&(args={}),_extends({},args,attributes.arrow,{style:_extends({},args.style,styles.arrow),\"data-popper-arrow\":!0})};return _extends({getArrowProps,getTooltipProps,setTooltipRef,setTriggerRef,tooltipRef,triggerRef,visible},popperProps)}var match=memoize(1e3)((requests,actual,value,fallback=0)=>actual.split(\"-\")[0]===requests?value:fallback),ArrowSpacing=8,Arrow=styled.div({position:\"absolute\",borderStyle:\"solid\"},({placement})=>{let x=0,y=0;switch(!0){case(placement.startsWith(\"left\")||placement.startsWith(\"right\")):{y=8;break}case(placement.startsWith(\"top\")||placement.startsWith(\"bottom\")):{x=8;break}}return {transform:`translate3d(${x}px, ${y}px, 0px)`}},({theme,color,placement})=>({bottom:`${match(\"top\",placement,`${ArrowSpacing*-1}px`,\"auto\")}`,top:`${match(\"bottom\",placement,`${ArrowSpacing*-1}px`,\"auto\")}`,right:`${match(\"left\",placement,`${ArrowSpacing*-1}px`,\"auto\")}`,left:`${match(\"right\",placement,`${ArrowSpacing*-1}px`,\"auto\")}`,borderBottomWidth:`${match(\"top\",placement,\"0\",ArrowSpacing)}px`,borderTopWidth:`${match(\"bottom\",placement,\"0\",ArrowSpacing)}px`,borderRightWidth:`${match(\"left\",placement,\"0\",ArrowSpacing)}px`,borderLeftWidth:`${match(\"right\",placement,\"0\",ArrowSpacing)}px`,borderTopColor:match(\"top\",placement,theme.color[color]||color||theme.base===\"light\"?lighten(theme.background.app):theme.background.app,\"transparent\"),borderBottomColor:match(\"bottom\",placement,theme.color[color]||color||theme.base===\"light\"?lighten(theme.background.app):theme.background.app,\"transparent\"),borderLeftColor:match(\"left\",placement,theme.color[color]||color||theme.base===\"light\"?lighten(theme.background.app):theme.background.app,\"transparent\"),borderRightColor:match(\"right\",placement,theme.color[color]||color||theme.base===\"light\"?lighten(theme.background.app):theme.background.app,\"transparent\")})),Wrapper=styled.div(({hidden})=>({display:hidden?\"none\":\"inline-block\",zIndex:2147483647}),({theme,color,hasChrome})=>hasChrome?{background:theme.color[color]||color||theme.base===\"light\"?lighten(theme.background.app):theme.background.app,filter:`\n            drop-shadow(0px 5px 5px rgba(0,0,0,0.05))\n            drop-shadow(0 1px 3px rgba(0,0,0,0.1))\n          `,borderRadius:theme.appBorderRadius,fontSize:theme.typography.size.s1}:{}),Tooltip=React3__default.forwardRef(({placement=\"top\",hasChrome=!0,children,arrowProps={},tooltipRef,color,withArrows,...props},ref)=>React3__default.createElement(Wrapper,{\"data-testid\":\"tooltip\",hasChrome,ref,...props,color},hasChrome&&withArrows&&React3__default.createElement(Arrow,{placement,...arrowProps,color}),children));Tooltip.displayName=\"Tooltip\";var {document:document2}=global,TargetContainer=styled.div`\n  display: inline-block;\n  cursor: ${props=>props.trigger===\"hover\"||props.trigger.includes(\"hover\")?\"default\":\"pointer\"};\n`,TargetSvgContainer=styled.g`\n  cursor: ${props=>props.trigger===\"hover\"||props.trigger.includes(\"hover\")?\"default\":\"pointer\"};\n`,WithTooltipPure=({svg=!1,trigger=\"click\",closeOnOutsideClick=!1,placement=\"top\",modifiers=[{name:\"preventOverflow\",options:{padding:8}},{name:\"offset\",options:{offset:[8,8]}},{name:\"arrow\",options:{padding:8}}],hasChrome=!0,defaultVisible=!1,withArrows,offset:offset2,tooltip,children,closeOnTriggerHidden,mutationObserverOptions,delayHide,visible,interactive,delayShow,strategy,followCursor,onVisibleChange,...props})=>{let Container=svg?TargetSvgContainer:TargetContainer,{getArrowProps,getTooltipProps,setTooltipRef,setTriggerRef,visible:isVisible,state}=usePopperTooltip({trigger,placement,defaultVisible,delayHide,interactive,closeOnOutsideClick,closeOnTriggerHidden,onVisibleChange,delayShow,followCursor,mutationObserverOptions,visible,offset:offset2},{modifiers,strategy}),tooltipComponent=React3__default.createElement(Tooltip,{placement:state?.placement,ref:setTooltipRef,hasChrome,arrowProps:getArrowProps(),withArrows,...getTooltipProps()},typeof tooltip==\"function\"?tooltip({onHide:()=>onVisibleChange(!1)}):tooltip);return React3__default.createElement(React3__default.Fragment,null,React3__default.createElement(Container,{trigger,ref:setTriggerRef,...props},children),isVisible&&ReactDOM__default.createPortal(tooltipComponent,document2.body))},WithToolTipState=({startOpen=!1,onVisibleChange:onChange,...rest})=>{let[tooltipShown,setTooltipShown]=useState(startOpen),onVisibilityChange=useCallback(visibility=>{onChange&&onChange(visibility)===!1||setTooltipShown(visibility);},[onChange]);return useEffect(()=>{let hide2=()=>onVisibilityChange(!1);document2.addEventListener(\"keydown\",hide2,!1);let iframes=Array.from(document2.getElementsByTagName(\"iframe\")),unbinders=[];return iframes.forEach(iframe=>{let bind=()=>{try{iframe.contentWindow.document&&(iframe.contentWindow.document.addEventListener(\"click\",hide2),unbinders.push(()=>{try{iframe.contentWindow.document.removeEventListener(\"click\",hide2);}catch{}}));}catch{}};bind(),iframe.addEventListener(\"load\",bind),unbinders.push(()=>{iframe.removeEventListener(\"load\",bind);});}),()=>{document2.removeEventListener(\"keydown\",hide2),unbinders.forEach(unbind=>{unbind();});}}),React3__default.createElement(WithTooltipPure,{...rest,visible:tooltipShown,onVisibleChange:onVisibilityChange})};\n\nexport { WithToolTipState, WithTooltipPure };\n", "import { <PERSON>ynta<PERSON><PERSON><PERSON><PERSON><PERSON>, ScrollA<PERSON> } from './chunk-WDXN43AK.mjs';\nexport { ActionBar, ScrollArea, createCopyToClipboardFunction } from './chunk-WDXN43AK.mjs';\nimport { WithToolTipState } from './chunk-V2RRM4BX.mjs';\nimport { _objectWithoutPropertiesLoose, _extends } from './chunk-FD4M6EBV.mjs';\nimport './chunk-BHPIX5YC.mjs';\nimport './chunk-LYX77QOF.mjs';\nimport './chunk-NFNSWKHS.mjs';\nimport './chunk-SN5EBR6M.mjs';\nimport './chunk-LERD5YUM.mjs';\nimport './chunk-SMISFLQK.mjs';\nimport './chunk-YXGXI2BM.mjs';\nimport './chunk-F5ZZUECD.mjs';\nimport './chunk-HJRLQGCK.mjs';\nimport './chunk-OTNQW4FU.mjs';\nimport './chunk-HYMES5AD.mjs';\nimport './chunk-5DL7KOVF.mjs';\nimport './chunk-CJWKQSGB.mjs';\nimport './chunk-DYAYMNJN.mjs';\nimport { __export } from './chunk-GN5PWX3D.mjs';\nimport * as React3 from 'react';\nimport React3__default, { lazy, forwardRef, useState, useEffect, memo, useMemo, createElement, Children, Suspense, Fragment, useRef, useCallback, useLayoutEffect, Component } from 'react';\nimport { styled, isPropValid, keyframes, ignoreSsrWarning, color, typography } from '@storybook/theming';\nimport * as StorybookIcons from '@storybook/icons';\nimport { CrossIcon, LightningOffIcon, ChevronRightIcon } from '@storybook/icons';\nimport * as Dialog2 from '@radix-ui/react-dialog';\nimport { Slot } from '@radix-ui/react-slot';\nimport { deprecate, logger } from '@storybook/client-logger';\nimport { global } from '@storybook/global';\nimport memoize from 'memoizerific';\nimport { sanitize } from '@storybook/csf';\n\nvar nameSpaceClassNames=({...props},key)=>{let classes=[props.class,props.className];return delete props.class,props.className=[\"sbdocs\",`sbdocs-${key}`,...classes].filter(Boolean).join(\" \"),props};function _assertThisInitialized(self){if(self===void 0)throw new ReferenceError(\"this hasn't been initialised - super() hasn't been called\");return self}function _setPrototypeOf(o,p){return _setPrototypeOf=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(o2,p2){return o2.__proto__=p2,o2},_setPrototypeOf(o,p)}function _inheritsLoose(subClass,superClass){subClass.prototype=Object.create(superClass.prototype),subClass.prototype.constructor=subClass,_setPrototypeOf(subClass,superClass);}function _getPrototypeOf(o){return _getPrototypeOf=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(o2){return o2.__proto__||Object.getPrototypeOf(o2)},_getPrototypeOf(o)}function _isNativeFunction(fn){try{return Function.toString.call(fn).indexOf(\"[native code]\")!==-1}catch{return typeof fn==\"function\"}}function _isNativeReflectConstruct(){try{var t=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}));}catch{}return (_isNativeReflectConstruct=function(){return !!t})()}function _construct(t,e,r){if(_isNativeReflectConstruct())return Reflect.construct.apply(null,arguments);var o=[null];o.push.apply(o,e);var p=new(t.bind.apply(t,o));return r&&_setPrototypeOf(p,r.prototype),p}function _wrapNativeSuper(Class){var _cache=typeof Map==\"function\"?new Map:void 0;return _wrapNativeSuper=function(Class2){if(Class2===null||!_isNativeFunction(Class2))return Class2;if(typeof Class2!=\"function\")throw new TypeError(\"Super expression must either be null or a function\");if(typeof _cache<\"u\"){if(_cache.has(Class2))return _cache.get(Class2);_cache.set(Class2,Wrapper4);}function Wrapper4(){return _construct(Class2,arguments,_getPrototypeOf(this).constructor)}return Wrapper4.prototype=Object.create(Class2.prototype,{constructor:{value:Wrapper4,enumerable:!1,writable:!0,configurable:!0}}),_setPrototypeOf(Wrapper4,Class2)},_wrapNativeSuper(Class)}var ERRORS={1:`Passed invalid arguments to hsl, please pass multiple numbers e.g. hsl(360, 0.75, 0.4) or an object e.g. rgb({ hue: 255, saturation: 0.4, lightness: 0.75 }).\n\n`,2:`Passed invalid arguments to hsla, please pass multiple numbers e.g. hsla(360, 0.75, 0.4, 0.7) or an object e.g. rgb({ hue: 255, saturation: 0.4, lightness: 0.75, alpha: 0.7 }).\n\n`,3:`Passed an incorrect argument to a color function, please pass a string representation of a color.\n\n`,4:`Couldn't generate valid rgb string from %s, it returned %s.\n\n`,5:`Couldn't parse the color string. Please provide the color as a string in hex, rgb, rgba, hsl or hsla notation.\n\n`,6:`Passed invalid arguments to rgb, please pass multiple numbers e.g. rgb(255, 205, 100) or an object e.g. rgb({ red: 255, green: 205, blue: 100 }).\n\n`,7:`Passed invalid arguments to rgba, please pass multiple numbers e.g. rgb(255, 205, 100, 0.75) or an object e.g. rgb({ red: 255, green: 205, blue: 100, alpha: 0.75 }).\n\n`,8:`Passed invalid argument to toColorString, please pass a RgbColor, RgbaColor, HslColor or HslaColor object.\n\n`,9:`Please provide a number of steps to the modularScale helper.\n\n`,10:`Please pass a number or one of the predefined scales to the modularScale helper as the ratio.\n\n`,11:`Invalid value passed as base to modularScale, expected number or em string but got \"%s\"\n\n`,12:`Expected a string ending in \"px\" or a number passed as the first argument to %s(), got \"%s\" instead.\n\n`,13:`Expected a string ending in \"px\" or a number passed as the second argument to %s(), got \"%s\" instead.\n\n`,14:`Passed invalid pixel value (\"%s\") to %s(), please pass a value like \"12px\" or 12.\n\n`,15:`Passed invalid base value (\"%s\") to %s(), please pass a value like \"12px\" or 12.\n\n`,16:`You must provide a template to this method.\n\n`,17:`You passed an unsupported selector state to this method.\n\n`,18:`minScreen and maxScreen must be provided as stringified numbers with the same units.\n\n`,19:`fromSize and toSize must be provided as stringified numbers with the same units.\n\n`,20:`expects either an array of objects or a single object with the properties prop, fromSize, and toSize.\n\n`,21:\"expects the objects in the first argument array to have the properties `prop`, `fromSize`, and `toSize`.\\n\\n\",22:\"expects the first argument object to have the properties `prop`, `fromSize`, and `toSize`.\\n\\n\",23:`fontFace expects a name of a font-family.\n\n`,24:`fontFace expects either the path to the font file(s) or a name of a local copy.\n\n`,25:`fontFace expects localFonts to be an array.\n\n`,26:`fontFace expects fileFormats to be an array.\n\n`,27:`radialGradient requries at least 2 color-stops to properly render.\n\n`,28:`Please supply a filename to retinaImage() as the first argument.\n\n`,29:`Passed invalid argument to triangle, please pass correct pointingDirection e.g. 'right'.\n\n`,30:\"Passed an invalid value to `height` or `width`. Please provide a pixel based unit.\\n\\n\",31:`The animation shorthand only takes 8 arguments. See the specification for more information: http://mdn.io/animation\n\n`,32:`To pass multiple animations please supply them in arrays, e.g. animation(['rotate', '2s'], ['move', '1s'])\nTo pass a single animation please supply them in simple values, e.g. animation('rotate', '2s')\n\n`,33:`The animation shorthand arrays can only have 8 elements. See the specification for more information: http://mdn.io/animation\n\n`,34:`borderRadius expects a radius value as a string or number as the second argument.\n\n`,35:`borderRadius expects one of \"top\", \"bottom\", \"left\" or \"right\" as the first argument.\n\n`,36:`Property must be a string value.\n\n`,37:`Syntax Error at %s.\n\n`,38:`Formula contains a function that needs parentheses at %s.\n\n`,39:`Formula is missing closing parenthesis at %s.\n\n`,40:`Formula has too many closing parentheses at %s.\n\n`,41:`All values in a formula must have the same unit or be unitless.\n\n`,42:`Please provide a number of steps to the modularScale helper.\n\n`,43:`Please pass a number or one of the predefined scales to the modularScale helper as the ratio.\n\n`,44:`Invalid value passed as base to modularScale, expected number or em/rem string but got %s.\n\n`,45:`Passed invalid argument to hslToColorString, please pass a HslColor or HslaColor object.\n\n`,46:`Passed invalid argument to rgbToColorString, please pass a RgbColor or RgbaColor object.\n\n`,47:`minScreen and maxScreen must be provided as stringified numbers with the same units.\n\n`,48:`fromSize and toSize must be provided as stringified numbers with the same units.\n\n`,49:`Expects either an array of objects or a single object with the properties prop, fromSize, and toSize.\n\n`,50:`Expects the objects in the first argument array to have the properties prop, fromSize, and toSize.\n\n`,51:`Expects the first argument object to have the properties prop, fromSize, and toSize.\n\n`,52:`fontFace expects either the path to the font file(s) or a name of a local copy.\n\n`,53:`fontFace expects localFonts to be an array.\n\n`,54:`fontFace expects fileFormats to be an array.\n\n`,55:`fontFace expects a name of a font-family.\n\n`,56:`linearGradient requries at least 2 color-stops to properly render.\n\n`,57:`radialGradient requries at least 2 color-stops to properly render.\n\n`,58:`Please supply a filename to retinaImage() as the first argument.\n\n`,59:`Passed invalid argument to triangle, please pass correct pointingDirection e.g. 'right'.\n\n`,60:\"Passed an invalid value to `height` or `width`. Please provide a pixel based unit.\\n\\n\",61:`Property must be a string value.\n\n`,62:`borderRadius expects a radius value as a string or number as the second argument.\n\n`,63:`borderRadius expects one of \"top\", \"bottom\", \"left\" or \"right\" as the first argument.\n\n`,64:`The animation shorthand only takes 8 arguments. See the specification for more information: http://mdn.io/animation.\n\n`,65:`To pass multiple animations please supply them in arrays, e.g. animation(['rotate', '2s'], ['move', '1s'])\\\\nTo pass a single animation please supply them in simple values, e.g. animation('rotate', '2s').\n\n`,66:`The animation shorthand arrays can only have 8 elements. See the specification for more information: http://mdn.io/animation.\n\n`,67:`You must provide a template to this method.\n\n`,68:`You passed an unsupported selector state to this method.\n\n`,69:`Expected a string ending in \"px\" or a number passed as the first argument to %s(), got %s instead.\n\n`,70:`Expected a string ending in \"px\" or a number passed as the second argument to %s(), got %s instead.\n\n`,71:`Passed invalid pixel value %s to %s(), please pass a value like \"12px\" or 12.\n\n`,72:`Passed invalid base value %s to %s(), please pass a value like \"12px\" or 12.\n\n`,73:`Please provide a valid CSS variable.\n\n`,74:`CSS variable not found and no default was provided.\n\n`,75:`important requires a valid style object, got a %s instead.\n\n`,76:`fromSize and toSize must be provided as stringified numbers with the same units as minScreen and maxScreen.\n\n`,77:`remToPx expects a value in \"rem\" but you provided it in \"%s\".\n\n`,78:`base must be set in \"px\" or \"%\" but you set it in \"%s\".\n`};function format(){for(var _len=arguments.length,args=new Array(_len),_key=0;_key<_len;_key++)args[_key]=arguments[_key];var a=args[0],b=[],c;for(c=1;c<args.length;c+=1)b.push(args[c]);return b.forEach(function(d){a=a.replace(/%[a-z]/,d);}),a}var PolishedError=function(_Error){_inheritsLoose(PolishedError2,_Error);function PolishedError2(code){for(var _this,_len2=arguments.length,args=new Array(_len2>1?_len2-1:0),_key2=1;_key2<_len2;_key2++)args[_key2-1]=arguments[_key2];return _this=_Error.call(this,format.apply(void 0,[ERRORS[code]].concat(args)))||this,_assertThisInitialized(_this)}return PolishedError2}(_wrapNativeSuper(Error));function colorToInt(color2){return Math.round(color2*255)}function convertToInt(red,green,blue){return colorToInt(red)+\",\"+colorToInt(green)+\",\"+colorToInt(blue)}function hslToRgb(hue,saturation,lightness,convert){if(convert===void 0&&(convert=convertToInt),saturation===0)return convert(lightness,lightness,lightness);var huePrime=(hue%360+360)%360/60,chroma=(1-Math.abs(2*lightness-1))*saturation,secondComponent=chroma*(1-Math.abs(huePrime%2-1)),red=0,green=0,blue=0;huePrime>=0&&huePrime<1?(red=chroma,green=secondComponent):huePrime>=1&&huePrime<2?(red=secondComponent,green=chroma):huePrime>=2&&huePrime<3?(green=chroma,blue=secondComponent):huePrime>=3&&huePrime<4?(green=secondComponent,blue=chroma):huePrime>=4&&huePrime<5?(red=secondComponent,blue=chroma):huePrime>=5&&huePrime<6&&(red=chroma,blue=secondComponent);var lightnessModification=lightness-chroma/2,finalRed=red+lightnessModification,finalGreen=green+lightnessModification,finalBlue=blue+lightnessModification;return convert(finalRed,finalGreen,finalBlue)}var namedColorMap={aliceblue:\"f0f8ff\",antiquewhite:\"faebd7\",aqua:\"00ffff\",aquamarine:\"7fffd4\",azure:\"f0ffff\",beige:\"f5f5dc\",bisque:\"ffe4c4\",black:\"000\",blanchedalmond:\"ffebcd\",blue:\"0000ff\",blueviolet:\"8a2be2\",brown:\"a52a2a\",burlywood:\"deb887\",cadetblue:\"5f9ea0\",chartreuse:\"7fff00\",chocolate:\"d2691e\",coral:\"ff7f50\",cornflowerblue:\"6495ed\",cornsilk:\"fff8dc\",crimson:\"dc143c\",cyan:\"00ffff\",darkblue:\"00008b\",darkcyan:\"008b8b\",darkgoldenrod:\"b8860b\",darkgray:\"a9a9a9\",darkgreen:\"006400\",darkgrey:\"a9a9a9\",darkkhaki:\"bdb76b\",darkmagenta:\"8b008b\",darkolivegreen:\"556b2f\",darkorange:\"ff8c00\",darkorchid:\"9932cc\",darkred:\"8b0000\",darksalmon:\"e9967a\",darkseagreen:\"8fbc8f\",darkslateblue:\"483d8b\",darkslategray:\"2f4f4f\",darkslategrey:\"2f4f4f\",darkturquoise:\"00ced1\",darkviolet:\"9400d3\",deeppink:\"ff1493\",deepskyblue:\"00bfff\",dimgray:\"696969\",dimgrey:\"696969\",dodgerblue:\"1e90ff\",firebrick:\"b22222\",floralwhite:\"fffaf0\",forestgreen:\"228b22\",fuchsia:\"ff00ff\",gainsboro:\"dcdcdc\",ghostwhite:\"f8f8ff\",gold:\"ffd700\",goldenrod:\"daa520\",gray:\"808080\",green:\"008000\",greenyellow:\"adff2f\",grey:\"808080\",honeydew:\"f0fff0\",hotpink:\"ff69b4\",indianred:\"cd5c5c\",indigo:\"4b0082\",ivory:\"fffff0\",khaki:\"f0e68c\",lavender:\"e6e6fa\",lavenderblush:\"fff0f5\",lawngreen:\"7cfc00\",lemonchiffon:\"fffacd\",lightblue:\"add8e6\",lightcoral:\"f08080\",lightcyan:\"e0ffff\",lightgoldenrodyellow:\"fafad2\",lightgray:\"d3d3d3\",lightgreen:\"90ee90\",lightgrey:\"d3d3d3\",lightpink:\"ffb6c1\",lightsalmon:\"ffa07a\",lightseagreen:\"20b2aa\",lightskyblue:\"87cefa\",lightslategray:\"789\",lightslategrey:\"789\",lightsteelblue:\"b0c4de\",lightyellow:\"ffffe0\",lime:\"0f0\",limegreen:\"32cd32\",linen:\"faf0e6\",magenta:\"f0f\",maroon:\"800000\",mediumaquamarine:\"66cdaa\",mediumblue:\"0000cd\",mediumorchid:\"ba55d3\",mediumpurple:\"9370db\",mediumseagreen:\"3cb371\",mediumslateblue:\"7b68ee\",mediumspringgreen:\"00fa9a\",mediumturquoise:\"48d1cc\",mediumvioletred:\"c71585\",midnightblue:\"191970\",mintcream:\"f5fffa\",mistyrose:\"ffe4e1\",moccasin:\"ffe4b5\",navajowhite:\"ffdead\",navy:\"000080\",oldlace:\"fdf5e6\",olive:\"808000\",olivedrab:\"6b8e23\",orange:\"ffa500\",orangered:\"ff4500\",orchid:\"da70d6\",palegoldenrod:\"eee8aa\",palegreen:\"98fb98\",paleturquoise:\"afeeee\",palevioletred:\"db7093\",papayawhip:\"ffefd5\",peachpuff:\"ffdab9\",peru:\"cd853f\",pink:\"ffc0cb\",plum:\"dda0dd\",powderblue:\"b0e0e6\",purple:\"800080\",rebeccapurple:\"639\",red:\"f00\",rosybrown:\"bc8f8f\",royalblue:\"4169e1\",saddlebrown:\"8b4513\",salmon:\"fa8072\",sandybrown:\"f4a460\",seagreen:\"2e8b57\",seashell:\"fff5ee\",sienna:\"a0522d\",silver:\"c0c0c0\",skyblue:\"87ceeb\",slateblue:\"6a5acd\",slategray:\"708090\",slategrey:\"708090\",snow:\"fffafa\",springgreen:\"00ff7f\",steelblue:\"4682b4\",tan:\"d2b48c\",teal:\"008080\",thistle:\"d8bfd8\",tomato:\"ff6347\",turquoise:\"40e0d0\",violet:\"ee82ee\",wheat:\"f5deb3\",white:\"fff\",whitesmoke:\"f5f5f5\",yellow:\"ff0\",yellowgreen:\"9acd32\"};function nameToHex(color2){if(typeof color2!=\"string\")return color2;var normalizedColorName=color2.toLowerCase();return namedColorMap[normalizedColorName]?\"#\"+namedColorMap[normalizedColorName]:color2}var hexRegex=/^#[a-fA-F0-9]{6}$/,hexRgbaRegex=/^#[a-fA-F0-9]{8}$/,reducedHexRegex=/^#[a-fA-F0-9]{3}$/,reducedRgbaHexRegex=/^#[a-fA-F0-9]{4}$/,rgbRegex=/^rgb\\(\\s*(\\d{1,3})\\s*(?:,)?\\s*(\\d{1,3})\\s*(?:,)?\\s*(\\d{1,3})\\s*\\)$/i,rgbaRegex=/^rgb(?:a)?\\(\\s*(\\d{1,3})\\s*(?:,)?\\s*(\\d{1,3})\\s*(?:,)?\\s*(\\d{1,3})\\s*(?:,|\\/)\\s*([-+]?\\d*[.]?\\d+[%]?)\\s*\\)$/i,hslRegex=/^hsl\\(\\s*(\\d{0,3}[.]?[0-9]+(?:deg)?)\\s*(?:,)?\\s*(\\d{1,3}[.]?[0-9]?)%\\s*(?:,)?\\s*(\\d{1,3}[.]?[0-9]?)%\\s*\\)$/i,hslaRegex=/^hsl(?:a)?\\(\\s*(\\d{0,3}[.]?[0-9]+(?:deg)?)\\s*(?:,)?\\s*(\\d{1,3}[.]?[0-9]?)%\\s*(?:,)?\\s*(\\d{1,3}[.]?[0-9]?)%\\s*(?:,|\\/)\\s*([-+]?\\d*[.]?\\d+[%]?)\\s*\\)$/i;function parseToRgb(color2){if(typeof color2!=\"string\")throw new PolishedError(3);var normalizedColor=nameToHex(color2);if(normalizedColor.match(hexRegex))return {red:parseInt(\"\"+normalizedColor[1]+normalizedColor[2],16),green:parseInt(\"\"+normalizedColor[3]+normalizedColor[4],16),blue:parseInt(\"\"+normalizedColor[5]+normalizedColor[6],16)};if(normalizedColor.match(hexRgbaRegex)){var alpha=parseFloat((parseInt(\"\"+normalizedColor[7]+normalizedColor[8],16)/255).toFixed(2));return {red:parseInt(\"\"+normalizedColor[1]+normalizedColor[2],16),green:parseInt(\"\"+normalizedColor[3]+normalizedColor[4],16),blue:parseInt(\"\"+normalizedColor[5]+normalizedColor[6],16),alpha}}if(normalizedColor.match(reducedHexRegex))return {red:parseInt(\"\"+normalizedColor[1]+normalizedColor[1],16),green:parseInt(\"\"+normalizedColor[2]+normalizedColor[2],16),blue:parseInt(\"\"+normalizedColor[3]+normalizedColor[3],16)};if(normalizedColor.match(reducedRgbaHexRegex)){var _alpha=parseFloat((parseInt(\"\"+normalizedColor[4]+normalizedColor[4],16)/255).toFixed(2));return {red:parseInt(\"\"+normalizedColor[1]+normalizedColor[1],16),green:parseInt(\"\"+normalizedColor[2]+normalizedColor[2],16),blue:parseInt(\"\"+normalizedColor[3]+normalizedColor[3],16),alpha:_alpha}}var rgbMatched=rgbRegex.exec(normalizedColor);if(rgbMatched)return {red:parseInt(\"\"+rgbMatched[1],10),green:parseInt(\"\"+rgbMatched[2],10),blue:parseInt(\"\"+rgbMatched[3],10)};var rgbaMatched=rgbaRegex.exec(normalizedColor.substring(0,50));if(rgbaMatched)return {red:parseInt(\"\"+rgbaMatched[1],10),green:parseInt(\"\"+rgbaMatched[2],10),blue:parseInt(\"\"+rgbaMatched[3],10),alpha:parseFloat(\"\"+rgbaMatched[4])>1?parseFloat(\"\"+rgbaMatched[4])/100:parseFloat(\"\"+rgbaMatched[4])};var hslMatched=hslRegex.exec(normalizedColor);if(hslMatched){var hue=parseInt(\"\"+hslMatched[1],10),saturation=parseInt(\"\"+hslMatched[2],10)/100,lightness=parseInt(\"\"+hslMatched[3],10)/100,rgbColorString=\"rgb(\"+hslToRgb(hue,saturation,lightness)+\")\",hslRgbMatched=rgbRegex.exec(rgbColorString);if(!hslRgbMatched)throw new PolishedError(4,normalizedColor,rgbColorString);return {red:parseInt(\"\"+hslRgbMatched[1],10),green:parseInt(\"\"+hslRgbMatched[2],10),blue:parseInt(\"\"+hslRgbMatched[3],10)}}var hslaMatched=hslaRegex.exec(normalizedColor.substring(0,50));if(hslaMatched){var _hue=parseInt(\"\"+hslaMatched[1],10),_saturation=parseInt(\"\"+hslaMatched[2],10)/100,_lightness=parseInt(\"\"+hslaMatched[3],10)/100,_rgbColorString=\"rgb(\"+hslToRgb(_hue,_saturation,_lightness)+\")\",_hslRgbMatched=rgbRegex.exec(_rgbColorString);if(!_hslRgbMatched)throw new PolishedError(4,normalizedColor,_rgbColorString);return {red:parseInt(\"\"+_hslRgbMatched[1],10),green:parseInt(\"\"+_hslRgbMatched[2],10),blue:parseInt(\"\"+_hslRgbMatched[3],10),alpha:parseFloat(\"\"+hslaMatched[4])>1?parseFloat(\"\"+hslaMatched[4])/100:parseFloat(\"\"+hslaMatched[4])}}throw new PolishedError(5)}function rgbToHsl(color2){var red=color2.red/255,green=color2.green/255,blue=color2.blue/255,max=Math.max(red,green,blue),min=Math.min(red,green,blue),lightness=(max+min)/2;if(max===min)return color2.alpha!==void 0?{hue:0,saturation:0,lightness,alpha:color2.alpha}:{hue:0,saturation:0,lightness};var hue,delta=max-min,saturation=lightness>.5?delta/(2-max-min):delta/(max+min);switch(max){case red:hue=(green-blue)/delta+(green<blue?6:0);break;case green:hue=(blue-red)/delta+2;break;default:hue=(red-green)/delta+4;break}return hue*=60,color2.alpha!==void 0?{hue,saturation,lightness,alpha:color2.alpha}:{hue,saturation,lightness}}function parseToHsl(color2){return rgbToHsl(parseToRgb(color2))}var reduceHexValue=function(value){return value.length===7&&value[1]===value[2]&&value[3]===value[4]&&value[5]===value[6]?\"#\"+value[1]+value[3]+value[5]:value},reduceHexValue$1=reduceHexValue;function numberToHex(value){var hex=value.toString(16);return hex.length===1?\"0\"+hex:hex}function colorToHex(color2){return numberToHex(Math.round(color2*255))}function convertToHex(red,green,blue){return reduceHexValue$1(\"#\"+colorToHex(red)+colorToHex(green)+colorToHex(blue))}function hslToHex(hue,saturation,lightness){return hslToRgb(hue,saturation,lightness,convertToHex)}function hsl(value,saturation,lightness){if(typeof value==\"number\"&&typeof saturation==\"number\"&&typeof lightness==\"number\")return hslToHex(value,saturation,lightness);if(typeof value==\"object\"&&saturation===void 0&&lightness===void 0)return hslToHex(value.hue,value.saturation,value.lightness);throw new PolishedError(1)}function hsla(value,saturation,lightness,alpha){if(typeof value==\"number\"&&typeof saturation==\"number\"&&typeof lightness==\"number\"&&typeof alpha==\"number\")return alpha>=1?hslToHex(value,saturation,lightness):\"rgba(\"+hslToRgb(value,saturation,lightness)+\",\"+alpha+\")\";if(typeof value==\"object\"&&saturation===void 0&&lightness===void 0&&alpha===void 0)return value.alpha>=1?hslToHex(value.hue,value.saturation,value.lightness):\"rgba(\"+hslToRgb(value.hue,value.saturation,value.lightness)+\",\"+value.alpha+\")\";throw new PolishedError(2)}function rgb(value,green,blue){if(typeof value==\"number\"&&typeof green==\"number\"&&typeof blue==\"number\")return reduceHexValue$1(\"#\"+numberToHex(value)+numberToHex(green)+numberToHex(blue));if(typeof value==\"object\"&&green===void 0&&blue===void 0)return reduceHexValue$1(\"#\"+numberToHex(value.red)+numberToHex(value.green)+numberToHex(value.blue));throw new PolishedError(6)}function rgba(firstValue,secondValue,thirdValue,fourthValue){if(typeof firstValue==\"string\"&&typeof secondValue==\"number\"){var rgbValue=parseToRgb(firstValue);return \"rgba(\"+rgbValue.red+\",\"+rgbValue.green+\",\"+rgbValue.blue+\",\"+secondValue+\")\"}else {if(typeof firstValue==\"number\"&&typeof secondValue==\"number\"&&typeof thirdValue==\"number\"&&typeof fourthValue==\"number\")return fourthValue>=1?rgb(firstValue,secondValue,thirdValue):\"rgba(\"+firstValue+\",\"+secondValue+\",\"+thirdValue+\",\"+fourthValue+\")\";if(typeof firstValue==\"object\"&&secondValue===void 0&&thirdValue===void 0&&fourthValue===void 0)return firstValue.alpha>=1?rgb(firstValue.red,firstValue.green,firstValue.blue):\"rgba(\"+firstValue.red+\",\"+firstValue.green+\",\"+firstValue.blue+\",\"+firstValue.alpha+\")\"}throw new PolishedError(7)}var isRgb=function(color2){return typeof color2.red==\"number\"&&typeof color2.green==\"number\"&&typeof color2.blue==\"number\"&&(typeof color2.alpha!=\"number\"||typeof color2.alpha>\"u\")},isRgba=function(color2){return typeof color2.red==\"number\"&&typeof color2.green==\"number\"&&typeof color2.blue==\"number\"&&typeof color2.alpha==\"number\"},isHsl=function(color2){return typeof color2.hue==\"number\"&&typeof color2.saturation==\"number\"&&typeof color2.lightness==\"number\"&&(typeof color2.alpha!=\"number\"||typeof color2.alpha>\"u\")},isHsla=function(color2){return typeof color2.hue==\"number\"&&typeof color2.saturation==\"number\"&&typeof color2.lightness==\"number\"&&typeof color2.alpha==\"number\"};function toColorString(color2){if(typeof color2!=\"object\")throw new PolishedError(8);if(isRgba(color2))return rgba(color2);if(isRgb(color2))return rgb(color2);if(isHsla(color2))return hsla(color2);if(isHsl(color2))return hsl(color2);throw new PolishedError(8)}function curried(f,length,acc){return function(){var combined=acc.concat(Array.prototype.slice.call(arguments));return combined.length>=length?f.apply(this,combined):curried(f,length,combined)}}function curry(f){return curried(f,f.length,[])}function guard(lowerBoundary,upperBoundary,value){return Math.max(lowerBoundary,Math.min(upperBoundary,value))}function darken(amount,color2){if(color2===\"transparent\")return color2;var hslColor=parseToHsl(color2);return toColorString(_extends({},hslColor,{lightness:guard(0,1,hslColor.lightness-parseFloat(amount))}))}var curriedDarken=curry(darken),curriedDarken$1=curriedDarken;function lighten(amount,color2){if(color2===\"transparent\")return color2;var hslColor=parseToHsl(color2);return toColorString(_extends({},hslColor,{lightness:guard(0,1,hslColor.lightness+parseFloat(amount))}))}var curriedLighten=curry(lighten),curriedLighten$1=curriedLighten;function transparentize(amount,color2){if(color2===\"transparent\")return color2;var parsedColor=parseToRgb(color2),alpha=typeof parsedColor.alpha==\"number\"?parsedColor.alpha:1,colorWithAlpha=_extends({},parsedColor,{alpha:guard(0,1,+(alpha*100-parseFloat(amount)*100).toFixed(2)/100)});return rgba(colorWithAlpha)}var curriedTransparentize=curry(transparentize),curriedTransparentize$1=curriedTransparentize;var headerCommon=({theme})=>({margin:\"20px 0 8px\",padding:0,cursor:\"text\",position:\"relative\",color:theme.color.defaultText,\"&:first-of-type\":{marginTop:0,paddingTop:0},\"&:hover a.anchor\":{textDecoration:\"none\"},\"& tt, & code\":{fontSize:\"inherit\"}}),codeCommon=({theme})=>({lineHeight:1,margin:\"0 2px\",padding:\"3px 5px\",whiteSpace:\"nowrap\",borderRadius:3,fontSize:theme.typography.size.s2-1,border:theme.base===\"light\"?`1px solid ${theme.color.mediumlight}`:`1px solid ${theme.color.darker}`,color:theme.base===\"light\"?curriedTransparentize$1(.1,theme.color.defaultText):curriedTransparentize$1(.3,theme.color.defaultText),backgroundColor:theme.base===\"light\"?theme.color.lighter:theme.color.border}),withReset=({theme})=>({fontFamily:theme.typography.fonts.base,fontSize:theme.typography.size.s3,margin:0,WebkitFontSmoothing:\"antialiased\",MozOsxFontSmoothing:\"grayscale\",WebkitTapHighlightColor:\"rgba(0, 0, 0, 0)\",WebkitOverflowScrolling:\"touch\"}),withMargin={margin:\"16px 0\"};var Link=({href:input,...props})=>{let href=/^\\//.test(input)?`./?path=${input}`:input,target=/^#.*/.test(input)?\"_self\":\"_top\";return React3__default.createElement(\"a\",{href,target,...props})};var A=styled(Link)(withReset,({theme})=>({fontSize:\"inherit\",lineHeight:\"24px\",color:theme.color.secondary,textDecoration:\"none\",\"&.absent\":{color:\"#cc0000\"},\"&.anchor\":{display:\"block\",paddingLeft:30,marginLeft:-30,cursor:\"pointer\",position:\"absolute\",top:0,left:0,bottom:0}}));var Blockquote=styled.blockquote(withReset,withMargin,({theme})=>({borderLeft:`4px solid ${theme.color.medium}`,padding:\"0 15px\",color:theme.color.dark,\"& > :first-of-type\":{marginTop:0},\"& > :last-child\":{marginBottom:0}}));var isReactChildString=child=>typeof child==\"string\";var isInlineCodeRegex=/[\\n\\r]/g,DefaultCodeBlock=styled.code(({theme})=>({fontFamily:theme.typography.fonts.mono,WebkitFontSmoothing:\"antialiased\",MozOsxFontSmoothing:\"grayscale\",display:\"inline-block\",paddingLeft:2,paddingRight:2,verticalAlign:\"baseline\",color:\"inherit\"}),codeCommon),StyledSyntaxHighlighter=styled(SyntaxHighlighter)(({theme})=>({fontFamily:theme.typography.fonts.mono,fontSize:`${theme.typography.size.s2-1}px`,lineHeight:\"19px\",margin:\"25px 0 40px\",borderRadius:theme.appBorderRadius,boxShadow:theme.base===\"light\"?\"rgba(0, 0, 0, 0.10) 0 1px 3px 0\":\"rgba(0, 0, 0, 0.20) 0 2px 5px 0\",\"pre.prismjs\":{padding:20,background:\"inherit\"}})),Code=({className,children,...props})=>{let language=(className||\"\").match(/lang-(\\S+)/),childrenArray=Children.toArray(children);return childrenArray.filter(isReactChildString).some(child=>child.match(isInlineCodeRegex))?React3__default.createElement(StyledSyntaxHighlighter,{bordered:!0,copyable:!0,language:language?.[1]??\"text\",format:!1,...props},children):React3__default.createElement(DefaultCodeBlock,{...props,className},childrenArray)};var Div=styled.div(withReset);var DL=styled.dl(withReset,withMargin,{padding:0,\"& dt\":{fontSize:\"14px\",fontWeight:\"bold\",fontStyle:\"italic\",padding:0,margin:\"16px 0 4px\"},\"& dt:first-of-type\":{padding:0},\"& dt > :first-of-type\":{marginTop:0},\"& dt > :last-child\":{marginBottom:0},\"& dd\":{margin:\"0 0 16px\",padding:\"0 15px\"},\"& dd > :first-of-type\":{marginTop:0},\"& dd > :last-child\":{marginBottom:0}});var H1=styled.h1(withReset,headerCommon,({theme})=>({fontSize:`${theme.typography.size.l1}px`,fontWeight:theme.typography.weight.bold}));var H2=styled.h2(withReset,headerCommon,({theme})=>({fontSize:`${theme.typography.size.m2}px`,paddingBottom:4,borderBottom:`1px solid ${theme.appBorderColor}`}));var H3=styled.h3(withReset,headerCommon,({theme})=>({fontSize:`${theme.typography.size.m1}px`}));var H4=styled.h4(withReset,headerCommon,({theme})=>({fontSize:`${theme.typography.size.s3}px`}));var H5=styled.h5(withReset,headerCommon,({theme})=>({fontSize:`${theme.typography.size.s2}px`}));var H6=styled.h6(withReset,headerCommon,({theme})=>({fontSize:`${theme.typography.size.s2}px`,color:theme.color.dark}));var HR=styled.hr(({theme})=>({border:\"0 none\",borderTop:`1px solid ${theme.appBorderColor}`,height:4,padding:0}));var Img=styled.img({maxWidth:\"100%\"});var LI=styled.li(withReset,({theme})=>({fontSize:theme.typography.size.s2,color:theme.color.defaultText,lineHeight:\"24px\",\"& + li\":{marginTop:\".25em\"},\"& ul, & ol\":{marginTop:\".25em\",marginBottom:0},\"& code\":codeCommon({theme})}));var listCommon={paddingLeft:30,\"& :first-of-type\":{marginTop:0},\"& :last-child\":{marginBottom:0}},OL=styled.ol(withReset,withMargin,listCommon,{listStyle:\"decimal\"});var P=styled.p(withReset,withMargin,({theme})=>({fontSize:theme.typography.size.s2,lineHeight:\"24px\",color:theme.color.defaultText,\"& code\":codeCommon({theme})}));var Pre=styled.pre(withReset,withMargin,({theme})=>({fontFamily:theme.typography.fonts.mono,WebkitFontSmoothing:\"antialiased\",MozOsxFontSmoothing:\"grayscale\",lineHeight:\"18px\",padding:\"11px 1rem\",whiteSpace:\"pre-wrap\",color:\"inherit\",borderRadius:3,margin:\"1rem 0\",\"&:not(.prismjs)\":{background:\"transparent\",border:\"none\",borderRadius:0,padding:0,margin:0},\"& pre, &.prismjs\":{padding:15,margin:0,whiteSpace:\"pre-wrap\",color:\"inherit\",fontSize:\"13px\",lineHeight:\"19px\",code:{color:\"inherit\",fontSize:\"inherit\"}},\"& code\":{whiteSpace:\"pre\"},\"& code, & tt\":{border:\"none\"}}));var Span=styled.span(withReset,({theme})=>({\"&.frame\":{display:\"block\",overflow:\"hidden\",\"& > span\":{border:`1px solid ${theme.color.medium}`,display:\"block\",float:\"left\",overflow:\"hidden\",margin:\"13px 0 0\",padding:7,width:\"auto\"},\"& span img\":{display:\"block\",float:\"left\"},\"& span span\":{clear:\"both\",color:theme.color.darkest,display:\"block\",padding:\"5px 0 0\"}},\"&.align-center\":{display:\"block\",overflow:\"hidden\",clear:\"both\",\"& > span\":{display:\"block\",overflow:\"hidden\",margin:\"13px auto 0\",textAlign:\"center\"},\"& span img\":{margin:\"0 auto\",textAlign:\"center\"}},\"&.align-right\":{display:\"block\",overflow:\"hidden\",clear:\"both\",\"& > span\":{display:\"block\",overflow:\"hidden\",margin:\"13px 0 0\",textAlign:\"right\"},\"& span img\":{margin:0,textAlign:\"right\"}},\"&.float-left\":{display:\"block\",marginRight:13,overflow:\"hidden\",float:\"left\",\"& span\":{margin:\"13px 0 0\"}},\"&.float-right\":{display:\"block\",marginLeft:13,overflow:\"hidden\",float:\"right\",\"& > span\":{display:\"block\",overflow:\"hidden\",margin:\"13px auto 0\",textAlign:\"right\"}}}));var Table=styled.table(withReset,withMargin,({theme})=>({fontSize:theme.typography.size.s2,lineHeight:\"24px\",padding:0,borderCollapse:\"collapse\",\"& tr\":{borderTop:`1px solid ${theme.appBorderColor}`,backgroundColor:theme.appContentBg,margin:0,padding:0},\"& tr:nth-of-type(2n)\":{backgroundColor:theme.base===\"dark\"?theme.color.darker:theme.color.lighter},\"& tr th\":{fontWeight:\"bold\",color:theme.color.defaultText,border:`1px solid ${theme.appBorderColor}`,margin:0,padding:\"6px 13px\"},\"& tr td\":{border:`1px solid ${theme.appBorderColor}`,color:theme.color.defaultText,margin:0,padding:\"6px 13px\"},\"& tr th :first-of-type, & tr td :first-of-type\":{marginTop:0},\"& tr th :last-child, & tr td :last-child\":{marginBottom:0}}));var TT=styled.title(codeCommon);var listCommon2={paddingLeft:30,\"& :first-of-type\":{marginTop:0},\"& :last-child\":{marginBottom:0}},UL=styled.ul(withReset,withMargin,listCommon2,{listStyle:\"disc\"});var ResetWrapper=styled.div(withReset);var components={h1:props=>React3__default.createElement(H1,{...nameSpaceClassNames(props,\"h1\")}),h2:props=>React3__default.createElement(H2,{...nameSpaceClassNames(props,\"h2\")}),h3:props=>React3__default.createElement(H3,{...nameSpaceClassNames(props,\"h3\")}),h4:props=>React3__default.createElement(H4,{...nameSpaceClassNames(props,\"h4\")}),h5:props=>React3__default.createElement(H5,{...nameSpaceClassNames(props,\"h5\")}),h6:props=>React3__default.createElement(H6,{...nameSpaceClassNames(props,\"h6\")}),pre:props=>React3__default.createElement(Pre,{...nameSpaceClassNames(props,\"pre\")}),a:props=>React3__default.createElement(A,{...nameSpaceClassNames(props,\"a\")}),hr:props=>React3__default.createElement(HR,{...nameSpaceClassNames(props,\"hr\")}),dl:props=>React3__default.createElement(DL,{...nameSpaceClassNames(props,\"dl\")}),blockquote:props=>React3__default.createElement(Blockquote,{...nameSpaceClassNames(props,\"blockquote\")}),table:props=>React3__default.createElement(Table,{...nameSpaceClassNames(props,\"table\")}),img:props=>React3__default.createElement(Img,{...nameSpaceClassNames(props,\"img\")}),div:props=>React3__default.createElement(Div,{...nameSpaceClassNames(props,\"div\")}),span:props=>React3__default.createElement(Span,{...nameSpaceClassNames(props,\"span\")}),li:props=>React3__default.createElement(LI,{...nameSpaceClassNames(props,\"li\")}),ul:props=>React3__default.createElement(UL,{...nameSpaceClassNames(props,\"ul\")}),ol:props=>React3__default.createElement(OL,{...nameSpaceClassNames(props,\"ol\")}),p:props=>React3__default.createElement(P,{...nameSpaceClassNames(props,\"p\")}),code:props=>React3__default.createElement(Code,{...nameSpaceClassNames(props,\"code\")}),tt:props=>React3__default.createElement(TT,{...nameSpaceClassNames(props,\"tt\")}),resetwrapper:props=>React3__default.createElement(ResetWrapper,{...nameSpaceClassNames(props,\"resetwrapper\")})};var BadgeWrapper=styled.div(({theme})=>({display:\"inline-block\",fontSize:11,lineHeight:\"12px\",alignSelf:\"center\",padding:\"4px 12px\",borderRadius:\"3em\",fontWeight:theme.typography.weight.bold}),{svg:{height:12,width:12,marginRight:4,marginTop:-2,path:{fill:\"currentColor\"}}},({theme,status})=>{switch(status){case\"critical\":return {color:theme.color.critical,background:theme.background.critical};case\"negative\":return {color:theme.color.negativeText,background:theme.background.negative,boxShadow:theme.base===\"light\"?`inset 0 0 0 1px ${curriedTransparentize$1(.9,theme.color.negativeText)}`:\"none\"};case\"warning\":return {color:theme.color.warningText,background:theme.background.warning,boxShadow:theme.base===\"light\"?`inset 0 0 0 1px ${curriedTransparentize$1(.9,theme.color.warningText)}`:\"none\"};case\"neutral\":return {color:theme.color.dark,background:theme.color.mediumlight,boxShadow:theme.base===\"light\"?`inset 0 0 0 1px ${curriedTransparentize$1(.9,theme.color.dark)}`:\"none\"};case\"positive\":return {color:theme.color.positiveText,background:theme.background.positive,boxShadow:theme.base===\"light\"?`inset 0 0 0 1px ${curriedTransparentize$1(.9,theme.color.positiveText)}`:\"none\"};default:return {}}}),Badge=({...props})=>React3__default.createElement(BadgeWrapper,{...props});var LEFT_BUTTON=0,isPlainLeftClick=e=>e.button===LEFT_BUTTON&&!e.altKey&&!e.ctrlKey&&!e.metaKey&&!e.shiftKey,cancelled=(e,cb)=>{isPlainLeftClick(e)&&(e.preventDefault(),cb(e));},LinkInner=styled.span(({withArrow})=>withArrow?{\"> svg:last-of-type\":{height:\"0.7em\",width:\"0.7em\",marginRight:0,marginLeft:\"0.25em\",bottom:\"auto\",verticalAlign:\"inherit\"}}:{},({containsIcon})=>containsIcon?{svg:{height:\"1em\",width:\"1em\",verticalAlign:\"middle\",position:\"relative\",bottom:0,marginRight:0}}:{}),A2=styled.a(({theme})=>({display:\"inline-block\",transition:\"all 150ms ease-out\",textDecoration:\"none\",color:theme.color.secondary,\"&:hover, &:focus\":{cursor:\"pointer\",color:curriedDarken$1(.07,theme.color.secondary),\"svg path:not([fill])\":{fill:curriedDarken$1(.07,theme.color.secondary)}},\"&:active\":{color:curriedDarken$1(.1,theme.color.secondary),\"svg path:not([fill])\":{fill:curriedDarken$1(.1,theme.color.secondary)}},svg:{display:\"inline-block\",height:\"1em\",width:\"1em\",verticalAlign:\"text-top\",position:\"relative\",bottom:\"-0.125em\",marginRight:\"0.4em\",\"& path\":{fill:theme.color.secondary}}}),({theme,secondary,tertiary})=>{let colors;return secondary&&(colors=[theme.textMutedColor,theme.color.dark,theme.color.darker]),tertiary&&(colors=[theme.color.dark,theme.color.darkest,theme.textMutedColor]),colors?{color:colors[0],\"svg path:not([fill])\":{fill:colors[0]},\"&:hover\":{color:colors[1],\"svg path:not([fill])\":{fill:colors[1]}},\"&:active\":{color:colors[2],\"svg path:not([fill])\":{fill:colors[2]}}}:{}},({nochrome})=>nochrome?{color:\"inherit\",\"&:hover, &:active\":{color:\"inherit\",textDecoration:\"underline\"}}:{},({theme,inverse})=>inverse?{color:theme.color.lightest,\":not([fill])\":{fill:theme.color.lightest},\"&:hover\":{color:theme.color.lighter,\"svg path:not([fill])\":{fill:theme.color.lighter}},\"&:active\":{color:theme.color.light,\"svg path:not([fill])\":{fill:theme.color.light}}}:{},({isButton:isButton2})=>isButton2?{border:0,borderRadius:0,background:\"none\",padding:0,fontSize:\"inherit\"}:{}),Link2=({cancel=!0,children,onClick=void 0,withArrow=!1,containsIcon=!1,className=void 0,style=void 0,...rest})=>React3__default.createElement(A2,{...rest,onClick:onClick&&cancel?e=>cancelled(e,onClick):onClick,className},React3__default.createElement(LinkInner,{withArrow,containsIcon},children,withArrow&&React3__default.createElement(ChevronRightIcon,null)));var DocumentWrapper=styled.div(({theme})=>({fontSize:`${theme.typography.size.s2}px`,lineHeight:\"1.6\",h1:{fontSize:`${theme.typography.size.l1}px`,fontWeight:theme.typography.weight.bold},h2:{fontSize:`${theme.typography.size.m2}px`,borderBottom:`1px solid ${theme.appBorderColor}`},h3:{fontSize:`${theme.typography.size.m1}px`},h4:{fontSize:`${theme.typography.size.s3}px`},h5:{fontSize:`${theme.typography.size.s2}px`},h6:{fontSize:`${theme.typography.size.s2}px`,color:theme.color.dark},\"pre:not(.prismjs)\":{background:\"transparent\",border:\"none\",borderRadius:0,padding:0,margin:0},\"pre pre, pre.prismjs\":{padding:15,margin:0,whiteSpace:\"pre-wrap\",color:\"inherit\",fontSize:\"13px\",lineHeight:\"19px\"},\"pre pre code, pre.prismjs code\":{color:\"inherit\",fontSize:\"inherit\"},\"pre code\":{margin:0,padding:0,whiteSpace:\"pre\",border:\"none\",background:\"transparent\"},\"pre code, pre tt\":{backgroundColor:\"transparent\",border:\"none\"},\"body > *:first-of-type\":{marginTop:\"0 !important\"},\"body > *:last-child\":{marginBottom:\"0 !important\"},a:{color:theme.color.secondary,textDecoration:\"none\"},\"a.absent\":{color:\"#cc0000\"},\"a.anchor\":{display:\"block\",paddingLeft:30,marginLeft:-30,cursor:\"pointer\",position:\"absolute\",top:0,left:0,bottom:0},\"h1, h2, h3, h4, h5, h6\":{margin:\"20px 0 10px\",padding:0,cursor:\"text\",position:\"relative\",\"&:first-of-type\":{marginTop:0,paddingTop:0},\"&:hover a.anchor\":{textDecoration:\"none\"},\"& tt, & code\":{fontSize:\"inherit\"}},\"h1:first-of-type + h2\":{marginTop:0,paddingTop:0},\"p, blockquote, ul, ol, dl, li, table, pre\":{margin:\"15px 0\"},hr:{border:\"0 none\",borderTop:`1px solid ${theme.appBorderColor}`,height:4,padding:0},\"body > h1:first-of-type, body > h2:first-of-type, body > h3:first-of-type, body > h4:first-of-type, body > h5:first-of-type, body > h6:first-of-type\":{marginTop:0,paddingTop:0},\"body > h1:first-of-type + h2\":{marginTop:0,paddingTop:0},\"a:first-of-type h1, a:first-of-type h2, a:first-of-type h3, a:first-of-type h4, a:first-of-type h5, a:first-of-type h6\":{marginTop:0,paddingTop:0},\"h1 p, h2 p, h3 p, h4 p, h5 p, h6 p\":{marginTop:0},\"li p.first\":{display:\"inline-block\"},\"ul, ol\":{paddingLeft:30,\"& :first-of-type\":{marginTop:0},\"& :last-child\":{marginBottom:0}},dl:{padding:0},\"dl dt\":{fontSize:\"14px\",fontWeight:\"bold\",fontStyle:\"italic\",margin:\"0 0 15px\",padding:\"0 15px\",\"&:first-of-type\":{padding:0},\"& > :first-of-type\":{marginTop:0},\"& > :last-child\":{marginBottom:0}},blockquote:{borderLeft:`4px solid ${theme.color.medium}`,padding:\"0 15px\",color:theme.color.dark,\"& > :first-of-type\":{marginTop:0},\"& > :last-child\":{marginBottom:0}},table:{padding:0,borderCollapse:\"collapse\",\"& tr\":{borderTop:`1px solid ${theme.appBorderColor}`,backgroundColor:\"white\",margin:0,padding:0,\"& th\":{fontWeight:\"bold\",border:`1px solid ${theme.appBorderColor}`,textAlign:\"left\",margin:0,padding:\"6px 13px\"},\"& td\":{border:`1px solid ${theme.appBorderColor}`,textAlign:\"left\",margin:0,padding:\"6px 13px\"},\"&:nth-of-type(2n)\":{backgroundColor:theme.color.lighter},\"& th :first-of-type, & td :first-of-type\":{marginTop:0},\"& th :last-child, & td :last-child\":{marginBottom:0}}},img:{maxWidth:\"100%\"},\"span.frame\":{display:\"block\",overflow:\"hidden\",\"& > span\":{border:`1px solid ${theme.color.medium}`,display:\"block\",float:\"left\",overflow:\"hidden\",margin:\"13px 0 0\",padding:7,width:\"auto\"},\"& span img\":{display:\"block\",float:\"left\"},\"& span span\":{clear:\"both\",color:theme.color.darkest,display:\"block\",padding:\"5px 0 0\"}},\"span.align-center\":{display:\"block\",overflow:\"hidden\",clear:\"both\",\"& > span\":{display:\"block\",overflow:\"hidden\",margin:\"13px auto 0\",textAlign:\"center\"},\"& span img\":{margin:\"0 auto\",textAlign:\"center\"}},\"span.align-right\":{display:\"block\",overflow:\"hidden\",clear:\"both\",\"& > span\":{display:\"block\",overflow:\"hidden\",margin:\"13px 0 0\",textAlign:\"right\"},\"& span img\":{margin:0,textAlign:\"right\"}},\"span.float-left\":{display:\"block\",marginRight:13,overflow:\"hidden\",float:\"left\",\"& span\":{margin:\"13px 0 0\"}},\"span.float-right\":{display:\"block\",marginLeft:13,overflow:\"hidden\",float:\"right\",\"& > span\":{display:\"block\",overflow:\"hidden\",margin:\"13px auto 0\",textAlign:\"right\"}},\"code, tt\":{margin:\"0 2px\",padding:\"0 5px\",whiteSpace:\"nowrap\",border:`1px solid ${theme.color.mediumlight}`,backgroundColor:theme.color.lighter,borderRadius:3,color:theme.base===\"dark\"&&theme.color.darkest}}));var languages=[],Comp=null,LazySyntaxHighlighter=lazy(async()=>{let{SyntaxHighlighter:SyntaxHighlighter3}=await import('./syntaxhighlighter-BP7B2CQK.mjs');return languages.length>0&&(languages.forEach(args=>{SyntaxHighlighter3.registerLanguage(...args);}),languages=[]),Comp===null&&(Comp=SyntaxHighlighter3),{default:props=>React3__default.createElement(SyntaxHighlighter3,{...props})}}),LazySyntaxHighlighterWithFormatter=lazy(async()=>{let[{SyntaxHighlighter:SyntaxHighlighter3},{formatter}]=await Promise.all([import('./syntaxhighlighter-BP7B2CQK.mjs'),import('./formatter-2WMMO6ZP.mjs')]);return languages.length>0&&(languages.forEach(args=>{SyntaxHighlighter3.registerLanguage(...args);}),languages=[]),Comp===null&&(Comp=SyntaxHighlighter3),{default:props=>React3__default.createElement(SyntaxHighlighter3,{...props,formatter})}}),SyntaxHighlighter2=props=>React3__default.createElement(Suspense,{fallback:React3__default.createElement(\"div\",null)},props.format!==!1?React3__default.createElement(LazySyntaxHighlighterWithFormatter,{...props}):React3__default.createElement(LazySyntaxHighlighter,{...props}));SyntaxHighlighter2.registerLanguage=(...args)=>{if(Comp!==null){Comp.registerLanguage(...args);return}languages.push(args);};var Modal_styled_exports={};__export(Modal_styled_exports,{Actions:()=>Actions,CloseButton:()=>CloseButton,Col:()=>Col,Container:()=>Container,Content:()=>Content,Description:()=>Description2,Error:()=>Error2,ErrorWrapper:()=>ErrorWrapper,Header:()=>Header,Overlay:()=>Overlay,Row:()=>Row,Title:()=>Title2});var Button=forwardRef(({asChild=!1,animation=\"none\",size=\"small\",variant=\"outline\",padding=\"medium\",disabled=!1,active=!1,onClick,...props},ref)=>{let Comp2=\"button\";props.isLink&&(Comp2=\"a\"),asChild&&(Comp2=Slot);let localVariant=variant,localSize=size,[isAnimating,setIsAnimating]=useState(!1),handleClick=event=>{onClick&&onClick(event),animation!==\"none\"&&setIsAnimating(!0);};if(useEffect(()=>{let timer=setTimeout(()=>{isAnimating&&setIsAnimating(!1);},1e3);return ()=>clearTimeout(timer)},[isAnimating]),props.primary&&(localVariant=\"solid\",localSize=\"medium\"),(props.secondary||props.tertiary||props.gray||props.outline||props.inForm)&&(localVariant=\"outline\",localSize=\"medium\"),props.small||props.isLink||props.primary||props.secondary||props.tertiary||props.gray||props.outline||props.inForm||props.containsIcon){let buttonContent=React3__default.Children.toArray(props.children).filter(e=>typeof e==\"string\"&&e!==\"\");deprecate(`Use of deprecated props in the button ${buttonContent.length>0?`\"${buttonContent.join(\" \")}\"`:\"component\"} detected, see the migration notes at https://github.com/storybookjs/storybook/blob/next/MIGRATION.md#new-ui-and-props-for-button-and-iconbutton-components`);}return React3__default.createElement(StyledButton,{as:Comp2,ref,variant:localVariant,size:localSize,padding,disabled,active,animating:isAnimating,animation,onClick:handleClick,...props})});Button.displayName=\"Button\";var StyledButton=styled(\"button\",{shouldForwardProp:prop=>isPropValid(prop)})(({theme,variant,size,disabled,active,animating,animation,padding})=>({border:0,cursor:disabled?\"not-allowed\":\"pointer\",display:\"inline-flex\",gap:\"6px\",alignItems:\"center\",justifyContent:\"center\",overflow:\"hidden\",padding:padding===\"small\"&&size===\"small\"?\"0 7px\":padding===\"small\"&&size===\"medium\"?\"0 9px\":size===\"small\"?\"0 10px\":size===\"medium\"?\"0 12px\":0,height:size===\"small\"?\"28px\":\"32px\",position:\"relative\",textAlign:\"center\",textDecoration:\"none\",transitionProperty:\"background, box-shadow\",transitionDuration:\"150ms\",transitionTimingFunction:\"ease-out\",verticalAlign:\"top\",whiteSpace:\"nowrap\",userSelect:\"none\",opacity:disabled?.5:1,margin:0,fontSize:`${theme.typography.size.s1}px`,fontWeight:theme.typography.weight.bold,lineHeight:\"1\",background:variant===\"solid\"?theme.color.secondary:variant===\"outline\"?theme.button.background:variant===\"ghost\"&&active?theme.background.hoverable:\"transparent\",...variant===\"ghost\"?{\".sb-bar &\":{background:active?curriedTransparentize$1(.9,theme.barTextColor):\"transparent\",color:active?theme.barSelectedColor:theme.barTextColor,\"&:hover\":{color:theme.barHoverColor,background:curriedTransparentize$1(.86,theme.barHoverColor)},\"&:active\":{color:theme.barSelectedColor,background:curriedTransparentize$1(.9,theme.barSelectedColor)},\"&:focus\":{boxShadow:`${rgba(theme.barHoverColor,1)} 0 0 0 1px inset`,outline:\"none\"}}}:{},color:variant===\"solid\"?theme.color.lightest:variant===\"outline\"?theme.input.color:variant===\"ghost\"&&active?theme.color.secondary:variant===\"ghost\"?theme.color.mediumdark:theme.input.color,boxShadow:variant===\"outline\"?`${theme.button.border} 0 0 0 1px inset`:\"none\",borderRadius:theme.input.borderRadius,flexShrink:0,\"&:hover\":{color:variant===\"ghost\"?theme.color.secondary:null,background:(()=>{let bgColor=theme.color.secondary;return variant===\"solid\"&&(bgColor=theme.color.secondary),variant===\"outline\"&&(bgColor=theme.button.background),variant===\"ghost\"?curriedTransparentize$1(.86,theme.color.secondary):theme.base===\"light\"?curriedDarken$1(.02,bgColor):curriedLighten$1(.03,bgColor)})()},\"&:active\":{color:variant===\"ghost\"?theme.color.secondary:null,background:(()=>{let bgColor=theme.color.secondary;return variant===\"solid\"&&(bgColor=theme.color.secondary),variant===\"outline\"&&(bgColor=theme.button.background),variant===\"ghost\"?theme.background.hoverable:theme.base===\"light\"?curriedDarken$1(.02,bgColor):curriedLighten$1(.03,bgColor)})()},\"&:focus\":{boxShadow:`${rgba(theme.color.secondary,1)} 0 0 0 1px inset`,outline:\"none\"},\"> svg\":{animation:animating&&animation!==\"none\"?`${theme.animation[animation]} 1000ms ease-out`:\"\"}}));var IconButton=forwardRef(({padding=\"small\",variant=\"ghost\",...props},ref)=>React3__default.createElement(Button,{padding,variant,ref,...props}));IconButton.displayName=\"IconButton\";var fadeIn=keyframes({from:{opacity:0},to:{opacity:1}}),expand=keyframes({from:{maxHeight:0},to:{}}),zoomIn=keyframes({from:{opacity:0,transform:\"translate(-50%, -50%) scale(0.9)\"},to:{opacity:1,transform:\"translate(-50%, -50%) scale(1)\"}}),Overlay=styled.div({backgroundColor:\"rgba(27, 28, 29, 0.2)\",position:\"fixed\",inset:0,width:\"100%\",height:\"100%\",zIndex:10,animation:`${fadeIn} 200ms`}),Container=styled.div(({theme,width,height})=>({backgroundColor:theme.background.bar,borderRadius:6,boxShadow:\"rgba(255, 255, 255, 0.05) 0 0 0 1px inset, rgba(14, 18, 22, 0.35) 0px 10px 38px -10px\",position:\"fixed\",top:\"50%\",left:\"50%\",transform:\"translate(-50%, -50%)\",width:width??740,height:height??\"auto\",maxWidth:\"calc(100% - 40px)\",maxHeight:\"85vh\",overflow:\"hidden\",zIndex:11,animation:`${zoomIn} 200ms`,\"&:focus-visible\":{outline:\"none\"}})),CloseButton=props=>React3__default.createElement(Dialog2.Close,{asChild:!0},React3__default.createElement(IconButton,{...props},React3__default.createElement(CrossIcon,null))),Content=styled.div({display:\"flex\",flexDirection:\"column\",margin:16,gap:16}),Row=styled.div({display:\"flex\",justifyContent:\"space-between\",gap:16}),Col=styled.div({display:\"flex\",flexDirection:\"column\",gap:4}),Header=props=>React3__default.createElement(Row,null,React3__default.createElement(Col,{...props}),React3__default.createElement(CloseButton,null)),Title2=styled(Dialog2.Title)(({theme})=>({margin:0,fontSize:theme.typography.size.s3,fontWeight:theme.typography.weight.bold})),Description2=styled(Dialog2.Description)(({theme})=>({position:\"relative\",zIndex:1,margin:0,fontSize:theme.typography.size.s2})),Actions=styled.div({display:\"flex\",flexDirection:\"row-reverse\",gap:8}),ErrorWrapper=styled.div(({theme})=>({maxHeight:100,overflow:\"auto\",animation:`${expand} 300ms, ${fadeIn} 300ms`,backgroundColor:theme.background.critical,color:theme.color.lightest,fontSize:theme.typography.size.s2,\"& > div\":{position:\"relative\",padding:\"8px 16px\"}})),Error2=({children,...props})=>React3__default.createElement(ErrorWrapper,{...props},React3__default.createElement(\"div\",null,children));function BaseModal({children,width,height,onEscapeKeyDown,onInteractOutside=ev=>ev.preventDefault(),className,container,...rootProps}){return React3__default.createElement(Dialog2.Root,{...rootProps},React3__default.createElement(Dialog2.Portal,{container},React3__default.createElement(Dialog2.Overlay,{asChild:!0},React3__default.createElement(Overlay,null)),React3__default.createElement(Dialog2.Content,{asChild:!0,onInteractOutside,onEscapeKeyDown},React3__default.createElement(Container,{className,width,height},children))))}var Modal=Object.assign(BaseModal,Modal_styled_exports,{Dialog:Dialog2});var toNumber=input=>typeof input==\"number\"?input:Number(input),Container2=styled.div(({theme,col,row=1})=>col?{display:\"inline-block\",verticalAlign:\"inherit\",\"& > *\":{marginLeft:col*theme.layoutMargin,verticalAlign:\"inherit\"},[`& > *:first-child${ignoreSsrWarning}`]:{marginLeft:0}}:{\"& > *\":{marginTop:row*theme.layoutMargin},[`& > *:first-child${ignoreSsrWarning}`]:{marginTop:0}},({theme,outer,col,row})=>{switch(!0){case!!(outer&&col):return {marginLeft:outer*theme.layoutMargin,marginRight:outer*theme.layoutMargin};case!!(outer&&row):return {marginTop:outer*theme.layoutMargin,marginBottom:outer*theme.layoutMargin};default:return {}}}),Spaced=({col,row,outer,children,...rest})=>{let outerAmount=toNumber(typeof outer==\"number\"||!outer?outer:col||row);return React3__default.createElement(Container2,{col,row,outer:outerAmount,...rest},children)};var Title3=styled.div(({theme})=>({fontWeight:theme.typography.weight.bold})),Desc=styled.div(),Message=styled.div(({theme})=>({padding:30,textAlign:\"center\",color:theme.color.defaultText,fontSize:theme.typography.size.s2-1})),Placeholder=({children,...props})=>{let[title,desc]=Children.toArray(children);return React3__default.createElement(Message,{...props},React3__default.createElement(Title3,null,title),desc&&React3__default.createElement(Desc,null,desc))};function useResolvedElement(subscriber,refOrElement){var lastReportRef=useRef(null),refOrElementRef=useRef(null);refOrElementRef.current=refOrElement;var cbElementRef=useRef(null);useEffect(function(){evaluateSubscription();});var evaluateSubscription=useCallback(function(){var cbElement=cbElementRef.current,refOrElement2=refOrElementRef.current,element=cbElement||(refOrElement2?refOrElement2 instanceof Element?refOrElement2:refOrElement2.current:null);lastReportRef.current&&lastReportRef.current.element===element&&lastReportRef.current.subscriber===subscriber||(lastReportRef.current&&lastReportRef.current.cleanup&&lastReportRef.current.cleanup(),lastReportRef.current={element,subscriber,cleanup:element?subscriber(element):void 0});},[subscriber]);return useEffect(function(){return function(){lastReportRef.current&&lastReportRef.current.cleanup&&(lastReportRef.current.cleanup(),lastReportRef.current=null);}},[]),useCallback(function(element){cbElementRef.current=element,evaluateSubscription();},[evaluateSubscription])}function extractSize(entry,boxProp,sizeType){return entry[boxProp]?entry[boxProp][0]?entry[boxProp][0][sizeType]:entry[boxProp][sizeType]:boxProp===\"contentBoxSize\"?entry.contentRect[sizeType===\"inlineSize\"?\"width\":\"height\"]:void 0}function useResizeObserver(opts){opts===void 0&&(opts={});var onResize=opts.onResize,onResizeRef=useRef(void 0);onResizeRef.current=onResize;var round=opts.round||Math.round,resizeObserverRef=useRef(),_useState=useState({width:void 0,height:void 0}),size=_useState[0],setSize=_useState[1],didUnmount=useRef(!1);useEffect(function(){return didUnmount.current=!1,function(){didUnmount.current=!0;}},[]);var previous=useRef({width:void 0,height:void 0}),refCallback=useResolvedElement(useCallback(function(element){return (!resizeObserverRef.current||resizeObserverRef.current.box!==opts.box||resizeObserverRef.current.round!==round)&&(resizeObserverRef.current={box:opts.box,round,instance:new ResizeObserver(function(entries){var entry=entries[0],boxProp=opts.box===\"border-box\"?\"borderBoxSize\":opts.box===\"device-pixel-content-box\"?\"devicePixelContentBoxSize\":\"contentBoxSize\",reportedWidth=extractSize(entry,boxProp,\"inlineSize\"),reportedHeight=extractSize(entry,boxProp,\"blockSize\"),newWidth=reportedWidth?round(reportedWidth):void 0,newHeight=reportedHeight?round(reportedHeight):void 0;if(previous.current.width!==newWidth||previous.current.height!==newHeight){var newSize={width:newWidth,height:newHeight};previous.current.width=newWidth,previous.current.height=newHeight,onResizeRef.current?onResizeRef.current(newSize):didUnmount.current||setSize(newSize);}})}),resizeObserverRef.current.instance.observe(element,{box:opts.box}),function(){resizeObserverRef.current&&resizeObserverRef.current.instance.unobserve(element);}},[opts.box,round]),opts.ref);return useMemo(function(){return {ref:refCallback,width:size.width,height:size.height}},[refCallback,size.width,size.height])}var ZoomElementWrapper=styled.div(({scale=1,elementHeight})=>({height:elementHeight||\"auto\",transformOrigin:\"top left\",transform:`scale(${1/scale})`}));function ZoomElement({scale,children}){let componentWrapperRef=useRef(null),[elementHeight,setElementHeight]=useState(0),onResize=useCallback(({height})=>{height&&setElementHeight(height/scale);},[scale]);return useEffect(()=>{componentWrapperRef.current&&setElementHeight(componentWrapperRef.current.getBoundingClientRect().height);},[scale]),useResizeObserver({ref:componentWrapperRef,onResize}),React3__default.createElement(ZoomElementWrapper,{scale,elementHeight},React3__default.createElement(\"div\",{ref:componentWrapperRef,className:\"innerZoomElementWrapper\"},children))}var ZoomIFrame=class extends Component{constructor(){super(...arguments);this.iframe=null;}componentDidMount(){let{iFrameRef}=this.props;this.iframe=iFrameRef.current;}shouldComponentUpdate(nextProps){let{scale,active}=this.props;return scale!==nextProps.scale&&this.setIframeInnerZoom(nextProps.scale),active!==nextProps.active&&this.iframe.setAttribute(\"data-is-storybook\",nextProps.active?\"true\":\"false\"),nextProps.children.props.src!==this.props.children.props.src}setIframeInnerZoom(scale){try{Object.assign(this.iframe.contentDocument.body.style,{width:`${scale*100}%`,height:`${scale*100}%`,transform:`scale(${1/scale})`,transformOrigin:\"top left\"});}catch{this.setIframeZoom(scale);}}setIframeZoom(scale){Object.assign(this.iframe.style,{width:`${scale*100}%`,height:`${scale*100}%`,transform:`scale(${1/scale})`,transformOrigin:\"top left\"});}render(){let{children}=this.props;return React3__default.createElement(React3__default.Fragment,null,children)}};var Zoom={Element:ZoomElement,IFrame:ZoomIFrame};var {document:document2}=global,ErrorName=styled.strong(({theme})=>({color:theme.color.orange})),ErrorImportant=styled.strong(({theme})=>({color:theme.color.ancillary,textDecoration:\"underline\"})),ErrorDetail=styled.em(({theme})=>({color:theme.textMutedColor})),firstLineRegex=/(Error): (.*)\\n/,linesRegexChromium=/at (?:(.*) )?\\(?(.+)\\)?/,linesRegexFirefox=/([^@]+)?(?:\\/<)?@(.+)?/,linesRegexSafari=/([^@]+)?@(.+)?/,ErrorFormatter=({error})=>{if(!error)return React3__default.createElement(Fragment,null,\"This error has no stack or message\");if(!error.stack)return React3__default.createElement(Fragment,null,error.message||\"This error has no stack or message\");let input=error.stack.toString();input&&error.message&&!input.includes(error.message)&&(input=`Error: ${error.message}\n\n${input}`);let match=input.match(firstLineRegex);if(!match)return React3__default.createElement(Fragment,null,input);let[,type,name]=match,rawLines=input.split(/\\n/).slice(1),[,...lines]=rawLines.map(line=>{let result=line.match(linesRegexChromium)||line.match(linesRegexFirefox)||line.match(linesRegexSafari);return result?{name:(result[1]||\"\").replace(\"/<\",\"\"),location:result[2].replace(document2.location.origin,\"\")}:null}).filter(Boolean);return React3__default.createElement(Fragment,null,React3__default.createElement(\"span\",null,type),\": \",React3__default.createElement(ErrorName,null,name),React3__default.createElement(\"br\",null),lines.map((l,i)=>l.name?React3__default.createElement(Fragment,{key:i},\"  \",\"at \",React3__default.createElement(ErrorImportant,null,l.name),\" (\",React3__default.createElement(ErrorDetail,null,l.location),\")\",React3__default.createElement(\"br\",null)):React3__default.createElement(Fragment,{key:i},\"  \",\"at \",React3__default.createElement(ErrorDetail,null,l.location),React3__default.createElement(\"br\",null))))};var Wrapper=styled.label(({theme})=>({display:\"flex\",borderBottom:`1px solid ${theme.appBorderColor}`,margin:\"0 15px\",padding:\"8px 0\",\"&:last-child\":{marginBottom:\"3rem\"}})),Label=styled.span(({theme})=>({minWidth:100,fontWeight:theme.typography.weight.bold,marginRight:15,display:\"flex\",justifyContent:\"flex-start\",alignItems:\"center\",lineHeight:\"16px\"})),Field=({label,children,...props})=>React3__default.createElement(Wrapper,{...props},label?React3__default.createElement(Label,null,React3__default.createElement(\"span\",null,label)):null,children);var index=useLayoutEffect,use_isomorphic_layout_effect_browser_esm_default=index;var useLatest=function(value){var ref=React3.useRef(value);return use_isomorphic_layout_effect_browser_esm_default(function(){ref.current=value;}),ref};var updateRef=function(ref,value){if(typeof ref==\"function\"){ref(value);return}ref.current=value;},useComposedRef=function(libRef,userRef){var prevUserRef=useRef();return useCallback(function(instance){libRef.current=instance,prevUserRef.current&&updateRef(prevUserRef.current,null),prevUserRef.current=userRef,userRef&&updateRef(userRef,instance);},[userRef])},use_composed_ref_esm_default=useComposedRef;var HIDDEN_TEXTAREA_STYLE={\"min-height\":\"0\",\"max-height\":\"none\",height:\"0\",visibility:\"hidden\",overflow:\"hidden\",position:\"absolute\",\"z-index\":\"-1000\",top:\"0\",right:\"0\"},forceHiddenStyles=function(node){Object.keys(HIDDEN_TEXTAREA_STYLE).forEach(function(key){node.style.setProperty(key,HIDDEN_TEXTAREA_STYLE[key],\"important\");});},forceHiddenStyles$1=forceHiddenStyles,hiddenTextarea=null,getHeight=function(node,sizingData){var height=node.scrollHeight;return sizingData.sizingStyle.boxSizing===\"border-box\"?height+sizingData.borderSize:height-sizingData.paddingSize};function calculateNodeHeight(sizingData,value,minRows,maxRows){minRows===void 0&&(minRows=1),maxRows===void 0&&(maxRows=1/0),hiddenTextarea||(hiddenTextarea=document.createElement(\"textarea\"),hiddenTextarea.setAttribute(\"tabindex\",\"-1\"),hiddenTextarea.setAttribute(\"aria-hidden\",\"true\"),forceHiddenStyles$1(hiddenTextarea)),hiddenTextarea.parentNode===null&&document.body.appendChild(hiddenTextarea);var paddingSize=sizingData.paddingSize,borderSize=sizingData.borderSize,sizingStyle=sizingData.sizingStyle,boxSizing=sizingStyle.boxSizing;Object.keys(sizingStyle).forEach(function(_key){var key=_key;hiddenTextarea.style[key]=sizingStyle[key];}),forceHiddenStyles$1(hiddenTextarea),hiddenTextarea.value=value;var height=getHeight(hiddenTextarea,sizingData);hiddenTextarea.value=value,height=getHeight(hiddenTextarea,sizingData),hiddenTextarea.value=\"x\";var rowHeight=hiddenTextarea.scrollHeight-paddingSize,minHeight=rowHeight*minRows;boxSizing===\"border-box\"&&(minHeight=minHeight+paddingSize+borderSize),height=Math.max(minHeight,height);var maxHeight=rowHeight*maxRows;return boxSizing===\"border-box\"&&(maxHeight=maxHeight+paddingSize+borderSize),height=Math.min(maxHeight,height),[height,rowHeight]}var noop=function(){},pick=function(props,obj){return props.reduce(function(acc,prop){return acc[prop]=obj[prop],acc},{})},SIZING_STYLE=[\"borderBottomWidth\",\"borderLeftWidth\",\"borderRightWidth\",\"borderTopWidth\",\"boxSizing\",\"fontFamily\",\"fontSize\",\"fontStyle\",\"fontWeight\",\"letterSpacing\",\"lineHeight\",\"paddingBottom\",\"paddingLeft\",\"paddingRight\",\"paddingTop\",\"tabSize\",\"textIndent\",\"textRendering\",\"textTransform\",\"width\",\"wordBreak\"],isIE=!!document.documentElement.currentStyle,getSizingData=function(node){var style=window.getComputedStyle(node);if(style===null)return null;var sizingStyle=pick(SIZING_STYLE,style),boxSizing=sizingStyle.boxSizing;if(boxSizing===\"\")return null;isIE&&boxSizing===\"border-box\"&&(sizingStyle.width=parseFloat(sizingStyle.width)+parseFloat(sizingStyle.borderRightWidth)+parseFloat(sizingStyle.borderLeftWidth)+parseFloat(sizingStyle.paddingRight)+parseFloat(sizingStyle.paddingLeft)+\"px\");var paddingSize=parseFloat(sizingStyle.paddingBottom)+parseFloat(sizingStyle.paddingTop),borderSize=parseFloat(sizingStyle.borderBottomWidth)+parseFloat(sizingStyle.borderTopWidth);return {sizingStyle,paddingSize,borderSize}},getSizingData$1=getSizingData;function useListener(target,type,listener){var latestListener=useLatest(listener);React3.useLayoutEffect(function(){var handler=function(ev){return latestListener.current(ev)};if(target)return target.addEventListener(type,handler),function(){return target.removeEventListener(type,handler)}},[]);}var useWindowResizeListener=function(listener){useListener(window,\"resize\",listener);},useFontsLoadedListener=function(listener){useListener(document.fonts,\"loadingdone\",listener);},_excluded=[\"cacheMeasurements\",\"maxRows\",\"minRows\",\"onChange\",\"onHeightChange\"],TextareaAutosize=function(_ref,userRef){var cacheMeasurements=_ref.cacheMeasurements,maxRows=_ref.maxRows,minRows=_ref.minRows,_ref$onChange=_ref.onChange,onChange=_ref$onChange===void 0?noop:_ref$onChange,_ref$onHeightChange=_ref.onHeightChange,onHeightChange=_ref$onHeightChange===void 0?noop:_ref$onHeightChange,props=_objectWithoutPropertiesLoose(_ref,_excluded),isControlled=props.value!==void 0,libRef=React3.useRef(null),ref=use_composed_ref_esm_default(libRef,userRef),heightRef=React3.useRef(0),measurementsCacheRef=React3.useRef(),resizeTextarea=function(){var node=libRef.current,nodeSizingData=cacheMeasurements&&measurementsCacheRef.current?measurementsCacheRef.current:getSizingData$1(node);if(nodeSizingData){measurementsCacheRef.current=nodeSizingData;var _calculateNodeHeight=calculateNodeHeight(nodeSizingData,node.value||node.placeholder||\"x\",minRows,maxRows),height=_calculateNodeHeight[0],rowHeight=_calculateNodeHeight[1];heightRef.current!==height&&(heightRef.current=height,node.style.setProperty(\"height\",height+\"px\",\"important\"),onHeightChange(height,{rowHeight}));}},handleChange=function(event){isControlled||resizeTextarea(),onChange(event);};return React3.useLayoutEffect(resizeTextarea),useWindowResizeListener(resizeTextarea),useFontsLoadedListener(resizeTextarea),React3.createElement(\"textarea\",_extends({},props,{onChange:handleChange,ref}))},index2=React3.forwardRef(TextareaAutosize);var styleResets={appearance:\"none\",border:\"0 none\",boxSizing:\"inherit\",display:\" block\",margin:\" 0\",background:\"transparent\",padding:0,fontSize:\"inherit\",position:\"relative\"},styles=({theme})=>({...styleResets,transition:\"box-shadow 200ms ease-out, opacity 200ms ease-out\",color:theme.input.color||\"inherit\",background:theme.input.background,boxShadow:`${theme.input.border} 0 0 0 1px inset`,borderRadius:theme.input.borderRadius,fontSize:theme.typography.size.s2-1,lineHeight:\"20px\",padding:\"6px 10px\",boxSizing:\"border-box\",height:32,'&[type=\"file\"]':{height:\"auto\"},\"&:focus\":{boxShadow:`${theme.color.secondary} 0 0 0 1px inset`,outline:\"none\"},\"&[disabled]\":{cursor:\"not-allowed\",opacity:.5},\"&:-webkit-autofill\":{WebkitBoxShadow:`0 0 0 3em ${theme.color.lightest} inset`},\"&::placeholder\":{color:theme.textMutedColor,opacity:1}}),sizes=({size})=>{switch(size){case\"100%\":return {width:\"100%\"};case\"flex\":return {flex:1};case\"auto\":default:return {display:\"inline\"}}},alignment=({align})=>{switch(align){case\"end\":return {textAlign:\"right\"};case\"center\":return {textAlign:\"center\"};case\"start\":default:return {textAlign:\"left\"}}},validation=({valid,theme})=>{switch(valid){case\"valid\":return {boxShadow:`${theme.color.positive} 0 0 0 1px inset !important`};case\"error\":return {boxShadow:`${theme.color.negative} 0 0 0 1px inset !important`};case\"warn\":return {boxShadow:`${theme.color.warning} 0 0 0 1px inset`};case void 0:case null:default:return {}}},Input=Object.assign(styled(forwardRef(function({size,valid,align,...props},ref){return React3__default.createElement(\"input\",{...props,ref})}))(styles,sizes,alignment,validation,{minHeight:32}),{displayName:\"Input\"}),Select=Object.assign(styled(forwardRef(function({size,valid,align,...props},ref){return React3__default.createElement(\"select\",{...props,ref})}))(styles,sizes,validation,{height:32,userSelect:\"none\",paddingRight:20,appearance:\"menulist\"}),{displayName:\"Select\"}),Textarea=Object.assign(styled(forwardRef(function({size,valid,align,...props},ref){return React3__default.createElement(index2,{...props,ref})}))(styles,sizes,alignment,validation,({height=400})=>({overflow:\"visible\",maxHeight:height})),{displayName:\"Textarea\"});var Form=Object.assign(styled.form({boxSizing:\"border-box\",width:\"100%\"}),{Field,Input,Select,Textarea,Button});var LazyWithTooltip=lazy(()=>import('./WithTooltip-KJL26V4Q.mjs').then(mod=>({default:mod.WithTooltip}))),WithTooltip=props=>React3__default.createElement(Suspense,{fallback:React3__default.createElement(\"div\",null)},React3__default.createElement(LazyWithTooltip,{...props})),LazyWithTooltipPure=lazy(()=>import('./WithTooltip-KJL26V4Q.mjs').then(mod=>({default:mod.WithTooltipPure}))),WithTooltipPure=props=>React3__default.createElement(Suspense,{fallback:React3__default.createElement(\"div\",null)},React3__default.createElement(LazyWithTooltipPure,{...props}));var Title4=styled.div(({theme})=>({fontWeight:theme.typography.weight.bold})),Desc2=styled.span(),Links=styled.div(({theme})=>({marginTop:8,textAlign:\"center\",\"> *\":{margin:\"0 8px\",fontWeight:theme.typography.weight.bold}})),Message2=styled.div(({theme})=>({color:theme.color.defaultText,lineHeight:\"18px\"})),MessageWrapper=styled.div({padding:15,width:280,boxSizing:\"border-box\"}),TooltipMessage=({title,desc,links})=>React3__default.createElement(MessageWrapper,null,React3__default.createElement(Message2,null,title&&React3__default.createElement(Title4,null,title),desc&&React3__default.createElement(Desc2,null,desc)),links&&React3__default.createElement(Links,null,links.map(({title:linkTitle,...other})=>React3__default.createElement(Link2,{...other,key:linkTitle},linkTitle))));var Note=styled.div(({theme})=>({padding:\"2px 6px\",lineHeight:\"16px\",fontSize:10,fontWeight:theme.typography.weight.bold,color:theme.color.lightest,boxShadow:\"0 0 5px 0 rgba(0, 0, 0, 0.3)\",borderRadius:4,whiteSpace:\"nowrap\",pointerEvents:\"none\",zIndex:-1,background:theme.base===\"light\"?\"rgba(60, 60, 60, 0.9)\":\"rgba(0, 0, 0, 0.95)\",margin:6})),TooltipNote=({note,...props})=>React3__default.createElement(Note,{...props},note);var Title5=styled(({active,loading,disabled,...rest})=>React3__default.createElement(\"span\",{...rest}))(({theme})=>({color:theme.color.defaultText,fontWeight:theme.typography.weight.regular}),({active,theme})=>active?{color:theme.color.secondary,fontWeight:theme.typography.weight.bold}:{},({loading,theme})=>loading?{display:\"inline-block\",flex:\"none\",...theme.animation.inlineGlow}:{},({disabled,theme})=>disabled?{color:curriedTransparentize$1(.7,theme.color.defaultText)}:{}),Right=styled.span({display:\"flex\",\"& svg\":{height:12,width:12,margin:\"3px 0\",verticalAlign:\"top\"},\"& path\":{fill:\"inherit\"}}),Center=styled.span({flex:1,textAlign:\"left\",display:\"flex\",flexDirection:\"column\"},({isIndented})=>isIndented?{marginLeft:24}:{}),CenterText=styled.span(({theme})=>({fontSize:\"11px\",lineHeight:\"14px\"}),({active,theme})=>active?{color:theme.color.secondary}:{},({theme,disabled})=>disabled?{color:theme.textMutedColor}:{}),Left=styled.span(({active,theme})=>active?{color:theme.color.secondary}:{},()=>({display:\"flex\",maxWidth:14})),Item=styled.a(({theme})=>({fontSize:theme.typography.size.s1,transition:\"all 150ms ease-out\",color:theme.color.dark,textDecoration:\"none\",cursor:\"pointer\",justifyContent:\"space-between\",lineHeight:\"18px\",padding:\"7px 10px\",display:\"flex\",alignItems:\"center\",\"& > * + *\":{paddingLeft:10},\"&:hover\":{background:theme.background.hoverable},\"&:hover svg\":{opacity:1}}),({disabled})=>disabled?{cursor:\"not-allowed\"}:{}),getItemProps=memoize(100)((onClick,href,LinkWrapper)=>{let result={};return onClick&&Object.assign(result,{onClick}),href&&Object.assign(result,{href}),LinkWrapper&&href&&Object.assign(result,{to:href,as:LinkWrapper}),result}),ListItem=({loading=!1,title=React3__default.createElement(\"span\",null,\"Loading state\"),center=null,right=null,active=!1,disabled=!1,isIndented,href=null,onClick=null,icon,LinkWrapper=null,...rest})=>{let itemProps=getItemProps(onClick,href,LinkWrapper),commonProps={active,disabled};return React3__default.createElement(Item,{...commonProps,...rest,...itemProps},icon&&React3__default.createElement(Left,{...commonProps},icon),title||center?React3__default.createElement(Center,{isIndented:!icon&&isIndented},title&&React3__default.createElement(Title5,{...commonProps,loading},title),center&&React3__default.createElement(CenterText,{...commonProps},center)):null,right&&React3__default.createElement(Right,{...commonProps},right))},ListItem_default=ListItem;var List=styled.div({minWidth:180,overflow:\"hidden\",overflowY:\"auto\",maxHeight:15.5*32},({theme})=>({borderRadius:theme.appBorderRadius})),Item2=props=>{let{LinkWrapper,onClick:onClickFromProps,id,isIndented,...rest}=props,{title,href,active}=rest,onClick=useCallback(event=>{onClickFromProps(event,rest);},[onClickFromProps]),hasOnClick=!!onClickFromProps;return React3__default.createElement(ListItem_default,{title,active,href,id:`list-item-${id}`,LinkWrapper,isIndented,...rest,...hasOnClick?{onClick}:{}})},TooltipLinkList=({links,LinkWrapper=null})=>{let hasIcon=links.some(link=>link.icon);return React3__default.createElement(List,null,links.map(({isGatsby,...p})=>React3__default.createElement(Item2,{key:p.id,LinkWrapper:isGatsby?LinkWrapper:null,isIndented:hasIcon,...p})))};var isLink=obj=>typeof obj.props.href==\"string\",isButton=obj=>typeof obj.props.href!=\"string\";function ForwardRefFunction({children,...rest},ref){let o={props:rest,ref};if(isLink(o))return React3__default.createElement(\"a\",{ref:o.ref,...o.props},children);if(isButton(o))return React3__default.createElement(\"button\",{ref:o.ref,type:\"button\",...o.props},children);throw new Error(\"invalid props\")}var ButtonOrLink=forwardRef(ForwardRefFunction);ButtonOrLink.displayName=\"ButtonOrLink\";var TabButton=styled(ButtonOrLink,{shouldForwardProp:isPropValid})({whiteSpace:\"normal\",display:\"inline-flex\",overflow:\"hidden\",verticalAlign:\"top\",justifyContent:\"center\",alignItems:\"center\",textAlign:\"center\",textDecoration:\"none\",\"&:empty\":{display:\"none\"},\"&[hidden]\":{display:\"none\"}},({theme})=>({padding:\"0 15px\",transition:\"color 0.2s linear, border-bottom-color 0.2s linear\",height:40,lineHeight:\"12px\",cursor:\"pointer\",background:\"transparent\",border:\"0 solid transparent\",borderTop:\"3px solid transparent\",borderBottom:\"3px solid transparent\",fontWeight:\"bold\",fontSize:13,\"&:focus\":{outline:\"0 none\",borderBottomColor:theme.barSelectedColor}}),({active,textColor,theme})=>active?{color:textColor||theme.barSelectedColor,borderBottomColor:theme.barSelectedColor}:{color:textColor||theme.barTextColor,borderBottomColor:\"transparent\",\"&:hover\":{color:theme.barHoverColor}});TabButton.displayName=\"TabButton\";var IconPlaceholder=styled.div(({theme})=>({width:14,height:14,backgroundColor:theme.appBorderColor,animation:`${theme.animation.glow} 1.5s ease-in-out infinite`})),IconButtonSkeletonWrapper=styled.div(()=>({marginTop:6,padding:7,height:28})),IconButtonSkeleton=()=>React3__default.createElement(IconButtonSkeletonWrapper,null,React3__default.createElement(IconPlaceholder,null));var Side=styled.div({display:\"flex\",whiteSpace:\"nowrap\",flexBasis:\"auto\",marginLeft:3,marginRight:3},({scrollable})=>scrollable?{flexShrink:0}:{},({left})=>left?{\"& > *\":{marginLeft:4}}:{},({right})=>right?{marginLeft:30,\"& > *\":{marginRight:4}}:{});Side.displayName=\"Side\";var UnstyledBar=({children,className,scrollable})=>scrollable?React3__default.createElement(ScrollArea,{vertical:!1,className},children):React3__default.createElement(\"div\",{className},children),Bar=styled(UnstyledBar)(({theme,scrollable=!0})=>({color:theme.barTextColor,width:\"100%\",height:40,flexShrink:0,overflow:scrollable?\"auto\":\"hidden\",overflowY:\"hidden\"}),({theme,border=!1})=>border?{boxShadow:`${theme.appBorderColor}  0 -1px 0 0 inset`,background:theme.barBg}:{});Bar.displayName=\"Bar\";var BarInner=styled.div(({bgColor})=>({display:\"flex\",justifyContent:\"space-between\",position:\"relative\",flexWrap:\"nowrap\",flexShrink:0,height:40,backgroundColor:bgColor||\"\"})),FlexBar=({children,backgroundColor,className,...rest})=>{let[left,right]=Children.toArray(children);return React3__default.createElement(Bar,{className:`sb-bar ${className}`,...rest},React3__default.createElement(BarInner,{bgColor:backgroundColor},React3__default.createElement(Side,{scrollable:rest.scrollable,left:!0},left),right?React3__default.createElement(Side,{right:!0},right):null))};FlexBar.displayName=\"FlexBar\";var VisuallyHidden=styled.div(({active})=>active?{display:\"block\"}:{display:\"none\"}),childrenToList=children=>Children.toArray(children).map(({props:{title,id,color:color2,children:childrenOfChild}})=>{let content=Array.isArray(childrenOfChild)?childrenOfChild[0]:childrenOfChild;return {title,id,...color2?{color:color2}:{},render:typeof content==\"function\"?content:({active})=>React3__default.createElement(VisuallyHidden,{active,role:\"tabpanel\"},content)}});var CollapseIcon=styled.span(({theme,isActive})=>({display:\"inline-block\",width:0,height:0,marginLeft:8,color:isActive?theme.color.secondary:theme.color.mediumdark,borderRight:\"3px solid transparent\",borderLeft:\"3px solid transparent\",borderTop:\"3px solid\",transition:\"transform .1s ease-out\"})),AddonButton=styled(TabButton)(({active,theme,preActive})=>`\n    color: ${preActive||active?theme.barSelectedColor:theme.barTextColor};\n    .addon-collapsible-icon {\n      color: ${preActive||active?theme.barSelectedColor:theme.barTextColor};\n    }\n    &:hover {\n      color: ${theme.barHoverColor};\n      .addon-collapsible-icon {\n        color: ${theme.barHoverColor};\n      }\n    }\n  `);function useList(list){let tabBarRef=useRef(),addonsRef=useRef(),tabRefs=useRef(new Map),{width:tabBarWidth=1}=useResizeObserver({ref:tabBarRef}),[visibleList,setVisibleList]=useState(list),[invisibleList,setInvisibleList]=useState([]),previousList=useRef(list),AddonTab=useCallback(({menuName,actions})=>{let isAddonsActive=invisibleList.some(({active})=>active),[isTooltipVisible,setTooltipVisible]=useState(!1);return React3__default.createElement(React3__default.Fragment,null,React3__default.createElement(WithToolTipState,{interactive:!0,visible:isTooltipVisible,onVisibleChange:setTooltipVisible,placement:\"bottom\",delayHide:100,tooltip:React3__default.createElement(TooltipLinkList,{links:invisibleList.map(({title,id,color:color2,active})=>({id,title,color:color2,active,onClick:e=>{e.preventDefault(),actions.onSelect(id);}}))})},React3__default.createElement(AddonButton,{ref:addonsRef,active:isAddonsActive,preActive:isTooltipVisible,style:{visibility:invisibleList.length?\"visible\":\"hidden\"},\"aria-hidden\":!invisibleList.length,className:\"tabbutton\",type:\"button\",role:\"tab\"},menuName,React3__default.createElement(CollapseIcon,{className:\"addon-collapsible-icon\",isActive:isAddonsActive||isTooltipVisible}))),invisibleList.map(({title,id,color:color2},index3)=>{let indexId=`index-${index3}`;return React3__default.createElement(TabButton,{id:`tabbutton-${sanitize(id)??indexId}`,style:{visibility:\"hidden\"},\"aria-hidden\":!0,tabIndex:-1,ref:ref=>{tabRefs.current.set(id,ref);},className:\"tabbutton\",type:\"button\",key:id,textColor:color2,role:\"tab\"},title)}))},[invisibleList]),setTabLists=useCallback(()=>{if(!tabBarRef.current||!addonsRef.current)return;let{x,width}=tabBarRef.current.getBoundingClientRect(),{width:widthAddonsTab}=addonsRef.current.getBoundingClientRect(),rightBorder=invisibleList.length?x+width-widthAddonsTab:x+width,newVisibleList=[],widthSum=0,newInvisibleList=list.filter(item=>{let{id}=item,tabButton=tabRefs.current.get(id),{width:tabWidth=0}=tabButton?.getBoundingClientRect()||{},crossBorder=x+widthSum+tabWidth>rightBorder;return (!crossBorder||!tabButton)&&newVisibleList.push(item),widthSum+=tabWidth,crossBorder});(newVisibleList.length!==visibleList.length||previousList.current!==list)&&(setVisibleList(newVisibleList),setInvisibleList(newInvisibleList),previousList.current=list);},[invisibleList.length,list,visibleList]);return useLayoutEffect(setTabLists,[setTabLists,tabBarWidth]),{tabRefs,addonsRef,tabBarRef,visibleList,invisibleList,AddonTab}}var Wrapper2=styled.div(({theme})=>({height:\"100%\",display:\"flex\",padding:30,alignItems:\"center\",justifyContent:\"center\",flexDirection:\"column\",gap:15,background:theme.background.content})),Content3=styled.div({display:\"flex\",flexDirection:\"column\",gap:4,maxWidth:415}),Title6=styled.div(({theme})=>({fontWeight:theme.typography.weight.bold,fontSize:theme.typography.size.s2-1,textAlign:\"center\",color:theme.textColor})),Description3=styled.div(({theme})=>({fontWeight:theme.typography.weight.regular,fontSize:theme.typography.size.s2-1,textAlign:\"center\",color:theme.textMutedColor})),EmptyTabContent=({title,description,footer})=>React3__default.createElement(Wrapper2,null,React3__default.createElement(Content3,null,React3__default.createElement(Title6,null,title),description&&React3__default.createElement(Description3,null,description)),footer);var ignoreSsrWarning2=\"/* emotion-disable-server-rendering-unsafe-selector-warning-please-do-not-use-this-the-warning-exists-for-a-reason */\",Wrapper3=styled.div(({theme,bordered})=>bordered?{backgroundClip:\"padding-box\",border:`1px solid ${theme.appBorderColor}`,borderRadius:theme.appBorderRadius,overflow:\"hidden\",boxSizing:\"border-box\"}:{},({absolute})=>absolute?{width:\"100%\",height:\"100%\",boxSizing:\"border-box\",display:\"flex\",flexDirection:\"column\"}:{display:\"block\"}),TabBar=styled.div({overflow:\"hidden\",\"&:first-of-type\":{marginLeft:-3},whiteSpace:\"nowrap\",flexGrow:1});TabBar.displayName=\"TabBar\";var Content4=styled.div({display:\"block\",position:\"relative\"},({theme})=>({fontSize:theme.typography.size.s2-1,background:theme.background.content}),({bordered,theme})=>bordered?{borderRadius:`0 0 ${theme.appBorderRadius-1}px ${theme.appBorderRadius-1}px`}:{},({absolute,bordered})=>absolute?{height:`calc(100% - ${bordered?42:40}px)`,position:\"absolute\",left:0+(bordered?1:0),right:0+(bordered?1:0),bottom:0+(bordered?1:0),top:40+(bordered?1:0),overflow:\"auto\",[`& > *:first-child${ignoreSsrWarning2}`]:{position:\"absolute\",left:0+(bordered?1:0),right:0+(bordered?1:0),bottom:0+(bordered?1:0),top:0+(bordered?1:0),height:`calc(100% - ${bordered?2:0}px)`,overflow:\"auto\"}}:{}),TabWrapper=({active,render,children})=>React3__default.createElement(VisuallyHidden,{active},render?render():children);var Tabs=memo(({children,selected=null,actions,absolute=!1,bordered=!1,tools=null,backgroundColor,id:htmlId=null,menuName=\"Tabs\",emptyState,showToolsWhenEmpty})=>{let list=useMemo(()=>childrenToList(children).map((i,index3)=>({...i,active:selected?i.id===selected:index3===0})),[children,selected]),{visibleList,tabBarRef,tabRefs,AddonTab}=useList(list),EmptyContent=emptyState??React3__default.createElement(EmptyTabContent,{title:\"Nothing found\"});return !showToolsWhenEmpty&&list.length===0?EmptyContent:React3__default.createElement(Wrapper3,{absolute,bordered,id:htmlId},React3__default.createElement(FlexBar,{scrollable:!1,border:!0,backgroundColor},React3__default.createElement(TabBar,{style:{whiteSpace:\"normal\"},ref:tabBarRef,role:\"tablist\"},visibleList.map(({title,id,active,color:color2},index3)=>{let indexId=`index-${index3}`;return React3__default.createElement(TabButton,{id:`tabbutton-${sanitize(id)??indexId}`,ref:ref=>{tabRefs.current.set(id,ref);},className:`tabbutton ${active?\"tabbutton-active\":\"\"}`,type:\"button\",key:id,active,textColor:color2,onClick:e=>{e.preventDefault(),actions.onSelect(id);},role:\"tab\"},typeof title==\"function\"?React3__default.createElement(\"title\",null):title)}),React3__default.createElement(AddonTab,{menuName,actions})),tools),React3__default.createElement(Content4,{id:\"panel-tab-content\",bordered,absolute},list.length?list.map(({id,active,render})=>React3__default.createElement(render,{key:id,active},null)):EmptyContent))});Tabs.displayName=\"Tabs\";var TabsState=class extends Component{constructor(props){super(props);this.handlers={onSelect:id=>this.setState({selected:id})};this.state={selected:props.initial};}render(){let{bordered=!1,absolute=!1,children,backgroundColor,menuName}=this.props,{selected}=this.state;return React3__default.createElement(Tabs,{bordered,absolute,selected,backgroundColor,menuName,actions:this.handlers},children)}};TabsState.defaultProps={children:[],initial:null,absolute:!1,bordered:!1,backgroundColor:\"\",menuName:void 0};var Separator=styled.span(({theme})=>({width:1,height:20,background:theme.appBorderColor,marginLeft:2,marginRight:2}),({force})=>force?{}:{\"& + &\":{display:\"none\"}});Separator.displayName=\"Separator\";var interleaveSeparators=list=>list.reduce((acc,item,index3)=>item?React3__default.createElement(Fragment,{key:item.id||item.key||`f-${index3}`},acc,index3>0?React3__default.createElement(Separator,{key:`s-${index3}`}):null,item.render()||item):acc,null);var usePrevious=value=>{let ref=useRef();return useEffect(()=>{ref.current=value;},[value]),ref.current},useUpdate=(update,value)=>{let previousValue=usePrevious(value);return update?value:previousValue},AddonPanel=({active,children})=>React3__default.createElement(\"div\",{hidden:!active},useUpdate(active,children));var NEW_ICON_MAP=StorybookIcons,Svg=styled.svg`\n  display: inline-block;\n  shape-rendering: inherit;\n  vertical-align: middle;\n  fill: currentColor;\n  path {\n    fill: currentColor;\n  }\n`,Icons=({icon,useSymbol,__suppressDeprecationWarning=!1,...props})=>{__suppressDeprecationWarning||deprecate(`Use of the deprecated Icons ${`(${icon})`||\"\"} component detected. Please use the @storybook/icons component directly. For more informations, see the migration notes at https://github.com/storybookjs/storybook/blob/next/MIGRATION.md#icons-is-deprecated`);let findIcon=icons[icon]||null;if(!findIcon)return logger.warn(`Use of an unknown prop ${`(${icon})`||\"\"} in the Icons component. The Icons component is deprecated. Please use the @storybook/icons component directly. For more informations, see the migration notes at https://github.com/storybookjs/storybook/blob/next/MIGRATION.md#icons-is-deprecated`),null;let Icon=NEW_ICON_MAP[findIcon];return React3__default.createElement(Icon,{...props})},Symbols=memo(function({icons:keys=Object.keys(icons)}){return React3__default.createElement(Svg,{viewBox:\"0 0 14 14\",style:{position:\"absolute\",width:0,height:0},\"data-chromatic\":\"ignore\"},keys.map(key=>React3__default.createElement(\"symbol\",{id:`icon--${key}`,key},icons[key])))}),icons={user:\"UserIcon\",useralt:\"UserAltIcon\",useradd:\"UserAddIcon\",users:\"UsersIcon\",profile:\"ProfileIcon\",facehappy:\"FaceHappyIcon\",faceneutral:\"FaceNeutralIcon\",facesad:\"FaceSadIcon\",accessibility:\"AccessibilityIcon\",accessibilityalt:\"AccessibilityAltIcon\",arrowup:\"ChevronUpIcon\",arrowdown:\"ChevronDownIcon\",arrowleft:\"ChevronLeftIcon\",arrowright:\"ChevronRightIcon\",arrowupalt:\"ArrowUpIcon\",arrowdownalt:\"ArrowDownIcon\",arrowleftalt:\"ArrowLeftIcon\",arrowrightalt:\"ArrowRightIcon\",expandalt:\"ExpandAltIcon\",collapse:\"CollapseIcon\",expand:\"ExpandIcon\",unfold:\"UnfoldIcon\",transfer:\"TransferIcon\",redirect:\"RedirectIcon\",undo:\"UndoIcon\",reply:\"ReplyIcon\",sync:\"SyncIcon\",upload:\"UploadIcon\",download:\"DownloadIcon\",back:\"BackIcon\",proceed:\"ProceedIcon\",refresh:\"RefreshIcon\",globe:\"GlobeIcon\",compass:\"CompassIcon\",location:\"LocationIcon\",pin:\"PinIcon\",time:\"TimeIcon\",dashboard:\"DashboardIcon\",timer:\"TimerIcon\",home:\"HomeIcon\",admin:\"AdminIcon\",info:\"InfoIcon\",question:\"QuestionIcon\",support:\"SupportIcon\",alert:\"AlertIcon\",email:\"EmailIcon\",phone:\"PhoneIcon\",link:\"LinkIcon\",unlink:\"LinkBrokenIcon\",bell:\"BellIcon\",rss:\"RSSIcon\",sharealt:\"ShareAltIcon\",share:\"ShareIcon\",circle:\"CircleIcon\",circlehollow:\"CircleHollowIcon\",bookmarkhollow:\"BookmarkHollowIcon\",bookmark:\"BookmarkIcon\",hearthollow:\"HeartHollowIcon\",heart:\"HeartIcon\",starhollow:\"StarHollowIcon\",star:\"StarIcon\",certificate:\"CertificateIcon\",verified:\"VerifiedIcon\",thumbsup:\"ThumbsUpIcon\",shield:\"ShieldIcon\",basket:\"BasketIcon\",beaker:\"BeakerIcon\",hourglass:\"HourglassIcon\",flag:\"FlagIcon\",cloudhollow:\"CloudHollowIcon\",edit:\"EditIcon\",cog:\"CogIcon\",nut:\"NutIcon\",wrench:\"WrenchIcon\",ellipsis:\"EllipsisIcon\",check:\"CheckIcon\",form:\"FormIcon\",batchdeny:\"BatchDenyIcon\",batchaccept:\"BatchAcceptIcon\",controls:\"ControlsIcon\",plus:\"PlusIcon\",closeAlt:\"CloseAltIcon\",cross:\"CrossIcon\",trash:\"TrashIcon\",pinalt:\"PinAltIcon\",unpin:\"UnpinIcon\",add:\"AddIcon\",subtract:\"SubtractIcon\",close:\"CloseIcon\",delete:\"DeleteIcon\",passed:\"PassedIcon\",changed:\"ChangedIcon\",failed:\"FailedIcon\",clear:\"ClearIcon\",comment:\"CommentIcon\",commentadd:\"CommentAddIcon\",requestchange:\"RequestChangeIcon\",comments:\"CommentsIcon\",lock:\"LockIcon\",unlock:\"UnlockIcon\",key:\"KeyIcon\",outbox:\"OutboxIcon\",credit:\"CreditIcon\",button:\"ButtonIcon\",type:\"TypeIcon\",pointerdefault:\"PointerDefaultIcon\",pointerhand:\"PointerHandIcon\",browser:\"BrowserIcon\",tablet:\"TabletIcon\",mobile:\"MobileIcon\",watch:\"WatchIcon\",sidebar:\"SidebarIcon\",sidebaralt:\"SidebarAltIcon\",sidebaralttoggle:\"SidebarAltToggleIcon\",sidebartoggle:\"SidebarToggleIcon\",bottombar:\"BottomBarIcon\",bottombartoggle:\"BottomBarToggleIcon\",cpu:\"CPUIcon\",database:\"DatabaseIcon\",memory:\"MemoryIcon\",structure:\"StructureIcon\",box:\"BoxIcon\",power:\"PowerIcon\",photo:\"PhotoIcon\",component:\"ComponentIcon\",grid:\"GridIcon\",outline:\"OutlineIcon\",photodrag:\"PhotoDragIcon\",search:\"SearchIcon\",zoom:\"ZoomIcon\",zoomout:\"ZoomOutIcon\",zoomreset:\"ZoomResetIcon\",eye:\"EyeIcon\",eyeclose:\"EyeCloseIcon\",lightning:\"LightningIcon\",lightningoff:\"LightningOffIcon\",contrast:\"ContrastIcon\",switchalt:\"SwitchAltIcon\",mirror:\"MirrorIcon\",grow:\"GrowIcon\",paintbrush:\"PaintBrushIcon\",ruler:\"RulerIcon\",stop:\"StopIcon\",camera:\"CameraIcon\",video:\"VideoIcon\",speaker:\"SpeakerIcon\",play:\"PlayIcon\",playback:\"PlayBackIcon\",playnext:\"PlayNextIcon\",rewind:\"RewindIcon\",fastforward:\"FastForwardIcon\",stopalt:\"StopAltIcon\",sidebyside:\"SideBySideIcon\",stacked:\"StackedIcon\",sun:\"SunIcon\",moon:\"MoonIcon\",book:\"BookIcon\",document:\"DocumentIcon\",copy:\"CopyIcon\",category:\"CategoryIcon\",folder:\"FolderIcon\",print:\"PrintIcon\",graphline:\"GraphLineIcon\",calendar:\"CalendarIcon\",graphbar:\"GraphBarIcon\",menu:\"MenuIcon\",menualt:\"MenuIcon\",filter:\"FilterIcon\",docchart:\"DocChartIcon\",doclist:\"DocListIcon\",markup:\"MarkupIcon\",bold:\"BoldIcon\",paperclip:\"PaperClipIcon\",listordered:\"ListOrderedIcon\",listunordered:\"ListUnorderedIcon\",paragraph:\"ParagraphIcon\",markdown:\"MarkdownIcon\",repository:\"RepoIcon\",commit:\"CommitIcon\",branch:\"BranchIcon\",pullrequest:\"PullRequestIcon\",merge:\"MergeIcon\",apple:\"AppleIcon\",linux:\"LinuxIcon\",ubuntu:\"UbuntuIcon\",windows:\"WindowsIcon\",storybook:\"StorybookIcon\",azuredevops:\"AzureDevOpsIcon\",bitbucket:\"BitbucketIcon\",chrome:\"ChromeIcon\",chromatic:\"ChromaticIcon\",componentdriven:\"ComponentDrivenIcon\",discord:\"DiscordIcon\",facebook:\"FacebookIcon\",figma:\"FigmaIcon\",gdrive:\"GDriveIcon\",github:\"GithubIcon\",gitlab:\"GitlabIcon\",google:\"GoogleIcon\",graphql:\"GraphqlIcon\",medium:\"MediumIcon\",redux:\"ReduxIcon\",twitter:\"TwitterIcon\",youtube:\"YoutubeIcon\",vscode:\"VSCodeIcon\"};var StorybookLogo=({alt,...props})=>React3__default.createElement(\"svg\",{width:\"200px\",height:\"40px\",viewBox:\"0 0 200 40\",...props,role:\"img\"},alt?React3__default.createElement(\"title\",null,alt):null,React3__default.createElement(\"defs\",null,React3__default.createElement(\"path\",{d:\"M1.2 36.9L0 3.9c0-1.1.8-2 1.9-2.1l28-1.8a2 2 0 0 1 2.2 1.9 2 2 0 0 1 0 .1v36a2 2 0 0 1-2 2 2 2 0 0 1-.1 0L3.2 38.8a2 2 0 0 1-2-2z\",id:\"a\"})),React3__default.createElement(\"g\",{fill:\"none\",fillRule:\"evenodd\"},React3__default.createElement(\"path\",{d:\"M53.3 31.7c-1.7 0-3.4-.3-5-.7-1.5-.5-2.8-1.1-3.9-2l1.6-3.5c2.2 1.5 4.6 2.3 7.3 2.3 1.5 0 2.5-.2 3.3-.7.7-.5 1.1-1 1.1-1.9 0-.7-.3-1.3-1-1.7s-2-.8-3.7-1.2c-2-.4-3.6-.9-4.8-1.5-1.1-.5-2-1.2-2.6-2-.5-1-.8-2-.8-3.2 0-1.4.4-2.6 1.2-3.6.7-1.1 1.8-2 3.2-2.6 1.3-.6 2.9-.9 4.7-.9 1.6 0 3.1.3 4.6.7 1.5.5 2.7 1.1 3.5 2l-1.6 3.5c-2-1.5-4.2-2.3-6.5-2.3-1.3 0-2.3.2-3 .8-.8.5-1.2 1.1-1.2 2 0 .5.2 1 .5 1.3.2.3.7.6 1.4.9l2.9.8c2.9.6 5 1.4 6.2 2.4a5 5 0 0 1 2 4.2 6 6 0 0 1-2.5 5c-1.7 1.2-4 1.9-7 1.9zm21-3.6l1.4-.1-.2 3.5-1.9.1c-2.4 0-4.1-.5-5.2-1.5-1.1-1-1.6-2.7-1.6-4.8v-6h-3v-3.6h3V11h4.8v4.6h4v3.6h-4v6c0 1.8.9 2.8 2.6 2.8zm11.1 3.5c-1.6 0-3-.3-4.3-1a7 7 0 0 1-3-2.8c-.6-1.3-1-2.7-1-4.4 0-1.6.4-3 1-4.3a7 7 0 0 1 3-2.8c1.2-.7 2.7-1 4.3-1 1.7 0 3.2.3 4.4 1a7 7 0 0 1 3 2.8c.6 1.2 1 2.7 1 4.3 0 1.7-.4 3.1-1 4.4a7 7 0 0 1-3 2.8c-1.2.7-2.7 1-4.4 1zm0-3.6c2.4 0 3.6-1.6 3.6-4.6 0-1.5-.3-2.6-1-3.4a3.2 3.2 0 0 0-2.6-1c-2.3 0-3.5 1.4-3.5 4.4 0 3 1.2 4.6 3.5 4.6zm21.7-8.8l-2.7.3c-1.3.2-2.3.5-2.8 1.2-.6.6-.9 1.4-.9 2.5v8.2H96V15.7h4.6v2.6c.8-1.8 2.5-2.8 5-3h1.3l.3 4zm14-3.5h4.8L116.4 37h-4.9l3-6.6-6.4-14.8h5l4 10 4-10zm16-.4c1.4 0 2.6.3 3.6 1 1 .6 1.9 1.6 2.5 2.8.6 1.2.9 2.7.9 4.3 0 1.6-.3 3-1 4.3a6.9 6.9 0 0 1-2.4 2.9c-1 .7-2.2 1-3.6 1-1 0-2-.2-3-.7-.8-.4-1.5-1-2-1.9v2.4h-4.7V8.8h4.8v9c.5-.8 1.2-1.4 2-1.9.9-.4 1.8-.6 3-.6zM135.7 28c1.1 0 2-.4 2.6-1.2.6-.8 1-2 1-3.4 0-1.5-.4-2.5-1-3.3s-1.5-1.1-2.6-1.1-2 .3-2.6 1.1c-.6.8-1 2-1 3.3 0 1.5.4 2.6 1 3.4.6.8 1.5 1.2 2.6 1.2zm18.9 3.6c-1.7 0-3.2-.3-4.4-1a7 7 0 0 1-3-2.8c-.6-1.3-1-2.7-1-4.4 0-1.6.4-3 1-4.3a7 7 0 0 1 3-2.8c1.2-.7 2.7-1 4.4-1 1.6 0 3 .3 4.3 1a7 7 0 0 1 3 2.8c.6 1.2 1 2.7 1 4.3 0 1.7-.4 3.1-1 4.4a7 7 0 0 1-3 2.8c-1.2.7-2.7 1-4.3 1zm0-3.6c2.3 0 3.5-1.6 3.5-4.6 0-1.5-.3-2.6-1-3.4a3.2 3.2 0 0 0-2.5-1c-2.4 0-3.6 1.4-3.6 4.4 0 3 1.2 4.6 3.6 4.6zm18 3.6c-1.7 0-3.2-.3-4.4-1a7 7 0 0 1-3-2.8c-.6-1.3-1-2.7-1-4.4 0-1.6.4-3 1-4.3a7 7 0 0 1 3-2.8c1.2-.7 2.7-1 4.4-1 1.6 0 3 .3 4.4 1a7 7 0 0 1 2.9 2.8c.6 1.2 1 2.7 1 4.3 0 1.7-.4 3.1-1 4.4a7 7 0 0 1-3 2.8c-1.2.7-2.7 1-4.3 1zm0-3.6c2.3 0 3.5-1.6 3.5-4.6 0-1.5-.3-2.6-1-3.4a3.2 3.2 0 0 0-2.5-1c-2.4 0-3.6 1.4-3.6 4.4 0 3 1.2 4.6 3.6 4.6zm27.4 3.4h-6l-6-7v7h-4.8V8.8h4.9v13.6l5.8-6.7h5.7l-6.6 7.5 7 8.2z\",fill:\"currentColor\"}),React3__default.createElement(\"mask\",{id:\"b\",fill:\"#fff\"},React3__default.createElement(\"use\",{xlinkHref:\"#a\"})),React3__default.createElement(\"use\",{fill:\"#FF4785\",fillRule:\"nonzero\",xlinkHref:\"#a\"}),React3__default.createElement(\"path\",{d:\"M23.7 5L24 .2l3.9-.3.1 4.8a.3.3 0 0 1-.5.2L26 3.8l-1.7 1.4a.3.3 0 0 1-.5-.3zm-5 10c0 .9 5.3.5 6 0 0-5.4-2.8-8.2-8-8.2-5.3 0-8.2 2.8-8.2 7.1 0 7.4 10 7.6 10 11.6 0 1.2-.5 1.9-1.8 1.9-1.6 0-2.2-.9-2.1-3.6 0-.6-6.1-.8-6.3 0-.5 6.7 3.7 8.6 8.5 8.6 4.6 0 8.3-2.5 8.3-7 0-7.9-10.2-7.7-10.2-11.6 0-1.6 1.2-1.8 2-1.8.6 0 2 0 1.9 3z\",fill:\"#FFF\",fillRule:\"nonzero\",mask:\"url(#b)\"})));var StorybookIcon=props=>React3__default.createElement(\"svg\",{viewBox:\"0 0 64 64\",...props},React3__default.createElement(\"title\",null,\"Storybook icon\"),React3__default.createElement(\"g\",{id:\"Artboard\",stroke:\"none\",strokeWidth:\"1\",fill:\"none\",fillRule:\"evenodd\"},React3__default.createElement(\"path\",{d:\"M8.04798541,58.7875918 L6.07908839,6.32540407 C6.01406344,4.5927838 7.34257463,3.12440831 9.07303814,3.01625434 L53.6958037,0.227331489 C55.457209,0.117243658 56.974354,1.45590096 57.0844418,3.21730626 C57.0885895,3.28366922 57.0906648,3.35014546 57.0906648,3.41663791 L57.0906648,60.5834697 C57.0906648,62.3483119 55.6599776,63.7789992 53.8951354,63.7789992 C53.847325,63.7789992 53.7995207,63.7779262 53.7517585,63.775781 L11.0978899,61.8600599 C9.43669044,61.7854501 8.11034889,60.4492961 8.04798541,58.7875918 Z\",id:\"path-1\",fill:\"#FF4785\",fillRule:\"nonzero\"}),React3__default.createElement(\"path\",{d:\"M35.9095005,24.1768792 C35.9095005,25.420127 44.2838488,24.8242707 45.4080313,23.9509748 C45.4080313,15.4847538 40.8652557,11.0358878 32.5466666,11.0358878 C24.2280775,11.0358878 19.5673077,15.553972 19.5673077,22.3311017 C19.5673077,34.1346028 35.4965208,34.3605071 35.4965208,40.7987804 C35.4965208,42.606015 34.6115646,43.6790606 32.6646607,43.6790606 C30.127786,43.6790606 29.1248356,42.3834613 29.2428298,37.9783269 C29.2428298,37.0226907 19.5673077,36.7247626 19.2723223,37.9783269 C18.5211693,48.6535354 25.1720308,51.7326752 32.7826549,51.7326752 C40.1572906,51.7326752 45.939005,47.8018145 45.939005,40.6858282 C45.939005,28.035186 29.7738035,28.3740425 29.7738035,22.1051974 C29.7738035,19.5637737 31.6617103,19.2249173 32.7826549,19.2249173 C33.9625966,19.2249173 36.0864917,19.4328883 35.9095005,24.1768792 Z\",id:\"path9_fill-path\",fill:\"#FFFFFF\",fillRule:\"nonzero\"}),React3__default.createElement(\"path\",{d:\"M44.0461638,0.830433986 L50.1874092,0.446606143 L50.443532,7.7810017 C50.4527198,8.04410717 50.2468789,8.26484453 49.9837734,8.27403237 C49.871115,8.27796649 49.7607078,8.24184808 49.6721567,8.17209069 L47.3089847,6.3104681 L44.5110468,8.43287463 C44.3012992,8.591981 44.0022839,8.55092814 43.8431776,8.34118051 C43.7762017,8.25288717 43.742082,8.14401677 43.7466857,8.03329059 L44.0461638,0.830433986 Z\",id:\"Path\",fill:\"#FFFFFF\"})));var rotate360=keyframes`\n\tfrom {\n\t\ttransform: rotate(0deg);\n\t}\n\tto {\n\t\ttransform: rotate(360deg);\n\t}\n`;var LoaderWrapper=styled.div(({size=32})=>({borderRadius:\"50%\",cursor:\"progress\",display:\"inline-block\",overflow:\"hidden\",position:\"absolute\",transition:\"all 200ms ease-out\",verticalAlign:\"top\",top:\"50%\",left:\"50%\",marginTop:-(size/2),marginLeft:-(size/2),height:size,width:size,zIndex:4,borderWidth:2,borderStyle:\"solid\",borderColor:\"rgba(97, 97, 97, 0.29)\",borderTopColor:\"rgb(100,100,100)\",animation:`${rotate360} 0.7s linear infinite`,mixBlendMode:\"difference\"})),ProgressWrapper=styled.div({position:\"absolute\",display:\"flex\",flexDirection:\"column\",justifyContent:\"center\",alignItems:\"center\",width:\"100%\",height:\"100%\"}),ProgressTrack=styled.div(({theme})=>({position:\"relative\",width:\"80%\",marginBottom:\"0.75rem\",maxWidth:300,height:5,borderRadius:5,background:curriedTransparentize$1(.8,theme.color.secondary),overflow:\"hidden\",cursor:\"progress\"})),ProgressBar=styled.div(({theme})=>({position:\"absolute\",top:0,left:0,height:\"100%\",background:theme.color.secondary})),ProgressMessage=styled.div(({theme})=>({minHeight:\"2em\",fontSize:`${theme.typography.size.s1}px`,color:theme.barTextColor})),ErrorIcon=styled(LightningOffIcon)(({theme})=>({width:20,height:20,marginBottom:\"0.5rem\",color:theme.textMutedColor})),ellipsis=keyframes`\n  from { content: \"...\" }\n  33% { content: \".\" }\n  66% { content: \"..\" }\n  to { content: \"...\" }\n`,Ellipsis=styled.span({\"&::after\":{content:\"'...'\",animation:`${ellipsis} 1s linear infinite`,animationDelay:\"1s\",display:\"inline-block\",width:\"1em\",height:\"auto\"}}),Loader=({progress,error,size,...props})=>{if(error)return React3__default.createElement(ProgressWrapper,{\"aria-label\":error.toString(),\"aria-live\":\"polite\",role:\"status\",...props},React3__default.createElement(ErrorIcon,null),React3__default.createElement(ProgressMessage,null,error.message));if(progress){let{value,modules}=progress,{message}=progress;return modules&&(message+=` ${modules.complete} / ${modules.total} modules`),React3__default.createElement(ProgressWrapper,{\"aria-label\":\"Content is loading...\",\"aria-live\":\"polite\",\"aria-valuemin\":0,\"aria-valuemax\":100,\"aria-valuenow\":value*100,\"aria-valuetext\":message,role:\"progressbar\",...props},React3__default.createElement(ProgressTrack,null,React3__default.createElement(ProgressBar,{style:{width:`${value*100}%`}})),React3__default.createElement(ProgressMessage,null,message,value<1&&React3__default.createElement(Ellipsis,{key:message})))}return React3__default.createElement(LoaderWrapper,{\"aria-label\":\"Content is loading...\",\"aria-live\":\"polite\",role:\"status\",size,...props})};function parseQuery(queryString){let query={},pairs=queryString.split(\"&\");for(let i=0;i<pairs.length;i++){let pair=pairs[i].split(\"=\");query[decodeURIComponent(pair[0])]=decodeURIComponent(pair[1]||\"\");}return query}var getStoryHref=(baseUrl,storyId,additionalParams={})=>{let[url,paramsStr]=baseUrl.split(\"?\"),params=paramsStr?{...parseQuery(paramsStr),...additionalParams,id:storyId}:{...additionalParams,id:storyId};return `${url}?${Object.entries(params).map(item=>`${item[0]}=${item[1]}`).join(\"&\")}`};var Code2=styled.pre`\n  line-height: 18px;\n  padding: 11px 1rem;\n  white-space: pre-wrap;\n  background: rgba(0, 0, 0, 0.05);\n  color: ${color.darkest};\n  border-radius: 3px;\n  margin: 1rem 0;\n  width: 100%;\n  display: block;\n  overflow: hidden;\n  font-family: ${typography.fonts.mono};\n  font-size: ${typography.size.s2-1}px;\n`,ClipboardCode=({code,...props})=>React3__default.createElement(Code2,{id:\"clipboard-code\",...props},code);var components2=components,resetComponents={};Object.keys(components).forEach(key=>{resetComponents[key]=forwardRef((props,ref)=>createElement(key,{...props,ref}));});\n\nexport { A, AddonPanel, Badge, Bar, Blockquote, Button, ClipboardCode, Code, DL, Div, DocumentWrapper, EmptyTabContent, ErrorFormatter, FlexBar, Form, H1, H2, H3, H4, H5, H6, HR, IconButton, IconButtonSkeleton, Icons, Img, LI, Link2 as Link, ListItem_default as ListItem, Loader, Modal, OL, P, Placeholder, Pre, ResetWrapper, Separator, Spaced, Span, StorybookIcon, StorybookLogo, Symbols, SyntaxHighlighter2 as SyntaxHighlighter, TT, TabBar, TabButton, TabWrapper, Table, Tabs, TabsState, TooltipLinkList, TooltipMessage, TooltipNote, UL, WithTooltip, WithTooltipPure, Zoom, codeCommon, components2 as components, getStoryHref, icons, interleaveSeparators, nameSpaceClassNames, resetComponents, withReset };\n", "import { __commonJS, __toESM, _extends, background, typography, curriedOpacify$1, themes, getPreferredColorScheme, light_default, color, mkColor } from './chunk-QN4WKJDJ.mjs';\nexport { background, color, create, darkenColor as darken, lightenColor as lighten, themes, typography } from './chunk-QN4WKJDJ.mjs';\nimport * as React from 'react';\nimport { forwardRef, useContext } from 'react';\nimport { useInsertionEffectWithLayoutFallback, useInsertionEffectAlwaysWithSyncFallback } from '@emotion/use-insertion-effect-with-fallbacks';\nimport memoize2 from 'memoizerific';\nimport { logger } from '@storybook/client-logger';\n\nvar require_react_is_development=__commonJS({\"../../node_modules/react-is/cjs/react-is.development.js\"(exports){(function(){var hasSymbol=typeof Symbol==\"function\"&&Symbol.for,REACT_ELEMENT_TYPE=hasSymbol?Symbol.for(\"react.element\"):60103,REACT_PORTAL_TYPE=hasSymbol?Symbol.for(\"react.portal\"):60106,REACT_FRAGMENT_TYPE=hasSymbol?Symbol.for(\"react.fragment\"):60107,REACT_STRICT_MODE_TYPE=hasSymbol?Symbol.for(\"react.strict_mode\"):60108,REACT_PROFILER_TYPE=hasSymbol?Symbol.for(\"react.profiler\"):60114,REACT_PROVIDER_TYPE=hasSymbol?Symbol.for(\"react.provider\"):60109,REACT_CONTEXT_TYPE=hasSymbol?Symbol.for(\"react.context\"):60110,REACT_ASYNC_MODE_TYPE=hasSymbol?Symbol.for(\"react.async_mode\"):60111,REACT_CONCURRENT_MODE_TYPE=hasSymbol?Symbol.for(\"react.concurrent_mode\"):60111,REACT_FORWARD_REF_TYPE=hasSymbol?Symbol.for(\"react.forward_ref\"):60112,REACT_SUSPENSE_TYPE=hasSymbol?Symbol.for(\"react.suspense\"):60113,REACT_SUSPENSE_LIST_TYPE=hasSymbol?Symbol.for(\"react.suspense_list\"):60120,REACT_MEMO_TYPE=hasSymbol?Symbol.for(\"react.memo\"):60115,REACT_LAZY_TYPE=hasSymbol?Symbol.for(\"react.lazy\"):60116,REACT_BLOCK_TYPE=hasSymbol?Symbol.for(\"react.block\"):60121,REACT_FUNDAMENTAL_TYPE=hasSymbol?Symbol.for(\"react.fundamental\"):60117,REACT_RESPONDER_TYPE=hasSymbol?Symbol.for(\"react.responder\"):60118,REACT_SCOPE_TYPE=hasSymbol?Symbol.for(\"react.scope\"):60119;function isValidElementType(type){return typeof type==\"string\"||typeof type==\"function\"||type===REACT_FRAGMENT_TYPE||type===REACT_CONCURRENT_MODE_TYPE||type===REACT_PROFILER_TYPE||type===REACT_STRICT_MODE_TYPE||type===REACT_SUSPENSE_TYPE||type===REACT_SUSPENSE_LIST_TYPE||typeof type==\"object\"&&type!==null&&(type.$$typeof===REACT_LAZY_TYPE||type.$$typeof===REACT_MEMO_TYPE||type.$$typeof===REACT_PROVIDER_TYPE||type.$$typeof===REACT_CONTEXT_TYPE||type.$$typeof===REACT_FORWARD_REF_TYPE||type.$$typeof===REACT_FUNDAMENTAL_TYPE||type.$$typeof===REACT_RESPONDER_TYPE||type.$$typeof===REACT_SCOPE_TYPE||type.$$typeof===REACT_BLOCK_TYPE)}function typeOf(object){if(typeof object==\"object\"&&object!==null){var $$typeof=object.$$typeof;switch($$typeof){case REACT_ELEMENT_TYPE:var type=object.type;switch(type){case REACT_ASYNC_MODE_TYPE:case REACT_CONCURRENT_MODE_TYPE:case REACT_FRAGMENT_TYPE:case REACT_PROFILER_TYPE:case REACT_STRICT_MODE_TYPE:case REACT_SUSPENSE_TYPE:return type;default:var $$typeofType=type&&type.$$typeof;switch($$typeofType){case REACT_CONTEXT_TYPE:case REACT_FORWARD_REF_TYPE:case REACT_LAZY_TYPE:case REACT_MEMO_TYPE:case REACT_PROVIDER_TYPE:return $$typeofType;default:return $$typeof}}case REACT_PORTAL_TYPE:return $$typeof}}}var AsyncMode=REACT_ASYNC_MODE_TYPE,ConcurrentMode=REACT_CONCURRENT_MODE_TYPE,ContextConsumer=REACT_CONTEXT_TYPE,ContextProvider=REACT_PROVIDER_TYPE,Element=REACT_ELEMENT_TYPE,ForwardRef=REACT_FORWARD_REF_TYPE,Fragment4=REACT_FRAGMENT_TYPE,Lazy=REACT_LAZY_TYPE,Memo=REACT_MEMO_TYPE,Portal=REACT_PORTAL_TYPE,Profiler=REACT_PROFILER_TYPE,StrictMode=REACT_STRICT_MODE_TYPE,Suspense=REACT_SUSPENSE_TYPE,hasWarnedAboutDeprecatedIsAsyncMode=!1;function isAsyncMode(object){return hasWarnedAboutDeprecatedIsAsyncMode||(hasWarnedAboutDeprecatedIsAsyncMode=!0,console.warn(\"The ReactIs.isAsyncMode() alias has been deprecated, and will be removed in React 17+. Update your code to use ReactIs.isConcurrentMode() instead. It has the exact same API.\")),isConcurrentMode(object)||typeOf(object)===REACT_ASYNC_MODE_TYPE}function isConcurrentMode(object){return typeOf(object)===REACT_CONCURRENT_MODE_TYPE}function isContextConsumer(object){return typeOf(object)===REACT_CONTEXT_TYPE}function isContextProvider(object){return typeOf(object)===REACT_PROVIDER_TYPE}function isElement(object){return typeof object==\"object\"&&object!==null&&object.$$typeof===REACT_ELEMENT_TYPE}function isForwardRef(object){return typeOf(object)===REACT_FORWARD_REF_TYPE}function isFragment(object){return typeOf(object)===REACT_FRAGMENT_TYPE}function isLazy(object){return typeOf(object)===REACT_LAZY_TYPE}function isMemo(object){return typeOf(object)===REACT_MEMO_TYPE}function isPortal(object){return typeOf(object)===REACT_PORTAL_TYPE}function isProfiler(object){return typeOf(object)===REACT_PROFILER_TYPE}function isStrictMode(object){return typeOf(object)===REACT_STRICT_MODE_TYPE}function isSuspense(object){return typeOf(object)===REACT_SUSPENSE_TYPE}exports.AsyncMode=AsyncMode,exports.ConcurrentMode=ConcurrentMode,exports.ContextConsumer=ContextConsumer,exports.ContextProvider=ContextProvider,exports.Element=Element,exports.ForwardRef=ForwardRef,exports.Fragment=Fragment4,exports.Lazy=Lazy,exports.Memo=Memo,exports.Portal=Portal,exports.Profiler=Profiler,exports.StrictMode=StrictMode,exports.Suspense=Suspense,exports.isAsyncMode=isAsyncMode,exports.isConcurrentMode=isConcurrentMode,exports.isContextConsumer=isContextConsumer,exports.isContextProvider=isContextProvider,exports.isElement=isElement,exports.isForwardRef=isForwardRef,exports.isFragment=isFragment,exports.isLazy=isLazy,exports.isMemo=isMemo,exports.isPortal=isPortal,exports.isProfiler=isProfiler,exports.isStrictMode=isStrictMode,exports.isSuspense=isSuspense,exports.isValidElementType=isValidElementType,exports.typeOf=typeOf;})();}});var require_react_is=__commonJS({\"../../node_modules/react-is/index.js\"(exports,module){module.exports=require_react_is_development();}});var require_hoist_non_react_statics_cjs=__commonJS({\"../../node_modules/hoist-non-react-statics/dist/hoist-non-react-statics.cjs.js\"(exports,module){var reactIs=require_react_is(),REACT_STATICS={childContextTypes:!0,contextType:!0,contextTypes:!0,defaultProps:!0,displayName:!0,getDefaultProps:!0,getDerivedStateFromError:!0,getDerivedStateFromProps:!0,mixins:!0,propTypes:!0,type:!0},KNOWN_STATICS={name:!0,length:!0,prototype:!0,caller:!0,callee:!0,arguments:!0,arity:!0},FORWARD_REF_STATICS={$$typeof:!0,render:!0,defaultProps:!0,displayName:!0,propTypes:!0},MEMO_STATICS={$$typeof:!0,compare:!0,defaultProps:!0,displayName:!0,propTypes:!0,type:!0},TYPE_STATICS={};TYPE_STATICS[reactIs.ForwardRef]=FORWARD_REF_STATICS;TYPE_STATICS[reactIs.Memo]=MEMO_STATICS;function getStatics(component){return reactIs.isMemo(component)?MEMO_STATICS:TYPE_STATICS[component.$$typeof]||REACT_STATICS}var defineProperty=Object.defineProperty,getOwnPropertyNames=Object.getOwnPropertyNames,getOwnPropertySymbols=Object.getOwnPropertySymbols,getOwnPropertyDescriptor=Object.getOwnPropertyDescriptor,getPrototypeOf=Object.getPrototypeOf,objectPrototype=Object.prototype;function hoistNonReactStatics2(targetComponent,sourceComponent,blacklist){if(typeof sourceComponent!=\"string\"){if(objectPrototype){var inheritedComponent=getPrototypeOf(sourceComponent);inheritedComponent&&inheritedComponent!==objectPrototype&&hoistNonReactStatics2(targetComponent,inheritedComponent,blacklist);}var keys=getOwnPropertyNames(sourceComponent);getOwnPropertySymbols&&(keys=keys.concat(getOwnPropertySymbols(sourceComponent)));for(var targetStatics=getStatics(targetComponent),sourceStatics=getStatics(sourceComponent),i=0;i<keys.length;++i){var key=keys[i];if(!KNOWN_STATICS[key]&&!(blacklist&&blacklist[key])&&!(sourceStatics&&sourceStatics[key])&&!(targetStatics&&targetStatics[key])){var descriptor=getOwnPropertyDescriptor(sourceComponent,key);try{defineProperty(targetComponent,key,descriptor);}catch{}}}}return targetComponent}module.exports=hoistNonReactStatics2;}});function memoize(fn){var cache=Object.create(null);return function(arg){return cache[arg]===void 0&&(cache[arg]=fn(arg)),cache[arg]}}var reactPropsRegex=/^((children|dangerouslySetInnerHTML|key|ref|autoFocus|defaultValue|defaultChecked|innerHTML|suppressContentEditableWarning|suppressHydrationWarning|valueLink|abbr|accept|acceptCharset|accessKey|action|allow|allowUserMedia|allowPaymentRequest|allowFullScreen|allowTransparency|alt|async|autoComplete|autoPlay|capture|cellPadding|cellSpacing|challenge|charSet|checked|cite|classID|className|cols|colSpan|content|contentEditable|contextMenu|controls|controlsList|coords|crossOrigin|data|dateTime|decoding|default|defer|dir|disabled|disablePictureInPicture|disableRemotePlayback|download|draggable|encType|enterKeyHint|form|formAction|formEncType|formMethod|formNoValidate|formTarget|frameBorder|headers|height|hidden|high|href|hrefLang|htmlFor|httpEquiv|id|inputMode|integrity|is|keyParams|keyType|kind|label|lang|list|loading|loop|low|marginHeight|marginWidth|max|maxLength|media|mediaGroup|method|min|minLength|multiple|muted|name|nonce|noValidate|open|optimum|pattern|placeholder|playsInline|poster|preload|profile|radioGroup|readOnly|referrerPolicy|rel|required|reversed|role|rows|rowSpan|sandbox|scope|scoped|scrolling|seamless|selected|shape|size|sizes|slot|span|spellCheck|src|srcDoc|srcLang|srcSet|start|step|style|summary|tabIndex|target|title|translate|type|useMap|value|width|wmode|wrap|about|datatype|inlist|prefix|property|resource|typeof|vocab|autoCapitalize|autoCorrect|autoSave|color|incremental|fallback|inert|itemProp|itemScope|itemType|itemID|itemRef|on|option|results|security|unselectable|accentHeight|accumulate|additive|alignmentBaseline|allowReorder|alphabetic|amplitude|arabicForm|ascent|attributeName|attributeType|autoReverse|azimuth|baseFrequency|baselineShift|baseProfile|bbox|begin|bias|by|calcMode|capHeight|clip|clipPathUnits|clipPath|clipRule|colorInterpolation|colorInterpolationFilters|colorProfile|colorRendering|contentScriptType|contentStyleType|cursor|cx|cy|d|decelerate|descent|diffuseConstant|direction|display|divisor|dominantBaseline|dur|dx|dy|edgeMode|elevation|enableBackground|end|exponent|externalResourcesRequired|fill|fillOpacity|fillRule|filter|filterRes|filterUnits|floodColor|floodOpacity|focusable|fontFamily|fontSize|fontSizeAdjust|fontStretch|fontStyle|fontVariant|fontWeight|format|from|fr|fx|fy|g1|g2|glyphName|glyphOrientationHorizontal|glyphOrientationVertical|glyphRef|gradientTransform|gradientUnits|hanging|horizAdvX|horizOriginX|ideographic|imageRendering|in|in2|intercept|k|k1|k2|k3|k4|kernelMatrix|kernelUnitLength|kerning|keyPoints|keySplines|keyTimes|lengthAdjust|letterSpacing|lightingColor|limitingConeAngle|local|markerEnd|markerMid|markerStart|markerHeight|markerUnits|markerWidth|mask|maskContentUnits|maskUnits|mathematical|mode|numOctaves|offset|opacity|operator|order|orient|orientation|origin|overflow|overlinePosition|overlineThickness|panose1|paintOrder|pathLength|patternContentUnits|patternTransform|patternUnits|pointerEvents|points|pointsAtX|pointsAtY|pointsAtZ|preserveAlpha|preserveAspectRatio|primitiveUnits|r|radius|refX|refY|renderingIntent|repeatCount|repeatDur|requiredExtensions|requiredFeatures|restart|result|rotate|rx|ry|scale|seed|shapeRendering|slope|spacing|specularConstant|specularExponent|speed|spreadMethod|startOffset|stdDeviation|stemh|stemv|stitchTiles|stopColor|stopOpacity|strikethroughPosition|strikethroughThickness|string|stroke|strokeDasharray|strokeDashoffset|strokeLinecap|strokeLinejoin|strokeMiterlimit|strokeOpacity|strokeWidth|surfaceScale|systemLanguage|tableValues|targetX|targetY|textAnchor|textDecoration|textRendering|textLength|to|transform|u1|u2|underlinePosition|underlineThickness|unicode|unicodeBidi|unicodeRange|unitsPerEm|vAlphabetic|vHanging|vIdeographic|vMathematical|values|vectorEffect|version|vertAdvY|vertOriginX|vertOriginY|viewBox|viewTarget|visibility|widths|wordSpacing|writingMode|x|xHeight|x1|x2|xChannelSelector|xlinkActuate|xlinkArcrole|xlinkHref|xlinkRole|xlinkShow|xlinkTitle|xlinkType|xmlBase|xmlns|xmlnsXlink|xmlLang|xmlSpace|y|y1|y2|yChannelSelector|z|zoomAndPan|for|class|autofocus)|(([Dd][Aa][Tt][Aa]|[Aa][Rr][Ii][Aa]|x)-.*))$/,isPropValid=memoize(function(prop){return reactPropsRegex.test(prop)||prop.charCodeAt(0)===111&&prop.charCodeAt(1)===110&&prop.charCodeAt(2)<91});function sheetForTag(tag){if(tag.sheet)return tag.sheet;for(var i=0;i<document.styleSheets.length;i++)if(document.styleSheets[i].ownerNode===tag)return document.styleSheets[i]}function createStyleElement(options){var tag=document.createElement(\"style\");return tag.setAttribute(\"data-emotion\",options.key),options.nonce!==void 0&&tag.setAttribute(\"nonce\",options.nonce),tag.appendChild(document.createTextNode(\"\")),tag.setAttribute(\"data-s\",\"\"),tag}var StyleSheet=function(){function StyleSheet2(options){var _this=this;this._insertTag=function(tag){var before;_this.tags.length===0?_this.insertionPoint?before=_this.insertionPoint.nextSibling:_this.prepend?before=_this.container.firstChild:before=_this.before:before=_this.tags[_this.tags.length-1].nextSibling,_this.container.insertBefore(tag,before),_this.tags.push(tag);},this.isSpeedy=options.speedy===void 0?!1:options.speedy,this.tags=[],this.ctr=0,this.nonce=options.nonce,this.key=options.key,this.container=options.container,this.prepend=options.prepend,this.insertionPoint=options.insertionPoint,this.before=null;}var _proto=StyleSheet2.prototype;return _proto.hydrate=function(nodes){nodes.forEach(this._insertTag);},_proto.insert=function(rule){this.ctr%(this.isSpeedy?65e3:1)===0&&this._insertTag(createStyleElement(this));var tag=this.tags[this.tags.length-1],isImportRule3=rule.charCodeAt(0)===64&&rule.charCodeAt(1)===105;if(isImportRule3&&this._alreadyInsertedOrderInsensitiveRule&&console.error(`You're attempting to insert the following rule:\n`+rule+\"\\n\\n`@import` rules must be before all other types of rules in a stylesheet but other rules have already been inserted. Please ensure that `@import` rules are before all other rules.\"),this._alreadyInsertedOrderInsensitiveRule=this._alreadyInsertedOrderInsensitiveRule||!isImportRule3,this.isSpeedy){var sheet=sheetForTag(tag);try{sheet.insertRule(rule,sheet.cssRules.length);}catch(e){/:(-moz-placeholder|-moz-focus-inner|-moz-focusring|-ms-input-placeholder|-moz-read-write|-moz-read-only|-ms-clear|-ms-expand|-ms-reveal){/.test(rule)||console.error('There was a problem inserting the following rule: \"'+rule+'\"',e);}}else tag.appendChild(document.createTextNode(rule));this.ctr++;},_proto.flush=function(){this.tags.forEach(function(tag){return tag.parentNode&&tag.parentNode.removeChild(tag)}),this.tags=[],this.ctr=0,this._alreadyInsertedOrderInsensitiveRule=!1;},StyleSheet2}();var MS=\"-ms-\",MOZ=\"-moz-\",WEBKIT=\"-webkit-\",COMMENT=\"comm\",RULESET=\"rule\",DECLARATION=\"decl\";var IMPORT=\"@import\";var KEYFRAMES=\"@keyframes\";var LAYER=\"@layer\";var abs=Math.abs,from=String.fromCharCode,assign=Object.assign;function hash(value,length2){return charat(value,0)^45?(((length2<<2^charat(value,0))<<2^charat(value,1))<<2^charat(value,2))<<2^charat(value,3):0}function trim(value){return value.trim()}function match(value,pattern){return (value=pattern.exec(value))?value[0]:value}function replace(value,pattern,replacement){return value.replace(pattern,replacement)}function indexof(value,search){return value.indexOf(search)}function charat(value,index){return value.charCodeAt(index)|0}function substr(value,begin,end){return value.slice(begin,end)}function strlen(value){return value.length}function sizeof(value){return value.length}function append(value,array){return array.push(value),value}function combine(array,callback){return array.map(callback).join(\"\")}var line=1,column=1,length=0,position=0,character=0,characters=\"\";function node(value,root,parent,type,props,children,length2){return {value,root,parent,type,props,children,line,column,length:length2,return:\"\"}}function copy(root,props){return assign(node(\"\",null,null,\"\",null,null,0),root,{length:-root.length},props)}function char(){return character}function prev(){return character=position>0?charat(characters,--position):0,column--,character===10&&(column=1,line--),character}function next(){return character=position<length?charat(characters,position++):0,column++,character===10&&(column=1,line++),character}function peek(){return charat(characters,position)}function caret(){return position}function slice(begin,end){return substr(characters,begin,end)}function token(type){switch(type){case 0:case 9:case 10:case 13:case 32:return 5;case 33:case 43:case 44:case 47:case 62:case 64:case 126:case 59:case 123:case 125:return 4;case 58:return 3;case 34:case 39:case 40:case 91:return 2;case 41:case 93:return 1}return 0}function alloc(value){return line=column=1,length=strlen(characters=value),position=0,[]}function dealloc(value){return characters=\"\",value}function delimit(type){return trim(slice(position-1,delimiter(type===91?type+2:type===40?type+1:type)))}function whitespace(type){for(;(character=peek())&&character<33;)next();return token(type)>2||token(character)>3?\"\":\" \"}function escaping(index,count){for(;--count&&next()&&!(character<48||character>102||character>57&&character<65||character>70&&character<97););return slice(index,caret()+(count<6&&peek()==32&&next()==32))}function delimiter(type){for(;next();)switch(character){case type:return position;case 34:case 39:type!==34&&type!==39&&delimiter(character);break;case 40:type===41&&delimiter(type);break;case 92:next();break}return position}function commenter(type,index){for(;next()&&type+character!==57;)if(type+character===84&&peek()===47)break;return \"/*\"+slice(index,position-1)+\"*\"+from(type===47?type:next())}function identifier(index){for(;!token(peek());)next();return slice(index,position)}function compile(value){return dealloc(parse(\"\",null,null,null,[\"\"],value=alloc(value),0,[0],value))}function parse(value,root,parent,rule,rules,rulesets,pseudo,points,declarations){for(var index=0,offset=0,length2=pseudo,atrule=0,property=0,previous=0,variable=1,scanning=1,ampersand=1,character2=0,type=\"\",props=rules,children=rulesets,reference=rule,characters2=type;scanning;)switch(previous=character2,character2=next()){case 40:if(previous!=108&&charat(characters2,length2-1)==58){indexof(characters2+=replace(delimit(character2),\"&\",\"&\\f\"),\"&\\f\")!=-1&&(ampersand=-1);break}case 34:case 39:case 91:characters2+=delimit(character2);break;case 9:case 10:case 13:case 32:characters2+=whitespace(previous);break;case 92:characters2+=escaping(caret()-1,7);continue;case 47:switch(peek()){case 42:case 47:append(comment(commenter(next(),caret()),root,parent),declarations);break;default:characters2+=\"/\";}break;case 123*variable:points[index++]=strlen(characters2)*ampersand;case 125*variable:case 59:case 0:switch(character2){case 0:case 125:scanning=0;case 59+offset:ampersand==-1&&(characters2=replace(characters2,/\\f/g,\"\")),property>0&&strlen(characters2)-length2&&append(property>32?declaration(characters2+\";\",rule,parent,length2-1):declaration(replace(characters2,\" \",\"\")+\";\",rule,parent,length2-2),declarations);break;case 59:characters2+=\";\";default:if(append(reference=ruleset(characters2,root,parent,index,offset,rules,points,type,props=[],children=[],length2),rulesets),character2===123)if(offset===0)parse(characters2,root,reference,reference,props,rulesets,length2,points,children);else switch(atrule===99&&charat(characters2,3)===110?100:atrule){case 100:case 108:case 109:case 115:parse(value,reference,reference,rule&&append(ruleset(value,reference,reference,0,0,rules,points,type,rules,props=[],length2),children),rules,children,length2,points,rule?props:children);break;default:parse(characters2,reference,reference,reference,[\"\"],children,0,points,children);}}index=offset=property=0,variable=ampersand=1,type=characters2=\"\",length2=pseudo;break;case 58:length2=1+strlen(characters2),property=previous;default:if(variable<1){if(character2==123)--variable;else if(character2==125&&variable++==0&&prev()==125)continue}switch(characters2+=from(character2),character2*variable){case 38:ampersand=offset>0?1:(characters2+=\"\\f\",-1);break;case 44:points[index++]=(strlen(characters2)-1)*ampersand,ampersand=1;break;case 64:peek()===45&&(characters2+=delimit(next())),atrule=peek(),offset=length2=strlen(type=characters2+=identifier(caret())),character2++;break;case 45:previous===45&&strlen(characters2)==2&&(variable=0);}}return rulesets}function ruleset(value,root,parent,index,offset,rules,points,type,props,children,length2){for(var post=offset-1,rule=offset===0?rules:[\"\"],size=sizeof(rule),i=0,j=0,k=0;i<index;++i)for(var x=0,y=substr(value,post+1,post=abs(j=points[i])),z=value;x<size;++x)(z=trim(j>0?rule[x]+\" \"+y:replace(y,/&\\f/g,rule[x])))&&(props[k++]=z);return node(value,root,parent,offset===0?RULESET:type,props,children,length2)}function comment(value,root,parent){return node(value,root,parent,COMMENT,from(char()),substr(value,2,-2),0)}function declaration(value,root,parent,length2){return node(value,root,parent,DECLARATION,substr(value,0,length2),substr(value,length2+1,-1),length2)}function serialize(children,callback){for(var output=\"\",length2=sizeof(children),i=0;i<length2;i++)output+=callback(children[i],i,children,callback)||\"\";return output}function stringify(element,index,children,callback){switch(element.type){case LAYER:if(element.children.length)break;case IMPORT:case DECLARATION:return element.return=element.return||element.value;case COMMENT:return \"\";case KEYFRAMES:return element.return=element.value+\"{\"+serialize(element.children,callback)+\"}\";case RULESET:element.value=element.props.join(\",\");}return strlen(children=serialize(element.children,callback))?element.return=element.value+\"{\"+children+\"}\":\"\"}function middleware(collection){var length2=sizeof(collection);return function(element,index,children,callback){for(var output=\"\",i=0;i<length2;i++)output+=collection[i](element,index,children,callback)||\"\";return output}}var weakMemoize=function(func){var cache=new WeakMap;return function(arg){if(cache.has(arg))return cache.get(arg);var ret=func(arg);return cache.set(arg,ret),ret}};var identifierWithPointTracking=function(begin,points,index){for(var previous=0,character2=0;previous=character2,character2=peek(),previous===38&&character2===12&&(points[index]=1),!token(character2);)next();return slice(begin,position)},toRules=function(parsed,points){var index=-1,character2=44;do switch(token(character2)){case 0:character2===38&&peek()===12&&(points[index]=1),parsed[index]+=identifierWithPointTracking(position-1,points,index);break;case 2:parsed[index]+=delimit(character2);break;case 4:if(character2===44){parsed[++index]=peek()===58?\"&\\f\":\"\",points[index]=parsed[index].length;break}default:parsed[index]+=from(character2);}while(character2=next());return parsed},getRules=function(value,points){return dealloc(toRules(alloc(value),points))},fixedElements=new WeakMap,compat=function(element){if(!(element.type!==\"rule\"||!element.parent||element.length<1)){for(var value=element.value,parent=element.parent,isImplicitRule=element.column===parent.column&&element.line===parent.line;parent.type!==\"rule\";)if(parent=parent.parent,!parent)return;if(!(element.props.length===1&&value.charCodeAt(0)!==58&&!fixedElements.get(parent))&&!isImplicitRule){fixedElements.set(element,!0);for(var points=[],rules=getRules(value,points),parentRules=parent.props,i=0,k=0;i<rules.length;i++)for(var j=0;j<parentRules.length;j++,k++)element.props[k]=points[i]?rules[i].replace(/&\\f/g,parentRules[j]):parentRules[j]+\" \"+rules[i];}}},removeLabel=function(element){if(element.type===\"decl\"){var value=element.value;value.charCodeAt(0)===108&&value.charCodeAt(2)===98&&(element.return=\"\",element.value=\"\");}},ignoreFlag=\"emotion-disable-server-rendering-unsafe-selector-warning-please-do-not-use-this-the-warning-exists-for-a-reason\",isIgnoringComment=function(element){return element.type===\"comm\"&&element.children.indexOf(ignoreFlag)>-1},createUnsafeSelectorsAlarm=function(cache){return function(element,index,children){if(!(element.type!==\"rule\"||cache.compat)){var unsafePseudoClasses=element.value.match(/(:first|:nth|:nth-last)-child/g);if(unsafePseudoClasses){for(var isNested=!!element.parent,commentContainer=isNested?element.parent.children:children,i=commentContainer.length-1;i>=0;i--){var node2=commentContainer[i];if(node2.line<element.line)break;if(node2.column<element.column){if(isIgnoringComment(node2))return;break}}unsafePseudoClasses.forEach(function(unsafePseudoClass){console.error('The pseudo class \"'+unsafePseudoClass+'\" is potentially unsafe when doing server-side rendering. Try changing it to \"'+unsafePseudoClass.split(\"-child\")[0]+'-of-type\".');});}}}},isImportRule=function(element){return element.type.charCodeAt(1)===105&&element.type.charCodeAt(0)===64},isPrependedWithRegularRules=function(index,children){for(var i=index-1;i>=0;i--)if(!isImportRule(children[i]))return !0;return !1},nullifyElement=function(element){element.type=\"\",element.value=\"\",element.return=\"\",element.children=\"\",element.props=\"\";},incorrectImportAlarm=function(element,index,children){isImportRule(element)&&(element.parent?(console.error(\"`@import` rules can't be nested inside other rules. Please move it to the top level and put it before regular rules. Keep in mind that they can only be used within global styles.\"),nullifyElement(element)):isPrependedWithRegularRules(index,children)&&(console.error(\"`@import` rules can't be after other rules. Please put your `@import` rules before your other rules.\"),nullifyElement(element)));};function prefix(value,length2){switch(hash(value,length2)){case 5103:return WEBKIT+\"print-\"+value+value;case 5737:case 4201:case 3177:case 3433:case 1641:case 4457:case 2921:case 5572:case 6356:case 5844:case 3191:case 6645:case 3005:case 6391:case 5879:case 5623:case 6135:case 4599:case 4855:case 4215:case 6389:case 5109:case 5365:case 5621:case 3829:return WEBKIT+value+value;case 5349:case 4246:case 4810:case 6968:case 2756:return WEBKIT+value+MOZ+value+MS+value+value;case 6828:case 4268:return WEBKIT+value+MS+value+value;case 6165:return WEBKIT+value+MS+\"flex-\"+value+value;case 5187:return WEBKIT+value+replace(value,/(\\w+).+(:[^]+)/,WEBKIT+\"box-$1$2\"+MS+\"flex-$1$2\")+value;case 5443:return WEBKIT+value+MS+\"flex-item-\"+replace(value,/flex-|-self/,\"\")+value;case 4675:return WEBKIT+value+MS+\"flex-line-pack\"+replace(value,/align-content|flex-|-self/,\"\")+value;case 5548:return WEBKIT+value+MS+replace(value,\"shrink\",\"negative\")+value;case 5292:return WEBKIT+value+MS+replace(value,\"basis\",\"preferred-size\")+value;case 6060:return WEBKIT+\"box-\"+replace(value,\"-grow\",\"\")+WEBKIT+value+MS+replace(value,\"grow\",\"positive\")+value;case 4554:return WEBKIT+replace(value,/([^-])(transform)/g,\"$1\"+WEBKIT+\"$2\")+value;case 6187:return replace(replace(replace(value,/(zoom-|grab)/,WEBKIT+\"$1\"),/(image-set)/,WEBKIT+\"$1\"),value,\"\")+value;case 5495:case 3959:return replace(value,/(image-set\\([^]*)/,WEBKIT+\"$1$`$1\");case 4968:return replace(replace(value,/(.+:)(flex-)?(.*)/,WEBKIT+\"box-pack:$3\"+MS+\"flex-pack:$3\"),/s.+-b[^;]+/,\"justify\")+WEBKIT+value+value;case 4095:case 3583:case 4068:case 2532:return replace(value,/(.+)-inline(.+)/,WEBKIT+\"$1$2\")+value;case 8116:case 7059:case 5753:case 5535:case 5445:case 5701:case 4933:case 4677:case 5533:case 5789:case 5021:case 4765:if(strlen(value)-1-length2>6)switch(charat(value,length2+1)){case 109:if(charat(value,length2+4)!==45)break;case 102:return replace(value,/(.+:)(.+)-([^]+)/,\"$1\"+WEBKIT+\"$2-$3$1\"+MOZ+(charat(value,length2+3)==108?\"$3\":\"$2-$3\"))+value;case 115:return ~indexof(value,\"stretch\")?prefix(replace(value,\"stretch\",\"fill-available\"),length2)+value:value}break;case 4949:if(charat(value,length2+1)!==115)break;case 6444:switch(charat(value,strlen(value)-3-(~indexof(value,\"!important\")&&10))){case 107:return replace(value,\":\",\":\"+WEBKIT)+value;case 101:return replace(value,/(.+:)([^;!]+)(;|!.+)?/,\"$1\"+WEBKIT+(charat(value,14)===45?\"inline-\":\"\")+\"box$3$1\"+WEBKIT+\"$2$3$1\"+MS+\"$2box$3\")+value}break;case 5936:switch(charat(value,length2+11)){case 114:return WEBKIT+value+MS+replace(value,/[svh]\\w+-[tblr]{2}/,\"tb\")+value;case 108:return WEBKIT+value+MS+replace(value,/[svh]\\w+-[tblr]{2}/,\"tb-rl\")+value;case 45:return WEBKIT+value+MS+replace(value,/[svh]\\w+-[tblr]{2}/,\"lr\")+value}return WEBKIT+value+MS+value+value}return value}var prefixer=function(element,index,children,callback){if(element.length>-1&&!element.return)switch(element.type){case DECLARATION:element.return=prefix(element.value,element.length);break;case KEYFRAMES:return serialize([copy(element,{value:replace(element.value,\"@\",\"@\"+WEBKIT)})],callback);case RULESET:if(element.length)return combine(element.props,function(value){switch(match(value,/(::plac\\w+|:read-\\w+)/)){case\":read-only\":case\":read-write\":return serialize([copy(element,{props:[replace(value,/:(read-\\w+)/,\":\"+MOZ+\"$1\")]})],callback);case\"::placeholder\":return serialize([copy(element,{props:[replace(value,/:(plac\\w+)/,\":\"+WEBKIT+\"input-$1\")]}),copy(element,{props:[replace(value,/:(plac\\w+)/,\":\"+MOZ+\"$1\")]}),copy(element,{props:[replace(value,/:(plac\\w+)/,MS+\"input-$1\")]})],callback)}return \"\"})}},defaultStylisPlugins=[prefixer],createCache=function(options){var key=options.key;if(!key)throw new Error(`You have to configure \\`key\\` for your cache. Please make sure it's unique (and not equal to 'css') as it's used for linking styles to your cache.\nIf multiple caches share the same key they might \"fight\" for each other's style elements.`);if(key===\"css\"){var ssrStyles=document.querySelectorAll(\"style[data-emotion]:not([data-s])\");Array.prototype.forEach.call(ssrStyles,function(node2){var dataEmotionAttribute=node2.getAttribute(\"data-emotion\");dataEmotionAttribute.indexOf(\" \")!==-1&&(document.head.appendChild(node2),node2.setAttribute(\"data-s\",\"\"));});}var stylisPlugins=options.stylisPlugins||defaultStylisPlugins;if(/[^a-z-]/.test(key))throw new Error('Emotion key must only contain lower case alphabetical characters and - but \"'+key+'\" was passed');var inserted={},container,nodesToHydrate=[];container=options.container||document.head,Array.prototype.forEach.call(document.querySelectorAll('style[data-emotion^=\"'+key+' \"]'),function(node2){for(var attrib=node2.getAttribute(\"data-emotion\").split(\" \"),i=1;i<attrib.length;i++)inserted[attrib[i]]=!0;nodesToHydrate.push(node2);});var _insert,omnipresentPlugins=[compat,removeLabel];omnipresentPlugins.push(createUnsafeSelectorsAlarm({get compat(){return cache.compat}}),incorrectImportAlarm);{var currentSheet,finalizingPlugins=[stringify,function(element){element.root||(element.return?currentSheet.insert(element.return):element.value&&element.type!==COMMENT&&currentSheet.insert(element.value+\"{}\"));}],serializer=middleware(omnipresentPlugins.concat(stylisPlugins,finalizingPlugins)),stylis=function(styles){return serialize(compile(styles),serializer)};_insert=function(selector,serialized,sheet,shouldCache){currentSheet=sheet,serialized.map!==void 0&&(currentSheet={insert:function(rule){sheet.insert(rule+serialized.map);}}),stylis(selector?selector+\"{\"+serialized.styles+\"}\":serialized.styles),shouldCache&&(cache.inserted[serialized.name]=!0);};}var cache={key,sheet:new StyleSheet({key,container,nonce:options.nonce,speedy:options.speedy,prepend:options.prepend,insertionPoint:options.insertionPoint}),nonce:options.nonce,inserted,registered:{},insert:_insert};return cache.sheet.hydrate(nodesToHydrate),cache};var import_hoist_non_react_statics=__toESM(require_hoist_non_react_statics_cjs()),hoistNonReactStatics=function(targetComponent,sourceComponent){return (0, import_hoist_non_react_statics.default)(targetComponent,sourceComponent)};var isBrowser=!0;function getRegisteredStyles(registered,registeredStyles,classNames){var rawClassName=\"\";return classNames.split(\" \").forEach(function(className){registered[className]!==void 0?registeredStyles.push(registered[className]+\";\"):rawClassName+=className+\" \";}),rawClassName}var registerStyles=function(cache,serialized,isStringTag){var className=cache.key+\"-\"+serialized.name;(isStringTag===!1||isBrowser===!1)&&cache.registered[className]===void 0&&(cache.registered[className]=serialized.styles);},insertStyles=function(cache,serialized,isStringTag){registerStyles(cache,serialized,isStringTag);var className=cache.key+\"-\"+serialized.name;if(cache.inserted[serialized.name]===void 0){var current=serialized;do cache.insert(serialized===current?\".\"+className:\"\",current,cache.sheet,!0),current=current.next;while(current!==void 0)}};function murmur2(str){for(var h=0,k,i=0,len=str.length;len>=4;++i,len-=4)k=str.charCodeAt(i)&255|(str.charCodeAt(++i)&255)<<8|(str.charCodeAt(++i)&255)<<16|(str.charCodeAt(++i)&255)<<24,k=(k&65535)*1540483477+((k>>>16)*59797<<16),k^=k>>>24,h=(k&65535)*1540483477+((k>>>16)*59797<<16)^(h&65535)*1540483477+((h>>>16)*59797<<16);switch(len){case 3:h^=(str.charCodeAt(i+2)&255)<<16;case 2:h^=(str.charCodeAt(i+1)&255)<<8;case 1:h^=str.charCodeAt(i)&255,h=(h&65535)*1540483477+((h>>>16)*59797<<16);}return h^=h>>>13,h=(h&65535)*1540483477+((h>>>16)*59797<<16),((h^h>>>15)>>>0).toString(36)}var unitlessKeys={animationIterationCount:1,aspectRatio:1,borderImageOutset:1,borderImageSlice:1,borderImageWidth:1,boxFlex:1,boxFlexGroup:1,boxOrdinalGroup:1,columnCount:1,columns:1,flex:1,flexGrow:1,flexPositive:1,flexShrink:1,flexNegative:1,flexOrder:1,gridRow:1,gridRowEnd:1,gridRowSpan:1,gridRowStart:1,gridColumn:1,gridColumnEnd:1,gridColumnSpan:1,gridColumnStart:1,msGridRow:1,msGridRowSpan:1,msGridColumn:1,msGridColumnSpan:1,fontWeight:1,lineHeight:1,opacity:1,order:1,orphans:1,tabSize:1,widows:1,zIndex:1,zoom:1,WebkitLineClamp:1,fillOpacity:1,floodOpacity:1,stopOpacity:1,strokeDasharray:1,strokeDashoffset:1,strokeMiterlimit:1,strokeOpacity:1,strokeWidth:1};var ILLEGAL_ESCAPE_SEQUENCE_ERROR=`You have illegal escape sequence in your template literal, most likely inside content's property value.\nBecause you write your CSS inside a JavaScript string you actually have to do double escaping, so for example \"content: '\\\\00d7';\" should become \"content: '\\\\\\\\00d7';\".\nYou can read more about this here:\nhttps://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Template_literals#ES2018_revision_of_illegal_escape_sequences`,UNDEFINED_AS_OBJECT_KEY_ERROR=\"You have passed in falsy value as style object's key (can happen when in example you pass unexported component as computed key).\",hyphenateRegex=/[A-Z]|^ms/g,animationRegex=/_EMO_([^_]+?)_([^]*?)_EMO_/g,isCustomProperty=function(property){return property.charCodeAt(1)===45},isProcessableValue=function(value){return value!=null&&typeof value!=\"boolean\"},processStyleName=memoize(function(styleName){return isCustomProperty(styleName)?styleName:styleName.replace(hyphenateRegex,\"-$&\").toLowerCase()}),processStyleValue=function(key,value){switch(key){case\"animation\":case\"animationName\":if(typeof value==\"string\")return value.replace(animationRegex,function(match2,p1,p2){return cursor={name:p1,styles:p2,next:cursor},p1})}return unitlessKeys[key]!==1&&!isCustomProperty(key)&&typeof value==\"number\"&&value!==0?value+\"px\":value};contentValuePattern=/(var|attr|counters?|url|element|(((repeating-)?(linear|radial))|conic)-gradient)\\(|(no-)?(open|close)-quote/,contentValues=[\"normal\",\"none\",\"initial\",\"inherit\",\"unset\"],oldProcessStyleValue=processStyleValue,msPattern=/^-ms-/,hyphenPattern=/-(.)/g,hyphenatedCache={},processStyleValue=function(key,value){if(key===\"content\"&&(typeof value!=\"string\"||contentValues.indexOf(value)===-1&&!contentValuePattern.test(value)&&(value.charAt(0)!==value.charAt(value.length-1)||value.charAt(0)!=='\"'&&value.charAt(0)!==\"'\")))throw new Error(\"You seem to be using a value for 'content' without quotes, try replacing it with `content: '\\\"\"+value+\"\\\"'`\");var processed=oldProcessStyleValue(key,value);return processed!==\"\"&&!isCustomProperty(key)&&key.indexOf(\"-\")!==-1&&hyphenatedCache[key]===void 0&&(hyphenatedCache[key]=!0,console.error(\"Using kebab-case for css properties in objects is not supported. Did you mean \"+key.replace(msPattern,\"ms-\").replace(hyphenPattern,function(str,_char){return _char.toUpperCase()})+\"?\")),processed};var contentValuePattern,contentValues,oldProcessStyleValue,msPattern,hyphenPattern,hyphenatedCache,noComponentSelectorMessage=\"Component selectors can only be used in conjunction with @emotion/babel-plugin, the swc Emotion plugin, or another Emotion-aware compiler transform.\";function handleInterpolation(mergedProps,registered,interpolation){if(interpolation==null)return \"\";if(interpolation.__emotion_styles!==void 0){if(interpolation.toString()===\"NO_COMPONENT_SELECTOR\")throw new Error(noComponentSelectorMessage);return interpolation}switch(typeof interpolation){case\"boolean\":return \"\";case\"object\":{if(interpolation.anim===1)return cursor={name:interpolation.name,styles:interpolation.styles,next:cursor},interpolation.name;if(interpolation.styles!==void 0){var next2=interpolation.next;if(next2!==void 0)for(;next2!==void 0;)cursor={name:next2.name,styles:next2.styles,next:cursor},next2=next2.next;var styles=interpolation.styles+\";\";return interpolation.map!==void 0&&(styles+=interpolation.map),styles}return createStringFromObject(mergedProps,registered,interpolation)}case\"function\":{if(mergedProps!==void 0){var previousCursor=cursor,result=interpolation(mergedProps);return cursor=previousCursor,handleInterpolation(mergedProps,registered,result)}else console.error(\"Functions that are interpolated in css calls will be stringified.\\nIf you want to have a css call based on props, create a function that returns a css call like this\\nlet dynamicStyle = (props) => css`color: ${props.color}`\\nIt can be called directly with props or interpolated in a styled call like this\\nlet SomeComponent = styled('div')`${dynamicStyle}`\");break}case\"string\":var matched=[],replaced=interpolation.replace(animationRegex,function(match2,p1,p2){var fakeVarName=\"animation\"+matched.length;return matched.push(\"const \"+fakeVarName+\" = keyframes`\"+p2.replace(/^@keyframes animation-\\w+/,\"\")+\"`\"),\"${\"+fakeVarName+\"}\"});matched.length&&console.error(\"`keyframes` output got interpolated into plain string, please wrap it with `css`.\\n\\nInstead of doing this:\\n\\n\"+[].concat(matched,[\"`\"+replaced+\"`\"]).join(`\n`)+`\n\nYou should wrap it with \\`css\\` like this:\n\n`+(\"css`\"+replaced+\"`\"));break}if(registered==null)return interpolation;var cached=registered[interpolation];return cached!==void 0?cached:interpolation}function createStringFromObject(mergedProps,registered,obj){var string=\"\";if(Array.isArray(obj))for(var i=0;i<obj.length;i++)string+=handleInterpolation(mergedProps,registered,obj[i])+\";\";else for(var _key in obj){var value=obj[_key];if(typeof value!=\"object\")registered!=null&&registered[value]!==void 0?string+=_key+\"{\"+registered[value]+\"}\":isProcessableValue(value)&&(string+=processStyleName(_key)+\":\"+processStyleValue(_key,value)+\";\");else {if(_key===\"NO_COMPONENT_SELECTOR\")throw new Error(noComponentSelectorMessage);if(Array.isArray(value)&&typeof value[0]==\"string\"&&(registered==null||registered[value[0]]===void 0))for(var _i=0;_i<value.length;_i++)isProcessableValue(value[_i])&&(string+=processStyleName(_key)+\":\"+processStyleValue(_key,value[_i])+\";\");else {var interpolated=handleInterpolation(mergedProps,registered,value);switch(_key){case\"animation\":case\"animationName\":{string+=processStyleName(_key)+\":\"+interpolated+\";\";break}default:_key===\"undefined\"&&console.error(UNDEFINED_AS_OBJECT_KEY_ERROR),string+=_key+\"{\"+interpolated+\"}\";}}}}return string}var labelPattern=/label:\\s*([^\\s;\\n{]+)\\s*(;|$)/g,sourceMapPattern;sourceMapPattern=/\\/\\*#\\ssourceMappingURL=data:application\\/json;\\S+\\s+\\*\\//g;var cursor,serializeStyles=function(args,registered,mergedProps){if(args.length===1&&typeof args[0]==\"object\"&&args[0]!==null&&args[0].styles!==void 0)return args[0];var stringMode=!0,styles=\"\";cursor=void 0;var strings=args[0];strings==null||strings.raw===void 0?(stringMode=!1,styles+=handleInterpolation(mergedProps,registered,strings)):(strings[0]===void 0&&console.error(ILLEGAL_ESCAPE_SEQUENCE_ERROR),styles+=strings[0]);for(var i=1;i<args.length;i++)styles+=handleInterpolation(mergedProps,registered,args[i]),stringMode&&(strings[i]===void 0&&console.error(ILLEGAL_ESCAPE_SEQUENCE_ERROR),styles+=strings[i]);var sourceMap;styles=styles.replace(sourceMapPattern,function(match3){return sourceMap=match3,\"\"}),labelPattern.lastIndex=0;for(var identifierName=\"\",match2;(match2=labelPattern.exec(styles))!==null;)identifierName+=\"-\"+match2[1];var name=murmur2(styles)+identifierName;return {name,styles,map:sourceMap,next:cursor,toString:function(){return \"You have tried to stringify object returned from `css` function. It isn't supposed to be used directly (e.g. as value of the `className` prop), but rather handed to emotion so it can handle it (e.g. as value of `css` prop).\"}}};var hasOwn={}.hasOwnProperty,EmotionCacheContext=React.createContext(typeof HTMLElement<\"u\"?createCache({key:\"css\"}):null);EmotionCacheContext.displayName=\"EmotionCacheContext\";var CacheProvider=EmotionCacheContext.Provider;var withEmotionCache=function(func){return forwardRef(function(props,ref){var cache=useContext(EmotionCacheContext);return func(props,cache,ref)})};var ThemeContext=React.createContext({});ThemeContext.displayName=\"EmotionThemeContext\";var useTheme=function(){return React.useContext(ThemeContext)},getTheme=function(outerTheme,theme){if(typeof theme==\"function\"){var mergedTheme=theme(outerTheme);if(mergedTheme==null||typeof mergedTheme!=\"object\"||Array.isArray(mergedTheme))throw new Error(\"[ThemeProvider] Please return an object from your theme function, i.e. theme={() => ({})}!\");return mergedTheme}if(theme==null||typeof theme!=\"object\"||Array.isArray(theme))throw new Error(\"[ThemeProvider] Please make your theme prop a plain object\");return _extends({},outerTheme,theme)},createCacheWithTheme=weakMemoize(function(outerTheme){return weakMemoize(function(theme){return getTheme(outerTheme,theme)})}),ThemeProvider=function(props){var theme=React.useContext(ThemeContext);return props.theme!==theme&&(theme=createCacheWithTheme(theme)(props.theme)),React.createElement(ThemeContext.Provider,{value:theme},props.children)};function withTheme(Component){var componentName=Component.displayName||Component.name||\"Component\",render=function(props,ref){var theme=React.useContext(ThemeContext);return React.createElement(Component,_extends({theme,ref},props))},WithTheme=React.forwardRef(render);return WithTheme.displayName=\"WithTheme(\"+componentName+\")\",hoistNonReactStatics(WithTheme,Component)}var getLastPart=function(functionName){var parts=functionName.split(\".\");return parts[parts.length-1]},getFunctionNameFromStackTraceLine=function(line2){var match2=/^\\s+at\\s+([A-Za-z0-9$.]+)\\s/.exec(line2);if(match2||(match2=/^([A-Za-z0-9$.]+)@/.exec(line2),match2))return getLastPart(match2[1])},internalReactFunctionNames=new Set([\"renderWithHooks\",\"processChild\",\"finishClassComponent\",\"renderToString\"]),sanitizeIdentifier=function(identifier2){return identifier2.replace(/\\$/g,\"-\")},getLabelFromStackTrace=function(stackTrace){if(stackTrace)for(var lines=stackTrace.split(`\n`),i=0;i<lines.length;i++){var functionName=getFunctionNameFromStackTraceLine(lines[i]);if(functionName){if(internalReactFunctionNames.has(functionName))break;if(/^[A-Z]/.test(functionName))return sanitizeIdentifier(functionName)}}},typePropName=\"__EMOTION_TYPE_PLEASE_DO_NOT_USE__\",labelPropName=\"__EMOTION_LABEL_PLEASE_DO_NOT_USE__\",createEmotionProps=function(type,props){if(typeof props.css==\"string\"&&props.css.indexOf(\":\")!==-1)throw new Error(\"Strings are not allowed as css prop values, please wrap it in a css template literal from '@emotion/react' like this: css`\"+props.css+\"`\");var newProps={};for(var key in props)hasOwn.call(props,key)&&(newProps[key]=props[key]);if(newProps[typePropName]=type,props.css&&(typeof props.css!=\"object\"||typeof props.css.name!=\"string\"||props.css.name.indexOf(\"-\")===-1)){var label=getLabelFromStackTrace(new Error().stack);label&&(newProps[labelPropName]=label);}return newProps},Insertion=function(_ref){var cache=_ref.cache,serialized=_ref.serialized,isStringTag=_ref.isStringTag;return registerStyles(cache,serialized,isStringTag),useInsertionEffectAlwaysWithSyncFallback(function(){return insertStyles(cache,serialized,isStringTag)}),null},Emotion=withEmotionCache(function(props,cache,ref){var cssProp=props.css;typeof cssProp==\"string\"&&cache.registered[cssProp]!==void 0&&(cssProp=cache.registered[cssProp]);var WrappedComponent=props[typePropName],registeredStyles=[cssProp],className=\"\";typeof props.className==\"string\"?className=getRegisteredStyles(cache.registered,registeredStyles,props.className):props.className!=null&&(className=props.className+\" \");var serialized=serializeStyles(registeredStyles,void 0,React.useContext(ThemeContext));if(serialized.name.indexOf(\"-\")===-1){var labelFromStack=props[labelPropName];labelFromStack&&(serialized=serializeStyles([serialized,\"label:\"+labelFromStack+\";\"]));}className+=cache.key+\"-\"+serialized.name;var newProps={};for(var key in props)hasOwn.call(props,key)&&key!==\"css\"&&key!==typePropName&&key!==labelPropName&&(newProps[key]=props[key]);return newProps.ref=ref,newProps.className=className,React.createElement(React.Fragment,null,React.createElement(Insertion,{cache,serialized,isStringTag:typeof WrappedComponent==\"string\"}),React.createElement(WrappedComponent,newProps))});Emotion.displayName=\"EmotionCssPropInternal\";var Emotion$1=Emotion;__toESM(require_hoist_non_react_statics_cjs());var pkg={name:\"@emotion/react\",version:\"11.11.4\",main:\"dist/emotion-react.cjs.js\",module:\"dist/emotion-react.esm.js\",browser:{\"./dist/emotion-react.esm.js\":\"./dist/emotion-react.browser.esm.js\"},exports:{\".\":{module:{worker:\"./dist/emotion-react.worker.esm.js\",browser:\"./dist/emotion-react.browser.esm.js\",default:\"./dist/emotion-react.esm.js\"},import:\"./dist/emotion-react.cjs.mjs\",default:\"./dist/emotion-react.cjs.js\"},\"./jsx-runtime\":{module:{worker:\"./jsx-runtime/dist/emotion-react-jsx-runtime.worker.esm.js\",browser:\"./jsx-runtime/dist/emotion-react-jsx-runtime.browser.esm.js\",default:\"./jsx-runtime/dist/emotion-react-jsx-runtime.esm.js\"},import:\"./jsx-runtime/dist/emotion-react-jsx-runtime.cjs.mjs\",default:\"./jsx-runtime/dist/emotion-react-jsx-runtime.cjs.js\"},\"./_isolated-hnrs\":{module:{worker:\"./_isolated-hnrs/dist/emotion-react-_isolated-hnrs.worker.esm.js\",browser:\"./_isolated-hnrs/dist/emotion-react-_isolated-hnrs.browser.esm.js\",default:\"./_isolated-hnrs/dist/emotion-react-_isolated-hnrs.esm.js\"},import:\"./_isolated-hnrs/dist/emotion-react-_isolated-hnrs.cjs.mjs\",default:\"./_isolated-hnrs/dist/emotion-react-_isolated-hnrs.cjs.js\"},\"./jsx-dev-runtime\":{module:{worker:\"./jsx-dev-runtime/dist/emotion-react-jsx-dev-runtime.worker.esm.js\",browser:\"./jsx-dev-runtime/dist/emotion-react-jsx-dev-runtime.browser.esm.js\",default:\"./jsx-dev-runtime/dist/emotion-react-jsx-dev-runtime.esm.js\"},import:\"./jsx-dev-runtime/dist/emotion-react-jsx-dev-runtime.cjs.mjs\",default:\"./jsx-dev-runtime/dist/emotion-react-jsx-dev-runtime.cjs.js\"},\"./package.json\":\"./package.json\",\"./types/css-prop\":\"./types/css-prop.d.ts\",\"./macro\":{types:{import:\"./macro.d.mts\",default:\"./macro.d.ts\"},default:\"./macro.js\"}},types:\"types/index.d.ts\",files:[\"src\",\"dist\",\"jsx-runtime\",\"jsx-dev-runtime\",\"_isolated-hnrs\",\"types/*.d.ts\",\"macro.*\"],sideEffects:!1,author:\"Emotion Contributors\",license:\"MIT\",scripts:{\"test:typescript\":\"dtslint types\"},dependencies:{\"@babel/runtime\":\"^7.18.3\",\"@emotion/babel-plugin\":\"^11.11.0\",\"@emotion/cache\":\"^11.11.0\",\"@emotion/serialize\":\"^1.1.3\",\"@emotion/use-insertion-effect-with-fallbacks\":\"^1.0.1\",\"@emotion/utils\":\"^1.2.1\",\"@emotion/weak-memoize\":\"^0.3.1\",\"hoist-non-react-statics\":\"^3.3.1\"},peerDependencies:{react:\">=16.8.0\"},peerDependenciesMeta:{\"@types/react\":{optional:!0}},devDependencies:{\"@definitelytyped/dtslint\":\"0.0.112\",\"@emotion/css\":\"11.11.2\",\"@emotion/css-prettifier\":\"1.1.3\",\"@emotion/server\":\"11.11.0\",\"@emotion/styled\":\"11.11.0\",\"html-tag-names\":\"^1.1.2\",react:\"16.14.0\",\"svg-tag-names\":\"^1.1.1\",typescript:\"^4.5.5\"},repository:\"https://github.com/emotion-js/emotion/tree/main/packages/react\",publishConfig:{access:\"public\"},\"umd:main\":\"dist/emotion-react.umd.min.js\",preconstruct:{entrypoints:[\"./index.js\",\"./jsx-runtime.js\",\"./jsx-dev-runtime.js\",\"./_isolated-hnrs.js\"],umdName:\"emotionReact\",exports:{envConditions:[\"browser\",\"worker\"],extra:{\"./types/css-prop\":\"./types/css-prop.d.ts\",\"./macro\":{types:{import:\"./macro.d.mts\",default:\"./macro.d.ts\"},default:\"./macro.js\"}}}}},jsx=function(type,props){var args=arguments;if(props==null||!hasOwn.call(props,\"css\"))return React.createElement.apply(void 0,args);var argsLength=args.length,createElementArgArray=new Array(argsLength);createElementArgArray[0]=Emotion$1,createElementArgArray[1]=createEmotionProps(type,props);for(var i=2;i<argsLength;i++)createElementArgArray[i]=args[i];return React.createElement.apply(null,createElementArgArray)},warnedAboutCssPropForGlobal=!1,Global=withEmotionCache(function(props,cache){!warnedAboutCssPropForGlobal&&(props.className||props.css)&&(console.error(\"It looks like you're using the css prop on Global, did you mean to use the styles prop instead?\"),warnedAboutCssPropForGlobal=!0);var styles=props.styles,serialized=serializeStyles([styles],void 0,React.useContext(ThemeContext));var sheetRef=React.useRef();return useInsertionEffectWithLayoutFallback(function(){var key=cache.key+\"-global\",sheet=new cache.sheet.constructor({key,nonce:cache.sheet.nonce,container:cache.sheet.container,speedy:cache.sheet.isSpeedy}),rehydrating=!1,node2=document.querySelector('style[data-emotion=\"'+key+\" \"+serialized.name+'\"]');return cache.sheet.tags.length&&(sheet.before=cache.sheet.tags[0]),node2!==null&&(rehydrating=!0,node2.setAttribute(\"data-emotion\",key),sheet.hydrate([node2])),sheetRef.current=[sheet,rehydrating],function(){sheet.flush();}},[cache]),useInsertionEffectWithLayoutFallback(function(){var sheetRefCurrent=sheetRef.current,sheet=sheetRefCurrent[0],rehydrating=sheetRefCurrent[1];if(rehydrating){sheetRefCurrent[1]=!1;return}if(serialized.next!==void 0&&insertStyles(cache,serialized.next,!0),sheet.tags.length){var element=sheet.tags[sheet.tags.length-1].nextElementSibling;sheet.before=element,sheet.flush();}cache.insert(\"\",serialized,sheet,!1);},[cache,serialized.name]),null});Global.displayName=\"EmotionGlobal\";function css(){for(var _len=arguments.length,args=new Array(_len),_key=0;_key<_len;_key++)args[_key]=arguments[_key];return serializeStyles(args)}var keyframes=function(){var insertable=css.apply(void 0,arguments),name=\"animation-\"+insertable.name;return {name,styles:\"@keyframes \"+name+\"{\"+insertable.styles+\"}\",anim:1,toString:function(){return \"_EMO_\"+this.name+\"_\"+this.styles+\"_EMO_\"}}},classnames=function classnames2(args){for(var len=args.length,i=0,cls=\"\";i<len;i++){var arg=args[i];if(arg!=null){var toAdd=void 0;switch(typeof arg){case\"boolean\":break;case\"object\":{if(Array.isArray(arg))toAdd=classnames2(arg);else {arg.styles!==void 0&&arg.name!==void 0&&console.error(\"You have passed styles created with `css` from `@emotion/react` package to the `cx`.\\n`cx` is meant to compose class names (strings) so you should convert those styles to a class name by passing them to the `css` received from <ClassNames/> component.\"),toAdd=\"\";for(var k in arg)arg[k]&&k&&(toAdd&&(toAdd+=\" \"),toAdd+=k);}break}default:toAdd=arg;}toAdd&&(cls&&(cls+=\" \"),cls+=toAdd);}}return cls};function merge(registered,css2,className){var registeredStyles=[],rawClassName=getRegisteredStyles(registered,registeredStyles,className);return registeredStyles.length<2?className:rawClassName+css2(registeredStyles)}var Insertion3=function(_ref){var cache=_ref.cache,serializedArr=_ref.serializedArr;return useInsertionEffectAlwaysWithSyncFallback(function(){for(var i=0;i<serializedArr.length;i++)insertStyles(cache,serializedArr[i],!1);}),null},ClassNames=withEmotionCache(function(props,cache){var hasRendered=!1,serializedArr=[],css2=function(){if(hasRendered)throw new Error(\"css can only be used during render\");for(var _len=arguments.length,args=new Array(_len),_key=0;_key<_len;_key++)args[_key]=arguments[_key];var serialized=serializeStyles(args,cache.registered);return serializedArr.push(serialized),registerStyles(cache,serialized,!1),cache.key+\"-\"+serialized.name},cx=function(){if(hasRendered)throw new Error(\"cx can only be used during render\");for(var _len2=arguments.length,args=new Array(_len2),_key2=0;_key2<_len2;_key2++)args[_key2]=arguments[_key2];return merge(cache.registered,css2,classnames(args))},content={css:css2,cx,theme:React.useContext(ThemeContext)},ele=props.children(content);return hasRendered=!0,React.createElement(React.Fragment,null,React.createElement(Insertion3,{cache,serializedArr}),ele)});ClassNames.displayName=\"EmotionClassNames\";isBrowser3=!0,isTestEnv=typeof jest<\"u\"||typeof vi<\"u\",isBrowser3&&!isTestEnv&&(globalContext=typeof globalThis<\"u\"?globalThis:isBrowser3?window:global,globalKey=\"__EMOTION_REACT_\"+pkg.version.split(\".\")[0]+\"__\",globalContext[globalKey]&&console.warn(\"You are loading @emotion/react when it is already loaded. Running multiple instances may cause problems. This can happen if multiple versions are used, or if multiple builds of the same version are used.\"),globalContext[globalKey]=!0);var isBrowser3,isTestEnv,globalContext,globalKey;var testOmitPropsOnStringTag=isPropValid,testOmitPropsOnComponent=function(key){return key!==\"theme\"},getDefaultShouldForwardProp=function(tag){return typeof tag==\"string\"&&tag.charCodeAt(0)>96?testOmitPropsOnStringTag:testOmitPropsOnComponent},composeShouldForwardProps=function(tag,options,isReal){var shouldForwardProp;if(options){var optionsShouldForwardProp=options.shouldForwardProp;shouldForwardProp=tag.__emotion_forwardProp&&optionsShouldForwardProp?function(propName){return tag.__emotion_forwardProp(propName)&&optionsShouldForwardProp(propName)}:optionsShouldForwardProp;}return typeof shouldForwardProp!=\"function\"&&isReal&&(shouldForwardProp=tag.__emotion_forwardProp),shouldForwardProp},ILLEGAL_ESCAPE_SEQUENCE_ERROR2=`You have illegal escape sequence in your template literal, most likely inside content's property value.\nBecause you write your CSS inside a JavaScript string you actually have to do double escaping, so for example \"content: '\\\\00d7';\" should become \"content: '\\\\\\\\00d7';\".\nYou can read more about this here:\nhttps://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Template_literals#ES2018_revision_of_illegal_escape_sequences`,Insertion5=function(_ref){var cache=_ref.cache,serialized=_ref.serialized,isStringTag=_ref.isStringTag;return registerStyles(cache,serialized,isStringTag),useInsertionEffectAlwaysWithSyncFallback(function(){return insertStyles(cache,serialized,isStringTag)}),null},createStyled=function createStyled2(tag,options){if(tag===void 0)throw new Error(`You are trying to create a styled element with an undefined component.\nYou may have forgotten to import it.`);var isReal=tag.__emotion_real===tag,baseTag=isReal&&tag.__emotion_base||tag,identifierName,targetClassName;options!==void 0&&(identifierName=options.label,targetClassName=options.target);var shouldForwardProp=composeShouldForwardProps(tag,options,isReal),defaultShouldForwardProp=shouldForwardProp||getDefaultShouldForwardProp(baseTag),shouldUseAs=!defaultShouldForwardProp(\"as\");return function(){var args=arguments,styles=isReal&&tag.__emotion_styles!==void 0?tag.__emotion_styles.slice(0):[];if(identifierName!==void 0&&styles.push(\"label:\"+identifierName+\";\"),args[0]==null||args[0].raw===void 0)styles.push.apply(styles,args);else {args[0][0]===void 0&&console.error(ILLEGAL_ESCAPE_SEQUENCE_ERROR2),styles.push(args[0][0]);for(var len=args.length,i=1;i<len;i++)args[0][i]===void 0&&console.error(ILLEGAL_ESCAPE_SEQUENCE_ERROR2),styles.push(args[i],args[0][i]);}var Styled=withEmotionCache(function(props,cache,ref){var FinalTag=shouldUseAs&&props.as||baseTag,className=\"\",classInterpolations=[],mergedProps=props;if(props.theme==null){mergedProps={};for(var key in props)mergedProps[key]=props[key];mergedProps.theme=React.useContext(ThemeContext);}typeof props.className==\"string\"?className=getRegisteredStyles(cache.registered,classInterpolations,props.className):props.className!=null&&(className=props.className+\" \");var serialized=serializeStyles(styles.concat(classInterpolations),cache.registered,mergedProps);className+=cache.key+\"-\"+serialized.name,targetClassName!==void 0&&(className+=\" \"+targetClassName);var finalShouldForwardProp=shouldUseAs&&shouldForwardProp===void 0?getDefaultShouldForwardProp(FinalTag):defaultShouldForwardProp,newProps={};for(var _key in props)shouldUseAs&&_key===\"as\"||finalShouldForwardProp(_key)&&(newProps[_key]=props[_key]);return newProps.className=className,newProps.ref=ref,React.createElement(React.Fragment,null,React.createElement(Insertion5,{cache,serialized,isStringTag:typeof FinalTag==\"string\"}),React.createElement(FinalTag,newProps))});return Styled.displayName=identifierName!==void 0?identifierName:\"Styled(\"+(typeof baseTag==\"string\"?baseTag:baseTag.displayName||baseTag.name||\"Component\")+\")\",Styled.defaultProps=tag.defaultProps,Styled.__emotion_real=Styled,Styled.__emotion_base=baseTag,Styled.__emotion_styles=styles,Styled.__emotion_forwardProp=shouldForwardProp,Object.defineProperty(Styled,\"toString\",{value:function(){return targetClassName===void 0?\"NO_COMPONENT_SELECTOR\":\".\"+targetClassName}}),Styled.withComponent=function(nextTag,nextOptions){return createStyled2(nextTag,_extends({},options,nextOptions,{shouldForwardProp:composeShouldForwardProps(Styled,nextOptions,!0)})).apply(void 0,styles)},Styled}};var tags=[\"a\",\"abbr\",\"address\",\"area\",\"article\",\"aside\",\"audio\",\"b\",\"base\",\"bdi\",\"bdo\",\"big\",\"blockquote\",\"body\",\"br\",\"button\",\"canvas\",\"caption\",\"cite\",\"code\",\"col\",\"colgroup\",\"data\",\"datalist\",\"dd\",\"del\",\"details\",\"dfn\",\"dialog\",\"div\",\"dl\",\"dt\",\"em\",\"embed\",\"fieldset\",\"figcaption\",\"figure\",\"footer\",\"form\",\"h1\",\"h2\",\"h3\",\"h4\",\"h5\",\"h6\",\"head\",\"header\",\"hgroup\",\"hr\",\"html\",\"i\",\"iframe\",\"img\",\"input\",\"ins\",\"kbd\",\"keygen\",\"label\",\"legend\",\"li\",\"link\",\"main\",\"map\",\"mark\",\"marquee\",\"menu\",\"menuitem\",\"meta\",\"meter\",\"nav\",\"noscript\",\"object\",\"ol\",\"optgroup\",\"option\",\"output\",\"p\",\"param\",\"picture\",\"pre\",\"progress\",\"q\",\"rp\",\"rt\",\"ruby\",\"s\",\"samp\",\"script\",\"section\",\"select\",\"small\",\"source\",\"span\",\"strong\",\"style\",\"sub\",\"summary\",\"sup\",\"table\",\"tbody\",\"td\",\"textarea\",\"tfoot\",\"th\",\"thead\",\"time\",\"title\",\"tr\",\"track\",\"u\",\"ul\",\"var\",\"video\",\"wbr\",\"circle\",\"clipPath\",\"defs\",\"ellipse\",\"foreignObject\",\"g\",\"image\",\"line\",\"linearGradient\",\"mask\",\"path\",\"pattern\",\"polygon\",\"polyline\",\"radialGradient\",\"rect\",\"stop\",\"svg\",\"text\",\"tspan\"],newStyled=createStyled.bind();tags.forEach(function(tagName){newStyled[tagName]=newStyled(tagName);});var createReset=memoize2(1)(({typography:typography2})=>({body:{fontFamily:typography2.fonts.base,fontSize:typography2.size.s3,margin:0,WebkitFontSmoothing:\"antialiased\",MozOsxFontSmoothing:\"grayscale\",WebkitTapHighlightColor:\"rgba(0, 0, 0, 0)\",WebkitOverflowScrolling:\"touch\"},\"*\":{boxSizing:\"border-box\"},\"h1, h2, h3, h4, h5, h6\":{fontWeight:typography2.weight.regular,margin:0,padding:0},\"button, input, textarea, select\":{fontFamily:\"inherit\",fontSize:\"inherit\",boxSizing:\"border-box\"},sub:{fontSize:\"0.8em\",bottom:\"-0.2em\"},sup:{fontSize:\"0.8em\",top:\"-0.2em\"},\"b, strong\":{fontWeight:typography2.weight.bold},hr:{border:\"none\",borderTop:\"1px solid silver\",clear:\"both\",marginBottom:\"1.25rem\"},code:{fontFamily:typography2.fonts.mono,WebkitFontSmoothing:\"antialiased\",MozOsxFontSmoothing:\"grayscale\",display:\"inline-block\",paddingLeft:2,paddingRight:2,verticalAlign:\"baseline\",color:\"inherit\"},pre:{fontFamily:typography2.fonts.mono,WebkitFontSmoothing:\"antialiased\",MozOsxFontSmoothing:\"grayscale\",lineHeight:\"18px\",padding:\"11px 1rem\",whiteSpace:\"pre-wrap\",color:\"inherit\",borderRadius:3,margin:\"1rem 0\"}})),createGlobal=memoize2(1)(({color:color2,background:background2,typography:typography2})=>{let resetStyles=createReset({typography:typography2});return {...resetStyles,body:{...resetStyles.body,color:color2.defaultText,background:background2.app,overflow:\"hidden\"},hr:{...resetStyles.hr,borderTop:`1px solid ${color2.border}`}}});var easing={rubber:\"cubic-bezier(0.175, 0.885, 0.335, 1.05)\"},rotate360=keyframes`\n\tfrom {\n\t\ttransform: rotate(0deg);\n\t}\n\tto {\n\t\ttransform: rotate(360deg);\n\t}\n`,glow=keyframes`\n  0%, 100% { opacity: 1; }\n  50% { opacity: .4; }\n`,float=keyframes`\n  0% { transform: translateY(1px); }\n  25% { transform: translateY(0px); }\n  50% { transform: translateY(-3px); }\n  100% { transform: translateY(1px); }\n`,jiggle=keyframes`\n  0%, 100% { transform:translate3d(0,0,0); }\n  12.5%, 62.5% { transform:translate3d(-4px,0,0); }\n  37.5%, 87.5% {  transform: translate3d(4px,0,0);  }\n`,inlineGlow=css`\n  animation: ${glow} 1.5s ease-in-out infinite;\n  color: transparent;\n  cursor: progress;\n`,hoverable=css`\n  transition: all 150ms ease-out;\n  transform: translate3d(0, 0, 0);\n\n  &:hover {\n    transform: translate3d(0, -2px, 0);\n  }\n\n  &:active {\n    transform: translate3d(0, 0, 0);\n  }\n`,animation={rotate360,glow,float,jiggle,inlineGlow,hoverable};var chromeDark={BASE_FONT_FAMILY:\"Menlo, monospace\",BASE_FONT_SIZE:\"11px\",BASE_LINE_HEIGHT:1.2,BASE_BACKGROUND_COLOR:\"rgb(36, 36, 36)\",BASE_COLOR:\"rgb(213, 213, 213)\",OBJECT_PREVIEW_ARRAY_MAX_PROPERTIES:10,OBJECT_PREVIEW_OBJECT_MAX_PROPERTIES:5,OBJECT_NAME_COLOR:\"rgb(227, 110, 236)\",OBJECT_VALUE_NULL_COLOR:\"rgb(127, 127, 127)\",OBJECT_VALUE_UNDEFINED_COLOR:\"rgb(127, 127, 127)\",OBJECT_VALUE_REGEXP_COLOR:\"rgb(233, 63, 59)\",OBJECT_VALUE_STRING_COLOR:\"rgb(233, 63, 59)\",OBJECT_VALUE_SYMBOL_COLOR:\"rgb(233, 63, 59)\",OBJECT_VALUE_NUMBER_COLOR:\"hsl(252, 100%, 75%)\",OBJECT_VALUE_BOOLEAN_COLOR:\"hsl(252, 100%, 75%)\",OBJECT_VALUE_FUNCTION_PREFIX_COLOR:\"rgb(85, 106, 242)\",HTML_TAG_COLOR:\"rgb(93, 176, 215)\",HTML_TAGNAME_COLOR:\"rgb(93, 176, 215)\",HTML_TAGNAME_TEXT_TRANSFORM:\"lowercase\",HTML_ATTRIBUTE_NAME_COLOR:\"rgb(155, 187, 220)\",HTML_ATTRIBUTE_VALUE_COLOR:\"rgb(242, 151, 102)\",HTML_COMMENT_COLOR:\"rgb(137, 137, 137)\",HTML_DOCTYPE_COLOR:\"rgb(192, 192, 192)\",ARROW_COLOR:\"rgb(145, 145, 145)\",ARROW_MARGIN_RIGHT:3,ARROW_FONT_SIZE:12,ARROW_ANIMATION_DURATION:\"0\",TREENODE_FONT_FAMILY:\"Menlo, monospace\",TREENODE_FONT_SIZE:\"11px\",TREENODE_LINE_HEIGHT:1.2,TREENODE_PADDING_LEFT:12,TABLE_BORDER_COLOR:\"rgb(85, 85, 85)\",TABLE_TH_BACKGROUND_COLOR:\"rgb(44, 44, 44)\",TABLE_TH_HOVER_COLOR:\"rgb(48, 48, 48)\",TABLE_SORT_ICON_COLOR:\"black\",TABLE_DATA_BACKGROUND_IMAGE:\"linear-gradient(rgba(255, 255, 255, 0), rgba(255, 255, 255, 0) 50%, rgba(51, 139, 255, 0.0980392) 50%, rgba(51, 139, 255, 0.0980392))\",TABLE_DATA_BACKGROUND_SIZE:\"128px 32px\"},chromeLight={BASE_FONT_FAMILY:\"Menlo, monospace\",BASE_FONT_SIZE:\"11px\",BASE_LINE_HEIGHT:1.2,BASE_BACKGROUND_COLOR:\"white\",BASE_COLOR:\"black\",OBJECT_PREVIEW_ARRAY_MAX_PROPERTIES:10,OBJECT_PREVIEW_OBJECT_MAX_PROPERTIES:5,OBJECT_NAME_COLOR:\"rgb(136, 19, 145)\",OBJECT_VALUE_NULL_COLOR:\"rgb(128, 128, 128)\",OBJECT_VALUE_UNDEFINED_COLOR:\"rgb(128, 128, 128)\",OBJECT_VALUE_REGEXP_COLOR:\"rgb(196, 26, 22)\",OBJECT_VALUE_STRING_COLOR:\"rgb(196, 26, 22)\",OBJECT_VALUE_SYMBOL_COLOR:\"rgb(196, 26, 22)\",OBJECT_VALUE_NUMBER_COLOR:\"rgb(28, 0, 207)\",OBJECT_VALUE_BOOLEAN_COLOR:\"rgb(28, 0, 207)\",OBJECT_VALUE_FUNCTION_PREFIX_COLOR:\"rgb(13, 34, 170)\",HTML_TAG_COLOR:\"rgb(168, 148, 166)\",HTML_TAGNAME_COLOR:\"rgb(136, 18, 128)\",HTML_TAGNAME_TEXT_TRANSFORM:\"lowercase\",HTML_ATTRIBUTE_NAME_COLOR:\"rgb(153, 69, 0)\",HTML_ATTRIBUTE_VALUE_COLOR:\"rgb(26, 26, 166)\",HTML_COMMENT_COLOR:\"rgb(35, 110, 37)\",HTML_DOCTYPE_COLOR:\"rgb(192, 192, 192)\",ARROW_COLOR:\"#6e6e6e\",ARROW_MARGIN_RIGHT:3,ARROW_FONT_SIZE:12,ARROW_ANIMATION_DURATION:\"0\",TREENODE_FONT_FAMILY:\"Menlo, monospace\",TREENODE_FONT_SIZE:\"11px\",TREENODE_LINE_HEIGHT:1.2,TREENODE_PADDING_LEFT:12,TABLE_BORDER_COLOR:\"#aaa\",TABLE_TH_BACKGROUND_COLOR:\"#eee\",TABLE_TH_HOVER_COLOR:\"hsla(0, 0%, 90%, 1)\",TABLE_SORT_ICON_COLOR:\"#6e6e6e\",TABLE_DATA_BACKGROUND_IMAGE:\"linear-gradient(to bottom, white, white 50%, rgb(234, 243, 255) 50%, rgb(234, 243, 255))\",TABLE_DATA_BACKGROUND_SIZE:\"128px 32px\"},convertColors=colors=>Object.entries(colors).reduce((acc,[k,v])=>({...acc,[k]:mkColor(v)}),{}),create2=({colors,mono})=>{let colorsObjs=convertColors(colors);return {token:{fontFamily:mono,WebkitFontSmoothing:\"antialiased\",\"&.tag\":colorsObjs.red3,\"&.comment\":{...colorsObjs.green1,fontStyle:\"italic\"},\"&.prolog\":{...colorsObjs.green1,fontStyle:\"italic\"},\"&.doctype\":{...colorsObjs.green1,fontStyle:\"italic\"},\"&.cdata\":{...colorsObjs.green1,fontStyle:\"italic\"},\"&.string\":colorsObjs.red1,\"&.url\":colorsObjs.cyan1,\"&.symbol\":colorsObjs.cyan1,\"&.number\":colorsObjs.cyan1,\"&.boolean\":colorsObjs.cyan1,\"&.variable\":colorsObjs.cyan1,\"&.constant\":colorsObjs.cyan1,\"&.inserted\":colorsObjs.cyan1,\"&.atrule\":colorsObjs.blue1,\"&.keyword\":colorsObjs.blue1,\"&.attr-value\":colorsObjs.blue1,\"&.punctuation\":colorsObjs.gray1,\"&.operator\":colorsObjs.gray1,\"&.function\":colorsObjs.gray1,\"&.deleted\":colorsObjs.red2,\"&.important\":{fontWeight:\"bold\"},\"&.bold\":{fontWeight:\"bold\"},\"&.italic\":{fontStyle:\"italic\"},\"&.class-name\":colorsObjs.cyan2,\"&.selector\":colorsObjs.red3,\"&.attr-name\":colorsObjs.red4,\"&.property\":colorsObjs.red4,\"&.regex\":colorsObjs.red4,\"&.entity\":colorsObjs.red4,\"&.directive.tag .tag\":{background:\"#ffff00\",...colorsObjs.gray1}},\"language-json .token.boolean\":colorsObjs.blue1,\"language-json .token.number\":colorsObjs.blue1,\"language-json .token.property\":colorsObjs.cyan2,namespace:{opacity:.7}}};var lightSyntaxColors={green1:\"#008000\",red1:\"#A31515\",red2:\"#9a050f\",red3:\"#800000\",red4:\"#ff0000\",gray1:\"#393A34\",cyan1:\"#36acaa\",cyan2:\"#2B91AF\",blue1:\"#0000ff\",blue2:\"#00009f\"},darkSyntaxColors={green1:\"#7C7C7C\",red1:\"#92C379\",red2:\"#9a050f\",red3:\"#A8FF60\",red4:\"#96CBFE\",gray1:\"#EDEDED\",cyan1:\"#C6C5FE\",cyan2:\"#FFFFB6\",blue1:\"#B474DD\",blue2:\"#00009f\"},createColors=vars=>({primary:vars.colorPrimary,secondary:vars.colorSecondary,tertiary:color.tertiary,ancillary:color.ancillary,orange:color.orange,gold:color.gold,green:color.green,seafoam:color.seafoam,purple:color.purple,ultraviolet:color.ultraviolet,lightest:color.lightest,lighter:color.lighter,light:color.light,mediumlight:color.mediumlight,medium:color.medium,mediumdark:color.mediumdark,dark:color.dark,darker:color.darker,darkest:color.darkest,border:color.border,positive:color.positive,negative:color.negative,warning:color.warning,critical:color.critical,defaultText:vars.textColor||color.darkest,inverseText:vars.textInverseColor||color.lightest,positiveText:color.positiveText,negativeText:color.negativeText,warningText:color.warningText}),convert=(inherit=themes[getPreferredColorScheme()])=>{let{base,colorPrimary,colorSecondary,appBg,appContentBg,appPreviewBg,appBorderColor,appBorderRadius,fontBase,fontCode,textColor,textInverseColor,barTextColor,barHoverColor,barSelectedColor,barBg,buttonBg,buttonBorder,booleanBg,booleanSelectedBg,inputBg,inputBorder,inputTextColor,inputBorderRadius,brandTitle,brandUrl,brandImage,brandTarget,gridCellSize,...rest}=inherit;return {...rest,base,color:createColors(inherit),background:{app:appBg,bar:barBg,content:appContentBg,preview:appPreviewBg,gridCellSize:gridCellSize||background.gridCellSize,hoverable:background.hoverable,positive:background.positive,negative:background.negative,warning:background.warning,critical:background.critical},typography:{fonts:{base:fontBase,mono:fontCode},weight:typography.weight,size:typography.size},animation,easing,input:{background:inputBg,border:inputBorder,borderRadius:inputBorderRadius,color:inputTextColor},button:{background:buttonBg||inputBg,border:buttonBorder||inputBorder},boolean:{background:booleanBg||inputBorder,selectedBackground:booleanSelectedBg||inputBg},layoutMargin:10,appBorderColor,appBorderRadius,barTextColor,barHoverColor:barHoverColor||colorSecondary,barSelectedColor:barSelectedColor||colorSecondary,barBg,brand:{title:brandTitle,url:brandUrl,image:brandImage||(brandTitle?null:void 0),target:brandTarget},code:create2({colors:base===\"light\"?lightSyntaxColors:darkSyntaxColors,mono:fontCode}),addonActionsTheme:{...base===\"light\"?chromeLight:chromeDark,BASE_FONT_FAMILY:fontCode,BASE_FONT_SIZE:typography.size.s2-1,BASE_LINE_HEIGHT:\"18px\",BASE_BACKGROUND_COLOR:\"transparent\",BASE_COLOR:textColor,ARROW_COLOR:curriedOpacify$1(.2,appBorderColor),ARROW_MARGIN_RIGHT:4,ARROW_FONT_SIZE:8,TREENODE_FONT_FAMILY:fontCode,TREENODE_FONT_SIZE:typography.size.s2-1,TREENODE_LINE_HEIGHT:\"18px\",TREENODE_PADDING_LEFT:12}}};var isEmpty=o=>Object.keys(o).length===0,isObject=o=>o!=null&&typeof o==\"object\",hasOwnProperty=(o,...args)=>Object.prototype.hasOwnProperty.call(o,...args);var makeObjectWithoutPrototype=()=>Object.create(null);var deletedDiff=(lhs,rhs)=>lhs===rhs||!isObject(lhs)||!isObject(rhs)?{}:Object.keys(lhs).reduce((acc,key)=>{if(hasOwnProperty(rhs,key)){let difference=deletedDiff(lhs[key],rhs[key]);return isObject(difference)&&isEmpty(difference)||(acc[key]=difference),acc}return acc[key]=void 0,acc},makeObjectWithoutPrototype()),deleted_default=deletedDiff;function dedent(templ){for(var values=[],_i=1;_i<arguments.length;_i++)values[_i-1]=arguments[_i];var strings=Array.from(typeof templ==\"string\"?[templ]:templ);strings[strings.length-1]=strings[strings.length-1].replace(/\\r?\\n([\\t ]*)$/,\"\");var indentLengths=strings.reduce(function(arr,str){var matches=str.match(/\\n([\\t ]+|(?!\\s).)/g);return matches?arr.concat(matches.map(function(match2){var _a,_b;return (_b=(_a=match2.match(/[\\t ]/g))===null||_a===void 0?void 0:_a.length)!==null&&_b!==void 0?_b:0})):arr},[]);if(indentLengths.length){var pattern_1=new RegExp(`\n[\t ]{`+Math.min.apply(Math,indentLengths)+\"}\",\"g\");strings=strings.map(function(str){return str.replace(pattern_1,`\n`)});}strings[0]=strings[0].replace(/^\\r?\\n/,\"\");var string=strings[0];return values.forEach(function(value,i){var endentations=string.match(/(?:^|\\n)( *)$/),endentation=endentations?endentations[1]:\"\",indentedValue=value;typeof value==\"string\"&&value.includes(`\n`)&&(indentedValue=String(value).split(`\n`).map(function(str,i2){return i2===0?str:\"\"+endentation+str}).join(`\n`)),string+=indentedValue+strings[i+1];}),string}var ensure=input=>{if(!input)return convert(light_default);let missing=deleted_default(light_default,input);return Object.keys(missing).length&&logger.warn(dedent`\n          Your theme is missing properties, you should update your theme!\n\n          theme-data missing:\n        `,missing),convert(input)};var ignoreSsrWarning=\"/* emotion-disable-server-rendering-unsafe-selector-warning-please-do-not-use-this-the-warning-exists-for-a-reason */\";\n\nexport { CacheProvider, ClassNames, Global, ThemeProvider, convert, createCache, createGlobal, createReset, css, ensure, ignoreSsrWarning, isPropValid, jsx, keyframes, newStyled as styled, useTheme, withTheme };\n"], "names": [], "sourceRoot": ""}