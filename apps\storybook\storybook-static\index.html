<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="utf-8" />

    <title>@storybook/cli - Storybook</title>
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1" />

    
    <link rel="icon" type="image/svg+xml" href="./favicon.svg" />
    
    <link
      rel="prefetch"
      href="./sb-common-assets/nunito-sans-regular.woff2"
      as="font"
      type="font/woff2"
      crossorigin
    />
    <link
      rel="prefetch"
      href="./sb-common-assets/nunito-sans-bold.woff2"
      as="font"
      type="font/woff2"
      crossorigin
    />
    <link rel="stylesheet" href="./sb-common-assets/fonts.css" />

    <link href="./sb-manager/runtime.js" rel="modulepreload" />

    
    <link href="./sb-addons/storybook-core-server-presets-0/common-manager-bundle.js" rel="modulepreload" />
    
    <link href="./sb-addons/onboarding-1/manager-bundle.js" rel="modulepreload" />
    
    <link href="./sb-addons/links-2/manager-bundle.js" rel="modulepreload" />
    
    <link href="./sb-addons/essentials-controls-3/manager-bundle.js" rel="modulepreload" />
    
    <link href="./sb-addons/essentials-actions-4/manager-bundle.js" rel="modulepreload" />
    
    <link href="./sb-addons/essentials-backgrounds-5/manager-bundle.js" rel="modulepreload" />
    
    <link href="./sb-addons/essentials-viewport-6/manager-bundle.js" rel="modulepreload" />
    
    <link href="./sb-addons/essentials-toolbars-7/manager-bundle.js" rel="modulepreload" />
    
    <link href="./sb-addons/essentials-measure-8/manager-bundle.js" rel="modulepreload" />
    
    <link href="./sb-addons/essentials-outline-9/manager-bundle.js" rel="modulepreload" />
    
    <link href="./sb-addons/chromatic-com-storybook-10/manager-bundle.js" rel="modulepreload" />
    
    <link href="./sb-addons/interactions-11/manager-bundle.js" rel="modulepreload" />
    
    <link href="./sb-addons/storybook-12/manager-bundle.js" rel="modulepreload" />
      <style>
    /* Gradient toolbar background */
    .sb-bar {
        background: linear-gradient(135deg, #3826A6 0%, #A11DB1 100%) !important;
    }

    /* Optional: white icons on toolbar */
    .sb-bar button,
    .sb-bar a {
        color: #ffffff !important;
    }

    .sb-bar button:hover {
        background: rgba(255, 255, 255, 0.1) !important;
    }
</style>
 

    <style>
      #storybook-root[hidden] {
        display: none !important;
      }
    </style>

    
  </head>
  <body>
    <div id="root"></div>

    
    <script>
      
        
          window['FEATURES'] = {
  "argTypeTargetsV7": true,
  "legacyDecoratorFileOrder": false,
  "disallowImplicitActionsInRenderV8": true
};
        
      
        
          window['REFS'] = {};
        
      
        
          window['LOGLEVEL'] = "info";
        
      
        
          window['DOCS_OPTIONS'] = {
  "defaultName": "Docs",
  "autodocs": "tag"
};
        
      
        
          window['CONFIG_TYPE'] = "PRODUCTION";
        
      
        
      
        
      
        
          window['TAGS_OPTIONS'] = {
  "dev-only": {
    "excludeFromDocsStories": true
  },
  "docs-only": {
    "excludeFromSidebar": true
  },
  "test-only": {
    "excludeFromSidebar": true,
    "excludeFromDocsStories": true
  }
};
        
      
        
          window['STORYBOOK_RENDERER'] = "react";
        
      
        
          window['STORYBOOK_BUILDER'] = "@storybook/builder-webpack5";
        
      
        
          window['STORYBOOK_FRAMEWORK'] = "@storybook/nextjs";
        
      
    </script>
    

    <script type="module">
      import './sb-manager/globals-runtime.js';
      
      
        import './sb-addons/storybook-core-server-presets-0/common-manager-bundle.js';
      
        import './sb-addons/onboarding-1/manager-bundle.js';
      
        import './sb-addons/links-2/manager-bundle.js';
      
        import './sb-addons/essentials-controls-3/manager-bundle.js';
      
        import './sb-addons/essentials-actions-4/manager-bundle.js';
      
        import './sb-addons/essentials-backgrounds-5/manager-bundle.js';
      
        import './sb-addons/essentials-viewport-6/manager-bundle.js';
      
        import './sb-addons/essentials-toolbars-7/manager-bundle.js';
      
        import './sb-addons/essentials-measure-8/manager-bundle.js';
      
        import './sb-addons/essentials-outline-9/manager-bundle.js';
      
        import './sb-addons/chromatic-com-storybook-10/manager-bundle.js';
      
        import './sb-addons/interactions-11/manager-bundle.js';
      
        import './sb-addons/storybook-12/manager-bundle.js';
      
      
      import './sb-manager/runtime.js';
    </script>

    
    <link href="./sb-preview/runtime.js" rel="prefetch" as="script" />
    
  </body>
</html>
