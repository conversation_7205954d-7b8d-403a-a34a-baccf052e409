{"v": 5, "entries": {"introduction-welcome-to-ever-cloc-sdk--docs": {"id": "introduction-welcome-to-ever-cloc-sdk--docs", "title": "Introduction/Welcome to Ever Cloc SDK", "name": "Docs", "importPath": "./src/stories/introduction/Introduction.mdx", "storiesImports": [], "type": "docs", "tags": ["dev", "test", "unattached-mdx"]}, "charts-reports-charts-cloc-chart--default": {"type": "story", "id": "charts-reports-charts-cloc-chart--default", "name": "<PERSON><PERSON><PERSON>", "title": "Charts & Reports/Charts/Cloc Chart", "importPath": "./src/stories/analytics-charts/charts/ClocChart.stories.tsx", "tags": ["dev", "test"]}, "charts-reports-charts-cloc-chart--bar-vertical": {"type": "story", "id": "charts-reports-charts-cloc-chart--bar-vertical", "name": "Bar Vertical", "title": "Charts & Reports/Charts/Cloc Chart", "importPath": "./src/stories/analytics-charts/charts/ClocChart.stories.tsx", "tags": ["dev", "test"]}, "charts-reports-charts-cloc-chart--area-chart": {"type": "story", "id": "charts-reports-charts-cloc-chart--area-chart", "name": "Area Chart", "title": "Charts & Reports/Charts/Cloc Chart", "importPath": "./src/stories/analytics-charts/charts/ClocChart.stories.tsx", "tags": ["dev", "test"]}, "charts-reports-charts-cloc-chart--line-chart": {"type": "story", "id": "charts-reports-charts-cloc-chart--line-chart", "name": "Line Chart", "title": "Charts & Reports/Charts/Cloc Chart", "importPath": "./src/stories/analytics-charts/charts/ClocChart.stories.tsx", "tags": ["dev", "test"]}, "charts-reports-charts-cloc-chart--pie-chart": {"type": "story", "id": "charts-reports-charts-cloc-chart--pie-chart", "name": "Pie Chart", "title": "Charts & Reports/Charts/Cloc Chart", "importPath": "./src/stories/analytics-charts/charts/ClocChart.stories.tsx", "tags": ["dev", "test"]}, "charts-reports-charts-cloc-chart--radar-chart": {"type": "story", "id": "charts-reports-charts-cloc-chart--radar-chart", "name": "Radar Chart", "title": "Charts & Reports/Charts/Cloc Chart", "importPath": "./src/stories/analytics-charts/charts/ClocChart.stories.tsx", "tags": ["dev", "test"]}, "charts-reports-charts-cloc-chart--radial-chart": {"type": "story", "id": "charts-reports-charts-cloc-chart--radial-chart", "name": "Radial Chart", "title": "Charts & Reports/Charts/Cloc Chart", "importPath": "./src/stories/analytics-charts/charts/ClocChart.stories.tsx", "tags": ["dev", "test"]}, "charts-reports-charts-cloc-chart--tooltip-chart": {"type": "story", "id": "charts-reports-charts-cloc-chart--tooltip-chart", "name": "Tooltip Chart", "title": "Charts & Reports/Charts/Cloc Chart", "importPath": "./src/stories/analytics-charts/charts/ClocChart.stories.tsx", "tags": ["dev", "test"]}, "charts-reports-reports-cloc-apps-url-list--default": {"type": "story", "id": "charts-reports-reports-cloc-apps-url-list--default", "name": "<PERSON><PERSON><PERSON>", "title": "Charts & Reports/Reports/Cloc Apps URL List", "importPath": "./src/stories/analytics-charts/reports/ClocAppsUrlList.stories.tsx", "tags": ["dev", "test"]}, "charts-reports-reports-cloc-apps-url-list--small-size": {"type": "story", "id": "charts-reports-reports-cloc-apps-url-list--small-size", "name": "Small Size", "title": "Charts & Reports/Reports/Cloc Apps URL List", "importPath": "./src/stories/analytics-charts/reports/ClocAppsUrlList.stories.tsx", "tags": ["dev", "test"]}, "charts-reports-reports-cloc-apps-url-list--large-size": {"type": "story", "id": "charts-reports-reports-cloc-apps-url-list--large-size", "name": "Large Size", "title": "Charts & Reports/Reports/Cloc Apps URL List", "importPath": "./src/stories/analytics-charts/reports/ClocAppsUrlList.stories.tsx", "tags": ["dev", "test"]}, "charts-reports-reports-cloc-apps-url-list--bordered-variant": {"type": "story", "id": "charts-reports-reports-cloc-apps-url-list--bordered-variant", "name": "Bordered Variant", "title": "Charts & Reports/Reports/Cloc Apps URL List", "importPath": "./src/stories/analytics-charts/reports/ClocAppsUrlList.stories.tsx", "tags": ["dev", "test"]}, "charts-reports-reports-cloc-projects-list--default": {"type": "story", "id": "charts-reports-reports-cloc-projects-list--default", "name": "<PERSON><PERSON><PERSON>", "title": "Charts & Reports/Reports/Cloc Projects List", "importPath": "./src/stories/analytics-charts/reports/ClocProjectsList.stories.tsx", "tags": ["dev", "test"]}, "charts-reports-reports-cloc-projects-list--small-size": {"type": "story", "id": "charts-reports-reports-cloc-projects-list--small-size", "name": "Small Size", "title": "Charts & Reports/Reports/Cloc Projects List", "importPath": "./src/stories/analytics-charts/reports/ClocProjectsList.stories.tsx", "tags": ["dev", "test"]}, "charts-reports-reports-cloc-projects-list--large-size": {"type": "story", "id": "charts-reports-reports-cloc-projects-list--large-size", "name": "Large Size", "title": "Charts & Reports/Reports/Cloc Projects List", "importPath": "./src/stories/analytics-charts/reports/ClocProjectsList.stories.tsx", "tags": ["dev", "test"]}, "charts-reports-reports-cloc-projects-list--bordered-variant": {"type": "story", "id": "charts-reports-reports-cloc-projects-list--bordered-variant", "name": "Bordered Variant", "title": "Charts & Reports/Reports/Cloc Projects List", "importPath": "./src/stories/analytics-charts/reports/ClocProjectsList.stories.tsx", "tags": ["dev", "test"]}, "charts-reports-reports-cloc-report--default": {"type": "story", "id": "charts-reports-reports-cloc-report--default", "name": "<PERSON><PERSON><PERSON>", "title": "Charts & Reports/Reports/Cloc Report", "importPath": "./src/stories/analytics-charts/reports/ClocReport.stories.tsx", "tags": ["dev", "test"]}, "charts-reports-reports-cloc-report--small-size": {"type": "story", "id": "charts-reports-reports-cloc-report--small-size", "name": "Small Size", "title": "Charts & Reports/Reports/Cloc Report", "importPath": "./src/stories/analytics-charts/reports/ClocReport.stories.tsx", "tags": ["dev", "test"]}, "charts-reports-reports-cloc-report--large-size": {"type": "story", "id": "charts-reports-reports-cloc-report--large-size", "name": "Large Size", "title": "Charts & Reports/Reports/Cloc Report", "importPath": "./src/stories/analytics-charts/reports/ClocReport.stories.tsx", "tags": ["dev", "test"]}, "charts-reports-reports-cloc-report--bordered-variant": {"type": "story", "id": "charts-reports-reports-cloc-report--bordered-variant", "name": "Bordered Variant", "title": "Charts & Reports/Reports/Cloc Report", "importPath": "./src/stories/analytics-charts/reports/ClocReport.stories.tsx", "tags": ["dev", "test"]}, "charts-reports-reports-cloc-report--area-chart-report": {"type": "story", "id": "charts-reports-reports-cloc-report--area-chart-report", "name": "Area Chart Report", "title": "Charts & Reports/Reports/Cloc Report", "importPath": "./src/stories/analytics-charts/reports/ClocReport.stories.tsx", "tags": ["dev", "test"]}, "charts-reports-reports-cloc-report--tooltip-chart-report": {"type": "story", "id": "charts-reports-reports-cloc-report--tooltip-chart-report", "name": "Tooltip Chart Report", "title": "Charts & Reports/Reports/Cloc Report", "importPath": "./src/stories/analytics-charts/reports/ClocReport.stories.tsx", "tags": ["dev", "test"]}, "charts-reports-reports-cloc-report--line-chart-report": {"type": "story", "id": "charts-reports-reports-cloc-report--line-chart-report", "name": "Line Chart Report", "title": "Charts & Reports/Reports/Cloc Report", "importPath": "./src/stories/analytics-charts/reports/ClocReport.stories.tsx", "tags": ["dev", "test"]}, "charts-reports-reports-cloc-report--bar-vertical-chart-report": {"type": "story", "id": "charts-reports-reports-cloc-report--bar-vertical-chart-report", "name": "Bar Vertical Chart Report", "title": "Charts & Reports/Reports/Cloc Report", "importPath": "./src/stories/analytics-charts/reports/ClocReport.stories.tsx", "tags": ["dev", "test"]}, "charts-reports-reports-cloc-report--bar-chart-report": {"type": "story", "id": "charts-reports-reports-cloc-report--bar-chart-report", "name": "Bar Chart Report", "title": "Charts & Reports/Reports/Cloc Report", "importPath": "./src/stories/analytics-charts/reports/ClocReport.stories.tsx", "tags": ["dev", "test"]}, "charts-reports-reports-cloc-report--pie-chart-report": {"type": "story", "id": "charts-reports-reports-cloc-report--pie-chart-report", "name": "Pie Chart Report", "title": "Charts & Reports/Reports/Cloc Report", "importPath": "./src/stories/analytics-charts/reports/ClocReport.stories.tsx", "tags": ["dev", "test"]}, "charts-reports-reports-cloc-report--radar-chart-report": {"type": "story", "id": "charts-reports-reports-cloc-report--radar-chart-report", "name": "Radar Chart Report", "title": "Charts & Reports/Reports/Cloc Report", "importPath": "./src/stories/analytics-charts/reports/ClocReport.stories.tsx", "tags": ["dev", "test"]}, "charts-reports-reports-cloc-report--radial-chart-report": {"type": "story", "id": "charts-reports-reports-cloc-report--radial-chart-report", "name": "Radial Chart Report", "title": "Charts & Reports/Reports/Cloc Report", "importPath": "./src/stories/analytics-charts/reports/ClocReport.stories.tsx", "tags": ["dev", "test"]}, "charts-reports-reports-cloc-tasks-list--default": {"type": "story", "id": "charts-reports-reports-cloc-tasks-list--default", "name": "<PERSON><PERSON><PERSON>", "title": "Charts & Reports/Reports/Cloc Tasks List", "importPath": "./src/stories/analytics-charts/reports/ClocTasksList.stories.tsx", "tags": ["dev", "test"]}, "charts-reports-reports-cloc-tasks-list--small-size": {"type": "story", "id": "charts-reports-reports-cloc-tasks-list--small-size", "name": "Small Size", "title": "Charts & Reports/Reports/Cloc Tasks List", "importPath": "./src/stories/analytics-charts/reports/ClocTasksList.stories.tsx", "tags": ["dev", "test"]}, "charts-reports-reports-cloc-tasks-list--large-size": {"type": "story", "id": "charts-reports-reports-cloc-tasks-list--large-size", "name": "Large Size", "title": "Charts & Reports/Reports/Cloc Tasks List", "importPath": "./src/stories/analytics-charts/reports/ClocTasksList.stories.tsx", "tags": ["dev", "test"]}, "charts-reports-reports-cloc-tasks-list--bordered-variant": {"type": "story", "id": "charts-reports-reports-cloc-tasks-list--bordered-variant", "name": "Bordered Variant", "title": "Charts & Reports/Reports/Cloc Tasks List", "importPath": "./src/stories/analytics-charts/reports/ClocTasksList.stories.tsx", "tags": ["dev", "test"]}, "authentication-login-dialog--default": {"type": "story", "id": "authentication-login-dialog--default", "name": "<PERSON><PERSON><PERSON>", "title": "Authentication/Login Dialog", "importPath": "./src/stories/authentication/ClocLoginDialog.stories.tsx", "tags": ["dev", "test"]}, "authentication-login-dialog--with-signup-link": {"type": "story", "id": "authentication-login-dialog--with-signup-link", "name": "With Signup Link", "title": "Authentication/Login Dialog", "importPath": "./src/stories/authentication/ClocLoginDialog.stories.tsx", "tags": ["dev", "test"]}, "authentication-login-dialog--custom-trigger": {"type": "story", "id": "authentication-login-dialog--custom-trigger", "name": "Custom Trigger", "title": "Authentication/Login Dialog", "importPath": "./src/stories/authentication/ClocLoginDialog.stories.tsx", "tags": ["dev", "test"]}, "authentication-login-dialog--icon-trigger": {"type": "story", "id": "authentication-login-dialog--icon-trigger", "name": "<PERSON><PERSON>", "title": "Authentication/Login Dialog", "importPath": "./src/stories/authentication/ClocLoginDialog.stories.tsx", "tags": ["dev", "test"]}, "authentication-login-dialog--primary-button": {"type": "story", "id": "authentication-login-dialog--primary-button", "name": "Primary Button", "title": "Authentication/Login Dialog", "importPath": "./src/stories/authentication/ClocLoginDialog.stories.tsx", "tags": ["dev", "test"]}, "authentication-login-dialog--navigation-item": {"type": "story", "id": "authentication-login-dialog--navigation-item", "name": "Navigation Item", "title": "Authentication/Login Dialog", "importPath": "./src/stories/authentication/ClocLoginDialog.stories.tsx", "tags": ["dev", "test"]}, "authentication-login-dialog--header-action": {"type": "story", "id": "authentication-login-dialog--header-action", "name": "Header Action", "title": "Authentication/Login Dialog", "importPath": "./src/stories/authentication/ClocLoginDialog.stories.tsx", "tags": ["dev", "test"]}, "authentication-login-dialog--call-to-action": {"type": "story", "id": "authentication-login-dialog--call-to-action", "name": "Call To Action", "title": "Authentication/Login Dialog", "importPath": "./src/stories/authentication/ClocLoginDialog.stories.tsx", "tags": ["dev", "test"]}, "authentication-login-dialog--settings-menu": {"type": "story", "id": "authentication-login-dialog--settings-menu", "name": "<PERSON><PERSON><PERSON>", "title": "Authentication/Login Dialog", "importPath": "./src/stories/authentication/ClocLoginDialog.stories.tsx", "tags": ["dev", "test"]}, "authentication-login-dialog--mobile-menu": {"type": "story", "id": "authentication-login-dialog--mobile-menu", "name": "Mobile Menu", "title": "Authentication/Login Dialog", "importPath": "./src/stories/authentication/ClocLoginDialog.stories.tsx", "tags": ["dev", "test"]}, "authentication-login-dialog--landing-page-hero": {"type": "story", "id": "authentication-login-dialog--landing-page-hero", "name": "<PERSON> Page <PERSON>", "title": "Authentication/Login Dialog", "importPath": "./src/stories/authentication/ClocLoginDialog.stories.tsx", "tags": ["dev", "test"]}, "authentication-login-form--default": {"type": "story", "id": "authentication-login-form--default", "name": "<PERSON><PERSON><PERSON>", "title": "Authentication/Login Form", "importPath": "./src/stories/authentication/ClocLoginForm.stories.tsx", "tags": ["dev", "test"]}, "authentication-login-form--with-signup-link": {"type": "story", "id": "authentication-login-form--with-signup-link", "name": "With Signup Link", "title": "Authentication/Login Form", "importPath": "./src/stories/authentication/ClocLoginForm.stories.tsx", "tags": ["dev", "test"]}, "authentication-login-form--custom-styling": {"type": "story", "id": "authentication-login-form--custom-styling", "name": "Custom Styling", "title": "Authentication/Login Form", "importPath": "./src/stories/authentication/ClocLoginForm.stories.tsx", "tags": ["dev", "test"]}, "authentication-login-form--with-redirect-handler": {"type": "story", "id": "authentication-login-form--with-redirect-handler", "name": "With Redirect <PERSON>", "title": "Authentication/Login Form", "importPath": "./src/stories/authentication/ClocLoginForm.stories.tsx", "tags": ["dev", "test"]}, "authentication-login-form--compact-form": {"type": "story", "id": "authentication-login-form--compact-form", "name": "Compact Form", "title": "Authentication/Login Form", "importPath": "./src/stories/authentication/ClocLoginForm.stories.tsx", "tags": ["dev", "test"]}, "authentication-login-form--full-width-form": {"type": "story", "id": "authentication-login-form--full-width-form", "name": "Full Width Form", "title": "Authentication/Login Form", "importPath": "./src/stories/authentication/ClocLoginForm.stories.tsx", "tags": ["dev", "test"]}, "authentication-login-form--in-modal": {"type": "story", "id": "authentication-login-form--in-modal", "name": "In Modal", "title": "Authentication/Login Form", "importPath": "./src/stories/authentication/ClocLoginForm.stories.tsx", "tags": ["dev", "test"]}, "authentication-login-form--onboarding-flow": {"type": "story", "id": "authentication-login-form--onboarding-flow", "name": "Onboarding Flow", "title": "Authentication/Login Form", "importPath": "./src/stories/authentication/ClocLoginForm.stories.tsx", "tags": ["dev", "test"]}, "authentication-login-form--with-branding": {"type": "story", "id": "authentication-login-form--with-branding", "name": "With Branding", "title": "Authentication/Login Form", "importPath": "./src/stories/authentication/ClocLoginForm.stories.tsx", "tags": ["dev", "test"]}, "authentication-login-form--mobile-optimized": {"type": "story", "id": "authentication-login-form--mobile-optimized", "name": "Mobile Optimized", "title": "Authentication/Login Form", "importPath": "./src/stories/authentication/ClocLoginForm.stories.tsx", "tags": ["dev", "test"]}, "authentication-profile-form--default": {"type": "story", "id": "authentication-profile-form--default", "name": "<PERSON><PERSON><PERSON>", "title": "Authentication/Profile Form", "importPath": "./src/stories/authentication/ClocProfileForm.stories.tsx", "tags": ["dev", "test"]}, "authentication-profile-form--custom-styling": {"type": "story", "id": "authentication-profile-form--custom-styling", "name": "Custom Styling", "title": "Authentication/Profile Form", "importPath": "./src/stories/authentication/ClocProfileForm.stories.tsx", "tags": ["dev", "test"]}, "authentication-profile-form--compact-layout": {"type": "story", "id": "authentication-profile-form--compact-layout", "name": "Compact Layout", "title": "Authentication/Profile Form", "importPath": "./src/stories/authentication/ClocProfileForm.stories.tsx", "tags": ["dev", "test"]}, "authentication-profile-form--in-settings-page": {"type": "story", "id": "authentication-profile-form--in-settings-page", "name": "In Settings Page", "title": "Authentication/Profile Form", "importPath": "./src/stories/authentication/ClocProfileForm.stories.tsx", "tags": ["dev", "test"]}, "authentication-profile-form--in-modal": {"type": "story", "id": "authentication-profile-form--in-modal", "name": "In Modal", "title": "Authentication/Profile Form", "importPath": "./src/stories/authentication/ClocProfileForm.stories.tsx", "tags": ["dev", "test"]}, "authentication-profile-form--in-tabs": {"type": "story", "id": "authentication-profile-form--in-tabs", "name": "In Tabs", "title": "Authentication/Profile Form", "importPath": "./src/stories/authentication/ClocProfileForm.stories.tsx", "tags": ["dev", "test"]}, "authentication-profile-form--mobile-view": {"type": "story", "id": "authentication-profile-form--mobile-view", "name": "Mobile View", "title": "Authentication/Profile Form", "importPath": "./src/stories/authentication/ClocProfileForm.stories.tsx", "tags": ["dev", "test"]}, "authentication-profile-form--with-sidebar": {"type": "story", "id": "authentication-profile-form--with-sidebar", "name": "With Sidebar", "title": "Authentication/Profile Form", "importPath": "./src/stories/authentication/ClocProfileForm.stories.tsx", "tags": ["dev", "test"]}, "authentication-profile-form--onboarding-step": {"type": "story", "id": "authentication-profile-form--onboarding-step", "name": "Onboarding Step", "title": "Authentication/Profile Form", "importPath": "./src/stories/authentication/ClocProfileForm.stories.tsx", "tags": ["dev", "test"]}, "authentication-registration-dialog--default": {"type": "story", "id": "authentication-registration-dialog--default", "name": "<PERSON><PERSON><PERSON>", "title": "Authentication/Registration Dialog", "importPath": "./src/stories/authentication/ClocRegistrationDialog.stories.tsx", "tags": ["dev", "test"]}, "authentication-registration-dialog--with-sign-in-link": {"type": "story", "id": "authentication-registration-dialog--with-sign-in-link", "name": "With Sign In Link", "title": "Authentication/Registration Dialog", "importPath": "./src/stories/authentication/ClocRegistrationDialog.stories.tsx", "tags": ["dev", "test"]}, "authentication-registration-dialog--custom-trigger": {"type": "story", "id": "authentication-registration-dialog--custom-trigger", "name": "Custom Trigger", "title": "Authentication/Registration Dialog", "importPath": "./src/stories/authentication/ClocRegistrationDialog.stories.tsx", "tags": ["dev", "test"]}, "authentication-registration-dialog--email-signup-trigger": {"type": "story", "id": "authentication-registration-dialog--email-signup-trigger", "name": "<PERSON><PERSON>", "title": "Authentication/Registration Dialog", "importPath": "./src/stories/authentication/ClocRegistrationDialog.stories.tsx", "tags": ["dev", "test"]}, "authentication-registration-dialog--team-invite-trigger": {"type": "story", "id": "authentication-registration-dialog--team-invite-trigger", "name": "Team <PERSON><PERSON><PERSON>", "title": "Authentication/Registration Dialog", "importPath": "./src/stories/authentication/ClocRegistrationDialog.stories.tsx", "tags": ["dev", "test"]}, "authentication-registration-form--default": {"type": "story", "id": "authentication-registration-form--default", "name": "<PERSON><PERSON><PERSON>", "title": "Authentication/Registration Form", "importPath": "./src/stories/authentication/ClocRegistrationForm.stories.tsx", "tags": ["dev", "test"]}, "authentication-registration-form--with-sign-in-link": {"type": "story", "id": "authentication-registration-form--with-sign-in-link", "name": "With Sign In Link", "title": "Authentication/Registration Form", "importPath": "./src/stories/authentication/ClocRegistrationForm.stories.tsx", "tags": ["dev", "test"]}, "authentication-registration-form--with-redirect-handler": {"type": "story", "id": "authentication-registration-form--with-redirect-handler", "name": "With Redirect <PERSON>", "title": "Authentication/Registration Form", "importPath": "./src/stories/authentication/ClocRegistrationForm.stories.tsx", "tags": ["dev", "test"]}, "authentication-registration-form--custom-styling": {"type": "story", "id": "authentication-registration-form--custom-styling", "name": "Custom Styling", "title": "Authentication/Registration Form", "importPath": "./src/stories/authentication/ClocRegistrationForm.stories.tsx", "tags": ["dev", "test"]}, "authentication-user-avatar--default": {"type": "story", "id": "authentication-user-avatar--default", "name": "<PERSON><PERSON><PERSON>", "title": "Authentication/User Avatar", "importPath": "./src/stories/authentication/ClocUserAvatar.stories.tsx", "tags": ["dev", "test"]}, "authentication-user-avatar--without-menu": {"type": "story", "id": "authentication-user-avatar--without-menu", "name": "Without Menu", "title": "Authentication/User Avatar", "importPath": "./src/stories/authentication/ClocUserAvatar.stories.tsx", "tags": ["dev", "test"]}, "authentication-user-avatar--center-position": {"type": "story", "id": "authentication-user-avatar--center-position", "name": "Center Position", "title": "Authentication/User Avatar", "importPath": "./src/stories/authentication/ClocUserAvatar.stories.tsx", "tags": ["dev", "test"]}, "authentication-user-avatar--start-position": {"type": "story", "id": "authentication-user-avatar--start-position", "name": "Start Position", "title": "Authentication/User Avatar", "importPath": "./src/stories/authentication/ClocUserAvatar.stories.tsx", "tags": ["dev", "test"]}, "authentication-user-avatar--in-navigation": {"type": "story", "id": "authentication-user-avatar--in-navigation", "name": "In Navigation", "title": "Authentication/User Avatar", "importPath": "./src/stories/authentication/ClocUserAvatar.stories.tsx", "tags": ["dev", "test"]}, "authentication-user-avatar--in-sidebar": {"type": "story", "id": "authentication-user-avatar--in-sidebar", "name": "In Sidebar", "title": "Authentication/User Avatar", "importPath": "./src/stories/authentication/ClocUserAvatar.stories.tsx", "tags": ["dev", "test"]}, "authentication-user-avatar--with-custom-menu": {"type": "story", "id": "authentication-user-avatar--with-custom-menu", "name": "With Custom Menu", "title": "Authentication/User Avatar", "importPath": "./src/stories/authentication/ClocUserAvatar.stories.tsx", "tags": ["dev", "test"]}, "authentication-user-avatar--compact-view": {"type": "story", "id": "authentication-user-avatar--compact-view", "name": "Compact View", "title": "Authentication/User Avatar", "importPath": "./src/stories/authentication/ClocUserAvatar.stories.tsx", "tags": ["dev", "test"]}, "authentication-user-avatar--in-toolbar": {"type": "story", "id": "authentication-user-avatar--in-toolbar", "name": "In Toolbar", "title": "Authentication/User Avatar", "importPath": "./src/stories/authentication/ClocUserAvatar.stories.tsx", "tags": ["dev", "test"]}, "authentication-user-avatar--mobile-header": {"type": "story", "id": "authentication-user-avatar--mobile-header", "name": "Mobile Header", "title": "Authentication/User Avatar", "importPath": "./src/stories/authentication/ClocUserAvatar.stories.tsx", "tags": ["dev", "test"]}, "authentication-password-form--default": {"type": "story", "id": "authentication-password-form--default", "name": "<PERSON><PERSON><PERSON>", "title": "Authentication/Password Form", "importPath": "./src/stories/authentication/PasswordForm.stories.tsx", "tags": ["dev", "test"]}, "authentication-password-form--with-redirect-handler": {"type": "story", "id": "authentication-password-form--with-redirect-handler", "name": "With Redirect <PERSON>", "title": "Authentication/Password Form", "importPath": "./src/stories/authentication/PasswordForm.stories.tsx", "tags": ["dev", "test"]}, "authentication-password-form--custom-styling": {"type": "story", "id": "authentication-password-form--custom-styling", "name": "Custom Styling", "title": "Authentication/Password Form", "importPath": "./src/stories/authentication/PasswordForm.stories.tsx", "tags": ["dev", "test"]}, "authentication-password-form--in-login-page": {"type": "story", "id": "authentication-password-form--in-login-page", "name": "In Login Page", "title": "Authentication/Password Form", "importPath": "./src/stories/authentication/PasswordForm.stories.tsx", "tags": ["dev", "test"]}, "authentication-token-form--default": {"type": "story", "id": "authentication-token-form--default", "name": "<PERSON><PERSON><PERSON>", "title": "Authentication/Token Form", "importPath": "./src/stories/authentication/TokenForm.stories.tsx", "tags": ["dev", "test"]}, "authentication-token-form--with-redirect-handler": {"type": "story", "id": "authentication-token-form--with-redirect-handler", "name": "With Redirect <PERSON>", "title": "Authentication/Token Form", "importPath": "./src/stories/authentication/TokenForm.stories.tsx", "tags": ["dev", "test"]}, "authentication-token-form--custom-styling": {"type": "story", "id": "authentication-token-form--custom-styling", "name": "Custom Styling", "title": "Authentication/Token Form", "importPath": "./src/stories/authentication/TokenForm.stories.tsx", "tags": ["dev", "test"]}, "foundation-button--default": {"type": "story", "id": "foundation-button--default", "name": "<PERSON><PERSON><PERSON>", "title": "Foundation/Button", "importPath": "./src/stories/foundation/Button.stories.tsx", "tags": ["dev", "test"]}, "foundation-button--destructive": {"type": "story", "id": "foundation-button--destructive", "name": "Destructive", "title": "Foundation/Button", "importPath": "./src/stories/foundation/Button.stories.tsx", "tags": ["dev", "test"]}, "foundation-button--outline": {"type": "story", "id": "foundation-button--outline", "name": "Outline", "title": "Foundation/Button", "importPath": "./src/stories/foundation/Button.stories.tsx", "tags": ["dev", "test"]}, "foundation-button--secondary": {"type": "story", "id": "foundation-button--secondary", "name": "Secondary", "title": "Foundation/Button", "importPath": "./src/stories/foundation/Button.stories.tsx", "tags": ["dev", "test"]}, "foundation-button--ghost": {"type": "story", "id": "foundation-button--ghost", "name": "Ghost", "title": "Foundation/Button", "importPath": "./src/stories/foundation/Button.stories.tsx", "tags": ["dev", "test"]}, "foundation-button--link": {"type": "story", "id": "foundation-button--link", "name": "Link", "title": "Foundation/Button", "importPath": "./src/stories/foundation/Button.stories.tsx", "tags": ["dev", "test"]}, "foundation-button--small": {"type": "story", "id": "foundation-button--small", "name": "Small", "title": "Foundation/Button", "importPath": "./src/stories/foundation/Button.stories.tsx", "tags": ["dev", "test"]}, "foundation-button--large": {"type": "story", "id": "foundation-button--large", "name": "Large", "title": "Foundation/Button", "importPath": "./src/stories/foundation/Button.stories.tsx", "tags": ["dev", "test"]}, "foundation-button--icon": {"type": "story", "id": "foundation-button--icon", "name": "Icon", "title": "Foundation/Button", "importPath": "./src/stories/foundation/Button.stories.tsx", "tags": ["dev", "test"]}, "foundation-button--disabled": {"type": "story", "id": "foundation-button--disabled", "name": "Disabled", "title": "Foundation/Button", "importPath": "./src/stories/foundation/Button.stories.tsx", "tags": ["dev", "test"]}, "foundation-calendar--default": {"type": "story", "id": "foundation-calendar--default", "name": "<PERSON><PERSON><PERSON>", "title": "Foundation/Calendar", "importPath": "./src/stories/foundation/Calendar.stories.tsx", "tags": ["dev", "test"]}, "foundation-calendar--without-outside-days": {"type": "story", "id": "foundation-calendar--without-outside-days", "name": "Without Outside Days", "title": "Foundation/Calendar", "importPath": "./src/stories/foundation/Calendar.stories.tsx", "tags": ["dev", "test"]}, "foundation-calendar--with-selected-date": {"type": "story", "id": "foundation-calendar--with-selected-date", "name": "With Selected Date", "title": "Foundation/Calendar", "importPath": "./src/stories/foundation/Calendar.stories.tsx", "tags": ["dev", "test"]}, "foundation-calendar--disabled": {"type": "story", "id": "foundation-calendar--disabled", "name": "Disabled", "title": "Foundation/Calendar", "importPath": "./src/stories/foundation/Calendar.stories.tsx", "tags": ["dev", "test"]}, "foundation-date-picker--default": {"type": "story", "id": "foundation-date-picker--default", "name": "<PERSON><PERSON><PERSON>", "title": "Foundation/Date Picker", "importPath": "./src/stories/foundation/DatePicker.stories.tsx", "tags": ["dev", "test"]}, "foundation-date-picker--without-icon": {"type": "story", "id": "foundation-date-picker--without-icon", "name": "Without Icon", "title": "Foundation/Date Picker", "importPath": "./src/stories/foundation/DatePicker.stories.tsx", "tags": ["dev", "test"]}, "foundation-date-picker--custom-placeholder": {"type": "story", "id": "foundation-date-picker--custom-placeholder", "name": "Custom Placeholder", "title": "Foundation/Date Picker", "importPath": "./src/stories/foundation/DatePicker.stories.tsx", "tags": ["dev", "test"]}, "foundation-date-picker--with-selected-date": {"type": "story", "id": "foundation-date-picker--with-selected-date", "name": "With Selected Date", "title": "Foundation/Date Picker", "importPath": "./src/stories/foundation/DatePicker.stories.tsx", "tags": ["dev", "test"]}, "foundation-date-picker--disabled": {"type": "story", "id": "foundation-date-picker--disabled", "name": "Disabled", "title": "Foundation/Date Picker", "importPath": "./src/stories/foundation/DatePicker.stories.tsx", "tags": ["dev", "test"]}, "foundation-date-range-picker--default": {"type": "story", "id": "foundation-date-range-picker--default", "name": "<PERSON><PERSON><PERSON>", "title": "Foundation/Date Range Picker", "importPath": "./src/stories/foundation/DateRangePicker.stories.tsx", "tags": ["dev", "test"]}, "foundation-date-range-picker--custom-placeholder": {"type": "story", "id": "foundation-date-range-picker--custom-placeholder", "name": "Custom Placeholder", "title": "Foundation/Date Range Picker", "importPath": "./src/stories/foundation/DateRangePicker.stories.tsx", "tags": ["dev", "test"]}, "foundation-date-range-picker--with-selected-range": {"type": "story", "id": "foundation-date-range-picker--with-selected-range", "name": "With Selected Range", "title": "Foundation/Date Range Picker", "importPath": "./src/stories/foundation/DateRangePicker.stories.tsx", "tags": ["dev", "test"]}, "foundation-date-range-picker--disabled": {"type": "story", "id": "foundation-date-range-picker--disabled", "name": "Disabled", "title": "Foundation/Date Range Picker", "importPath": "./src/stories/foundation/DateRangePicker.stories.tsx", "tags": ["dev", "test"]}, "foundation-input--default": {"type": "story", "id": "foundation-input--default", "name": "<PERSON><PERSON><PERSON>", "title": "Foundation/Input", "importPath": "./src/stories/foundation/Input.stories.tsx", "tags": ["dev", "test"]}, "foundation-input--email": {"type": "story", "id": "foundation-input--email", "name": "Email", "title": "Foundation/Input", "importPath": "./src/stories/foundation/Input.stories.tsx", "tags": ["dev", "test"]}, "foundation-input--password": {"type": "story", "id": "foundation-input--password", "name": "Password", "title": "Foundation/Input", "importPath": "./src/stories/foundation/Input.stories.tsx", "tags": ["dev", "test"]}, "foundation-input--number": {"type": "story", "id": "foundation-input--number", "name": "Number", "title": "Foundation/Input", "importPath": "./src/stories/foundation/Input.stories.tsx", "tags": ["dev", "test"]}, "foundation-input--search": {"type": "story", "id": "foundation-input--search", "name": "Search", "title": "Foundation/Input", "importPath": "./src/stories/foundation/Input.stories.tsx", "tags": ["dev", "test"]}, "foundation-input--disabled": {"type": "story", "id": "foundation-input--disabled", "name": "Disabled", "title": "Foundation/Input", "importPath": "./src/stories/foundation/Input.stories.tsx", "tags": ["dev", "test"]}, "foundation-progress--default": {"type": "story", "id": "foundation-progress--default", "name": "<PERSON><PERSON><PERSON>", "title": "Foundation/Progress", "importPath": "./src/stories/foundation/Progress.stories.tsx", "tags": ["dev", "test"]}, "foundation-progress--low-progress": {"type": "story", "id": "foundation-progress--low-progress", "name": "Low Progress", "title": "Foundation/Progress", "importPath": "./src/stories/foundation/Progress.stories.tsx", "tags": ["dev", "test"]}, "foundation-progress--high-progress": {"type": "story", "id": "foundation-progress--high-progress", "name": "High Progress", "title": "Foundation/Progress", "importPath": "./src/stories/foundation/Progress.stories.tsx", "tags": ["dev", "test"]}, "foundation-progress--complete": {"type": "story", "id": "foundation-progress--complete", "name": "Complete", "title": "Foundation/Progress", "importPath": "./src/stories/foundation/Progress.stories.tsx", "tags": ["dev", "test"]}, "foundation-progress--custom-styling": {"type": "story", "id": "foundation-progress--custom-styling", "name": "Custom Styling", "title": "Foundation/Progress", "importPath": "./src/stories/foundation/Progress.stories.tsx", "tags": ["dev", "test"]}, "foundation-progress--empty": {"type": "story", "id": "foundation-progress--empty", "name": "Empty", "title": "Foundation/Progress", "importPath": "./src/stories/foundation/Progress.stories.tsx", "tags": ["dev", "test"]}, "foundation-select--default": {"type": "story", "id": "foundation-select--default", "name": "<PERSON><PERSON><PERSON>", "title": "Foundation/Select", "importPath": "./src/stories/foundation/Select.stories.tsx", "tags": ["dev", "test"]}, "foundation-select--with-icons": {"type": "story", "id": "foundation-select--with-icons", "name": "With Icons", "title": "Foundation/Select", "importPath": "./src/stories/foundation/Select.stories.tsx", "tags": ["dev", "test"]}, "foundation-select--small": {"type": "story", "id": "foundation-select--small", "name": "Small", "title": "Foundation/Select", "importPath": "./src/stories/foundation/Select.stories.tsx", "tags": ["dev", "test"]}, "foundation-select--large": {"type": "story", "id": "foundation-select--large", "name": "Large", "title": "Foundation/Select", "importPath": "./src/stories/foundation/Select.stories.tsx", "tags": ["dev", "test"]}, "foundation-select--loading": {"type": "story", "id": "foundation-select--loading", "name": "Loading", "title": "Foundation/Select", "importPath": "./src/stories/foundation/Select.stories.tsx", "tags": ["dev", "test"]}, "foundation-select--disabled": {"type": "story", "id": "foundation-select--disabled", "name": "Disabled", "title": "Foundation/Select", "importPath": "./src/stories/foundation/Select.stories.tsx", "tags": ["dev", "test"]}, "inputs-global-selectors-cloc-active-employee-selector--default": {"type": "story", "id": "inputs-global-selectors-cloc-active-employee-selector--default", "name": "<PERSON><PERSON><PERSON>", "title": "Inputs/Global Selectors/Cloc Active Employee Selector", "importPath": "./src/stories/inputs/global-selectors/ClocActiveEmployeeSelector.stories.tsx", "tags": ["dev", "test"]}, "inputs-global-selectors-cloc-active-employee-selector--with-label": {"type": "story", "id": "inputs-global-selectors-cloc-active-employee-selector--with-label", "name": "With Label", "title": "Inputs/Global Selectors/Cloc Active Employee Selector", "importPath": "./src/stories/inputs/global-selectors/ClocActiveEmployeeSelector.stories.tsx", "tags": ["dev", "test"]}, "inputs-global-selectors-cloc-active-employee-selector--small-size": {"type": "story", "id": "inputs-global-selectors-cloc-active-employee-selector--small-size", "name": "Small Size", "title": "Inputs/Global Selectors/Cloc Active Employee Selector", "importPath": "./src/stories/inputs/global-selectors/ClocActiveEmployeeSelector.stories.tsx", "tags": ["dev", "test"]}, "inputs-global-selectors-cloc-active-employee-selector--large-size": {"type": "story", "id": "inputs-global-selectors-cloc-active-employee-selector--large-size", "name": "Large Size", "title": "Inputs/Global Selectors/Cloc Active Employee Selector", "importPath": "./src/stories/inputs/global-selectors/ClocActiveEmployeeSelector.stories.tsx", "tags": ["dev", "test"]}, "inputs-global-selectors-cloc-active-employee-selector--custom-styling": {"type": "story", "id": "inputs-global-selectors-cloc-active-employee-selector--custom-styling", "name": "Custom Styling", "title": "Inputs/Global Selectors/Cloc Active Employee Selector", "importPath": "./src/stories/inputs/global-selectors/ClocActiveEmployeeSelector.stories.tsx", "tags": ["dev", "test"]}, "inputs-global-selectors-cloc-active-organization-selector--default": {"type": "story", "id": "inputs-global-selectors-cloc-active-organization-selector--default", "name": "<PERSON><PERSON><PERSON>", "title": "Inputs/Global Selectors/Cloc Active Organization Selector", "importPath": "./src/stories/inputs/global-selectors/ClocActiveOrganizationSelector.stories.tsx", "tags": ["dev", "test"]}, "inputs-global-selectors-cloc-active-organization-selector--with-label": {"type": "story", "id": "inputs-global-selectors-cloc-active-organization-selector--with-label", "name": "With Label", "title": "Inputs/Global Selectors/Cloc Active Organization Selector", "importPath": "./src/stories/inputs/global-selectors/ClocActiveOrganizationSelector.stories.tsx", "tags": ["dev", "test"]}, "inputs-global-selectors-cloc-active-organization-selector--small-size": {"type": "story", "id": "inputs-global-selectors-cloc-active-organization-selector--small-size", "name": "Small Size", "title": "Inputs/Global Selectors/Cloc Active Organization Selector", "importPath": "./src/stories/inputs/global-selectors/ClocActiveOrganizationSelector.stories.tsx", "tags": ["dev", "test"]}, "inputs-global-selectors-cloc-active-organization-selector--large-size": {"type": "story", "id": "inputs-global-selectors-cloc-active-organization-selector--large-size", "name": "Large Size", "title": "Inputs/Global Selectors/Cloc Active Organization Selector", "importPath": "./src/stories/inputs/global-selectors/ClocActiveOrganizationSelector.stories.tsx", "tags": ["dev", "test"]}, "inputs-global-selectors-cloc-active-organization-selector--custom-styling": {"type": "story", "id": "inputs-global-selectors-cloc-active-organization-selector--custom-styling", "name": "Custom Styling", "title": "Inputs/Global Selectors/Cloc Active Organization Selector", "importPath": "./src/stories/inputs/global-selectors/ClocActiveOrganizationSelector.stories.tsx", "tags": ["dev", "test"]}, "inputs-global-selectors-cloc-active-team-selector--default": {"type": "story", "id": "inputs-global-selectors-cloc-active-team-selector--default", "name": "<PERSON><PERSON><PERSON>", "title": "Inputs/Global Selectors/Cloc Active Team Selector", "importPath": "./src/stories/inputs/global-selectors/ClocActiveTeamSelector.stories.tsx", "tags": ["dev", "test"]}, "inputs-global-selectors-cloc-active-team-selector--with-label": {"type": "story", "id": "inputs-global-selectors-cloc-active-team-selector--with-label", "name": "With Label", "title": "Inputs/Global Selectors/Cloc Active Team Selector", "importPath": "./src/stories/inputs/global-selectors/ClocActiveTeamSelector.stories.tsx", "tags": ["dev", "test"]}, "inputs-global-selectors-cloc-active-team-selector--small-size": {"type": "story", "id": "inputs-global-selectors-cloc-active-team-selector--small-size", "name": "Small Size", "title": "Inputs/Global Selectors/Cloc Active Team Selector", "importPath": "./src/stories/inputs/global-selectors/ClocActiveTeamSelector.stories.tsx", "tags": ["dev", "test"]}, "inputs-global-selectors-cloc-active-team-selector--large-size": {"type": "story", "id": "inputs-global-selectors-cloc-active-team-selector--large-size", "name": "Large Size", "title": "Inputs/Global Selectors/Cloc Active Team Selector", "importPath": "./src/stories/inputs/global-selectors/ClocActiveTeamSelector.stories.tsx", "tags": ["dev", "test"]}, "inputs-global-selectors-cloc-active-team-selector--custom-styling": {"type": "story", "id": "inputs-global-selectors-cloc-active-team-selector--custom-styling", "name": "Custom Styling", "title": "Inputs/Global Selectors/Cloc Active Team Selector", "importPath": "./src/stories/inputs/global-selectors/ClocActiveTeamSelector.stories.tsx", "tags": ["dev", "test"]}, "inputs-global-selectors-cloc-report-dates-range-picker--default": {"type": "story", "id": "inputs-global-selectors-cloc-report-dates-range-picker--default", "name": "<PERSON><PERSON><PERSON>", "title": "Inputs/Global Selectors/Cloc Report Dates Range Picker", "importPath": "./src/stories/inputs/global-selectors/ClocReportDatesRangePicker.stories.tsx", "tags": ["dev", "test"]}, "inputs-global-selectors-cloc-report-dates-range-picker--custom-styling": {"type": "story", "id": "inputs-global-selectors-cloc-report-dates-range-picker--custom-styling", "name": "Custom Styling", "title": "Inputs/Global Selectors/Cloc Report Dates Range Picker", "importPath": "./src/stories/inputs/global-selectors/ClocReportDatesRangePicker.stories.tsx", "tags": ["dev", "test"]}, "inputs-timer-selects-cloc-timer-client-select--default": {"type": "story", "id": "inputs-timer-selects-cloc-timer-client-select--default", "name": "<PERSON><PERSON><PERSON>", "title": "Inputs/Timer Selects/Cloc Timer Client Select", "importPath": "./src/stories/inputs/timer-selects/ClocTimerClientSelect.stories.tsx", "tags": ["dev", "test"]}, "inputs-timer-selects-cloc-timer-client-select--small-size": {"type": "story", "id": "inputs-timer-selects-cloc-timer-client-select--small-size", "name": "Small Size", "title": "Inputs/Timer Selects/Cloc Timer Client Select", "importPath": "./src/stories/inputs/timer-selects/ClocTimerClientSelect.stories.tsx", "tags": ["dev", "test"]}, "inputs-timer-selects-cloc-timer-client-select--large-size": {"type": "story", "id": "inputs-timer-selects-cloc-timer-client-select--large-size", "name": "Large Size", "title": "Inputs/Timer Selects/Cloc Timer Client Select", "importPath": "./src/stories/inputs/timer-selects/ClocTimerClientSelect.stories.tsx", "tags": ["dev", "test"]}, "inputs-timer-selects-cloc-timer-project-select--default": {"type": "story", "id": "inputs-timer-selects-cloc-timer-project-select--default", "name": "<PERSON><PERSON><PERSON>", "title": "Inputs/Timer Selects/Cloc Timer Project Select", "importPath": "./src/stories/inputs/timer-selects/ClocTimerProjectSelect.stories.tsx", "tags": ["dev", "test"]}, "inputs-timer-selects-cloc-timer-project-select--small-size": {"type": "story", "id": "inputs-timer-selects-cloc-timer-project-select--small-size", "name": "Small Size", "title": "Inputs/Timer Selects/Cloc Timer Project Select", "importPath": "./src/stories/inputs/timer-selects/ClocTimerProjectSelect.stories.tsx", "tags": ["dev", "test"]}, "inputs-timer-selects-cloc-timer-project-select--large-size": {"type": "story", "id": "inputs-timer-selects-cloc-timer-project-select--large-size", "name": "Large Size", "title": "Inputs/Timer Selects/Cloc Timer Project Select", "importPath": "./src/stories/inputs/timer-selects/ClocTimerProjectSelect.stories.tsx", "tags": ["dev", "test"]}, "inputs-timer-selects-cloc-timer-task-select--default": {"type": "story", "id": "inputs-timer-selects-cloc-timer-task-select--default", "name": "<PERSON><PERSON><PERSON>", "title": "Inputs/Timer Selects/Cloc Timer Task Select", "importPath": "./src/stories/inputs/timer-selects/ClocTimerTaskSelect.stories.tsx", "tags": ["dev", "test"]}, "inputs-timer-selects-cloc-timer-task-select--small-size": {"type": "story", "id": "inputs-timer-selects-cloc-timer-task-select--small-size", "name": "Small Size", "title": "Inputs/Timer Selects/Cloc Timer Task Select", "importPath": "./src/stories/inputs/timer-selects/ClocTimerTaskSelect.stories.tsx", "tags": ["dev", "test"]}, "inputs-timer-selects-cloc-timer-task-select--large-size": {"type": "story", "id": "inputs-timer-selects-cloc-timer-task-select--large-size", "name": "Large Size", "title": "Inputs/Timer Selects/Cloc Timer Task Select", "importPath": "./src/stories/inputs/timer-selects/ClocTimerTaskSelect.stories.tsx", "tags": ["dev", "test"]}, "inputs-timer-selects-cloc-timer-team-select--default": {"type": "story", "id": "inputs-timer-selects-cloc-timer-team-select--default", "name": "<PERSON><PERSON><PERSON>", "title": "Inputs/Timer Selects/Cloc Timer Team Select", "importPath": "./src/stories/inputs/timer-selects/ClocTimerTeamSelect.stories.tsx", "tags": ["dev", "test"]}, "inputs-timer-selects-cloc-timer-team-select--small-size": {"type": "story", "id": "inputs-timer-selects-cloc-timer-team-select--small-size", "name": "Small Size", "title": "Inputs/Timer Selects/Cloc Timer Team Select", "importPath": "./src/stories/inputs/timer-selects/ClocTimerTeamSelect.stories.tsx", "tags": ["dev", "test"]}, "inputs-timer-selects-cloc-timer-team-select--large-size": {"type": "story", "id": "inputs-timer-selects-cloc-timer-team-select--large-size", "name": "Large Size", "title": "Inputs/Timer Selects/Cloc Timer Team Select", "importPath": "./src/stories/inputs/timer-selects/ClocTimerTeamSelect.stories.tsx", "tags": ["dev", "test"]}, "report-displayers-activity-displayers-cloc-daily-activity-displayer--default": {"type": "story", "id": "report-displayers-activity-displayers-cloc-daily-activity-displayer--default", "name": "<PERSON><PERSON><PERSON>", "title": "Report Displayers/Activity Displayers/Cloc Daily Activity Displayer", "importPath": "./src/stories/report-displayers/activity-displayers/ClocDailyActivityDisplayer.stories.tsx", "tags": ["dev", "test"]}, "report-displayers-activity-displayers-cloc-daily-activity-displayer--without-progress": {"type": "story", "id": "report-displayers-activity-displayers-cloc-daily-activity-displayer--without-progress", "name": "Without Progress", "title": "Report Displayers/Activity Displayers/Cloc Daily Activity Displayer", "importPath": "./src/stories/report-displayers/activity-displayers/ClocDailyActivityDisplayer.stories.tsx", "tags": ["dev", "test"]}, "report-displayers-activity-displayers-cloc-daily-activity-displayer--custom-styling": {"type": "story", "id": "report-displayers-activity-displayers-cloc-daily-activity-displayer--custom-styling", "name": "Custom Styling", "title": "Report Displayers/Activity Displayers/Cloc Daily Activity Displayer", "importPath": "./src/stories/report-displayers/activity-displayers/ClocDailyActivityDisplayer.stories.tsx", "tags": ["dev", "test"]}, "report-displayers-activity-displayers-cloc-weekly-activity-displayer--default": {"type": "story", "id": "report-displayers-activity-displayers-cloc-weekly-activity-displayer--default", "name": "<PERSON><PERSON><PERSON>", "title": "Report Displayers/Activity Displayers/Cloc Weekly Activity Displayer", "importPath": "./src/stories/report-displayers/activity-displayers/ClocWeeklyActivityDisplayer.stories.tsx", "tags": ["dev", "test"]}, "report-displayers-activity-displayers-cloc-weekly-activity-displayer--without-progress": {"type": "story", "id": "report-displayers-activity-displayers-cloc-weekly-activity-displayer--without-progress", "name": "Without Progress", "title": "Report Displayers/Activity Displayers/Cloc Weekly Activity Displayer", "importPath": "./src/stories/report-displayers/activity-displayers/ClocWeeklyActivityDisplayer.stories.tsx", "tags": ["dev", "test"]}, "report-displayers-activity-displayers-cloc-weekly-activity-displayer--custom-styling": {"type": "story", "id": "report-displayers-activity-displayers-cloc-weekly-activity-displayer--custom-styling", "name": "Custom Styling", "title": "Report Displayers/Activity Displayers/Cloc Weekly Activity Displayer", "importPath": "./src/stories/report-displayers/activity-displayers/ClocWeeklyActivityDisplayer.stories.tsx", "tags": ["dev", "test"]}, "report-displayers-project-displayers-cloc-worked-project-displayer--default": {"type": "story", "id": "report-displayers-project-displayers-cloc-worked-project-displayer--default", "name": "<PERSON><PERSON><PERSON>", "title": "Report Displayers/Project Displayers/Cloc Worked Project Displayer", "importPath": "./src/stories/report-displayers/project-displayers/ClocWorkedProjectDisplayer.stories.tsx", "tags": ["dev", "test"]}, "report-displayers-project-displayers-cloc-worked-project-displayer--without-progress": {"type": "story", "id": "report-displayers-project-displayers-cloc-worked-project-displayer--without-progress", "name": "Without Progress", "title": "Report Displayers/Project Displayers/Cloc Worked Project Displayer", "importPath": "./src/stories/report-displayers/project-displayers/ClocWorkedProjectDisplayer.stories.tsx", "tags": ["dev", "test"]}, "report-displayers-project-displayers-cloc-worked-project-displayer--custom-styling": {"type": "story", "id": "report-displayers-project-displayers-cloc-worked-project-displayer--custom-styling", "name": "Custom Styling", "title": "Report Displayers/Project Displayers/Cloc Worked Project Displayer", "importPath": "./src/stories/report-displayers/project-displayers/ClocWorkedProjectDisplayer.stories.tsx", "tags": ["dev", "test"]}, "report-displayers-time-displayers-cloc-daily-worked-time-displayer--default": {"type": "story", "id": "report-displayers-time-displayers-cloc-daily-worked-time-displayer--default", "name": "<PERSON><PERSON><PERSON>", "title": "Report Displayers/Time Displayers/Cloc Daily Worked Time Displayer", "importPath": "./src/stories/report-displayers/time-displayers/ClocDailyWorkedTimeDisplayer.stories.tsx", "tags": ["dev", "test"]}, "report-displayers-time-displayers-cloc-daily-worked-time-displayer--without-progress": {"type": "story", "id": "report-displayers-time-displayers-cloc-daily-worked-time-displayer--without-progress", "name": "Without Progress", "title": "Report Displayers/Time Displayers/Cloc Daily Worked Time Displayer", "importPath": "./src/stories/report-displayers/time-displayers/ClocDailyWorkedTimeDisplayer.stories.tsx", "tags": ["dev", "test"]}, "report-displayers-time-displayers-cloc-daily-worked-time-displayer--custom-styling": {"type": "story", "id": "report-displayers-time-displayers-cloc-daily-worked-time-displayer--custom-styling", "name": "Custom Styling", "title": "Report Displayers/Time Displayers/Cloc Daily Worked Time Displayer", "importPath": "./src/stories/report-displayers/time-displayers/ClocDailyWorkedTimeDisplayer.stories.tsx", "tags": ["dev", "test"]}, "report-displayers-time-displayers-cloc-weekly-worked-time-displayer--default": {"type": "story", "id": "report-displayers-time-displayers-cloc-weekly-worked-time-displayer--default", "name": "<PERSON><PERSON><PERSON>", "title": "Report Displayers/Time Displayers/Cloc Weekly Worked Time Displayer", "importPath": "./src/stories/report-displayers/time-displayers/ClocWeeklyWorkedTimeDisplayer.stories.tsx", "tags": ["dev", "test"]}, "report-displayers-time-displayers-cloc-weekly-worked-time-displayer--without-progress": {"type": "story", "id": "report-displayers-time-displayers-cloc-weekly-worked-time-displayer--without-progress", "name": "Without Progress", "title": "Report Displayers/Time Displayers/Cloc Weekly Worked Time Displayer", "importPath": "./src/stories/report-displayers/time-displayers/ClocWeeklyWorkedTimeDisplayer.stories.tsx", "tags": ["dev", "test"]}, "report-displayers-time-displayers-cloc-weekly-worked-time-displayer--custom-styling": {"type": "story", "id": "report-displayers-time-displayers-cloc-weekly-worked-time-displayer--custom-styling", "name": "Custom Styling", "title": "Report Displayers/Time Displayers/Cloc Weekly Worked Time Displayer", "importPath": "./src/stories/report-displayers/time-displayers/ClocWeeklyWorkedTimeDisplayer.stories.tsx", "tags": ["dev", "test"]}, "team-management-member-invitation-form--default": {"type": "story", "id": "team-management-member-invitation-form--default", "name": "<PERSON><PERSON><PERSON>", "title": "Team Management/Member Invitation Form", "importPath": "./src/stories/team-management/ClocMemberInvitationForm.stories.tsx", "tags": ["dev", "test"]}, "team-management-member-invitation-form--in-modal": {"type": "story", "id": "team-management-member-invitation-form--in-modal", "name": "In Modal", "title": "Team Management/Member Invitation Form", "importPath": "./src/stories/team-management/ClocMemberInvitationForm.stories.tsx", "tags": ["dev", "test"]}, "team-management-member-invitation-form--custom-styling": {"type": "story", "id": "team-management-member-invitation-form--custom-styling", "name": "Custom Styling", "title": "Team Management/Member Invitation Form", "importPath": "./src/stories/team-management/ClocMemberInvitationForm.stories.tsx", "tags": ["dev", "test"]}, "team-management-member-invitation-form-dialog--default": {"type": "story", "id": "team-management-member-invitation-form-dialog--default", "name": "<PERSON><PERSON><PERSON>", "title": "Team Management/Member Invitation Form Dialog", "importPath": "./src/stories/team-management/ClocMemberInvitationFormDialog.stories.tsx", "tags": ["dev", "test"]}, "team-management-member-invitation-form-dialog--custom-styling": {"type": "story", "id": "team-management-member-invitation-form-dialog--custom-styling", "name": "Custom Styling", "title": "Team Management/Member Invitation Form Dialog", "importPath": "./src/stories/team-management/ClocMemberInvitationFormDialog.stories.tsx", "tags": ["dev", "test"]}, "team-management-member-invitation-form-dialog--custom-trigger": {"type": "story", "id": "team-management-member-invitation-form-dialog--custom-trigger", "name": "Custom Trigger", "title": "Team Management/Member Invitation Form Dialog", "importPath": "./src/stories/team-management/ClocMemberInvitationFormDialog.stories.tsx", "tags": ["dev", "test"]}, "team-management-member-invitation-form-dialog--icon-trigger": {"type": "story", "id": "team-management-member-invitation-form-dialog--icon-trigger", "name": "<PERSON><PERSON>", "title": "Team Management/Member Invitation Form Dialog", "importPath": "./src/stories/team-management/ClocMemberInvitationFormDialog.stories.tsx", "tags": ["dev", "test"]}, "team-management-team-creation-form--default": {"type": "story", "id": "team-management-team-creation-form--default", "name": "<PERSON><PERSON><PERSON>", "title": "Team Management/Team Creation Form", "importPath": "./src/stories/team-management/ClocTeamCreationForm.stories.tsx", "tags": ["dev", "test"]}, "team-management-team-creation-form--in-modal": {"type": "story", "id": "team-management-team-creation-form--in-modal", "name": "In Modal", "title": "Team Management/Team Creation Form", "importPath": "./src/stories/team-management/ClocTeamCreationForm.stories.tsx", "tags": ["dev", "test"]}, "team-management-team-creation-form--custom-styling": {"type": "story", "id": "team-management-team-creation-form--custom-styling", "name": "Custom Styling", "title": "Team Management/Team Creation Form", "importPath": "./src/stories/team-management/ClocTeamCreationForm.stories.tsx", "tags": ["dev", "test"]}, "team-management-team-creation-form--onboarding-flow": {"type": "story", "id": "team-management-team-creation-form--onboarding-flow", "name": "Onboarding Flow", "title": "Team Management/Team Creation Form", "importPath": "./src/stories/team-management/ClocTeamCreationForm.stories.tsx", "tags": ["dev", "test"]}, "team-management-team-creation-form-dialog--default": {"type": "story", "id": "team-management-team-creation-form-dialog--default", "name": "<PERSON><PERSON><PERSON>", "title": "Team Management/Team Creation Form Dialog", "importPath": "./src/stories/team-management/ClocTeamCreationFormDialog.stories.tsx", "tags": ["dev", "test"]}, "team-management-team-creation-form-dialog--custom-styling": {"type": "story", "id": "team-management-team-creation-form-dialog--custom-styling", "name": "Custom Styling", "title": "Team Management/Team Creation Form Dialog", "importPath": "./src/stories/team-management/ClocTeamCreationFormDialog.stories.tsx", "tags": ["dev", "test"]}, "team-management-team-creation-form-dialog--custom-trigger": {"type": "story", "id": "team-management-team-creation-form-dialog--custom-trigger", "name": "Custom Trigger", "title": "Team Management/Team Creation Form Dialog", "importPath": "./src/stories/team-management/ClocTeamCreationFormDialog.stories.tsx", "tags": ["dev", "test"]}, "team-management-team-creation-form-dialog--organization-trigger": {"type": "story", "id": "team-management-team-creation-form-dialog--organization-trigger", "name": "Organization Trigger", "title": "Team Management/Team Creation Form Dialog", "importPath": "./src/stories/team-management/ClocTeamCreationFormDialog.stories.tsx", "tags": ["dev", "test"]}, "team-management-team-creation-form-dialog--icon-trigger": {"type": "story", "id": "team-management-team-creation-form-dialog--icon-trigger", "name": "<PERSON><PERSON>", "title": "Team Management/Team Creation Form Dialog", "importPath": "./src/stories/team-management/ClocTeamCreationFormDialog.stories.tsx", "tags": ["dev", "test"]}, "team-management-cloc-team-members--default": {"type": "story", "id": "team-management-cloc-team-members--default", "name": "<PERSON><PERSON><PERSON>", "title": "Team Management/Cloc Team Members", "importPath": "./src/stories/team-management/ClocTeamMembers.stories.tsx", "tags": ["dev", "test"]}, "team-management-cloc-team-members--custom-styling": {"type": "story", "id": "team-management-cloc-team-members--custom-styling", "name": "Custom Styling", "title": "Team Management/Cloc Team Members", "importPath": "./src/stories/team-management/ClocTeamMembers.stories.tsx", "tags": ["dev", "test"]}, "team-management-team-setting--default": {"type": "story", "id": "team-management-team-setting--default", "name": "<PERSON><PERSON><PERSON>", "title": "Team Management/Team Setting", "importPath": "./src/stories/team-management/ClocTeamSetting.stories.tsx", "tags": ["dev", "test"]}, "team-management-team-setting--in-modal": {"type": "story", "id": "team-management-team-setting--in-modal", "name": "In Modal", "title": "Team Management/Team Setting", "importPath": "./src/stories/team-management/ClocTeamSetting.stories.tsx", "tags": ["dev", "test"]}, "team-management-team-setting--custom-styling": {"type": "story", "id": "team-management-team-setting--custom-styling", "name": "Custom Styling", "title": "Team Management/Team Setting", "importPath": "./src/stories/team-management/ClocTeamSetting.stories.tsx", "tags": ["dev", "test"]}, "team-management-team-setting--in-settings-page": {"type": "story", "id": "team-management-team-setting--in-settings-page", "name": "In Settings Page", "title": "Team Management/Team Setting", "importPath": "./src/stories/team-management/ClocTeamSetting.stories.tsx", "tags": ["dev", "test"]}, "team-management-cloc-teams-viewer--default": {"type": "story", "id": "team-management-cloc-teams-viewer--default", "name": "<PERSON><PERSON><PERSON>", "title": "Team Management/Cloc Teams Viewer", "importPath": "./src/stories/team-management/ClocTeamsViewer.stories.tsx", "tags": ["dev", "test"]}, "team-management-cloc-teams-viewer--custom-styling": {"type": "story", "id": "team-management-cloc-teams-viewer--custom-styling", "name": "Custom Styling", "title": "Team Management/Cloc Teams Viewer", "importPath": "./src/stories/team-management/ClocTeamsViewer.stories.tsx", "tags": ["dev", "test"]}, "time-trackers-basic-timer--default-basic-timer": {"type": "story", "id": "time-trackers-basic-timer--default-basic-timer", "name": "Default Basic Timer", "title": "Time Trackers/Basic Timer", "importPath": "./src/stories/time-trackers/BasicTimer.stories.tsx", "tags": ["dev", "test"]}, "time-trackers-basic-timer--basic-timer-border": {"type": "story", "id": "time-trackers-basic-timer--basic-timer-border", "name": "Basic Timer Border", "title": "Time Trackers/Basic Timer", "importPath": "./src/stories/time-trackers/BasicTimer.stories.tsx", "tags": ["dev", "test"]}, "time-trackers-basic-timer--basic-timer-border-rounded": {"type": "story", "id": "time-trackers-basic-timer--basic-timer-border-rounded", "name": "Basic Timer Border Rounded", "title": "Time Trackers/Basic Timer", "importPath": "./src/stories/time-trackers/BasicTimer.stories.tsx", "tags": ["dev", "test"]}, "time-trackers-basic-timer--basic-timer-border-full-rounded": {"type": "story", "id": "time-trackers-basic-timer--basic-timer-border-full-rounded", "name": "Basic Timer Border Full Rounded", "title": "Time Trackers/Basic Timer", "importPath": "./src/stories/time-trackers/BasicTimer.stories.tsx", "tags": ["dev", "test"]}, "time-trackers-basic-timer--basic-timer-gray": {"type": "story", "id": "time-trackers-basic-timer--basic-timer-gray", "name": "Basic Timer Gray", "title": "Time Trackers/Basic Timer", "importPath": "./src/stories/time-trackers/BasicTimer.stories.tsx", "tags": ["dev", "test"]}, "time-trackers-basic-timer--basic-timer-gray-rounded": {"type": "story", "id": "time-trackers-basic-timer--basic-timer-gray-rounded", "name": "Basic Timer Gray Rounded", "title": "Time Trackers/Basic Timer", "importPath": "./src/stories/time-trackers/BasicTimer.stories.tsx", "tags": ["dev", "test"]}, "time-trackers-basic-timer--basic-timer-gray-full-rounded": {"type": "story", "id": "time-trackers-basic-timer--basic-timer-gray-full-rounded", "name": "Basic Timer Gray Full Rounded", "title": "Time Trackers/Basic Timer", "importPath": "./src/stories/time-trackers/BasicTimer.stories.tsx", "tags": ["dev", "test"]}, "time-trackers-basic-timer--basic-timer-contained": {"type": "story", "id": "time-trackers-basic-timer--basic-timer-contained", "name": "Basic Timer Contained", "title": "Time Trackers/Basic Timer", "importPath": "./src/stories/time-trackers/BasicTimer.stories.tsx", "tags": ["dev", "test"]}, "time-trackers-basic-timer--basic-timer-contained-rounded": {"type": "story", "id": "time-trackers-basic-timer--basic-timer-contained-rounded", "name": "Basic Timer Contained Rounded", "title": "Time Trackers/Basic Timer", "importPath": "./src/stories/time-trackers/BasicTimer.stories.tsx", "tags": ["dev", "test"]}, "time-trackers-basic-timer--basic-timer-contained-full-rounded": {"type": "story", "id": "time-trackers-basic-timer--basic-timer-contained-full-rounded", "name": "Basic Timer Contained Full Rounded", "title": "Time Trackers/Basic Timer", "importPath": "./src/stories/time-trackers/BasicTimer.stories.tsx", "tags": ["dev", "test"]}, "time-trackers-basic-timer--basic-timer-icon": {"type": "story", "id": "time-trackers-basic-timer--basic-timer-icon", "name": "Basic Timer Icon", "title": "Time Trackers/Basic Timer", "importPath": "./src/stories/time-trackers/BasicTimer.stories.tsx", "tags": ["dev", "test"]}, "time-trackers-basic-timer--basic-timer-icon-border": {"type": "story", "id": "time-trackers-basic-timer--basic-timer-icon-border", "name": "Basic Timer Icon Border", "title": "Time Trackers/Basic Timer", "importPath": "./src/stories/time-trackers/BasicTimer.stories.tsx", "tags": ["dev", "test"]}, "time-trackers-basic-timer--basic-timer-icon-border-rounded": {"type": "story", "id": "time-trackers-basic-timer--basic-timer-icon-border-rounded", "name": "Basic Timer Icon Border Rounded", "title": "Time Trackers/Basic Timer", "importPath": "./src/stories/time-trackers/BasicTimer.stories.tsx", "tags": ["dev", "test"]}, "time-trackers-basic-timer--basic-timer-icon-border-full-rounded": {"type": "story", "id": "time-trackers-basic-timer--basic-timer-icon-border-full-rounded", "name": "Basic Timer Icon Border Full Rounded", "title": "Time Trackers/Basic Timer", "importPath": "./src/stories/time-trackers/BasicTimer.stories.tsx", "tags": ["dev", "test"]}, "time-trackers-basic-timer--basic-timer-icon-gray": {"type": "story", "id": "time-trackers-basic-timer--basic-timer-icon-gray", "name": "Basic Timer Icon Gray", "title": "Time Trackers/Basic Timer", "importPath": "./src/stories/time-trackers/BasicTimer.stories.tsx", "tags": ["dev", "test"]}, "time-trackers-basic-timer--basic-timer-icon-gray-rounded": {"type": "story", "id": "time-trackers-basic-timer--basic-timer-icon-gray-rounded", "name": "Basic Timer Icon Gray Rounded", "title": "Time Trackers/Basic Timer", "importPath": "./src/stories/time-trackers/BasicTimer.stories.tsx", "tags": ["dev", "test"]}, "time-trackers-basic-timer--basic-timer-icon-gray-full-rounded": {"type": "story", "id": "time-trackers-basic-timer--basic-timer-icon-gray-full-rounded", "name": "Basic Timer Icon Gray Full Rounded", "title": "Time Trackers/Basic Timer", "importPath": "./src/stories/time-trackers/BasicTimer.stories.tsx", "tags": ["dev", "test"]}, "time-trackers-basic-timer--basic-timer-icon-contained": {"type": "story", "id": "time-trackers-basic-timer--basic-timer-icon-contained", "name": "Basic Timer Icon Contained", "title": "Time Trackers/Basic Timer", "importPath": "./src/stories/time-trackers/BasicTimer.stories.tsx", "tags": ["dev", "test"]}, "time-trackers-basic-timer--basic-timer-icon-contained-rounded": {"type": "story", "id": "time-trackers-basic-timer--basic-timer-icon-contained-rounded", "name": "Basic Timer Icon Contained Rounded", "title": "Time Trackers/Basic Timer", "importPath": "./src/stories/time-trackers/BasicTimer.stories.tsx", "tags": ["dev", "test"]}, "time-trackers-basic-timer--basic-timer-icon-contained-full-rounded": {"type": "story", "id": "time-trackers-basic-timer--basic-timer-icon-contained-full-rounded", "name": "Basic Timer Icon Contained Full Rounded", "title": "Time Trackers/Basic Timer", "importPath": "./src/stories/time-trackers/BasicTimer.stories.tsx", "tags": ["dev", "test"]}, "time-trackers-basic-timer--basic-timer-icon-progress": {"type": "story", "id": "time-trackers-basic-timer--basic-timer-icon-progress", "name": "Basic Timer Icon Progress", "title": "Time Trackers/Basic Timer", "importPath": "./src/stories/time-trackers/BasicTimer.stories.tsx", "tags": ["dev", "test"]}, "time-trackers-basic-timer--basic-timer-icon-border-progress": {"type": "story", "id": "time-trackers-basic-timer--basic-timer-icon-border-progress", "name": "Basic Timer Icon Border Progress", "title": "Time Trackers/Basic Timer", "importPath": "./src/stories/time-trackers/BasicTimer.stories.tsx", "tags": ["dev", "test"]}, "time-trackers-basic-timer--basic-timer-icon-border-rounded-progress": {"type": "story", "id": "time-trackers-basic-timer--basic-timer-icon-border-rounded-progress", "name": "Basic Timer Icon Border Rounded Progress", "title": "Time Trackers/Basic Timer", "importPath": "./src/stories/time-trackers/BasicTimer.stories.tsx", "tags": ["dev", "test"]}, "time-trackers-basic-timer--basic-timer-icon-border-full-rounded-progress": {"type": "story", "id": "time-trackers-basic-timer--basic-timer-icon-border-full-rounded-progress", "name": "Basic Timer Icon Border Full Rounded Progress", "title": "Time Trackers/Basic Timer", "importPath": "./src/stories/time-trackers/BasicTimer.stories.tsx", "tags": ["dev", "test"]}, "time-trackers-basic-timer--basic-timer-icon-gray-progress": {"type": "story", "id": "time-trackers-basic-timer--basic-timer-icon-gray-progress", "name": "Basic Timer Icon Gray Progress", "title": "Time Trackers/Basic Timer", "importPath": "./src/stories/time-trackers/BasicTimer.stories.tsx", "tags": ["dev", "test"]}, "time-trackers-basic-timer--basic-timer-icon-gray-rounded-progress": {"type": "story", "id": "time-trackers-basic-timer--basic-timer-icon-gray-rounded-progress", "name": "Basic Timer Icon Gray Rounded Progress", "title": "Time Trackers/Basic Timer", "importPath": "./src/stories/time-trackers/BasicTimer.stories.tsx", "tags": ["dev", "test"]}, "time-trackers-basic-timer--basic-timer-icon-gray-full-rounded-progress": {"type": "story", "id": "time-trackers-basic-timer--basic-timer-icon-gray-full-rounded-progress", "name": "Basic Timer Icon Gray Full Rounded Progress", "title": "Time Trackers/Basic Timer", "importPath": "./src/stories/time-trackers/BasicTimer.stories.tsx", "tags": ["dev", "test"]}, "time-trackers-basic-timer--basic-timer-icon-contained-progress": {"type": "story", "id": "time-trackers-basic-timer--basic-timer-icon-contained-progress", "name": "Basic Timer Icon Contained Progress", "title": "Time Trackers/Basic Timer", "importPath": "./src/stories/time-trackers/BasicTimer.stories.tsx", "tags": ["dev", "test"]}, "time-trackers-basic-timer--basic-timer-icon-contained-rounded-progress": {"type": "story", "id": "time-trackers-basic-timer--basic-timer-icon-contained-rounded-progress", "name": "Basic Timer Icon Contained Rounded Progress", "title": "Time Trackers/Basic Timer", "importPath": "./src/stories/time-trackers/BasicTimer.stories.tsx", "tags": ["dev", "test"]}, "time-trackers-basic-timer--basic-timer-icon-contained-full-rounded-progress": {"type": "story", "id": "time-trackers-basic-timer--basic-timer-icon-contained-full-rounded-progress", "name": "Basic Timer Icon Contained Full Rounded Progress", "title": "Time Trackers/Basic Timer", "importPath": "./src/stories/time-trackers/BasicTimer.stories.tsx", "tags": ["dev", "test"]}, "time-trackers-basic-timer--basic-timer-icon-progress-button": {"type": "story", "id": "time-trackers-basic-timer--basic-timer-icon-progress-button", "name": "Basic Timer Icon Progress Button", "title": "Time Trackers/Basic Timer", "importPath": "./src/stories/time-trackers/BasicTimer.stories.tsx", "tags": ["dev", "test"]}, "time-trackers-basic-timer--basic-timer-icon-border-progress-button": {"type": "story", "id": "time-trackers-basic-timer--basic-timer-icon-border-progress-button", "name": "Basic Timer Icon Border Progress Button", "title": "Time Trackers/Basic Timer", "importPath": "./src/stories/time-trackers/BasicTimer.stories.tsx", "tags": ["dev", "test"]}, "time-trackers-basic-timer--basic-timer-icon-border-rounded-progress-button": {"type": "story", "id": "time-trackers-basic-timer--basic-timer-icon-border-rounded-progress-button", "name": "Basic Timer Icon Border Rounded Progress Button", "title": "Time Trackers/Basic Timer", "importPath": "./src/stories/time-trackers/BasicTimer.stories.tsx", "tags": ["dev", "test"]}, "time-trackers-basic-timer--basic-timer-icon-border-full-rounded-progress-button": {"type": "story", "id": "time-trackers-basic-timer--basic-timer-icon-border-full-rounded-progress-button", "name": "Basic Timer Icon Border Full Rounded Progress Button", "title": "Time Trackers/Basic Timer", "importPath": "./src/stories/time-trackers/BasicTimer.stories.tsx", "tags": ["dev", "test"]}, "time-trackers-basic-timer--basic-timer-icon-gray-progress-button": {"type": "story", "id": "time-trackers-basic-timer--basic-timer-icon-gray-progress-button", "name": "Basic Timer Icon Gray Progress Button", "title": "Time Trackers/Basic Timer", "importPath": "./src/stories/time-trackers/BasicTimer.stories.tsx", "tags": ["dev", "test"]}, "time-trackers-basic-timer--basic-timer-icon-gray-rounded-progress-button": {"type": "story", "id": "time-trackers-basic-timer--basic-timer-icon-gray-rounded-progress-button", "name": "Basic Timer Icon Gray Rounded Progress Button", "title": "Time Trackers/Basic Timer", "importPath": "./src/stories/time-trackers/BasicTimer.stories.tsx", "tags": ["dev", "test"]}, "time-trackers-basic-timer--basic-timer-icon-gray-full-rounded-progress-button": {"type": "story", "id": "time-trackers-basic-timer--basic-timer-icon-gray-full-rounded-progress-button", "name": "Basic Timer Icon Gray Full Rounded Progress Button", "title": "Time Trackers/Basic Timer", "importPath": "./src/stories/time-trackers/BasicTimer.stories.tsx", "tags": ["dev", "test"]}, "time-trackers-basic-timer--basic-timer-icon-contained-progress-button": {"type": "story", "id": "time-trackers-basic-timer--basic-timer-icon-contained-progress-button", "name": "Basic Timer Icon Contained Progress Button", "title": "Time Trackers/Basic Timer", "importPath": "./src/stories/time-trackers/BasicTimer.stories.tsx", "tags": ["dev", "test"]}, "time-trackers-basic-timer--basic-timer-icon-contained-rounded-progress-button": {"type": "story", "id": "time-trackers-basic-timer--basic-timer-icon-contained-rounded-progress-button", "name": "Basic Timer Icon Contained Rounded Progress Button", "title": "Time Trackers/Basic Timer", "importPath": "./src/stories/time-trackers/BasicTimer.stories.tsx", "tags": ["dev", "test"]}, "time-trackers-basic-timer--basic-timer-icon-contained-full-rounded-progress-button": {"type": "story", "id": "time-trackers-basic-timer--basic-timer-icon-contained-full-rounded-progress-button", "name": "Basic Timer Icon Contained Full Rounded Progress Button", "title": "Time Trackers/Basic Timer", "importPath": "./src/stories/time-trackers/BasicTimer.stories.tsx", "tags": ["dev", "test"]}, "time-trackers-basic-cloc--default-basic-timer": {"type": "story", "id": "time-trackers-basic-cloc--default-basic-timer", "name": "Default Basic Timer", "title": "Time Trackers/Basic Cloc", "importPath": "./src/stories/time-trackers/ClocBasic.stories.tsx", "tags": ["dev", "test"]}, "time-trackers-basic-cloc--basic-timer-border": {"type": "story", "id": "time-trackers-basic-cloc--basic-timer-border", "name": "Basic Timer Border", "title": "Time Trackers/Basic Cloc", "importPath": "./src/stories/time-trackers/ClocBasic.stories.tsx", "tags": ["dev", "test"]}, "time-trackers-basic-cloc--basic-timer-border-rounded": {"type": "story", "id": "time-trackers-basic-cloc--basic-timer-border-rounded", "name": "Basic Timer Border Rounded", "title": "Time Trackers/Basic Cloc", "importPath": "./src/stories/time-trackers/ClocBasic.stories.tsx", "tags": ["dev", "test"]}, "time-trackers-basic-cloc--basic-timer-border-full-rounded": {"type": "story", "id": "time-trackers-basic-cloc--basic-timer-border-full-rounded", "name": "Basic Timer Border Full Rounded", "title": "Time Trackers/Basic Cloc", "importPath": "./src/stories/time-trackers/ClocBasic.stories.tsx", "tags": ["dev", "test"]}, "time-trackers-basic-cloc--basic-timer-gray": {"type": "story", "id": "time-trackers-basic-cloc--basic-timer-gray", "name": "Basic Timer Gray", "title": "Time Trackers/Basic Cloc", "importPath": "./src/stories/time-trackers/ClocBasic.stories.tsx", "tags": ["dev", "test"]}, "time-trackers-basic-cloc--basic-timer-gray-rounded": {"type": "story", "id": "time-trackers-basic-cloc--basic-timer-gray-rounded", "name": "Basic Timer Gray Rounded", "title": "Time Trackers/Basic Cloc", "importPath": "./src/stories/time-trackers/ClocBasic.stories.tsx", "tags": ["dev", "test"]}, "time-trackers-basic-cloc--basic-timer-gray-full-rounded": {"type": "story", "id": "time-trackers-basic-cloc--basic-timer-gray-full-rounded", "name": "Basic Timer Gray Full Rounded", "title": "Time Trackers/Basic Cloc", "importPath": "./src/stories/time-trackers/ClocBasic.stories.tsx", "tags": ["dev", "test"]}, "time-trackers-basic-cloc--basic-timer-contained": {"type": "story", "id": "time-trackers-basic-cloc--basic-timer-contained", "name": "Basic Timer Contained", "title": "Time Trackers/Basic Cloc", "importPath": "./src/stories/time-trackers/ClocBasic.stories.tsx", "tags": ["dev", "test"]}, "time-trackers-basic-cloc--basic-timer-contained-rounded": {"type": "story", "id": "time-trackers-basic-cloc--basic-timer-contained-rounded", "name": "Basic Timer Contained Rounded", "title": "Time Trackers/Basic Cloc", "importPath": "./src/stories/time-trackers/ClocBasic.stories.tsx", "tags": ["dev", "test"]}, "time-trackers-basic-cloc--basic-timer-contained-full-rounded": {"type": "story", "id": "time-trackers-basic-cloc--basic-timer-contained-full-rounded", "name": "Basic Timer Contained Full Rounded", "title": "Time Trackers/Basic Cloc", "importPath": "./src/stories/time-trackers/ClocBasic.stories.tsx", "tags": ["dev", "test"]}, "time-trackers-basic-cloc--basic-timer-icon": {"type": "story", "id": "time-trackers-basic-cloc--basic-timer-icon", "name": "Basic Timer Icon", "title": "Time Trackers/Basic Cloc", "importPath": "./src/stories/time-trackers/ClocBasic.stories.tsx", "tags": ["dev", "test"]}, "time-trackers-basic-cloc--basic-timer-icon-border": {"type": "story", "id": "time-trackers-basic-cloc--basic-timer-icon-border", "name": "Basic Timer Icon Border", "title": "Time Trackers/Basic Cloc", "importPath": "./src/stories/time-trackers/ClocBasic.stories.tsx", "tags": ["dev", "test"]}, "time-trackers-basic-cloc--basic-timer-icon-border-rounded": {"type": "story", "id": "time-trackers-basic-cloc--basic-timer-icon-border-rounded", "name": "Basic Timer Icon Border Rounded", "title": "Time Trackers/Basic Cloc", "importPath": "./src/stories/time-trackers/ClocBasic.stories.tsx", "tags": ["dev", "test"]}, "time-trackers-basic-cloc--basic-timer-icon-border-full-rounded": {"type": "story", "id": "time-trackers-basic-cloc--basic-timer-icon-border-full-rounded", "name": "Basic Timer Icon Border Full Rounded", "title": "Time Trackers/Basic Cloc", "importPath": "./src/stories/time-trackers/ClocBasic.stories.tsx", "tags": ["dev", "test"]}, "time-trackers-basic-cloc--basic-timer-icon-gray": {"type": "story", "id": "time-trackers-basic-cloc--basic-timer-icon-gray", "name": "Basic Timer Icon Gray", "title": "Time Trackers/Basic Cloc", "importPath": "./src/stories/time-trackers/ClocBasic.stories.tsx", "tags": ["dev", "test"]}, "time-trackers-basic-cloc--basic-timer-icon-gray-rounded": {"type": "story", "id": "time-trackers-basic-cloc--basic-timer-icon-gray-rounded", "name": "Basic Timer Icon Gray Rounded", "title": "Time Trackers/Basic Cloc", "importPath": "./src/stories/time-trackers/ClocBasic.stories.tsx", "tags": ["dev", "test"]}, "time-trackers-basic-cloc--basic-timer-icon-gray-full-rounded": {"type": "story", "id": "time-trackers-basic-cloc--basic-timer-icon-gray-full-rounded", "name": "Basic Timer Icon Gray Full Rounded", "title": "Time Trackers/Basic Cloc", "importPath": "./src/stories/time-trackers/ClocBasic.stories.tsx", "tags": ["dev", "test"]}, "time-trackers-basic-cloc--basic-timer-icon-contained": {"type": "story", "id": "time-trackers-basic-cloc--basic-timer-icon-contained", "name": "Basic Timer Icon Contained", "title": "Time Trackers/Basic Cloc", "importPath": "./src/stories/time-trackers/ClocBasic.stories.tsx", "tags": ["dev", "test"]}, "time-trackers-basic-cloc--basic-timer-icon-contained-rounded": {"type": "story", "id": "time-trackers-basic-cloc--basic-timer-icon-contained-rounded", "name": "Basic Timer Icon Contained Rounded", "title": "Time Trackers/Basic Cloc", "importPath": "./src/stories/time-trackers/ClocBasic.stories.tsx", "tags": ["dev", "test"]}, "time-trackers-basic-cloc--basic-timer-icon-contained-full-rounded": {"type": "story", "id": "time-trackers-basic-cloc--basic-timer-icon-contained-full-rounded", "name": "Basic Timer Icon Contained Full Rounded", "title": "Time Trackers/Basic Cloc", "importPath": "./src/stories/time-trackers/ClocBasic.stories.tsx", "tags": ["dev", "test"]}, "time-trackers-basic-cloc--basic-timer-icon-progress": {"type": "story", "id": "time-trackers-basic-cloc--basic-timer-icon-progress", "name": "Basic Timer Icon Progress", "title": "Time Trackers/Basic Cloc", "importPath": "./src/stories/time-trackers/ClocBasic.stories.tsx", "tags": ["dev", "test"]}, "time-trackers-basic-cloc--basic-timer-icon-border-progress": {"type": "story", "id": "time-trackers-basic-cloc--basic-timer-icon-border-progress", "name": "Basic Timer Icon Border Progress", "title": "Time Trackers/Basic Cloc", "importPath": "./src/stories/time-trackers/ClocBasic.stories.tsx", "tags": ["dev", "test"]}, "time-trackers-basic-cloc--basic-timer-icon-border-rounded-progress": {"type": "story", "id": "time-trackers-basic-cloc--basic-timer-icon-border-rounded-progress", "name": "Basic Timer Icon Border Rounded Progress", "title": "Time Trackers/Basic Cloc", "importPath": "./src/stories/time-trackers/ClocBasic.stories.tsx", "tags": ["dev", "test"]}, "time-trackers-basic-cloc--basic-timer-icon-border-full-rounded-progress": {"type": "story", "id": "time-trackers-basic-cloc--basic-timer-icon-border-full-rounded-progress", "name": "Basic Timer Icon Border Full Rounded Progress", "title": "Time Trackers/Basic Cloc", "importPath": "./src/stories/time-trackers/ClocBasic.stories.tsx", "tags": ["dev", "test"]}, "time-trackers-basic-cloc--basic-timer-icon-gray-progress": {"type": "story", "id": "time-trackers-basic-cloc--basic-timer-icon-gray-progress", "name": "Basic Timer Icon Gray Progress", "title": "Time Trackers/Basic Cloc", "importPath": "./src/stories/time-trackers/ClocBasic.stories.tsx", "tags": ["dev", "test"]}, "time-trackers-basic-cloc--basic-timer-icon-gray-rounded-progress": {"type": "story", "id": "time-trackers-basic-cloc--basic-timer-icon-gray-rounded-progress", "name": "Basic Timer Icon Gray Rounded Progress", "title": "Time Trackers/Basic Cloc", "importPath": "./src/stories/time-trackers/ClocBasic.stories.tsx", "tags": ["dev", "test"]}, "time-trackers-basic-cloc--basic-timer-icon-gray-full-rounded-progress": {"type": "story", "id": "time-trackers-basic-cloc--basic-timer-icon-gray-full-rounded-progress", "name": "Basic Timer Icon Gray Full Rounded Progress", "title": "Time Trackers/Basic Cloc", "importPath": "./src/stories/time-trackers/ClocBasic.stories.tsx", "tags": ["dev", "test"]}, "time-trackers-basic-cloc--basic-timer-icon-contained-progress": {"type": "story", "id": "time-trackers-basic-cloc--basic-timer-icon-contained-progress", "name": "Basic Timer Icon Contained Progress", "title": "Time Trackers/Basic Cloc", "importPath": "./src/stories/time-trackers/ClocBasic.stories.tsx", "tags": ["dev", "test"]}, "time-trackers-basic-cloc--basic-timer-icon-contained-rounded-progress": {"type": "story", "id": "time-trackers-basic-cloc--basic-timer-icon-contained-rounded-progress", "name": "Basic Timer Icon Contained Rounded Progress", "title": "Time Trackers/Basic Cloc", "importPath": "./src/stories/time-trackers/ClocBasic.stories.tsx", "tags": ["dev", "test"]}, "time-trackers-basic-cloc--basic-timer-icon-contained-full-rounded-progress": {"type": "story", "id": "time-trackers-basic-cloc--basic-timer-icon-contained-full-rounded-progress", "name": "Basic Timer Icon Contained Full Rounded Progress", "title": "Time Trackers/Basic Cloc", "importPath": "./src/stories/time-trackers/ClocBasic.stories.tsx", "tags": ["dev", "test"]}, "time-trackers-basic-cloc--basic-timer-icon-progress-button": {"type": "story", "id": "time-trackers-basic-cloc--basic-timer-icon-progress-button", "name": "Basic Timer Icon Progress Button", "title": "Time Trackers/Basic Cloc", "importPath": "./src/stories/time-trackers/ClocBasic.stories.tsx", "tags": ["dev", "test"]}, "time-trackers-basic-cloc--basic-timer-icon-border-progress-button": {"type": "story", "id": "time-trackers-basic-cloc--basic-timer-icon-border-progress-button", "name": "Basic Timer Icon Border Progress Button", "title": "Time Trackers/Basic Cloc", "importPath": "./src/stories/time-trackers/ClocBasic.stories.tsx", "tags": ["dev", "test"]}, "time-trackers-basic-cloc--basic-timer-icon-border-rounded-progress-button": {"type": "story", "id": "time-trackers-basic-cloc--basic-timer-icon-border-rounded-progress-button", "name": "Basic Timer Icon Border Rounded Progress Button", "title": "Time Trackers/Basic Cloc", "importPath": "./src/stories/time-trackers/ClocBasic.stories.tsx", "tags": ["dev", "test"]}, "time-trackers-basic-cloc--basic-timer-icon-border-full-rounded-progress-button": {"type": "story", "id": "time-trackers-basic-cloc--basic-timer-icon-border-full-rounded-progress-button", "name": "Basic Timer Icon Border Full Rounded Progress Button", "title": "Time Trackers/Basic Cloc", "importPath": "./src/stories/time-trackers/ClocBasic.stories.tsx", "tags": ["dev", "test"]}, "time-trackers-basic-cloc--basic-timer-icon-gray-progress-button": {"type": "story", "id": "time-trackers-basic-cloc--basic-timer-icon-gray-progress-button", "name": "Basic Timer Icon Gray Progress Button", "title": "Time Trackers/Basic Cloc", "importPath": "./src/stories/time-trackers/ClocBasic.stories.tsx", "tags": ["dev", "test"]}, "time-trackers-basic-cloc--basic-timer-icon-gray-rounded-progress-button": {"type": "story", "id": "time-trackers-basic-cloc--basic-timer-icon-gray-rounded-progress-button", "name": "Basic Timer Icon Gray Rounded Progress Button", "title": "Time Trackers/Basic Cloc", "importPath": "./src/stories/time-trackers/ClocBasic.stories.tsx", "tags": ["dev", "test"]}, "time-trackers-basic-cloc--basic-timer-icon-gray-full-rounded-progress-button": {"type": "story", "id": "time-trackers-basic-cloc--basic-timer-icon-gray-full-rounded-progress-button", "name": "Basic Timer Icon Gray Full Rounded Progress Button", "title": "Time Trackers/Basic Cloc", "importPath": "./src/stories/time-trackers/ClocBasic.stories.tsx", "tags": ["dev", "test"]}, "time-trackers-basic-cloc--basic-timer-icon-contained-progress-button": {"type": "story", "id": "time-trackers-basic-cloc--basic-timer-icon-contained-progress-button", "name": "Basic Timer Icon Contained Progress Button", "title": "Time Trackers/Basic Cloc", "importPath": "./src/stories/time-trackers/ClocBasic.stories.tsx", "tags": ["dev", "test"]}, "time-trackers-basic-cloc--basic-timer-icon-contained-rounded-progress-button": {"type": "story", "id": "time-trackers-basic-cloc--basic-timer-icon-contained-rounded-progress-button", "name": "Basic Timer Icon Contained Rounded Progress Button", "title": "Time Trackers/Basic Cloc", "importPath": "./src/stories/time-trackers/ClocBasic.stories.tsx", "tags": ["dev", "test"]}, "time-trackers-basic-cloc--basic-timer-icon-contained-full-rounded-progress-button": {"type": "story", "id": "time-trackers-basic-cloc--basic-timer-icon-contained-full-rounded-progress-button", "name": "Basic Timer Icon Contained Full Rounded Progress Button", "title": "Time Trackers/Basic Cloc", "importPath": "./src/stories/time-trackers/ClocBasic.stories.tsx", "tags": ["dev", "test"]}, "time-trackers-cloc-custom--default-basic-timer": {"type": "story", "id": "time-trackers-cloc-custom--default-basic-timer", "name": "Default Basic Timer", "title": "Time Trackers/Cloc Custom", "importPath": "./src/stories/time-trackers/ClocCustom.stories.tsx", "tags": ["dev", "test"]}, "time-trackers-cloc-custom--basic-timer-border": {"type": "story", "id": "time-trackers-cloc-custom--basic-timer-border", "name": "Basic Timer Border", "title": "Time Trackers/Cloc Custom", "importPath": "./src/stories/time-trackers/ClocCustom.stories.tsx", "tags": ["dev", "test"]}, "time-trackers-cloc-custom--basic-timer-border-rounded": {"type": "story", "id": "time-trackers-cloc-custom--basic-timer-border-rounded", "name": "Basic Timer Border Rounded", "title": "Time Trackers/Cloc Custom", "importPath": "./src/stories/time-trackers/ClocCustom.stories.tsx", "tags": ["dev", "test"]}, "time-trackers-cloc-custom--basic-timer-border-full-rounded": {"type": "story", "id": "time-trackers-cloc-custom--basic-timer-border-full-rounded", "name": "Basic Timer Border Full Rounded", "title": "Time Trackers/Cloc Custom", "importPath": "./src/stories/time-trackers/ClocCustom.stories.tsx", "tags": ["dev", "test"]}, "time-trackers-cloc-custom--basic-timer-gray": {"type": "story", "id": "time-trackers-cloc-custom--basic-timer-gray", "name": "Basic Timer Gray", "title": "Time Trackers/Cloc Custom", "importPath": "./src/stories/time-trackers/ClocCustom.stories.tsx", "tags": ["dev", "test"]}, "time-trackers-cloc-custom--basic-timer-gray-rounded": {"type": "story", "id": "time-trackers-cloc-custom--basic-timer-gray-rounded", "name": "Basic Timer Gray Rounded", "title": "Time Trackers/Cloc Custom", "importPath": "./src/stories/time-trackers/ClocCustom.stories.tsx", "tags": ["dev", "test"]}, "time-trackers-cloc-custom--basic-timer-gray-full-rounded": {"type": "story", "id": "time-trackers-cloc-custom--basic-timer-gray-full-rounded", "name": "Basic Timer Gray Full Rounded", "title": "Time Trackers/Cloc Custom", "importPath": "./src/stories/time-trackers/ClocCustom.stories.tsx", "tags": ["dev", "test"]}, "time-trackers-cloc-custom--basic-timer-contained": {"type": "story", "id": "time-trackers-cloc-custom--basic-timer-contained", "name": "Basic Timer Contained", "title": "Time Trackers/Cloc Custom", "importPath": "./src/stories/time-trackers/ClocCustom.stories.tsx", "tags": ["dev", "test"]}, "time-trackers-cloc-custom--basic-timer-contained-rounded": {"type": "story", "id": "time-trackers-cloc-custom--basic-timer-contained-rounded", "name": "Basic Timer Contained Rounded", "title": "Time Trackers/Cloc Custom", "importPath": "./src/stories/time-trackers/ClocCustom.stories.tsx", "tags": ["dev", "test"]}, "time-trackers-cloc-custom--basic-timer-contained-full-rounded": {"type": "story", "id": "time-trackers-cloc-custom--basic-timer-contained-full-rounded", "name": "Basic Timer Contained Full Rounded", "title": "Time Trackers/Cloc Custom", "importPath": "./src/stories/time-trackers/ClocCustom.stories.tsx", "tags": ["dev", "test"]}, "time-trackers-cloc-custom--basic-timer-icon": {"type": "story", "id": "time-trackers-cloc-custom--basic-timer-icon", "name": "Basic Timer Icon", "title": "Time Trackers/Cloc Custom", "importPath": "./src/stories/time-trackers/ClocCustom.stories.tsx", "tags": ["dev", "test"]}, "time-trackers-cloc-custom--basic-timer-icon-border": {"type": "story", "id": "time-trackers-cloc-custom--basic-timer-icon-border", "name": "Basic Timer Icon Border", "title": "Time Trackers/Cloc Custom", "importPath": "./src/stories/time-trackers/ClocCustom.stories.tsx", "tags": ["dev", "test"]}, "time-trackers-cloc-custom--basic-timer-icon-border-rounded": {"type": "story", "id": "time-trackers-cloc-custom--basic-timer-icon-border-rounded", "name": "Basic Timer Icon Border Rounded", "title": "Time Trackers/Cloc Custom", "importPath": "./src/stories/time-trackers/ClocCustom.stories.tsx", "tags": ["dev", "test"]}, "time-trackers-cloc-custom--basic-timer-icon-border-full-rounded": {"type": "story", "id": "time-trackers-cloc-custom--basic-timer-icon-border-full-rounded", "name": "Basic Timer Icon Border Full Rounded", "title": "Time Trackers/Cloc Custom", "importPath": "./src/stories/time-trackers/ClocCustom.stories.tsx", "tags": ["dev", "test"]}, "time-trackers-cloc-custom--basic-timer-icon-gray": {"type": "story", "id": "time-trackers-cloc-custom--basic-timer-icon-gray", "name": "Basic Timer Icon Gray", "title": "Time Trackers/Cloc Custom", "importPath": "./src/stories/time-trackers/ClocCustom.stories.tsx", "tags": ["dev", "test"]}, "time-trackers-cloc-custom--basic-timer-icon-gray-rounded": {"type": "story", "id": "time-trackers-cloc-custom--basic-timer-icon-gray-rounded", "name": "Basic Timer Icon Gray Rounded", "title": "Time Trackers/Cloc Custom", "importPath": "./src/stories/time-trackers/ClocCustom.stories.tsx", "tags": ["dev", "test"]}, "time-trackers-cloc-custom--basic-timer-icon-gray-full-rounded": {"type": "story", "id": "time-trackers-cloc-custom--basic-timer-icon-gray-full-rounded", "name": "Basic Timer Icon Gray Full Rounded", "title": "Time Trackers/Cloc Custom", "importPath": "./src/stories/time-trackers/ClocCustom.stories.tsx", "tags": ["dev", "test"]}, "time-trackers-cloc-custom--basic-timer-icon-contained": {"type": "story", "id": "time-trackers-cloc-custom--basic-timer-icon-contained", "name": "Basic Timer Icon Contained", "title": "Time Trackers/Cloc Custom", "importPath": "./src/stories/time-trackers/ClocCustom.stories.tsx", "tags": ["dev", "test"]}, "time-trackers-cloc-custom--basic-timer-icon-contained-rounded": {"type": "story", "id": "time-trackers-cloc-custom--basic-timer-icon-contained-rounded", "name": "Basic Timer Icon Contained Rounded", "title": "Time Trackers/Cloc Custom", "importPath": "./src/stories/time-trackers/ClocCustom.stories.tsx", "tags": ["dev", "test"]}, "time-trackers-cloc-custom--basic-timer-icon-contained-full-rounded": {"type": "story", "id": "time-trackers-cloc-custom--basic-timer-icon-contained-full-rounded", "name": "Basic Timer Icon Contained Full Rounded", "title": "Time Trackers/Cloc Custom", "importPath": "./src/stories/time-trackers/ClocCustom.stories.tsx", "tags": ["dev", "test"]}, "time-trackers-cloc-custom--basic-timer-icon-progress": {"type": "story", "id": "time-trackers-cloc-custom--basic-timer-icon-progress", "name": "Basic Timer Icon Progress", "title": "Time Trackers/Cloc Custom", "importPath": "./src/stories/time-trackers/ClocCustom.stories.tsx", "tags": ["dev", "test"]}, "time-trackers-cloc-custom--basic-timer-icon-border-progress": {"type": "story", "id": "time-trackers-cloc-custom--basic-timer-icon-border-progress", "name": "Basic Timer Icon Border Progress", "title": "Time Trackers/Cloc Custom", "importPath": "./src/stories/time-trackers/ClocCustom.stories.tsx", "tags": ["dev", "test"]}, "time-trackers-cloc-custom--basic-timer-icon-border-rounded-progress": {"type": "story", "id": "time-trackers-cloc-custom--basic-timer-icon-border-rounded-progress", "name": "Basic Timer Icon Border Rounded Progress", "title": "Time Trackers/Cloc Custom", "importPath": "./src/stories/time-trackers/ClocCustom.stories.tsx", "tags": ["dev", "test"]}, "time-trackers-cloc-custom--basic-timer-icon-border-full-rounded-progress": {"type": "story", "id": "time-trackers-cloc-custom--basic-timer-icon-border-full-rounded-progress", "name": "Basic Timer Icon Border Full Rounded Progress", "title": "Time Trackers/Cloc Custom", "importPath": "./src/stories/time-trackers/ClocCustom.stories.tsx", "tags": ["dev", "test"]}, "time-trackers-cloc-custom--basic-timer-icon-gray-progress": {"type": "story", "id": "time-trackers-cloc-custom--basic-timer-icon-gray-progress", "name": "Basic Timer Icon Gray Progress", "title": "Time Trackers/Cloc Custom", "importPath": "./src/stories/time-trackers/ClocCustom.stories.tsx", "tags": ["dev", "test"]}, "time-trackers-cloc-custom--basic-timer-icon-gray-rounded-progress": {"type": "story", "id": "time-trackers-cloc-custom--basic-timer-icon-gray-rounded-progress", "name": "Basic Timer Icon Gray Rounded Progress", "title": "Time Trackers/Cloc Custom", "importPath": "./src/stories/time-trackers/ClocCustom.stories.tsx", "tags": ["dev", "test"]}, "time-trackers-cloc-custom--basic-timer-icon-gray-full-rounded-progress": {"type": "story", "id": "time-trackers-cloc-custom--basic-timer-icon-gray-full-rounded-progress", "name": "Basic Timer Icon Gray Full Rounded Progress", "title": "Time Trackers/Cloc Custom", "importPath": "./src/stories/time-trackers/ClocCustom.stories.tsx", "tags": ["dev", "test"]}, "time-trackers-cloc-custom--basic-timer-icon-contained-progress": {"type": "story", "id": "time-trackers-cloc-custom--basic-timer-icon-contained-progress", "name": "Basic Timer Icon Contained Progress", "title": "Time Trackers/Cloc Custom", "importPath": "./src/stories/time-trackers/ClocCustom.stories.tsx", "tags": ["dev", "test"]}, "time-trackers-cloc-custom--basic-timer-icon-contained-rounded-progress": {"type": "story", "id": "time-trackers-cloc-custom--basic-timer-icon-contained-rounded-progress", "name": "Basic Timer Icon Contained Rounded Progress", "title": "Time Trackers/Cloc Custom", "importPath": "./src/stories/time-trackers/ClocCustom.stories.tsx", "tags": ["dev", "test"]}, "time-trackers-cloc-custom--basic-timer-icon-contained-full-rounded-progress": {"type": "story", "id": "time-trackers-cloc-custom--basic-timer-icon-contained-full-rounded-progress", "name": "Basic Timer Icon Contained Full Rounded Progress", "title": "Time Trackers/Cloc Custom", "importPath": "./src/stories/time-trackers/ClocCustom.stories.tsx", "tags": ["dev", "test"]}, "time-trackers-cloc-custom--basic-timer-icon-progress-button": {"type": "story", "id": "time-trackers-cloc-custom--basic-timer-icon-progress-button", "name": "Basic Timer Icon Progress Button", "title": "Time Trackers/Cloc Custom", "importPath": "./src/stories/time-trackers/ClocCustom.stories.tsx", "tags": ["dev", "test"]}, "time-trackers-cloc-custom--basic-timer-icon-border-progress-button": {"type": "story", "id": "time-trackers-cloc-custom--basic-timer-icon-border-progress-button", "name": "Basic Timer Icon Border Progress Button", "title": "Time Trackers/Cloc Custom", "importPath": "./src/stories/time-trackers/ClocCustom.stories.tsx", "tags": ["dev", "test"]}, "time-trackers-cloc-custom--basic-timer-icon-border-rounded-progress-button": {"type": "story", "id": "time-trackers-cloc-custom--basic-timer-icon-border-rounded-progress-button", "name": "Basic Timer Icon Border Rounded Progress Button", "title": "Time Trackers/Cloc Custom", "importPath": "./src/stories/time-trackers/ClocCustom.stories.tsx", "tags": ["dev", "test"]}, "time-trackers-cloc-custom--basic-timer-icon-border-full-rounded-progress-button": {"type": "story", "id": "time-trackers-cloc-custom--basic-timer-icon-border-full-rounded-progress-button", "name": "Basic Timer Icon Border Full Rounded Progress Button", "title": "Time Trackers/Cloc Custom", "importPath": "./src/stories/time-trackers/ClocCustom.stories.tsx", "tags": ["dev", "test"]}, "time-trackers-cloc-custom--basic-timer-icon-gray-progress-button": {"type": "story", "id": "time-trackers-cloc-custom--basic-timer-icon-gray-progress-button", "name": "Basic Timer Icon Gray Progress Button", "title": "Time Trackers/Cloc Custom", "importPath": "./src/stories/time-trackers/ClocCustom.stories.tsx", "tags": ["dev", "test"]}, "time-trackers-cloc-custom--basic-timer-icon-gray-rounded-progress-button": {"type": "story", "id": "time-trackers-cloc-custom--basic-timer-icon-gray-rounded-progress-button", "name": "Basic Timer Icon Gray Rounded Progress Button", "title": "Time Trackers/Cloc Custom", "importPath": "./src/stories/time-trackers/ClocCustom.stories.tsx", "tags": ["dev", "test"]}, "time-trackers-cloc-custom--basic-timer-icon-gray-full-rounded-progress-button": {"type": "story", "id": "time-trackers-cloc-custom--basic-timer-icon-gray-full-rounded-progress-button", "name": "Basic Timer Icon Gray Full Rounded Progress Button", "title": "Time Trackers/Cloc Custom", "importPath": "./src/stories/time-trackers/ClocCustom.stories.tsx", "tags": ["dev", "test"]}, "time-trackers-cloc-custom--basic-timer-icon-contained-progress-button": {"type": "story", "id": "time-trackers-cloc-custom--basic-timer-icon-contained-progress-button", "name": "Basic Timer Icon Contained Progress Button", "title": "Time Trackers/Cloc Custom", "importPath": "./src/stories/time-trackers/ClocCustom.stories.tsx", "tags": ["dev", "test"]}, "time-trackers-cloc-custom--basic-timer-icon-contained-rounded-progress-button": {"type": "story", "id": "time-trackers-cloc-custom--basic-timer-icon-contained-rounded-progress-button", "name": "Basic Timer Icon Contained Rounded Progress Button", "title": "Time Trackers/Cloc Custom", "importPath": "./src/stories/time-trackers/ClocCustom.stories.tsx", "tags": ["dev", "test"]}, "time-trackers-cloc-custom--basic-timer-icon-contained-full-rounded-progress-button": {"type": "story", "id": "time-trackers-cloc-custom--basic-timer-icon-contained-full-rounded-progress-button", "name": "Basic Timer Icon Contained Full Rounded Progress Button", "title": "Time Trackers/Cloc Custom", "importPath": "./src/stories/time-trackers/ClocCustom.stories.tsx", "tags": ["dev", "test"]}, "time-trackers-cloc-extra--default-basic-timer": {"type": "story", "id": "time-trackers-cloc-extra--default-basic-timer", "name": "Default Basic Timer", "title": "Time Trackers/Cloc Extra", "importPath": "./src/stories/time-trackers/ClocExtra.stories.tsx", "tags": ["dev", "test"]}, "time-trackers-cloc-extra--basic-timer-border": {"type": "story", "id": "time-trackers-cloc-extra--basic-timer-border", "name": "Basic Timer Border", "title": "Time Trackers/Cloc Extra", "importPath": "./src/stories/time-trackers/ClocExtra.stories.tsx", "tags": ["dev", "test"]}, "time-trackers-cloc-extra--basic-timer-border-rounded": {"type": "story", "id": "time-trackers-cloc-extra--basic-timer-border-rounded", "name": "Basic Timer Border Rounded", "title": "Time Trackers/Cloc Extra", "importPath": "./src/stories/time-trackers/ClocExtra.stories.tsx", "tags": ["dev", "test"]}, "time-trackers-cloc-extra--basic-timer-border-full-rounded": {"type": "story", "id": "time-trackers-cloc-extra--basic-timer-border-full-rounded", "name": "Basic Timer Border Full Rounded", "title": "Time Trackers/Cloc Extra", "importPath": "./src/stories/time-trackers/ClocExtra.stories.tsx", "tags": ["dev", "test"]}, "time-trackers-cloc-extra--basic-timer-gray": {"type": "story", "id": "time-trackers-cloc-extra--basic-timer-gray", "name": "Basic Timer Gray", "title": "Time Trackers/Cloc Extra", "importPath": "./src/stories/time-trackers/ClocExtra.stories.tsx", "tags": ["dev", "test"]}, "time-trackers-cloc-extra--basic-timer-gray-rounded": {"type": "story", "id": "time-trackers-cloc-extra--basic-timer-gray-rounded", "name": "Basic Timer Gray Rounded", "title": "Time Trackers/Cloc Extra", "importPath": "./src/stories/time-trackers/ClocExtra.stories.tsx", "tags": ["dev", "test"]}, "time-trackers-cloc-extra--basic-timer-gray-full-rounded": {"type": "story", "id": "time-trackers-cloc-extra--basic-timer-gray-full-rounded", "name": "Basic Timer Gray Full Rounded", "title": "Time Trackers/Cloc Extra", "importPath": "./src/stories/time-trackers/ClocExtra.stories.tsx", "tags": ["dev", "test"]}, "time-trackers-cloc-extra--basic-timer-contained": {"type": "story", "id": "time-trackers-cloc-extra--basic-timer-contained", "name": "Basic Timer Contained", "title": "Time Trackers/Cloc Extra", "importPath": "./src/stories/time-trackers/ClocExtra.stories.tsx", "tags": ["dev", "test"]}, "time-trackers-cloc-extra--basic-timer-contained-rounded": {"type": "story", "id": "time-trackers-cloc-extra--basic-timer-contained-rounded", "name": "Basic Timer Contained Rounded", "title": "Time Trackers/Cloc Extra", "importPath": "./src/stories/time-trackers/ClocExtra.stories.tsx", "tags": ["dev", "test"]}, "time-trackers-cloc-extra--basic-timer-contained-full-rounded": {"type": "story", "id": "time-trackers-cloc-extra--basic-timer-contained-full-rounded", "name": "Basic Timer Contained Full Rounded", "title": "Time Trackers/Cloc Extra", "importPath": "./src/stories/time-trackers/ClocExtra.stories.tsx", "tags": ["dev", "test"]}, "time-trackers-cloc-extra--basic-timer-icon": {"type": "story", "id": "time-trackers-cloc-extra--basic-timer-icon", "name": "Basic Timer Icon", "title": "Time Trackers/Cloc Extra", "importPath": "./src/stories/time-trackers/ClocExtra.stories.tsx", "tags": ["dev", "test"]}, "time-trackers-cloc-extra--basic-timer-icon-border": {"type": "story", "id": "time-trackers-cloc-extra--basic-timer-icon-border", "name": "Basic Timer Icon Border", "title": "Time Trackers/Cloc Extra", "importPath": "./src/stories/time-trackers/ClocExtra.stories.tsx", "tags": ["dev", "test"]}, "time-trackers-cloc-extra--basic-timer-icon-border-rounded": {"type": "story", "id": "time-trackers-cloc-extra--basic-timer-icon-border-rounded", "name": "Basic Timer Icon Border Rounded", "title": "Time Trackers/Cloc Extra", "importPath": "./src/stories/time-trackers/ClocExtra.stories.tsx", "tags": ["dev", "test"]}, "time-trackers-cloc-extra--basic-timer-icon-border-full-rounded": {"type": "story", "id": "time-trackers-cloc-extra--basic-timer-icon-border-full-rounded", "name": "Basic Timer Icon Border Full Rounded", "title": "Time Trackers/Cloc Extra", "importPath": "./src/stories/time-trackers/ClocExtra.stories.tsx", "tags": ["dev", "test"]}, "time-trackers-cloc-extra--basic-timer-icon-gray": {"type": "story", "id": "time-trackers-cloc-extra--basic-timer-icon-gray", "name": "Basic Timer Icon Gray", "title": "Time Trackers/Cloc Extra", "importPath": "./src/stories/time-trackers/ClocExtra.stories.tsx", "tags": ["dev", "test"]}, "time-trackers-cloc-extra--basic-timer-icon-gray-rounded": {"type": "story", "id": "time-trackers-cloc-extra--basic-timer-icon-gray-rounded", "name": "Basic Timer Icon Gray Rounded", "title": "Time Trackers/Cloc Extra", "importPath": "./src/stories/time-trackers/ClocExtra.stories.tsx", "tags": ["dev", "test"]}, "time-trackers-cloc-extra--basic-timer-icon-gray-full-rounded": {"type": "story", "id": "time-trackers-cloc-extra--basic-timer-icon-gray-full-rounded", "name": "Basic Timer Icon Gray Full Rounded", "title": "Time Trackers/Cloc Extra", "importPath": "./src/stories/time-trackers/ClocExtra.stories.tsx", "tags": ["dev", "test"]}, "time-trackers-cloc-extra--basic-timer-icon-contained": {"type": "story", "id": "time-trackers-cloc-extra--basic-timer-icon-contained", "name": "Basic Timer Icon Contained", "title": "Time Trackers/Cloc Extra", "importPath": "./src/stories/time-trackers/ClocExtra.stories.tsx", "tags": ["dev", "test"]}, "time-trackers-cloc-extra--basic-timer-icon-contained-rounded": {"type": "story", "id": "time-trackers-cloc-extra--basic-timer-icon-contained-rounded", "name": "Basic Timer Icon Contained Rounded", "title": "Time Trackers/Cloc Extra", "importPath": "./src/stories/time-trackers/ClocExtra.stories.tsx", "tags": ["dev", "test"]}, "time-trackers-cloc-extra--basic-timer-icon-contained-full-rounded": {"type": "story", "id": "time-trackers-cloc-extra--basic-timer-icon-contained-full-rounded", "name": "Basic Timer Icon Contained Full Rounded", "title": "Time Trackers/Cloc Extra", "importPath": "./src/stories/time-trackers/ClocExtra.stories.tsx", "tags": ["dev", "test"]}, "time-trackers-cloc-extra--basic-timer-icon-progress": {"type": "story", "id": "time-trackers-cloc-extra--basic-timer-icon-progress", "name": "Basic Timer Icon Progress", "title": "Time Trackers/Cloc Extra", "importPath": "./src/stories/time-trackers/ClocExtra.stories.tsx", "tags": ["dev", "test"]}, "time-trackers-cloc-extra--basic-timer-icon-border-progress": {"type": "story", "id": "time-trackers-cloc-extra--basic-timer-icon-border-progress", "name": "Basic Timer Icon Border Progress", "title": "Time Trackers/Cloc Extra", "importPath": "./src/stories/time-trackers/ClocExtra.stories.tsx", "tags": ["dev", "test"]}, "time-trackers-cloc-extra--basic-timer-icon-border-rounded-progress": {"type": "story", "id": "time-trackers-cloc-extra--basic-timer-icon-border-rounded-progress", "name": "Basic Timer Icon Border Rounded Progress", "title": "Time Trackers/Cloc Extra", "importPath": "./src/stories/time-trackers/ClocExtra.stories.tsx", "tags": ["dev", "test"]}, "time-trackers-cloc-extra--basic-timer-icon-border-full-rounded-progress": {"type": "story", "id": "time-trackers-cloc-extra--basic-timer-icon-border-full-rounded-progress", "name": "Basic Timer Icon Border Full Rounded Progress", "title": "Time Trackers/Cloc Extra", "importPath": "./src/stories/time-trackers/ClocExtra.stories.tsx", "tags": ["dev", "test"]}, "time-trackers-cloc-extra--basic-timer-icon-gray-progress": {"type": "story", "id": "time-trackers-cloc-extra--basic-timer-icon-gray-progress", "name": "Basic Timer Icon Gray Progress", "title": "Time Trackers/Cloc Extra", "importPath": "./src/stories/time-trackers/ClocExtra.stories.tsx", "tags": ["dev", "test"]}, "time-trackers-cloc-extra--basic-timer-icon-gray-rounded-progress": {"type": "story", "id": "time-trackers-cloc-extra--basic-timer-icon-gray-rounded-progress", "name": "Basic Timer Icon Gray Rounded Progress", "title": "Time Trackers/Cloc Extra", "importPath": "./src/stories/time-trackers/ClocExtra.stories.tsx", "tags": ["dev", "test"]}, "time-trackers-cloc-extra--basic-timer-icon-gray-full-rounded-progress": {"type": "story", "id": "time-trackers-cloc-extra--basic-timer-icon-gray-full-rounded-progress", "name": "Basic Timer Icon Gray Full Rounded Progress", "title": "Time Trackers/Cloc Extra", "importPath": "./src/stories/time-trackers/ClocExtra.stories.tsx", "tags": ["dev", "test"]}, "time-trackers-cloc-extra--basic-timer-icon-contained-progress": {"type": "story", "id": "time-trackers-cloc-extra--basic-timer-icon-contained-progress", "name": "Basic Timer Icon Contained Progress", "title": "Time Trackers/Cloc Extra", "importPath": "./src/stories/time-trackers/ClocExtra.stories.tsx", "tags": ["dev", "test"]}, "time-trackers-cloc-extra--basic-timer-icon-contained-rounded-progress": {"type": "story", "id": "time-trackers-cloc-extra--basic-timer-icon-contained-rounded-progress", "name": "Basic Timer Icon Contained Rounded Progress", "title": "Time Trackers/Cloc Extra", "importPath": "./src/stories/time-trackers/ClocExtra.stories.tsx", "tags": ["dev", "test"]}, "time-trackers-cloc-extra--basic-timer-icon-contained-full-rounded-progress": {"type": "story", "id": "time-trackers-cloc-extra--basic-timer-icon-contained-full-rounded-progress", "name": "Basic Timer Icon Contained Full Rounded Progress", "title": "Time Trackers/Cloc Extra", "importPath": "./src/stories/time-trackers/ClocExtra.stories.tsx", "tags": ["dev", "test"]}, "time-trackers-cloc-extra--basic-timer-icon-progress-button": {"type": "story", "id": "time-trackers-cloc-extra--basic-timer-icon-progress-button", "name": "Basic Timer Icon Progress Button", "title": "Time Trackers/Cloc Extra", "importPath": "./src/stories/time-trackers/ClocExtra.stories.tsx", "tags": ["dev", "test"]}, "time-trackers-cloc-extra--basic-timer-icon-border-progress-button": {"type": "story", "id": "time-trackers-cloc-extra--basic-timer-icon-border-progress-button", "name": "Basic Timer Icon Border Progress Button", "title": "Time Trackers/Cloc Extra", "importPath": "./src/stories/time-trackers/ClocExtra.stories.tsx", "tags": ["dev", "test"]}, "time-trackers-cloc-extra--basic-timer-icon-border-rounded-progress-button": {"type": "story", "id": "time-trackers-cloc-extra--basic-timer-icon-border-rounded-progress-button", "name": "Basic Timer Icon Border Rounded Progress Button", "title": "Time Trackers/Cloc Extra", "importPath": "./src/stories/time-trackers/ClocExtra.stories.tsx", "tags": ["dev", "test"]}, "time-trackers-cloc-extra--basic-timer-icon-border-full-rounded-progress-button": {"type": "story", "id": "time-trackers-cloc-extra--basic-timer-icon-border-full-rounded-progress-button", "name": "Basic Timer Icon Border Full Rounded Progress Button", "title": "Time Trackers/Cloc Extra", "importPath": "./src/stories/time-trackers/ClocExtra.stories.tsx", "tags": ["dev", "test"]}, "time-trackers-cloc-extra--basic-timer-icon-gray-progress-button": {"type": "story", "id": "time-trackers-cloc-extra--basic-timer-icon-gray-progress-button", "name": "Basic Timer Icon Gray Progress Button", "title": "Time Trackers/Cloc Extra", "importPath": "./src/stories/time-trackers/ClocExtra.stories.tsx", "tags": ["dev", "test"]}, "time-trackers-cloc-extra--basic-timer-icon-gray-rounded-progress-button": {"type": "story", "id": "time-trackers-cloc-extra--basic-timer-icon-gray-rounded-progress-button", "name": "Basic Timer Icon Gray Rounded Progress Button", "title": "Time Trackers/Cloc Extra", "importPath": "./src/stories/time-trackers/ClocExtra.stories.tsx", "tags": ["dev", "test"]}, "time-trackers-cloc-extra--basic-timer-icon-gray-full-rounded-progress-button": {"type": "story", "id": "time-trackers-cloc-extra--basic-timer-icon-gray-full-rounded-progress-button", "name": "Basic Timer Icon Gray Full Rounded Progress Button", "title": "Time Trackers/Cloc Extra", "importPath": "./src/stories/time-trackers/ClocExtra.stories.tsx", "tags": ["dev", "test"]}, "time-trackers-cloc-extra--basic-timer-icon-contained-progress-button": {"type": "story", "id": "time-trackers-cloc-extra--basic-timer-icon-contained-progress-button", "name": "Basic Timer Icon Contained Progress Button", "title": "Time Trackers/Cloc Extra", "importPath": "./src/stories/time-trackers/ClocExtra.stories.tsx", "tags": ["dev", "test"]}, "time-trackers-cloc-extra--basic-timer-icon-contained-rounded-progress-button": {"type": "story", "id": "time-trackers-cloc-extra--basic-timer-icon-contained-rounded-progress-button", "name": "Basic Timer Icon Contained Rounded Progress Button", "title": "Time Trackers/Cloc Extra", "importPath": "./src/stories/time-trackers/ClocExtra.stories.tsx", "tags": ["dev", "test"]}, "time-trackers-cloc-extra--basic-timer-icon-contained-full-rounded-progress-button": {"type": "story", "id": "time-trackers-cloc-extra--basic-timer-icon-contained-full-rounded-progress-button", "name": "Basic Timer Icon Contained Full Rounded Progress Button", "title": "Time Trackers/Cloc Extra", "importPath": "./src/stories/time-trackers/ClocExtra.stories.tsx", "tags": ["dev", "test"]}, "time-trackers-pomodoro-timer--default": {"type": "story", "id": "time-trackers-pomodoro-timer--default", "name": "<PERSON><PERSON><PERSON>", "title": "Time Trackers/Pomodoro Timer", "importPath": "./src/stories/time-trackers/ClocPomodoroTimer.stories.tsx", "tags": ["dev", "test"]}, "time-trackers-pomodoro-timer--small-size": {"type": "story", "id": "time-trackers-pomodoro-timer--small-size", "name": "Small Size", "title": "Time Trackers/Pomodoro Timer", "importPath": "./src/stories/time-trackers/ClocPomodoroTimer.stories.tsx", "tags": ["dev", "test"]}, "time-trackers-pomodoro-timer--large-size": {"type": "story", "id": "time-trackers-pomodoro-timer--large-size", "name": "Large Size", "title": "Time Trackers/Pomodoro Timer", "importPath": "./src/stories/time-trackers/ClocPomodoroTimer.stories.tsx", "tags": ["dev", "test"]}, "time-trackers-pomodoro-timer--compact-variant": {"type": "story", "id": "time-trackers-pomodoro-timer--compact-variant", "name": "Compact <PERSON>", "title": "Time Trackers/Pomodoro Timer", "importPath": "./src/stories/time-trackers/ClocPomodoroTimer.stories.tsx", "tags": ["dev", "test"]}, "time-trackers-pomodoro-timer--minimal-variant": {"type": "story", "id": "time-trackers-pomodoro-timer--minimal-variant", "name": "<PERSON><PERSON>", "title": "Time Trackers/Pomodoro Timer", "importPath": "./src/stories/time-trackers/ClocPomodoroTimer.stories.tsx", "tags": ["dev", "test"]}, "time-trackers-pomodoro-timer--bordered-variant": {"type": "story", "id": "time-trackers-pomodoro-timer--bordered-variant", "name": "Bordered Variant", "title": "Time Trackers/Pomodoro Timer", "importPath": "./src/stories/time-trackers/ClocPomodoroTimer.stories.tsx", "tags": ["dev", "test"]}, "time-trackers-pomodoro-timer--small-compact-bordered": {"type": "story", "id": "time-trackers-pomodoro-timer--small-compact-bordered", "name": "Small Compact Bordered", "title": "Time Trackers/Pomodoro Timer", "importPath": "./src/stories/time-trackers/ClocPomodoroTimer.stories.tsx", "tags": ["dev", "test"]}, "time-trackers-pomodoro-timer--large-minimal": {"type": "story", "id": "time-trackers-pomodoro-timer--large-minimal", "name": "Large Minimal", "title": "Time Trackers/Pomodoro Timer", "importPath": "./src/stories/time-trackers/ClocPomodoroTimer.stories.tsx", "tags": ["dev", "test"]}, "time-trackers-pomodoro-timer--large-minimal-bordered": {"type": "story", "id": "time-trackers-pomodoro-timer--large-minimal-bordered", "name": "Large Minimal Bordered", "title": "Time Trackers/Pomodoro Timer", "importPath": "./src/stories/time-trackers/ClocPomodoroTimer.stories.tsx", "tags": ["dev", "test"]}, "time-trackers-pomodoro-timer--dark-theme": {"type": "story", "id": "time-trackers-pomodoro-timer--dark-theme", "name": "Dark Theme", "title": "Time Trackers/Pomodoro Timer", "importPath": "./src/stories/time-trackers/ClocPomodoroTimer.stories.tsx", "tags": ["dev", "test"]}, "time-trackers-pomodoro-timer--mobile-responsive": {"type": "story", "id": "time-trackers-pomodoro-timer--mobile-responsive", "name": "Mobile Responsive", "title": "Time Trackers/Pomodoro Timer", "importPath": "./src/stories/time-trackers/ClocPomodoroTimer.stories.tsx", "tags": ["dev", "test"]}, "time-trackers-pomodoro-timer--integration-showcase": {"type": "story", "id": "time-trackers-pomodoro-timer--integration-showcase", "name": "Integration Showcase", "title": "Time Trackers/Pomodoro Timer", "importPath": "./src/stories/time-trackers/ClocPomodoroTimer.stories.tsx", "tags": ["dev", "test"]}, "time-trackers-timer-form--default": {"type": "story", "id": "time-trackers-timer-form--default", "name": "<PERSON><PERSON><PERSON>", "title": "Time Trackers/Timer Form", "importPath": "./src/stories/time-trackers/ClocTimerForm.stories.tsx", "tags": ["dev", "test"]}, "time-trackers-timer-form--small": {"type": "story", "id": "time-trackers-timer-form--small", "name": "Small", "title": "Time Trackers/Timer Form", "importPath": "./src/stories/time-trackers/ClocTimerForm.stories.tsx", "tags": ["dev", "test"]}, "time-trackers-timer-form--large": {"type": "story", "id": "time-trackers-timer-form--large", "name": "Large", "title": "Time Trackers/Timer Form", "importPath": "./src/stories/time-trackers/ClocTimerForm.stories.tsx", "tags": ["dev", "test"]}, "time-trackers-timer-form--in-timer-widget": {"type": "story", "id": "time-trackers-timer-form--in-timer-widget", "name": "In Timer Widget", "title": "Time Trackers/Timer Form", "importPath": "./src/stories/time-trackers/ClocTimerForm.stories.tsx", "tags": ["dev", "test"]}, "time-trackers-timer-form--in-sidebar": {"type": "story", "id": "time-trackers-timer-form--in-sidebar", "name": "In Sidebar", "title": "Time Trackers/Timer Form", "importPath": "./src/stories/time-trackers/ClocTimerForm.stories.tsx", "tags": ["dev", "test"]}, "time-trackers-timer-form--compact-layout": {"type": "story", "id": "time-trackers-timer-form--compact-layout", "name": "Compact Layout", "title": "Time Trackers/Timer Form", "importPath": "./src/stories/time-trackers/ClocTimerForm.stories.tsx", "tags": ["dev", "test"]}, "time-trackers-timer-form--with-timer": {"type": "story", "id": "time-trackers-timer-form--with-timer", "name": "With Timer", "title": "Time Trackers/Timer Form", "importPath": "./src/stories/time-trackers/ClocTimerForm.stories.tsx", "tags": ["dev", "test"]}, "time-trackers-timer-form--mobile-view": {"type": "story", "id": "time-trackers-timer-form--mobile-view", "name": "Mobile View", "title": "Time Trackers/Timer Form", "importPath": "./src/stories/time-trackers/ClocTimerForm.stories.tsx", "tags": ["dev", "test"]}, "time-trackers-timer-form--quick-start": {"type": "story", "id": "time-trackers-timer-form--quick-start", "name": "Quick Start", "title": "Time Trackers/Timer Form", "importPath": "./src/stories/time-trackers/ClocTimerForm.stories.tsx", "tags": ["dev", "test"]}, "time-trackers-timer-form--with-presets": {"type": "story", "id": "time-trackers-timer-form--with-presets", "name": "With Presets", "title": "Time Trackers/Timer Form", "importPath": "./src/stories/time-trackers/ClocTimerForm.stories.tsx", "tags": ["dev", "test"]}, "time-trackers-modern-cloc--small": {"type": "story", "id": "time-trackers-modern-cloc--small", "name": "Small", "title": "Time Trackers/Modern Cloc", "importPath": "./src/stories/time-trackers/ModernCloc.stories.tsx", "tags": ["dev", "test"]}, "time-trackers-modern-cloc--small-bordered": {"type": "story", "id": "time-trackers-modern-cloc--small-bordered", "name": "Small Bordered", "title": "Time Trackers/Modern Cloc", "importPath": "./src/stories/time-trackers/ModernCloc.stories.tsx", "tags": ["dev", "test"]}, "time-trackers-modern-cloc--small-custom-separator": {"type": "story", "id": "time-trackers-modern-cloc--small-custom-separator", "name": "Small Custom Separator", "title": "Time Trackers/Modern Cloc", "importPath": "./src/stories/time-trackers/ModernCloc.stories.tsx", "tags": ["dev", "test"]}, "time-trackers-modern-cloc--small-with-no-progress": {"type": "story", "id": "time-trackers-modern-cloc--small-with-no-progress", "name": "Small With No Progress", "title": "Time Trackers/Modern Cloc", "importPath": "./src/stories/time-trackers/ModernCloc.stories.tsx", "tags": ["dev", "test"]}, "time-trackers-modern-cloc--small-expanded": {"type": "story", "id": "time-trackers-modern-cloc--small-expanded", "name": "Small Expanded", "title": "Time Trackers/Modern Cloc", "importPath": "./src/stories/time-trackers/ModernCloc.stories.tsx", "tags": ["dev", "test"]}, "time-trackers-modern-cloc--small-expanded-bordered": {"type": "story", "id": "time-trackers-modern-cloc--small-expanded-bordered", "name": "Small Expanded Bordered", "title": "Time Trackers/Modern Cloc", "importPath": "./src/stories/time-trackers/ModernCloc.stories.tsx", "tags": ["dev", "test"]}, "time-trackers-modern-cloc--default": {"type": "story", "id": "time-trackers-modern-cloc--default", "name": "<PERSON><PERSON><PERSON>", "title": "Time Trackers/Modern Cloc", "importPath": "./src/stories/time-trackers/ModernCloc.stories.tsx", "tags": ["dev", "test"]}, "time-trackers-modern-cloc--default-expanded": {"type": "story", "id": "time-trackers-modern-cloc--default-expanded", "name": "Default Expanded", "title": "Time Trackers/Modern Cloc", "importPath": "./src/stories/time-trackers/ModernCloc.stories.tsx", "tags": ["dev", "test"]}, "time-trackers-modern-cloc--default-expanded-borderded": {"type": "story", "id": "time-trackers-modern-cloc--default-expanded-borderded", "name": "Default Expanded Borderded", "title": "Time Trackers/Modern Cloc", "importPath": "./src/stories/time-trackers/ModernCloc.stories.tsx", "tags": ["dev", "test"]}, "time-trackers-modern-cloc--default-expanded-borderded-custom-theme": {"type": "story", "id": "time-trackers-modern-cloc--default-expanded-borderded-custom-theme", "name": "Default Expanded Borderded Custom Theme", "title": "Time Trackers/Modern Cloc", "importPath": "./src/stories/time-trackers/ModernCloc.stories.tsx", "tags": ["dev", "test"]}, "time-trackers-modern-cloc--default-expanded-borderded-custom-theme-2": {"type": "story", "id": "time-trackers-modern-cloc--default-expanded-borderded-custom-theme-2", "name": "Default Expanded Borderded Custom Theme 2", "title": "Time Trackers/Modern Cloc", "importPath": "./src/stories/time-trackers/ModernCloc.stories.tsx", "tags": ["dev", "test"]}, "time-trackers-modern-cloc--default-expanded-1": {"type": "story", "id": "time-trackers-modern-cloc--default-expanded-1", "name": "Default Expanded 1", "title": "Time Trackers/Modern Cloc", "importPath": "./src/stories/time-trackers/ModernCloc.stories.tsx", "tags": ["dev", "test"]}, "tracking-insights-cloc-tracking-click-insight--default": {"type": "story", "id": "tracking-insights-cloc-tracking-click-insight--default", "name": "<PERSON><PERSON><PERSON>", "title": "Tracking & Insights/Cloc Tracking Click Insight", "importPath": "./src/stories/tracking-insights/ClocTrackingClickInsight.stories.tsx", "tags": ["dev", "test"]}, "tracking-insights-cloc-tracking-click-insight--custom-styling": {"type": "story", "id": "tracking-insights-cloc-tracking-click-insight--custom-styling", "name": "Custom Styling", "title": "Tracking & Insights/Cloc Tracking Click Insight", "importPath": "./src/stories/tracking-insights/ClocTrackingClickInsight.stories.tsx", "tags": ["dev", "test"]}, "tracking-insights-cloc-tracking-filter--default": {"type": "story", "id": "tracking-insights-cloc-tracking-filter--default", "name": "<PERSON><PERSON><PERSON>", "title": "Tracking & Insights/Cloc Tracking Filter", "importPath": "./src/stories/tracking-insights/ClocTrackingFilter.stories.tsx", "tags": ["dev", "test"]}, "tracking-insights-cloc-tracking-filter--with-auto-refresh": {"type": "story", "id": "tracking-insights-cloc-tracking-filter--with-auto-refresh", "name": "With Auto Refresh", "title": "Tracking & Insights/Cloc Tracking Filter", "importPath": "./src/stories/tracking-insights/ClocTrackingFilter.stories.tsx", "tags": ["dev", "test"]}, "tracking-insights-cloc-tracking-filter--custom-refresh-interval": {"type": "story", "id": "tracking-insights-cloc-tracking-filter--custom-refresh-interval", "name": "Custom Refresh Interval", "title": "Tracking & Insights/Cloc Tracking Filter", "importPath": "./src/stories/tracking-insights/ClocTrackingFilter.stories.tsx", "tags": ["dev", "test"]}, "tracking-insights-cloc-tracking-filter--custom-styling": {"type": "story", "id": "tracking-insights-cloc-tracking-filter--custom-styling", "name": "Custom Styling", "title": "Tracking & Insights/Cloc Tracking Filter", "importPath": "./src/stories/tracking-insights/ClocTrackingFilter.stories.tsx", "tags": ["dev", "test"]}, "tracking-insights-cloc-tracking-heatmap--default": {"type": "story", "id": "tracking-insights-cloc-tracking-heatmap--default", "name": "<PERSON><PERSON><PERSON>", "title": "Tracking & Insights/Cloc Tracking Heatmap", "importPath": "./src/stories/tracking-insights/ClocTrackingHeatmap.stories.tsx", "tags": ["dev", "test"]}, "tracking-insights-cloc-tracking-heatmap--without-controls": {"type": "story", "id": "tracking-insights-cloc-tracking-heatmap--without-controls", "name": "Without Controls", "title": "Tracking & Insights/Cloc Tracking Heatmap", "importPath": "./src/stories/tracking-insights/ClocTrackingHeatmap.stories.tsx", "tags": ["dev", "test"]}, "tracking-insights-cloc-tracking-heatmap--custom-styling": {"type": "story", "id": "tracking-insights-cloc-tracking-heatmap--custom-styling", "name": "Custom Styling", "title": "Tracking & Insights/Cloc Tracking Heatmap", "importPath": "./src/stories/tracking-insights/ClocTrackingHeatmap.stories.tsx", "tags": ["dev", "test"]}, "tracking-insights-cloc-tracking-session-insight--default": {"type": "story", "id": "tracking-insights-cloc-tracking-session-insight--default", "name": "<PERSON><PERSON><PERSON>", "title": "Tracking & Insights/Cloc Tracking Session Insight", "importPath": "./src/stories/tracking-insights/ClocTrackingSessionInsight.stories.tsx", "tags": ["dev", "test"]}, "tracking-insights-cloc-tracking-session-insight--custom-styling": {"type": "story", "id": "tracking-insights-cloc-tracking-session-insight--custom-styling", "name": "Custom Styling", "title": "Tracking & Insights/Cloc Tracking Session Insight", "importPath": "./src/stories/tracking-insights/ClocTrackingSessionInsight.stories.tsx", "tags": ["dev", "test"]}, "tracking-insights-cloc-tracking-session-replay--default": {"type": "story", "id": "tracking-insights-cloc-tracking-session-replay--default", "name": "<PERSON><PERSON><PERSON>", "title": "Tracking & Insights/Cloc Tracking Session Replay", "importPath": "./src/stories/tracking-insights/ClocTrackingSessionReplay.stories.tsx", "tags": ["dev", "test"]}, "tracking-insights-cloc-tracking-session-replay--custom-styling": {"type": "story", "id": "tracking-insights-cloc-tracking-session-replay--custom-styling", "name": "Custom Styling", "title": "Tracking & Insights/Cloc Tracking Session Replay", "importPath": "./src/stories/tracking-insights/ClocTrackingSessionReplay.stories.tsx", "tags": ["dev", "test"]}, "user-account-management-account-deletion-form--default": {"type": "story", "id": "user-account-management-account-deletion-form--default", "name": "<PERSON><PERSON><PERSON>", "title": "User Account Management/Account Deletion Form", "importPath": "./src/stories/user-account-management/ClocAccountDeletionForm.stories.tsx", "tags": ["dev", "test"]}, "user-account-management-account-deletion-form--in-modal": {"type": "story", "id": "user-account-management-account-deletion-form--in-modal", "name": "In Modal", "title": "User Account Management/Account Deletion Form", "importPath": "./src/stories/user-account-management/ClocAccountDeletionForm.stories.tsx", "tags": ["dev", "test"]}, "user-account-management-account-deletion-form--custom-styling": {"type": "story", "id": "user-account-management-account-deletion-form--custom-styling", "name": "Custom Styling", "title": "User Account Management/Account Deletion Form", "importPath": "./src/stories/user-account-management/ClocAccountDeletionForm.stories.tsx", "tags": ["dev", "test"]}, "user-account-management-password-update-form--default": {"type": "story", "id": "user-account-management-password-update-form--default", "name": "<PERSON><PERSON><PERSON>", "title": "User Account Management/Password Update Form", "importPath": "./src/stories/user-account-management/ClocPasswordUpdateForm.stories.tsx", "tags": ["dev", "test"]}, "user-account-management-password-update-form--custom-styling": {"type": "story", "id": "user-account-management-password-update-form--custom-styling", "name": "Custom Styling", "title": "User Account Management/Password Update Form", "importPath": "./src/stories/user-account-management/ClocPasswordUpdateForm.stories.tsx", "tags": ["dev", "test"]}, "user-account-management-profile-photo-form--default": {"type": "story", "id": "user-account-management-profile-photo-form--default", "name": "<PERSON><PERSON><PERSON>", "title": "User Account Management/Profile Photo Form", "importPath": "./src/stories/user-account-management/ClocProfilePhotoForm.stories.tsx", "tags": ["dev", "test"]}, "user-account-management-profile-photo-form--in-profile-settings": {"type": "story", "id": "user-account-management-profile-photo-form--in-profile-settings", "name": "In Profile Settings", "title": "User Account Management/Profile Photo Form", "importPath": "./src/stories/user-account-management/ClocProfilePhotoForm.stories.tsx", "tags": ["dev", "test"]}, "user-account-management-profile-photo-form--in-user-card": {"type": "story", "id": "user-account-management-profile-photo-form--in-user-card", "name": "In User Card", "title": "User Account Management/Profile Photo Form", "importPath": "./src/stories/user-account-management/ClocProfilePhotoForm.stories.tsx", "tags": ["dev", "test"]}, "utilities-buttons-cloc-button--small-cloc-button": {"type": "story", "id": "utilities-buttons-cloc-button--small-cloc-button", "name": "Small Cloc Button", "title": "Utilities/Buttons/Cloc Button", "importPath": "./src/stories/utilities/buttons/ClocButton.stories.tsx", "tags": ["dev", "test"]}, "utilities-buttons-cloc-button--small-stop-button": {"type": "story", "id": "utilities-buttons-cloc-button--small-stop-button", "name": "Small Stop Button", "title": "Utilities/Buttons/Cloc Button", "importPath": "./src/stories/utilities/buttons/ClocButton.stories.tsx", "tags": ["dev", "test"]}, "utilities-buttons-cloc-button--small-pause-button": {"type": "story", "id": "utilities-buttons-cloc-button--small-pause-button", "name": "Small Pause Button", "title": "Utilities/Buttons/Cloc Button", "importPath": "./src/stories/utilities/buttons/ClocButton.stories.tsx", "tags": ["dev", "test"]}, "utilities-buttons-cloc-button--default-cloc-button": {"type": "story", "id": "utilities-buttons-cloc-button--default-cloc-button", "name": "<PERSON><PERSON><PERSON>", "title": "Utilities/Buttons/Cloc Button", "importPath": "./src/stories/utilities/buttons/ClocButton.stories.tsx", "tags": ["dev", "test"]}, "utilities-buttons-cloc-button--stop-button": {"type": "story", "id": "utilities-buttons-cloc-button--stop-button", "name": "Stop Button", "title": "Utilities/Buttons/Cloc Button", "importPath": "./src/stories/utilities/buttons/ClocButton.stories.tsx", "tags": ["dev", "test"]}, "utilities-buttons-cloc-button--pause-button": {"type": "story", "id": "utilities-buttons-cloc-button--pause-button", "name": "<PERSON><PERSON>", "title": "Utilities/Buttons/Cloc Button", "importPath": "./src/stories/utilities/buttons/ClocButton.stories.tsx", "tags": ["dev", "test"]}, "utilities-buttons-cloc-button--large-cloc-button": {"type": "story", "id": "utilities-buttons-cloc-button--large-cloc-button", "name": "Large Cloc Button", "title": "Utilities/Buttons/Cloc Button", "importPath": "./src/stories/utilities/buttons/ClocButton.stories.tsx", "tags": ["dev", "test"]}, "utilities-buttons-cloc-button--large-stop-button": {"type": "story", "id": "utilities-buttons-cloc-button--large-stop-button", "name": "Large Stop Button", "title": "Utilities/Buttons/Cloc Button", "importPath": "./src/stories/utilities/buttons/ClocButton.stories.tsx", "tags": ["dev", "test"]}, "utilities-buttons-cloc-button--large-pause-button": {"type": "story", "id": "utilities-buttons-cloc-button--large-pause-button", "name": "Large Pause Button", "title": "Utilities/Buttons/Cloc Button", "importPath": "./src/stories/utilities/buttons/ClocButton.stories.tsx", "tags": ["dev", "test"]}, "utilities-buttons-cloc-button--small-bordered-pause-button": {"type": "story", "id": "utilities-buttons-cloc-button--small-bordered-pause-button", "name": "Small Bordered Pause <PERSON>", "title": "Utilities/Buttons/Cloc Button", "importPath": "./src/stories/utilities/buttons/ClocButton.stories.tsx", "tags": ["dev", "test"]}, "utilities-buttons-cloc-button--default-bordered-pause-button": {"type": "story", "id": "utilities-buttons-cloc-button--default-bordered-pause-button", "name": "De<PERSON>ult Bordered <PERSON><PERSON>", "title": "Utilities/Buttons/Cloc Button", "importPath": "./src/stories/utilities/buttons/ClocButton.stories.tsx", "tags": ["dev", "test"]}, "utilities-buttons-cloc-button--large-bordered-pause-button": {"type": "story", "id": "utilities-buttons-cloc-button--large-bordered-pause-button", "name": "Large Bordered Pause <PERSON>", "title": "Utilities/Buttons/Cloc Button", "importPath": "./src/stories/utilities/buttons/ClocButton.stories.tsx", "tags": ["dev", "test"]}, "utilities-date-time-date-picker--default": {"type": "story", "id": "utilities-date-time-date-picker--default", "name": "<PERSON><PERSON><PERSON>", "title": "Utilities/Date & Time/Date Picker", "importPath": "./src/stories/utilities/date-time/ClocDatePicker.stories.tsx", "tags": ["dev", "test"]}, "utilities-date-time-date-picker--without-icon": {"type": "story", "id": "utilities-date-time-date-picker--without-icon", "name": "Without Icon", "title": "Utilities/Date & Time/Date Picker", "importPath": "./src/stories/utilities/date-time/ClocDatePicker.stories.tsx", "tags": ["dev", "test"]}, "utilities-date-time-date-picker--custom-placeholder": {"type": "story", "id": "utilities-date-time-date-picker--custom-placeholder", "name": "Custom Placeholder", "title": "Utilities/Date & Time/Date Picker", "importPath": "./src/stories/utilities/date-time/ClocDatePicker.stories.tsx", "tags": ["dev", "test"]}, "utilities-date-time-date-picker--start-date": {"type": "story", "id": "utilities-date-time-date-picker--start-date", "name": "Start Date", "title": "Utilities/Date & Time/Date Picker", "importPath": "./src/stories/utilities/date-time/ClocDatePicker.stories.tsx", "tags": ["dev", "test"]}, "utilities-date-time-date-picker--end-date": {"type": "story", "id": "utilities-date-time-date-picker--end-date", "name": "End Date", "title": "Utilities/Date & Time/Date Picker", "importPath": "./src/stories/utilities/date-time/ClocDatePicker.stories.tsx", "tags": ["dev", "test"]}, "utilities-date-time-date-picker--deadline-date": {"type": "story", "id": "utilities-date-time-date-picker--deadline-date", "name": "Deadline Date", "title": "Utilities/Date & Time/Date Picker", "importPath": "./src/stories/utilities/date-time/ClocDatePicker.stories.tsx", "tags": ["dev", "test"]}, "utilities-date-time-date-picker--custom-styling": {"type": "story", "id": "utilities-date-time-date-picker--custom-styling", "name": "Custom Styling", "title": "Utilities/Date & Time/Date Picker", "importPath": "./src/stories/utilities/date-time/ClocDatePicker.stories.tsx", "tags": ["dev", "test"]}, "utilities-date-time-date-picker--interactive": {"type": "story", "id": "utilities-date-time-date-picker--interactive", "name": "Interactive", "title": "Utilities/Date & Time/Date Picker", "importPath": "./src/stories/utilities/date-time/ClocDatePicker.stories.tsx", "tags": ["dev", "test"]}, "utilities-date-time-date-picker--form-example": {"type": "story", "id": "utilities-date-time-date-picker--form-example", "name": "Form Example", "title": "Utilities/Date & Time/Date Picker", "importPath": "./src/stories/utilities/date-time/ClocDatePicker.stories.tsx", "tags": ["dev", "test"]}, "utilities-date-time-date-picker--different-contexts": {"type": "story", "id": "utilities-date-time-date-picker--different-contexts", "name": "Different Contexts", "title": "Utilities/Date & Time/Date Picker", "importPath": "./src/stories/utilities/date-time/ClocDatePicker.stories.tsx", "tags": ["dev", "test"]}, "utilities-date-time-date-range-picker--default": {"type": "story", "id": "utilities-date-time-date-range-picker--default", "name": "<PERSON><PERSON><PERSON>", "title": "Utilities/Date & Time/Date Range Picker", "importPath": "./src/stories/utilities/date-time/ClocDateRangePicker.stories.tsx", "tags": ["dev", "test"]}, "utilities-date-time-date-range-picker--small": {"type": "story", "id": "utilities-date-time-date-range-picker--small", "name": "Small", "title": "Utilities/Date & Time/Date Range Picker", "importPath": "./src/stories/utilities/date-time/ClocDateRangePicker.stories.tsx", "tags": ["dev", "test"]}, "utilities-date-time-date-range-picker--large": {"type": "story", "id": "utilities-date-time-date-range-picker--large", "name": "Large", "title": "Utilities/Date & Time/Date Range Picker", "importPath": "./src/stories/utilities/date-time/ClocDateRangePicker.stories.tsx", "tags": ["dev", "test"]}, "utilities-date-time-date-range-picker--icon-size": {"type": "story", "id": "utilities-date-time-date-range-picker--icon-size", "name": "Icon Size", "title": "Utilities/Date & Time/Date Range Picker", "importPath": "./src/stories/utilities/date-time/ClocDateRangePicker.stories.tsx", "tags": ["dev", "test"]}, "utilities-date-time-date-range-picker--with-preselected-range": {"type": "story", "id": "utilities-date-time-date-range-picker--with-preselected-range", "name": "With Preselected Range", "title": "Utilities/Date & Time/Date Range Picker", "importPath": "./src/stories/utilities/date-time/ClocDateRangePicker.stories.tsx", "tags": ["dev", "test"]}, "utilities-date-time-date-range-picker--custom-styling": {"type": "story", "id": "utilities-date-time-date-range-picker--custom-styling", "name": "Custom Styling", "title": "Utilities/Date & Time/Date Range Picker", "importPath": "./src/stories/utilities/date-time/ClocDateRangePicker.stories.tsx", "tags": ["dev", "test"]}, "utilities-date-time-date-range-picker--interactive": {"type": "story", "id": "utilities-date-time-date-range-picker--interactive", "name": "Interactive", "title": "Utilities/Date & Time/Date Range Picker", "importPath": "./src/stories/utilities/date-time/ClocDateRangePicker.stories.tsx", "tags": ["dev", "test"]}, "utilities-date-time-date-range-picker--reporting-form": {"type": "story", "id": "utilities-date-time-date-range-picker--reporting-form", "name": "Reporting Form", "title": "Utilities/Date & Time/Date Range Picker", "importPath": "./src/stories/utilities/date-time/ClocDateRangePicker.stories.tsx", "tags": ["dev", "test"]}, "utilities-date-time-date-range-picker--size-comparison": {"type": "story", "id": "utilities-date-time-date-range-picker--size-comparison", "name": "<PERSON><PERSON> Comparison", "title": "Utilities/Date & Time/Date Range Picker", "importPath": "./src/stories/utilities/date-time/ClocDateRangePicker.stories.tsx", "tags": ["dev", "test"]}, "utilities-date-time-date-range-picker--project-timeframe": {"type": "story", "id": "utilities-date-time-date-range-picker--project-timeframe", "name": "Project Timeframe", "title": "Utilities/Date & Time/Date Range Picker", "importPath": "./src/stories/utilities/date-time/ClocDateRangePicker.stories.tsx", "tags": ["dev", "test"]}, "utilities-display-badge--default": {"type": "story", "id": "utilities-display-badge--default", "name": "<PERSON><PERSON><PERSON>", "title": "Utilities/Display/Badge", "importPath": "./src/stories/utilities/display/ClocBadge.stories.tsx", "tags": ["dev", "test"]}, "utilities-display-badge--secondary": {"type": "story", "id": "utilities-display-badge--secondary", "name": "Secondary", "title": "Utilities/Display/Badge", "importPath": "./src/stories/utilities/display/ClocBadge.stories.tsx", "tags": ["dev", "test"]}, "utilities-display-badge--destructive": {"type": "story", "id": "utilities-display-badge--destructive", "name": "Destructive", "title": "Utilities/Display/Badge", "importPath": "./src/stories/utilities/display/ClocBadge.stories.tsx", "tags": ["dev", "test"]}, "utilities-display-badge--outline": {"type": "story", "id": "utilities-display-badge--outline", "name": "Outline", "title": "Utilities/Display/Badge", "importPath": "./src/stories/utilities/display/ClocBadge.stories.tsx", "tags": ["dev", "test"]}, "utilities-display-badge--with-numbers": {"type": "story", "id": "utilities-display-badge--with-numbers", "name": "With Numbers", "title": "Utilities/Display/Badge", "importPath": "./src/stories/utilities/display/ClocBadge.stories.tsx", "tags": ["dev", "test"]}, "utilities-display-badge--long-text": {"type": "story", "id": "utilities-display-badge--long-text", "name": "Long Text", "title": "Utilities/Display/Badge", "importPath": "./src/stories/utilities/display/ClocBadge.stories.tsx", "tags": ["dev", "test"]}, "utilities-display-badge--status-badges": {"type": "story", "id": "utilities-display-badge--status-badges", "name": "Status Badges", "title": "Utilities/Display/Badge", "importPath": "./src/stories/utilities/display/ClocBadge.stories.tsx", "tags": ["dev", "test"]}, "utilities-display-badge--counter-badges": {"type": "story", "id": "utilities-display-badge--counter-badges", "name": "Counter Badges", "title": "Utilities/Display/Badge", "importPath": "./src/stories/utilities/display/ClocBadge.stories.tsx", "tags": ["dev", "test"]}, "utilities-display-badge--custom-styling": {"type": "story", "id": "utilities-display-badge--custom-styling", "name": "Custom Styling", "title": "Utilities/Display/Badge", "importPath": "./src/stories/utilities/display/ClocBadge.stories.tsx", "tags": ["dev", "test"]}, "utilities-display-badge--all-variants": {"type": "story", "id": "utilities-display-badge--all-variants", "name": "All Variants", "title": "Utilities/Display/Badge", "importPath": "./src/stories/utilities/display/ClocBadge.stories.tsx", "tags": ["dev", "test"]}, "utilities-display-carousel--default": {"type": "story", "id": "utilities-display-carousel--default", "name": "<PERSON><PERSON><PERSON>", "title": "Utilities/Display/Carousel", "importPath": "./src/stories/utilities/display/ClocCarousel.stories.tsx", "tags": ["dev", "test"]}, "utilities-display-carousel--with-numbers": {"type": "story", "id": "utilities-display-carousel--with-numbers", "name": "With Numbers", "title": "Utilities/Display/Carousel", "importPath": "./src/stories/utilities/display/ClocCarousel.stories.tsx", "tags": ["dev", "test"]}, "utilities-display-carousel--vertical": {"type": "story", "id": "utilities-display-carousel--vertical", "name": "Vertical", "title": "Utilities/Display/Carousel", "importPath": "./src/stories/utilities/display/ClocCarousel.stories.tsx", "tags": ["dev", "test"]}, "utilities-display-carousel--custom-item-width": {"type": "story", "id": "utilities-display-carousel--custom-item-width", "name": "Custom <PERSON>em <PERSON>", "title": "Utilities/Display/Carousel", "importPath": "./src/stories/utilities/display/ClocCarousel.stories.tsx", "tags": ["dev", "test"]}, "utilities-display-carousel--many-items": {"type": "story", "id": "utilities-display-carousel--many-items", "name": "Many Items", "title": "Utilities/Display/Carousel", "importPath": "./src/stories/utilities/display/ClocCarousel.stories.tsx", "tags": ["dev", "test"]}, "utilities-display-carousel--with-custom-render": {"type": "story", "id": "utilities-display-carousel--with-custom-render", "name": "With Custom Render", "title": "Utilities/Display/Carousel", "importPath": "./src/stories/utilities/display/ClocCarousel.stories.tsx", "tags": ["dev", "test"]}, "utilities-display-carousel--timer-cards": {"type": "story", "id": "utilities-display-carousel--timer-cards", "name": "Timer Cards", "title": "Utilities/Display/Carousel", "importPath": "./src/stories/utilities/display/ClocCarousel.stories.tsx", "tags": ["dev", "test"]}, "utilities-display-carousel--small-items": {"type": "story", "id": "utilities-display-carousel--small-items", "name": "Small Items", "title": "Utilities/Display/Carousel", "importPath": "./src/stories/utilities/display/ClocCarousel.stories.tsx", "tags": ["dev", "test"]}, "utilities-display-progress--default": {"type": "story", "id": "utilities-display-progress--default", "name": "<PERSON><PERSON><PERSON>", "title": "Utilities/Display/Progress", "importPath": "./src/stories/utilities/display/ClocProgress.stories.tsx", "tags": ["dev", "test"]}, "utilities-display-progress--full-width": {"type": "story", "id": "utilities-display-progress--full-width", "name": "Full Width", "title": "Utilities/Display/Progress", "importPath": "./src/stories/utilities/display/ClocProgress.stories.tsx", "tags": ["dev", "test"]}, "utilities-display-progress--small": {"type": "story", "id": "utilities-display-progress--small", "name": "Small", "title": "Utilities/Display/Progress", "importPath": "./src/stories/utilities/display/ClocProgress.stories.tsx", "tags": ["dev", "test"]}, "utilities-display-progress--large": {"type": "story", "id": "utilities-display-progress--large", "name": "Large", "title": "Utilities/Display/Progress", "importPath": "./src/stories/utilities/display/ClocProgress.stories.tsx", "tags": ["dev", "test"]}, "utilities-display-progress--custom-styling": {"type": "story", "id": "utilities-display-progress--custom-styling", "name": "Custom Styling", "title": "Utilities/Display/Progress", "importPath": "./src/stories/utilities/display/ClocProgress.stories.tsx", "tags": ["dev", "test"]}, "utilities-display-progress--with-label": {"type": "story", "id": "utilities-display-progress--with-label", "name": "With Label", "title": "Utilities/Display/Progress", "importPath": "./src/stories/utilities/display/ClocProgress.stories.tsx", "tags": ["dev", "test"]}, "utilities-display-progress--progress-card": {"type": "story", "id": "utilities-display-progress--progress-card", "name": "Progress Card", "title": "Utilities/Display/Progress", "importPath": "./src/stories/utilities/display/ClocProgress.stories.tsx", "tags": ["dev", "test"]}, "utilities-display-progress--multiple-progress": {"type": "story", "id": "utilities-display-progress--multiple-progress", "name": "Multiple Progress", "title": "Utilities/Display/Progress", "importPath": "./src/stories/utilities/display/ClocProgress.stories.tsx", "tags": ["dev", "test"]}, "utilities-display-progress--different-sizes": {"type": "story", "id": "utilities-display-progress--different-sizes", "name": "Different Sizes", "title": "Utilities/Display/Progress", "importPath": "./src/stories/utilities/display/ClocProgress.stories.tsx", "tags": ["dev", "test"]}, "utilities-display-progress-circle--default": {"type": "story", "id": "utilities-display-progress-circle--default", "name": "<PERSON><PERSON><PERSON>", "title": "Utilities/Display/Progress Circle", "importPath": "./src/stories/utilities/display/ClocProgressCircle.stories.tsx", "tags": ["dev", "test"]}, "utilities-display-progress-circle--custom-percentage": {"type": "story", "id": "utilities-display-progress-circle--custom-percentage", "name": "Custom Percentage", "title": "Utilities/Display/Progress Circle", "importPath": "./src/stories/utilities/display/ClocProgressCircle.stories.tsx", "tags": ["dev", "test"]}, "utilities-display-progress-circle--small-size": {"type": "story", "id": "utilities-display-progress-circle--small-size", "name": "Small Size", "title": "Utilities/Display/Progress Circle", "importPath": "./src/stories/utilities/display/ClocProgressCircle.stories.tsx", "tags": ["dev", "test"]}, "utilities-display-progress-circle--large-size": {"type": "story", "id": "utilities-display-progress-circle--large-size", "name": "Large Size", "title": "Utilities/Display/Progress Circle", "importPath": "./src/stories/utilities/display/ClocProgressCircle.stories.tsx", "tags": ["dev", "test"]}, "utilities-display-progress-circle--thin-stroke": {"type": "story", "id": "utilities-display-progress-circle--thin-stroke", "name": "Thin Stroke", "title": "Utilities/Display/Progress Circle", "importPath": "./src/stories/utilities/display/ClocProgressCircle.stories.tsx", "tags": ["dev", "test"]}, "utilities-display-progress-circle--thick-stroke": {"type": "story", "id": "utilities-display-progress-circle--thick-stroke", "name": "Thick Stroke", "title": "Utilities/Display/Progress Circle", "importPath": "./src/stories/utilities/display/ClocProgressCircle.stories.tsx", "tags": ["dev", "test"]}, "utilities-display-progress-circle--fast-animation": {"type": "story", "id": "utilities-display-progress-circle--fast-animation", "name": "Fast Animation", "title": "Utilities/Display/Progress Circle", "importPath": "./src/stories/utilities/display/ClocProgressCircle.stories.tsx", "tags": ["dev", "test"]}, "utilities-display-progress-circle--slow-animation": {"type": "story", "id": "utilities-display-progress-circle--slow-animation", "name": "Slow Animation", "title": "Utilities/Display/Progress Circle", "importPath": "./src/stories/utilities/display/ClocProgressCircle.stories.tsx", "tags": ["dev", "test"]}, "utilities-display-progress-circle--custom-colors": {"type": "story", "id": "utilities-display-progress-circle--custom-colors", "name": "Custom Colors", "title": "Utilities/Display/Progress Circle", "importPath": "./src/stories/utilities/display/ClocProgressCircle.stories.tsx", "tags": ["dev", "test"]}, "utilities-display-progress-circle--red-theme": {"type": "story", "id": "utilities-display-progress-circle--red-theme", "name": "Red Theme", "title": "Utilities/Display/Progress Circle", "importPath": "./src/stories/utilities/display/ClocProgressCircle.stories.tsx", "tags": ["dev", "test"]}, "utilities-display-progress-circle--blue-theme": {"type": "story", "id": "utilities-display-progress-circle--blue-theme", "name": "Blue Theme", "title": "Utilities/Display/Progress Circle", "importPath": "./src/stories/utilities/display/ClocProgressCircle.stories.tsx", "tags": ["dev", "test"]}, "utilities-display-progress-circle--progress-states": {"type": "story", "id": "utilities-display-progress-circle--progress-states", "name": "Progress States", "title": "Utilities/Display/Progress Circle", "importPath": "./src/stories/utilities/display/ClocProgressCircle.stories.tsx", "tags": ["dev", "test"]}, "utilities-display-progress-circle--size-variations": {"type": "story", "id": "utilities-display-progress-circle--size-variations", "name": "Size Variations", "title": "Utilities/Display/Progress Circle", "importPath": "./src/stories/utilities/display/ClocProgressCircle.stories.tsx", "tags": ["dev", "test"]}, "utilities-display-table--default": {"type": "story", "id": "utilities-display-table--default", "name": "<PERSON><PERSON><PERSON>", "title": "Utilities/Display/Table", "importPath": "./src/stories/utilities/display/ClocTable.stories.tsx", "tags": ["dev", "test"]}, "utilities-display-table--with-caption": {"type": "story", "id": "utilities-display-table--with-caption", "name": "With Caption", "title": "Utilities/Display/Table", "importPath": "./src/stories/utilities/display/ClocTable.stories.tsx", "tags": ["dev", "test"]}, "utilities-display-table--with-footer": {"type": "story", "id": "utilities-display-table--with-footer", "name": "With <PERSON>er", "title": "Utilities/Display/Table", "importPath": "./src/stories/utilities/display/ClocTable.stories.tsx", "tags": ["dev", "test"]}, "utilities-display-table--custom-headers": {"type": "story", "id": "utilities-display-table--custom-headers", "name": "Custom Headers", "title": "Utilities/Display/Table", "importPath": "./src/stories/utilities/display/ClocTable.stories.tsx", "tags": ["dev", "test"]}, "utilities-display-table--custom-cells": {"type": "story", "id": "utilities-display-table--custom-cells", "name": "Custom Cells", "title": "Utilities/Display/Table", "importPath": "./src/stories/utilities/display/ClocTable.stories.tsx", "tags": ["dev", "test"]}, "utilities-display-table--time-tracking-data": {"type": "story", "id": "utilities-display-table--time-tracking-data", "name": "Time Tracking Data", "title": "Utilities/Display/Table", "importPath": "./src/stories/utilities/display/ClocTable.stories.tsx", "tags": ["dev", "test"]}, "utilities-display-table--empty-table": {"type": "story", "id": "utilities-display-table--empty-table", "name": "Empty Table", "title": "Utilities/Display/Table", "importPath": "./src/stories/utilities/display/ClocTable.stories.tsx", "tags": ["dev", "test"]}, "utilities-display-table--single-row": {"type": "story", "id": "utilities-display-table--single-row", "name": "Single Row", "title": "Utilities/Display/Table", "importPath": "./src/stories/utilities/display/ClocTable.stories.tsx", "tags": ["dev", "test"]}, "utilities-display-table--custom-styling": {"type": "story", "id": "utilities-display-table--custom-styling", "name": "Custom Styling", "title": "Utilities/Display/Table", "importPath": "./src/stories/utilities/display/ClocTable.stories.tsx", "tags": ["dev", "test"]}, "utilities-settings-font-toggle--default": {"type": "story", "id": "utilities-settings-font-toggle--default", "name": "<PERSON><PERSON><PERSON>", "title": "Utilities/Settings/Font Toggle", "importPath": "./src/stories/utilities/settings/ClocFontToggle.stories.tsx", "tags": ["dev", "test"]}, "utilities-settings-font-toggle--in-toolbar": {"type": "story", "id": "utilities-settings-font-toggle--in-toolbar", "name": "In Toolbar", "title": "Utilities/Settings/Font Toggle", "importPath": "./src/stories/utilities/settings/ClocFontToggle.stories.tsx", "tags": ["dev", "test"]}, "utilities-settings-font-toggle--in-sidebar": {"type": "story", "id": "utilities-settings-font-toggle--in-sidebar", "name": "In Sidebar", "title": "Utilities/Settings/Font Toggle", "importPath": "./src/stories/utilities/settings/ClocFontToggle.stories.tsx", "tags": ["dev", "test"]}, "utilities-settings-font-toggle--with-preview": {"type": "story", "id": "utilities-settings-font-toggle--with-preview", "name": "With Preview", "title": "Utilities/Settings/Font Toggle", "importPath": "./src/stories/utilities/settings/ClocFontToggle.stories.tsx", "tags": ["dev", "test"]}, "utilities-settings-font-toggle--settings-panel": {"type": "story", "id": "utilities-settings-font-toggle--settings-panel", "name": "Settings Panel", "title": "Utilities/Settings/Font Toggle", "importPath": "./src/stories/utilities/settings/ClocFontToggle.stories.tsx", "tags": ["dev", "test"]}, "utilities-settings-font-toggle--accessibility-example": {"type": "story", "id": "utilities-settings-font-toggle--accessibility-example", "name": "Accessibility Example", "title": "Utilities/Settings/Font Toggle", "importPath": "./src/stories/utilities/settings/ClocFontToggle.stories.tsx", "tags": ["dev", "test"]}, "utilities-settings-font-toggle--compact-layout": {"type": "story", "id": "utilities-settings-font-toggle--compact-layout", "name": "Compact Layout", "title": "Utilities/Settings/Font Toggle", "importPath": "./src/stories/utilities/settings/ClocFontToggle.stories.tsx", "tags": ["dev", "test"]}, "utilities-settings-language-switch--default": {"type": "story", "id": "utilities-settings-language-switch--default", "name": "<PERSON><PERSON><PERSON>", "title": "Utilities/Settings/Language Switch", "importPath": "./src/stories/utilities/settings/ClocLanguageSwitch.stories.tsx", "tags": ["dev", "test"]}, "utilities-settings-language-switch--with-label": {"type": "story", "id": "utilities-settings-language-switch--with-label", "name": "With Label", "title": "Utilities/Settings/Language Switch", "importPath": "./src/stories/utilities/settings/ClocLanguageSwitch.stories.tsx", "tags": ["dev", "test"]}, "utilities-settings-language-switch--small": {"type": "story", "id": "utilities-settings-language-switch--small", "name": "Small", "title": "Utilities/Settings/Language Switch", "importPath": "./src/stories/utilities/settings/ClocLanguageSwitch.stories.tsx", "tags": ["dev", "test"]}, "utilities-settings-language-switch--large": {"type": "story", "id": "utilities-settings-language-switch--large", "name": "Large", "title": "Utilities/Settings/Language Switch", "importPath": "./src/stories/utilities/settings/ClocLanguageSwitch.stories.tsx", "tags": ["dev", "test"]}, "utilities-settings-language-switch--small-with-label": {"type": "story", "id": "utilities-settings-language-switch--small-with-label", "name": "Small With Label", "title": "Utilities/Settings/Language Switch", "importPath": "./src/stories/utilities/settings/ClocLanguageSwitch.stories.tsx", "tags": ["dev", "test"]}, "utilities-settings-theme-toggle--default": {"type": "story", "id": "utilities-settings-theme-toggle--default", "name": "<PERSON><PERSON><PERSON>", "title": "Utilities/Settings/Theme Toggle", "importPath": "./src/stories/utilities/settings/ClocThemeToggle.stories.tsx", "tags": ["dev", "test"]}, "utilities-settings-theme-toggle--small": {"type": "story", "id": "utilities-settings-theme-toggle--small", "name": "Small", "title": "Utilities/Settings/Theme Toggle", "importPath": "./src/stories/utilities/settings/ClocThemeToggle.stories.tsx", "tags": ["dev", "test"]}, "utilities-settings-theme-toggle--large": {"type": "story", "id": "utilities-settings-theme-toggle--large", "name": "Large", "title": "Utilities/Settings/Theme Toggle", "importPath": "./src/stories/utilities/settings/ClocThemeToggle.stories.tsx", "tags": ["dev", "test"]}, "utilities-settings-theme-toggle--custom-styling": {"type": "story", "id": "utilities-settings-theme-toggle--custom-styling", "name": "Custom Styling", "title": "Utilities/Settings/Theme Toggle", "importPath": "./src/stories/utilities/settings/ClocThemeToggle.stories.tsx", "tags": ["dev", "test"]}, "utilities-settings-theme-toggle--in-toolbar": {"type": "story", "id": "utilities-settings-theme-toggle--in-toolbar", "name": "In Toolbar", "title": "Utilities/Settings/Theme Toggle", "importPath": "./src/stories/utilities/settings/ClocThemeToggle.stories.tsx", "tags": ["dev", "test"]}, "utilities-settings-theme-toggle--in-sidebar": {"type": "story", "id": "utilities-settings-theme-toggle--in-sidebar", "name": "In Sidebar", "title": "Utilities/Settings/Theme Toggle", "importPath": "./src/stories/utilities/settings/ClocThemeToggle.stories.tsx", "tags": ["dev", "test"]}, "utilities-settings-theme-toggle--with-preview": {"type": "story", "id": "utilities-settings-theme-toggle--with-preview", "name": "With Preview", "title": "Utilities/Settings/Theme Toggle", "importPath": "./src/stories/utilities/settings/ClocThemeToggle.stories.tsx", "tags": ["dev", "test"]}, "utilities-settings-theme-toggle--size-comparison": {"type": "story", "id": "utilities-settings-theme-toggle--size-comparison", "name": "<PERSON><PERSON> Comparison", "title": "Utilities/Settings/Theme Toggle", "importPath": "./src/stories/utilities/settings/ClocThemeToggle.stories.tsx", "tags": ["dev", "test"]}, "utilities-settings-theme-toggle--settings-panel": {"type": "story", "id": "utilities-settings-theme-toggle--settings-panel", "name": "Settings Panel", "title": "Utilities/Settings/Theme Toggle", "importPath": "./src/stories/utilities/settings/ClocThemeToggle.stories.tsx", "tags": ["dev", "test"]}}}