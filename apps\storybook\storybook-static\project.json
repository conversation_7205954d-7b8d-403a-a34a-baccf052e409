{"generatedAt": 1763451175600, "hasCustomBabel": false, "hasCustomWebpack": false, "hasStaticDirs": true, "hasStorybookEslint": false, "refCount": 0, "metaFramework": {"name": "Next", "packageName": "next", "version": "15.4.7"}, "testPackages": {}, "monorepo": "Turborepo", "packageManager": {"type": "yarn", "version": "1.22.22"}, "preview": {"usesGlobals": false}, "framework": {"name": "@storybook/nextjs", "options": {}}, "builder": "@storybook/builder-webpack5", "renderer": "@storybook/react", "storybookVersion": "8.1.10", "storybookVersionSpecifier": "^8.1.10", "language": "typescript", "storybookPackages": {"@storybook/blocks": {"version": "8.1.10"}, "@storybook/nextjs": {"version": "8.1.10"}, "@storybook/react": {"version": "8.1.10"}, "@storybook/test": {"version": "8.1.10"}, "storybook": {"version": "8.1.10"}}, "addons": {"@storybook/addon-onboarding": {"version": "8.1.10"}, "@storybook/addon-links": {"version": "8.1.10"}, "@storybook/addon-essentials": {"version": "8.1.10"}, "@chromatic-com/storybook": {"version": "1.9.0"}, "@storybook/addon-interactions": {"version": "8.1.10"}}}