(()=>{"use strict";var deferred,leafPrototypes,getProto,inProgress,__webpack_modules__={},__webpack_module_cache__={};function __webpack_require__(moduleId){var cachedModule=__webpack_module_cache__[moduleId];if(void 0!==cachedModule)return cachedModule.exports;var module=__webpack_module_cache__[moduleId]={id:moduleId,loaded:!1,exports:{}};return __webpack_modules__[moduleId].call(module.exports,module,module.exports,__webpack_require__),module.loaded=!0,module.exports}__webpack_require__.m=__webpack_modules__,__webpack_require__.amdO={},deferred=[],__webpack_require__.O=(result,chunkIds,fn,priority)=>{if(!chunkIds){var notFulfilled=1/0;for(i=0;i<deferred.length;i++){for(var[chunkIds,fn,priority]=deferred[i],fulfilled=!0,j=0;j<chunkIds.length;j++)(!1&priority||notFulfilled>=priority)&&Object.keys(__webpack_require__.O).every(key=>__webpack_require__.O[key](chunkIds[j]))?chunkIds.splice(j--,1):(fulfilled=!1,priority<notFulfilled&&(notFulfilled=priority));if(fulfilled){deferred.splice(i--,1);var r=fn();void 0!==r&&(result=r)}}return result}priority=priority||0;for(var i=deferred.length;i>0&&deferred[i-1][2]>priority;i--)deferred[i]=deferred[i-1];deferred[i]=[chunkIds,fn,priority]},__webpack_require__.n=module=>{var getter=module&&module.__esModule?()=>module.default:()=>module;return __webpack_require__.d(getter,{a:getter}),getter},getProto=Object.getPrototypeOf?obj=>Object.getPrototypeOf(obj):obj=>obj.__proto__,__webpack_require__.t=function(value,mode){if(1&mode&&(value=this(value)),8&mode)return value;if("object"==typeof value&&value){if(4&mode&&value.__esModule)return value;if(16&mode&&"function"==typeof value.then)return value}var ns=Object.create(null);__webpack_require__.r(ns);var def={};leafPrototypes=leafPrototypes||[null,getProto({}),getProto([]),getProto(getProto)];for(var current=2&mode&&value;"object"==typeof current&&!~leafPrototypes.indexOf(current);current=getProto(current))Object.getOwnPropertyNames(current).forEach(key=>def[key]=()=>value[key]);return def.default=()=>value,__webpack_require__.d(ns,def),ns},__webpack_require__.d=(exports,definition)=>{for(var key in definition)__webpack_require__.o(definition,key)&&!__webpack_require__.o(exports,key)&&Object.defineProperty(exports,key,{enumerable:!0,get:definition[key]})},__webpack_require__.f={},__webpack_require__.e=chunkId=>Promise.all(Object.keys(__webpack_require__.f).reduce((promises,key)=>(__webpack_require__.f[key](chunkId,promises),promises),[])),__webpack_require__.u=chunkId=>(({78:"stories-authentication-ClocLoginDialog-stories",336:"stories-inputs-timer-selects-ClocTimerProjectSelect-stories",362:"stories-foundation-DateRangePicker-stories",448:"stories-utilities-date-time-ClocDatePicker-stories",811:"stories-user-account-management-ClocAccountDeletionForm-stories",846:"stories-time-trackers-BasicTimer-stories",896:"stories-utilities-display-ClocCarousel-stories",1165:"stories-analytics-charts-reports-ClocProjectsList-stories",1288:"stories-authentication-ClocProfileForm-stories",1960:"stories-authentication-ClocRegistrationDialog-stories",2411:"stories-foundation-DatePicker-stories",2445:"stories-utilities-buttons-ClocButton-stories",2663:"stories-foundation-Calendar-stories",2819:"stories-utilities-settings-ClocLanguageSwitch-stories",3173:"stories-user-account-management-ClocProfilePhotoForm-stories",3337:"stories-time-trackers-ModernCloc-stories",3459:"stories-inputs-global-selectors-ClocReportDatesRangePicker-stories",3486:"stories-inputs-timer-selects-ClocTimerTaskSelect-stories",3540:"stories-team-management-ClocTeamCreationForm-stories",3827:"stories-utilities-date-time-ClocDateRangePicker-stories",3928:"stories-user-account-management-ClocPasswordUpdateForm-stories",3977:"stories-authentication-PasswordForm-stories",4137:"stories-time-trackers-ClocTimerForm-stories",4140:"stories-utilities-settings-ClocThemeToggle-stories",4154:"stories-report-displayers-time-displayers-ClocDailyWorkedTimeDisplayer-stories",4364:"stories-authentication-ClocLoginForm-stories",4710:"stories-report-displayers-time-displayers-ClocWeeklyWorkedTimeDisplayer-stories",4726:"stories-introduction-Introduction-mdx",4795:"stories-team-management-ClocMemberInvitationFormDialog-stories",4823:"stories-utilities-display-ClocProgress-stories",4998:"stories-inputs-timer-selects-ClocTimerTeamSelect-stories",5135:"stories-team-management-ClocMemberInvitationForm-stories",5525:"stories-report-displayers-project-displayers-ClocWorkedProjectDisplayer-stories",5565:"stories-inputs-global-selectors-ClocActiveOrganizationSelector-stories",5591:"stories-foundation-Input-stories",5794:"stories-analytics-charts-reports-ClocAppsUrlList-stories",5804:"stories-inputs-timer-selects-ClocTimerClientSelect-stories",6366:"stories-report-displayers-activity-displayers-ClocWeeklyActivityDisplayer-stories",6386:"stories-report-displayers-activity-displayers-ClocDailyActivityDisplayer-stories",6418:"stories-authentication-ClocRegistrationForm-stories",6702:"stories-team-management-ClocTeamsViewer-stories",6759:"stories-team-management-ClocTeamSetting-stories",6769:"stories-analytics-charts-reports-ClocReport-stories",6789:"stories-authentication-ClocUserAvatar-stories",6875:"stories-inputs-global-selectors-ClocActiveTeamSelector-stories",6975:"stories-tracking-insights-ClocTrackingSessionInsight-stories",6997:"stories-authentication-TokenForm-stories",7254:"stories-time-trackers-ClocBasic-stories",7268:"stories-team-management-ClocTeamCreationFormDialog-stories",7365:"stories-tracking-insights-ClocTrackingFilter-stories",7419:"stories-analytics-charts-reports-ClocTasksList-stories",8157:"stories-foundation-Button-stories",8291:"stories-utilities-display-ClocBadge-stories",8334:"stories-utilities-display-ClocTable-stories",8359:"stories-analytics-charts-charts-ClocChart-stories",8553:"stories-tracking-insights-ClocTrackingHeatmap-stories",8704:"stories-time-trackers-ClocPomodoroTimer-stories",8883:"stories-utilities-display-ClocProgressCircle-stories",9050:"stories-utilities-settings-ClocFontToggle-stories",9077:"stories-foundation-Select-stories",9332:"stories-inputs-global-selectors-ClocActiveEmployeeSelector-stories",9463:"stories-tracking-insights-ClocTrackingClickInsight-stories",9476:"stories-foundation-Progress-stories",9553:"stories-time-trackers-ClocCustom-stories",9604:"stories-tracking-insights-ClocTrackingSessionReplay-stories",9894:"stories-time-trackers-ClocExtra-stories",9986:"stories-team-management-ClocTeamMembers-stories"}[chunkId]||chunkId)+"."+{78:"090b9382",336:"4c63af57",362:"63419b73",448:"771f9584",811:"165155ac",814:"b90d05fd",846:"1646241a",896:"d92b5549",1165:"fc31b913",1288:"c1473b68",1466:"08369097",1960:"ee123951",2411:"7b7c006b",2445:"b65f8bd5",2663:"aa27c894",2819:"db6926ad",3173:"26736435",3337:"03890b77",3459:"96cb67b7",3486:"3d408283",3540:"984c328d",3827:"082e8c5d",3928:"0f3b1d5d",3977:"bebc585b",4137:"751207ec",4140:"bfad8e00",4154:"221f1f47",4364:"37d96c38",4710:"8e6f98c7",4726:"579b9ff8",4795:"a6bbcbe1",4823:"6f745d86",4998:"4c9ac8f3",5135:"0db6d574",5525:"0868e9c9",5565:"eae57937",5591:"63af612c",5622:"e1586699",5794:"4adb288c",5804:"8efdafaa",6366:"59fc5f1d",6386:"fd488080",6418:"71d5fd0f",6608:"7fad2482",6702:"4cac6ea9",6759:"28f830c1",6769:"10829f99",6789:"af3b41cd",6875:"f2d361f7",6975:"d6c96f06",6997:"b167b2c7",7254:"acd0afae",7268:"fb452cc6",7365:"69b466b9",7419:"f686d084",7496:"37217a39",8118:"efcf615c",8157:"a0971a29",8238:"4f8d633b",8291:"b536fff1",8334:"8b9be0fd",8359:"92c63a6d",8553:"6667d363",8704:"f50c6a9d",8883:"ba80637a",9050:"5346d1d1",9077:"06feb187",9332:"3f18283f",9463:"7308832a",9476:"61a6cca1",9553:"538f3882",9604:"54589d5d",9894:"a03662f1",9915:"39e8aed2",9986:"84ea96e6"}[chunkId]+".iframe.bundle.js"),__webpack_require__.g=function(){if("object"==typeof globalThis)return globalThis;try{return this||new Function("return this")()}catch(e){if("object"==typeof window)return window}}(),__webpack_require__.hmd=module=>((module=Object.create(module)).children||(module.children=[]),Object.defineProperty(module,"exports",{enumerable:!0,set:()=>{throw new Error("ES Modules may not assign module.exports or exports.*, Use ESM export syntax, instead: "+module.id)}}),module),__webpack_require__.o=(obj,prop)=>Object.prototype.hasOwnProperty.call(obj,prop),inProgress={},__webpack_require__.l=(url,done,key,chunkId)=>{if(inProgress[url])inProgress[url].push(done);else{var script,needAttach;if(void 0!==key)for(var scripts=document.getElementsByTagName("script"),i=0;i<scripts.length;i++){var s=scripts[i];if(s.getAttribute("src")==url||s.getAttribute("data-webpack")=="@cloc/storybook:"+key){script=s;break}}script||(needAttach=!0,(script=document.createElement("script")).charset="utf-8",script.timeout=120,__webpack_require__.nc&&script.setAttribute("nonce",__webpack_require__.nc),script.setAttribute("data-webpack","@cloc/storybook:"+key),script.src=url),inProgress[url]=[done];var onScriptComplete=(prev,event)=>{script.onerror=script.onload=null,clearTimeout(timeout);var doneFns=inProgress[url];if(delete inProgress[url],script.parentNode&&script.parentNode.removeChild(script),doneFns&&doneFns.forEach(fn=>fn(event)),prev)return prev(event)},timeout=setTimeout(onScriptComplete.bind(null,void 0,{type:"timeout",target:script}),12e4);script.onerror=onScriptComplete.bind(null,script.onerror),script.onload=onScriptComplete.bind(null,script.onload),needAttach&&document.head.appendChild(script)}},__webpack_require__.r=exports=>{"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(exports,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(exports,"__esModule",{value:!0})},__webpack_require__.nmd=module=>(module.paths=[],module.children||(module.children=[]),module),__webpack_require__.p="",(()=>{var installedChunks={5354:0};__webpack_require__.f.j=(chunkId,promises)=>{var installedChunkData=__webpack_require__.o(installedChunks,chunkId)?installedChunks[chunkId]:void 0;if(0!==installedChunkData)if(installedChunkData)promises.push(installedChunkData[2]);else if(5354!=chunkId){var promise=new Promise((resolve,reject)=>installedChunkData=installedChunks[chunkId]=[resolve,reject]);promises.push(installedChunkData[2]=promise);var url=__webpack_require__.p+__webpack_require__.u(chunkId),error=new Error;__webpack_require__.l(url,event=>{if(__webpack_require__.o(installedChunks,chunkId)&&(0!==(installedChunkData=installedChunks[chunkId])&&(installedChunks[chunkId]=void 0),installedChunkData)){var errorType=event&&("load"===event.type?"missing":event.type),realSrc=event&&event.target&&event.target.src;error.message="Loading chunk "+chunkId+" failed.\n("+errorType+": "+realSrc+")",error.name="ChunkLoadError",error.type=errorType,error.request=realSrc,installedChunkData[1](error)}},"chunk-"+chunkId,chunkId)}else installedChunks[chunkId]=0},__webpack_require__.O.j=chunkId=>0===installedChunks[chunkId];var webpackJsonpCallback=(parentChunkLoadingFunction,data)=>{var moduleId,chunkId,[chunkIds,moreModules,runtime]=data,i=0;if(chunkIds.some(id=>0!==installedChunks[id])){for(moduleId in moreModules)__webpack_require__.o(moreModules,moduleId)&&(__webpack_require__.m[moduleId]=moreModules[moduleId]);if(runtime)var result=runtime(__webpack_require__)}for(parentChunkLoadingFunction&&parentChunkLoadingFunction(data);i<chunkIds.length;i++)chunkId=chunkIds[i],__webpack_require__.o(installedChunks,chunkId)&&installedChunks[chunkId]&&installedChunks[chunkId][0](),installedChunks[chunkId]=0;return __webpack_require__.O(result)},chunkLoadingGlobal=self.webpackChunk_cloc_storybook=self.webpackChunk_cloc_storybook||[];chunkLoadingGlobal.forEach(webpackJsonpCallback.bind(null,0)),chunkLoadingGlobal.push=webpackJsonpCallback.bind(null,chunkLoadingGlobal.push.bind(chunkLoadingGlobal))})(),__webpack_require__.nc=void 0})();