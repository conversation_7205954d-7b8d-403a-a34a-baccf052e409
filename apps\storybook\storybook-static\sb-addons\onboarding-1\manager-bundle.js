try{
(()=>{var vc=Object.create;var hn=Object.defineProperty;var bc=Object.getOwnPropertyDescriptor;var xc=Object.getOwnPropertyNames;var wc=Object.getPrototypeOf,Tc=Object.prototype.hasOwnProperty;var ke=(e,t)=>()=>(e&&(t=e(e=0)),t);var fn=(e,t)=>()=>(t||e((t={exports:{}}).exports,t),t.exports),Ec=(e,t)=>{for(var r in t)hn(e,r,{get:t[r],enumerable:!0})},Sc=(e,t,r,n)=>{if(t&&typeof t=="object"||typeof t=="function")for(let o of xc(t))!Tc.call(e,o)&&o!==r&&hn(e,o,{get:()=>t[o],enumerable:!(n=bc(t,o))||n.enumerable});return e};var Yo=(e,t,r)=>(r=e!=null?vc(wc(e)):{},Sc(t||!e||!e.__esModule?hn(r,"default",{value:e,enumerable:!0}):r,e));var Z=ke(()=>{});var $=ke(()=>{});var ee=ke(()=>{});var ht,iv,Ht,av,sv,lv,cv,uv,pv,Go,dv,Xo,hv,Pr=ke(()=>{Z();$();ee();ht=__REACT_DOM__,{__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED:iv,createPortal:Ht,createRoot:av,findDOMNode:sv,flushSync:lv,hydrate:cv,hydrateRoot:uv,render:pv,unmountComponentAtNode:Go,unstable_batchedUpdates:dv,unstable_renderSubtreeIntoContainer:Xo,version:hv}=__REACT_DOM__});var m,yv,ft,mn,vv,bv,xv,Ko,wv,Tv,mt,X,Ev,Qo,Or,Jo,Zo,Sv,Pv,Ov,Ut,ve,Cv,Av,ue,$o,Rv,ei,gn,$e,Iv,Ot,xe,kv,Mv,Dv,Wt=ke(()=>{Z();$();ee();m=__REACT__,{Children:yv,Component:ft,Fragment:mn,Profiler:vv,PureComponent:bv,StrictMode:xv,Suspense:Ko,__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED:wv,cloneElement:Tv,createContext:mt,createElement:X,createFactory:Ev,createRef:Qo,forwardRef:Or,isValidElement:Jo,lazy:Zo,memo:Sv,startTransition:Pv,unstable_act:Ov,useCallback:Ut,useContext:ve,useDebugValue:Cv,useDeferredValue:Av,useEffect:ue,useId:$o,useImperativeHandle:Rv,useInsertionEffect:ei,useLayoutEffect:gn,useMemo:$e,useReducer:Iv,useRef:Ot,useState:xe,useSyncExternalStore:kv,useTransition:Mv,version:Dv}=__REACT__});var Vv,Fv,Bv,zv,_v,Hv,Cr,Uv,Wv,qv,Yv,Gv,Xv,Kv,Qv,Jv,Zv,$v,e0,t0,r0,n0,o0,i0,a0,s0,l0,c0,u0,p0,d0,h0,f0,yn=ke(()=>{Z();$();ee();Vv=__STORYBOOK_API__,{ActiveTabs:Fv,Consumer:Bv,ManagerContext:zv,Provider:_v,RequestResponseError:Hv,addons:Cr,combineParameters:Uv,controlOrMetaKey:Wv,controlOrMetaSymbol:qv,eventMatchesShortcut:Yv,eventToShortcut:Gv,experimental_requestResponse:Xv,isMacLike:Kv,isShortcutTaken:Qv,keyToSymbol:Jv,merge:Zv,mockChannel:$v,optionOrAltSymbol:e0,shortcutMatchesShortcut:t0,shortcutToHumanString:r0,types:n0,useAddonState:o0,useArgTypes:i0,useArgs:a0,useChannel:s0,useGlobalTypes:l0,useGlobals:c0,useParameter:u0,useSharedState:p0,useStoryPrepared:d0,useStorybookApi:h0,useStorybookState:f0}=__STORYBOOK_API__});var b0,x0,w0,T0,E0,S0,P0,O0,C0,A0,R0,I0,k0,M0,D0,L0,N0,j0,V0,F0,B0,z0,_0,H0,U0,W0,q0,Y0,G0,X0,K0,Q0,J0,Z0,$0,e1,t1,r1,n1,o1,i1,vn,a1,s1,ti,l1,ri,c1,u1,p1,d1,h1,f1,m1,ni,bn=ke(()=>{Z();$();ee();b0=__STORYBOOK_CORE_EVENTS__,{ARGTYPES_INFO_REQUEST:x0,ARGTYPES_INFO_RESPONSE:w0,CHANNEL_CREATED:T0,CHANNEL_WS_DISCONNECT:E0,CONFIG_ERROR:S0,CREATE_NEW_STORYFILE_REQUEST:P0,CREATE_NEW_STORYFILE_RESPONSE:O0,CURRENT_STORY_WAS_SET:C0,DOCS_PREPARED:A0,DOCS_RENDERED:R0,FILE_COMPONENT_SEARCH_REQUEST:I0,FILE_COMPONENT_SEARCH_RESPONSE:k0,FORCE_REMOUNT:M0,FORCE_RE_RENDER:D0,GLOBALS_UPDATED:L0,NAVIGATE_URL:N0,PLAY_FUNCTION_THREW_EXCEPTION:j0,PRELOAD_ENTRIES:V0,PREVIEW_BUILDER_PROGRESS:F0,PREVIEW_KEYDOWN:B0,REGISTER_SUBSCRIPTION:z0,REQUEST_WHATS_NEW_DATA:_0,RESET_STORY_ARGS:H0,RESULT_WHATS_NEW_DATA:U0,SAVE_STORY_REQUEST:W0,SAVE_STORY_RESPONSE:q0,SELECT_STORY:Y0,SET_CONFIG:G0,SET_CURRENT_STORY:X0,SET_GLOBALS:K0,SET_INDEX:Q0,SET_STORIES:J0,SET_WHATS_NEW_CACHE:Z0,SHARED_STATE_CHANGED:$0,SHARED_STATE_SET:e1,STORIES_COLLAPSE_ALL:t1,STORIES_EXPAND_ALL:r1,STORY_ARGS_UPDATED:n1,STORY_CHANGED:o1,STORY_ERRORED:i1,STORY_INDEX_INVALIDATED:vn,STORY_MISSING:a1,STORY_PREPARED:s1,STORY_RENDERED:ti,STORY_RENDER_PHASE_CHANGED:l1,STORY_SPECIFIED:ri,STORY_THREW_EXCEPTION:c1,STORY_UNCHANGED:u1,TELEMETRY_ERROR:p1,TOGGLE_WHATS_NEW_NOTIFICATIONS:d1,UNHANDLED_ERRORS_WHILE_PLAYING:h1,UPDATE_GLOBALS:f1,UPDATE_QUERY_PARAMS:m1,UPDATE_STORY_ARGS:ni}=__STORYBOOK_CORE_EVENTS__});var x1,w1,T1,E1,xn,S1,P1,oi,O1,C1,A1,R1,I1,k1,ii,M1,D1,L1,gt,N1,j,ai,j1,wn,V1,si=ke(()=>{Z();$();ee();x1=__STORYBOOK_THEMING__,{CacheProvider:w1,ClassNames:T1,Global:E1,ThemeProvider:xn,background:S1,color:P1,convert:oi,create:O1,createCache:C1,createGlobal:A1,createReset:R1,css:I1,darken:k1,ensure:ii,ignoreSsrWarning:M1,isPropValid:D1,jsx:L1,keyframes:gt,lighten:N1,styled:j,themes:ai,typography:j1,useTheme:wn,withTheme:V1}=__STORYBOOK_THEMING__});var ci=fn(Ar=>{"use strict";Z();$();ee();var Cc=Symbol.for("react.transitional.element"),Ac=Symbol.for("react.fragment");function li(e,t,r){var n=null;if(r!==void 0&&(n=""+r),t.key!==void 0&&(n=""+t.key),"key"in t){r={};for(var o in t)o!=="key"&&(r[o]=t[o])}else r=t;return t=r.ref,{$$typeof:Cc,type:e,key:n,ref:t!==void 0?t:null,props:r}}Ar.Fragment=Ac;Ar.jsx=li;Ar.jsxs=li});var pi=fn((Y1,ui)=>{"use strict";Z();$();ee();ui.exports=ci()});var hi=fn((Q1,di)=>{"use strict";Z();$();ee();var Rr={linear:function(e,t,r,n){var o=r-t;return o*e/n+t},easeInQuad:function(e,t,r,n){var o=r-t;return o*(e/=n)*e+t},easeOutQuad:function(e,t,r,n){var o=r-t;return-o*(e/=n)*(e-2)+t},easeInOutQuad:function(e,t,r,n){var o=r-t;return(e/=n/2)<1?o/2*e*e+t:-o/2*(--e*(e-2)-1)+t},easeInCubic:function(e,t,r,n){var o=r-t;return o*(e/=n)*e*e+t},easeOutCubic:function(e,t,r,n){var o=r-t;return o*((e=e/n-1)*e*e+1)+t},easeInOutCubic:function(e,t,r,n){var o=r-t;return(e/=n/2)<1?o/2*e*e*e+t:o/2*((e-=2)*e*e+2)+t},easeInQuart:function(e,t,r,n){var o=r-t;return o*(e/=n)*e*e*e+t},easeOutQuart:function(e,t,r,n){var o=r-t;return-o*((e=e/n-1)*e*e*e-1)+t},easeInOutQuart:function(e,t,r,n){var o=r-t;return(e/=n/2)<1?o/2*e*e*e*e+t:-o/2*((e-=2)*e*e*e-2)+t},easeInQuint:function(e,t,r,n){var o=r-t;return o*(e/=n)*e*e*e*e+t},easeOutQuint:function(e,t,r,n){var o=r-t;return o*((e=e/n-1)*e*e*e*e+1)+t},easeInOutQuint:function(e,t,r,n){var o=r-t;return(e/=n/2)<1?o/2*e*e*e*e*e+t:o/2*((e-=2)*e*e*e*e+2)+t},easeInSine:function(e,t,r,n){var o=r-t;return-o*Math.cos(e/n*(Math.PI/2))+o+t},easeOutSine:function(e,t,r,n){var o=r-t;return o*Math.sin(e/n*(Math.PI/2))+t},easeInOutSine:function(e,t,r,n){var o=r-t;return-o/2*(Math.cos(Math.PI*e/n)-1)+t},easeInExpo:function(e,t,r,n){var o=r-t;return e==0?t:o*Math.pow(2,10*(e/n-1))+t},easeOutExpo:function(e,t,r,n){var o=r-t;return e==n?t+o:o*(-Math.pow(2,-10*e/n)+1)+t},easeInOutExpo:function(e,t,r,n){var o=r-t;return e===0?t:e===n?t+o:(e/=n/2)<1?o/2*Math.pow(2,10*(e-1))+t:o/2*(-Math.pow(2,-10*--e)+2)+t},easeInCirc:function(e,t,r,n){var o=r-t;return-o*(Math.sqrt(1-(e/=n)*e)-1)+t},easeOutCirc:function(e,t,r,n){var o=r-t;return o*Math.sqrt(1-(e=e/n-1)*e)+t},easeInOutCirc:function(e,t,r,n){var o=r-t;return(e/=n/2)<1?-o/2*(Math.sqrt(1-e*e)-1)+t:o/2*(Math.sqrt(1-(e-=2)*e)+1)+t},easeInElastic:function(e,t,r,n){var o=r-t,i,a,s;return s=1.70158,a=0,i=o,e===0?t:(e/=n)===1?t+o:(a||(a=n*.3),i<Math.abs(o)?(i=o,s=a/4):s=a/(2*Math.PI)*Math.asin(o/i),-(i*Math.pow(2,10*(e-=1))*Math.sin((e*n-s)*(2*Math.PI)/a))+t)},easeOutElastic:function(e,t,r,n){var o=r-t,i,a,s;return s=1.70158,a=0,i=o,e===0?t:(e/=n)===1?t+o:(a||(a=n*.3),i<Math.abs(o)?(i=o,s=a/4):s=a/(2*Math.PI)*Math.asin(o/i),i*Math.pow(2,-10*e)*Math.sin((e*n-s)*(2*Math.PI)/a)+o+t)},easeInOutElastic:function(e,t,r,n){var o=r-t,i,a,s;return s=1.70158,a=0,i=o,e===0?t:(e/=n/2)===2?t+o:(a||(a=n*.44999999999999996),i<Math.abs(o)?(i=o,s=a/4):s=a/(2*Math.PI)*Math.asin(o/i),e<1?-.5*(i*Math.pow(2,10*(e-=1))*Math.sin((e*n-s)*(2*Math.PI)/a))+t:i*Math.pow(2,-10*(e-=1))*Math.sin((e*n-s)*(2*Math.PI)/a)*.5+o+t)},easeInBack:function(e,t,r,n,o){var i=r-t;return o===void 0&&(o=1.70158),i*(e/=n)*e*((o+1)*e-o)+t},easeOutBack:function(e,t,r,n,o){var i=r-t;return o===void 0&&(o=1.70158),i*((e=e/n-1)*e*((o+1)*e+o)+1)+t},easeInOutBack:function(e,t,r,n,o){var i=r-t;return o===void 0&&(o=1.70158),(e/=n/2)<1?i/2*(e*e*(((o*=1.525)+1)*e-o))+t:i/2*((e-=2)*e*(((o*=1.525)+1)*e+o)+2)+t},easeInBounce:function(e,t,r,n){var o=r-t,i;return i=Rr.easeOutBounce(n-e,0,o,n),o-i+t},easeOutBounce:function(e,t,r,n){var o=r-t;return(e/=n)<.36363636363636365?o*(7.5625*e*e)+t:e<.7272727272727273?o*(7.5625*(e-=.5454545454545454)*e+.75)+t:e<.9090909090909091?o*(7.5625*(e-=.8181818181818182)*e+.9375)+t:o*(7.5625*(e-=.9545454545454546)*e+.984375)+t},easeInOutBounce:function(e,t,r,n){var o=r-t,i;return e<n/2?(i=Rr.easeInBounce(e*2,0,o,n),i*.5+t):(i=Rr.easeOutBounce(e*2-n,0,o,n),i*.5+o*.5+t)}};di.exports=Rr});function Rc(e){return e*Math.PI/180}function Ce(e,t){return e+Math.random()*(t-e)}function Ic(e,t){return Math.floor(e+Math.random()*(t-e+1))}function Tn(e){let t={},r={},n={},o=[...Object.keys(Cn),"confettiSource","drawShape","onConfettiComplete","frameRate"],i=["canvasRef"];for(let a in e){let s=e[a];o.includes(a)?t[a]=s:i.includes(a)?i[a]=s:n[a]=s}return[t,n,r]}var On,fi,qt,et,kc,En,Sn,Cn,Pn,Mc,Yt,mi,gi=ke(()=>{Z();$();ee();On=Yo(pi(),1);Wt();fi=Yo(hi(),1);(function(e){e[e.Circle=0]="Circle",e[e.Square=1]="Square",e[e.Strip=2]="Strip"})(qt||(qt={}));(function(e){e[e.Positive=1]="Positive",e[e.Negative=-1]="Negative"})(et||(et={}));kc=1e3/60,En=class{constructor(t,r,n,o){this.getOptions=r;let{colors:i,initialVelocityX:a,initialVelocityY:s}=this.getOptions();this.context=t,this.x=n,this.y=o,this.w=Ce(5,20),this.h=Ce(5,20),this.radius=Ce(5,10),this.vx=typeof a=="number"?Ce(-a,a):Ce(a.min,a.max),this.vy=typeof s=="number"?Ce(-s,0):Ce(s.min,s.max),this.shape=Ic(0,2),this.angle=Rc(Ce(0,360)),this.angularSpin=Ce(-.2,.2),this.color=i[Math.floor(Math.random()*i.length)],this.rotateY=Ce(0,1),this.rotationDirection=Ce(0,1)?et.Positive:et.Negative}update(t){let{gravity:r,wind:n,friction:o,opacity:i,drawShape:a}=this.getOptions(),s=t/kc;this.x+=this.vx*s,this.y+=this.vy*s,this.vy+=r*s,this.vx+=n*s,this.vx*=o**s,this.vy*=o**s,this.rotateY>=1&&this.rotationDirection===et.Positive?this.rotationDirection=et.Negative:this.rotateY<=-1&&this.rotationDirection===et.Negative&&(this.rotationDirection=et.Positive);let c=.1*this.rotationDirection*s;if(this.rotateY+=c,this.angle+=this.angularSpin,this.context.save(),this.context.translate(this.x,this.y),this.context.rotate(this.angle),this.context.scale(1,this.rotateY),this.context.rotate(this.angle),this.context.beginPath(),this.context.fillStyle=this.color,this.context.strokeStyle=this.color,this.context.globalAlpha=i,this.context.lineCap="round",this.context.lineWidth=2,a&&typeof a=="function")a.call(this,this.context);else switch(this.shape){case qt.Circle:{this.context.beginPath(),this.context.arc(0,0,this.radius,0,2*Math.PI),this.context.fill();break}case qt.Square:{this.context.fillRect(-this.w/2,-this.h/2,this.w,this.h);break}case qt.Strip:{this.context.fillRect(-this.w/6,-this.h/2,this.w/3,this.h);break}}this.context.closePath(),this.context.restore()}},Sn=class{constructor(t,r){this.x=0,this.y=0,this.w=0,this.h=0,this.lastNumberOfPieces=0,this.tweenProgress=0,this.tweenFrom=0,this.particles=[],this.particlesGenerated=0,this.removeParticleAt=o=>{this.particles.splice(o,1)},this.getParticle=()=>{let o=Ce(this.x,this.w+this.x),i=Ce(this.y,this.h+this.y);return new En(this.context,this.getOptions,o,i)},this.animate=o=>{let{canvas:i,context:a,particlesGenerated:s,lastNumberOfPieces:c}=this,{run:l,recycle:u,numberOfPieces:p,debug:d,tweenFunction:h,tweenDuration:f}=this.getOptions();if(!l)return!1;let y=this.particles.length,x=u?y:s;if(x<p){c!==p&&(this.tweenProgress=0,this.tweenFrom=x,this.lastNumberOfPieces=p),this.tweenProgress=Math.min(f,Math.max(0,this.tweenProgress+o));let b=h(this.tweenProgress,this.tweenFrom,p,f),g=Math.round(b-x);for(let v=0;v<g;v++)this.particles.push(this.getParticle());this.particlesGenerated+=g}d&&(a.font="12px sans-serif",a.fillStyle="#333",a.textAlign="right",a.fillText(`Particles: ${y}`,i.width-10,i.height-20));for(let b=this.particles.length-1;b>=0;b--){let g=this.particles[b];g.update(o),(g.y>i.height||g.y<-100||g.x>i.width+100||g.x<-100)&&(u&&x<=p?this.particles[b]=this.getParticle():this.removeParticleAt(b))}return y>0||x<p},this.canvas=t;let n=this.canvas.getContext("2d");if(!n)throw new Error("Could not get canvas context");this.context=n,this.getOptions=r}},Cn={width:typeof window<"u"?window.innerWidth:300,height:typeof window<"u"?window.innerHeight:200,numberOfPieces:200,friction:.99,wind:0,gravity:.1,initialVelocityX:4,initialVelocityY:10,colors:["#f44336","#e91e63","#9c27b0","#673ab7","#3f51b5","#2196f3","#03a9f4","#00bcd4","#009688","#4CAF50","#8BC34A","#CDDC39","#FFEB3B","#FFC107","#FF9800","#FF5722","#795548"],opacity:1,debug:!1,tweenFunction:fi.easeInOutQuad,tweenDuration:5e3,recycle:!0,run:!0},Pn=class{constructor(t,r){this.lastFrameTime=0,this.setOptionsWithDefaults=o=>{let i={confettiSource:{x:0,y:0,w:this.canvas.width,h:0}};this._options={...i,...Cn,...o},Object.assign(this,o.confettiSource)},this.update=(o=0)=>{let{options:{run:i,onConfettiComplete:a,frameRate:s},canvas:c,context:l}=this,u=Math.min(o-this.lastFrameTime,50);if(s&&u<1e3/s){this.rafId=requestAnimationFrame(this.update);return}this.lastFrameTime=o-(s?u%s:0),i&&(l.fillStyle="white",l.clearRect(0,0,c.width,c.height)),this.generator.animate(u)?this.rafId=requestAnimationFrame(this.update):(a&&typeof a=="function"&&this.generator.particlesGenerated>0&&a.call(this,this),this._options.run=!1)},this.reset=()=>{this.generator&&this.generator.particlesGenerated>0&&(this.generator.particlesGenerated=0,this.generator.particles=[],this.generator.lastNumberOfPieces=0)},this.stop=()=>{this.options={run:!1},this.rafId&&(cancelAnimationFrame(this.rafId),this.rafId=void 0)},this.canvas=t;let n=this.canvas.getContext("2d");if(!n)throw new Error("Could not get canvas context");this.context=n,this.generator=new Sn(this.canvas,()=>this.options),this.options=r,this.update()}get options(){return this._options}set options(t){let r=this._options?.run,n=this._options?.recycle;this.setOptionsWithDefaults(t),this.generator&&(Object.assign(this.generator,this.options.confettiSource),typeof t.recycle=="boolean"&&t.recycle&&n===!1&&(this.generator.lastNumberOfPieces=this.generator.particles.length)),typeof t.run=="boolean"&&t.run&&r===!1&&this.update()}},Mc=m.createRef(),Yt=class extends m.Component{constructor(t){super(t),this.canvas=m.createRef(),this.canvas=t.canvasRef||Mc}componentDidMount(){if(this.canvas.current){let t=Tn(this.props)[0];this.confetti=new Pn(this.canvas.current,t)}}componentDidUpdate(){let t=Tn(this.props)[0];this.confetti&&(this.confetti.options=t)}componentWillUnmount(){this.confetti&&this.confetti.stop(),this.confetti=void 0}render(){let[t,r]=Tn(this.props),n={zIndex:2,position:"absolute",pointerEvents:"none",top:0,left:0,bottom:0,right:0,...r.style};return(0,On.jsx)("canvas",{width:t.width,height:t.height,ref:this.canvas,...r,style:n})}};Yt.defaultProps={...Cn};Yt.displayName="ReactConfetti";mi=m.forwardRef((e,t)=>(0,On.jsx)(Yt,{canvasRef:t,...e}))});var ib,ab,sb,lb,cb,ub,pb,db,hb,fb,mb,gb,yb,vb,bb,xb,wb,Tb,Eb,Sb,Pb,Ob,Cb,Ab,Rb,Ib,kb,Mb,Db,Lb,Nb,jb,Ct,Vb,Fb,Bb,zb,_b,Hb,Ub,Wb,qb,Yb,Gb,Xb,Gt,Kb,Qb,Jb,Zb,$b,ex,tx,rx,nx,ox,ix,ax,sx,lx,cx,ux,px,dx,hx,fx,mx,gx,yx,yi=ke(()=>{Z();$();ee();ib=__STORYBOOK_COMPONENTS__,{A:ab,ActionBar:sb,AddonPanel:lb,Badge:cb,Bar:ub,Blockquote:pb,Button:db,ClipboardCode:hb,Code:fb,DL:mb,Div:gb,DocumentWrapper:yb,EmptyTabContent:vb,ErrorFormatter:bb,FlexBar:xb,Form:wb,H1:Tb,H2:Eb,H3:Sb,H4:Pb,H5:Ob,H6:Cb,HR:Ab,IconButton:Rb,IconButtonSkeleton:Ib,Icons:kb,Img:Mb,LI:Db,Link:Lb,ListItem:Nb,Loader:jb,Modal:Ct,OL:Vb,P:Fb,Placeholder:Bb,Pre:zb,ResetWrapper:_b,ScrollArea:Hb,Separator:Ub,Spaced:Wb,Span:qb,StorybookIcon:Yb,StorybookLogo:Gb,Symbols:Xb,SyntaxHighlighter:Gt,TT:Kb,TabBar:Qb,TabButton:Jb,TabWrapper:Zb,Table:$b,Tabs:ex,TabsState:tx,TooltipLinkList:rx,TooltipMessage:nx,TooltipNote:ox,UL:ix,WithTooltip:ax,WithTooltipPure:sx,Zoom:lx,codeCommon:cx,components:ux,createCopyToClipboardFunction:px,getStoryHref:dx,icons:hx,interleaveSeparators:fx,nameSpaceClassNames:mx,resetComponents:gx,withReset:yx}=__STORYBOOK_COMPONENTS__});var Tx,Ex,Sx,Px,Ox,Cx,Ax,Rx,Ix,kx,Mx,Dx,vi,Lx,Nx,jx,Vx,Fx,Bx,zx,_x,Hx,Ux,Wx,qx,Yx,Gx,Xx,bi,Kx,Qx,Jx,Zx,$x,ew,tw,rw,nw,ow,iw,aw,sw,lw,xi,cw,uw,pw,dw,hw,fw,mw,gw,yw,vw,bw,xw,ww,Tw,Ew,Sw,Pw,Ow,Cw,Aw,Rw,Iw,kw,Mw,Dw,Lw,Nw,jw,Vw,Fw,Bw,wi,zw,_w,Hw,Uw,Ww,qw,Yw,Gw,Xw,Kw,Qw,Jw,Zw,$w,eT,tT,rT,nT,oT,iT,aT,sT,lT,cT,uT,pT,dT,hT,fT,mT,gT,yT,vT,bT,xT,wT,TT,ET,ST,PT,OT,CT,AT,RT,IT,kT,MT,DT,LT,NT,jT,VT,FT,BT,zT,_T,HT,UT,WT,qT,YT,GT,XT,KT,QT,JT,ZT,$T,eE,tE,rE,nE,oE,iE,aE,sE,lE,cE,uE,pE,dE,hE,fE,mE,gE,yE,vE,bE,xE,wE,TE,EE,SE,PE,OE,CE,AE,RE,IE,kE,ME,DE,LE,NE,jE,VE,FE,BE,zE,_E,HE,UE,WE,qE,YE,GE,XE,KE,QE,JE,ZE,$E,eS,tS,rS,nS,oS,iS,aS,sS,lS,cS,uS,pS,dS,hS,fS,mS,gS,yS,vS,bS,xS,wS,TS,ES,SS,PS,OS,CS,AS,RS,IS,Ti=ke(()=>{Z();$();ee();Tx=__STORYBOOK_ICONS__,{AccessibilityAltIcon:Ex,AccessibilityIcon:Sx,AddIcon:Px,AdminIcon:Ox,AlertAltIcon:Cx,AlertIcon:Ax,AlignLeftIcon:Rx,AlignRightIcon:Ix,AppleIcon:kx,ArrowDownIcon:Mx,ArrowLeftIcon:Dx,ArrowRightIcon:vi,ArrowSolidDownIcon:Lx,ArrowSolidLeftIcon:Nx,ArrowSolidRightIcon:jx,ArrowSolidUpIcon:Vx,ArrowUpIcon:Fx,AzureDevOpsIcon:Bx,BackIcon:zx,BasketIcon:_x,BatchAcceptIcon:Hx,BatchDenyIcon:Ux,BeakerIcon:Wx,BellIcon:qx,BitbucketIcon:Yx,BoldIcon:Gx,BookIcon:Xx,BookmarkHollowIcon:bi,BookmarkIcon:Kx,BottomBarIcon:Qx,BottomBarToggleIcon:Jx,BoxIcon:Zx,BranchIcon:$x,BrowserIcon:ew,ButtonIcon:tw,CPUIcon:rw,CalendarIcon:nw,CameraIcon:ow,CategoryIcon:iw,CertificateIcon:aw,ChangedIcon:sw,ChatIcon:lw,CheckIcon:xi,ChevronDownIcon:cw,ChevronLeftIcon:uw,ChevronRightIcon:pw,ChevronSmallDownIcon:dw,ChevronSmallLeftIcon:hw,ChevronSmallRightIcon:fw,ChevronSmallUpIcon:mw,ChevronUpIcon:gw,ChromaticIcon:yw,ChromeIcon:vw,CircleHollowIcon:bw,CircleIcon:xw,ClearIcon:ww,CloseAltIcon:Tw,CloseIcon:Ew,CloudHollowIcon:Sw,CloudIcon:Pw,CogIcon:Ow,CollapseIcon:Cw,CommandIcon:Aw,CommentAddIcon:Rw,CommentIcon:Iw,CommentsIcon:kw,CommitIcon:Mw,CompassIcon:Dw,ComponentDrivenIcon:Lw,ComponentIcon:Nw,ContrastIcon:jw,ControlsIcon:Vw,CopyIcon:Fw,CreditIcon:Bw,CrossIcon:wi,DashboardIcon:zw,DatabaseIcon:_w,DeleteIcon:Hw,DiamondIcon:Uw,DirectionIcon:Ww,DiscordIcon:qw,DocChartIcon:Yw,DocListIcon:Gw,DocumentIcon:Xw,DownloadIcon:Kw,DragIcon:Qw,EditIcon:Jw,EllipsisIcon:Zw,EmailIcon:$w,ExpandAltIcon:eT,ExpandIcon:tT,EyeCloseIcon:rT,EyeIcon:nT,FaceHappyIcon:oT,FaceNeutralIcon:iT,FaceSadIcon:aT,FacebookIcon:sT,FailedIcon:lT,FastForwardIcon:cT,FigmaIcon:uT,FilterIcon:pT,FlagIcon:dT,FolderIcon:hT,FormIcon:fT,GDriveIcon:mT,GithubIcon:gT,GitlabIcon:yT,GlobeIcon:vT,GoogleIcon:bT,GraphBarIcon:xT,GraphLineIcon:wT,GraphqlIcon:TT,GridAltIcon:ET,GridIcon:ST,GrowIcon:PT,HeartHollowIcon:OT,HeartIcon:CT,HomeIcon:AT,HourglassIcon:RT,InfoIcon:IT,ItalicIcon:kT,JumpToIcon:MT,KeyIcon:DT,LightningIcon:LT,LightningOffIcon:NT,LinkBrokenIcon:jT,LinkIcon:VT,LinkedinIcon:FT,LinuxIcon:BT,ListOrderedIcon:zT,ListUnorderedIcon:_T,LocationIcon:HT,LockIcon:UT,MarkdownIcon:WT,MarkupIcon:qT,MediumIcon:YT,MemoryIcon:GT,MenuIcon:XT,MergeIcon:KT,MirrorIcon:QT,MobileIcon:JT,MoonIcon:ZT,NutIcon:$T,OutboxIcon:eE,OutlineIcon:tE,PaintBrushIcon:rE,PaperClipIcon:nE,ParagraphIcon:oE,PassedIcon:iE,PhoneIcon:aE,PhotoDragIcon:sE,PhotoIcon:lE,PinAltIcon:cE,PinIcon:uE,PlayBackIcon:pE,PlayIcon:dE,PlayNextIcon:hE,PlusIcon:fE,PointerDefaultIcon:mE,PointerHandIcon:gE,PowerIcon:yE,PrintIcon:vE,ProceedIcon:bE,ProfileIcon:xE,PullRequestIcon:wE,QuestionIcon:TE,RSSIcon:EE,RedirectIcon:SE,ReduxIcon:PE,RefreshIcon:OE,ReplyIcon:CE,RepoIcon:AE,RequestChangeIcon:RE,RewindIcon:IE,RulerIcon:kE,SearchIcon:ME,ShareAltIcon:DE,ShareIcon:LE,ShieldIcon:NE,SideBySideIcon:jE,SidebarAltIcon:VE,SidebarAltToggleIcon:FE,SidebarIcon:BE,SidebarToggleIcon:zE,SpeakerIcon:_E,StackedIcon:HE,StarHollowIcon:UE,StarIcon:WE,StickerIcon:qE,StopAltIcon:YE,StopIcon:GE,StorybookIcon:XE,StructureIcon:KE,SubtractIcon:QE,SunIcon:JE,SupportIcon:ZE,SwitchAltIcon:$E,SyncIcon:eS,TabletIcon:tS,ThumbsUpIcon:rS,TimeIcon:nS,TimerIcon:oS,TransferIcon:iS,TrashIcon:aS,TwitterIcon:sS,TypeIcon:lS,UbuntuIcon:cS,UndoIcon:uS,UnfoldIcon:pS,UnlockIcon:dS,UnpinIcon:hS,UploadIcon:fS,UserAddIcon:mS,UserAltIcon:gS,UserIcon:yS,UsersIcon:vS,VSCodeIcon:bS,VerifiedIcon:xS,VideoIcon:wS,WandIcon:TS,WatchIcon:ES,WindowsIcon:SS,WrenchIcon:PS,YoutubeIcon:OS,ZoomIcon:CS,ZoomOutIcon:AS,ZoomResetIcon:RS,iconList:IS}=__STORYBOOK_ICONS__});var gc={};Ec(gc,{default:()=>Qy});function Xc(e){var t={};return function(r){return t[r]===void 0&&(t[r]=e(r)),t[r]}}function gs(e){return t=>typeof t===e}function eu(e,t){let{length:r}=e;if(r!==t.length)return!1;for(let n=r;n--!==0;)if(!be(e[n],t[n]))return!1;return!0}function tu(e,t){if(e.byteLength!==t.byteLength)return!1;let r=new DataView(e.buffer),n=new DataView(t.buffer),o=e.byteLength;for(;o--;)if(r.getUint8(o)!==n.getUint8(o))return!1;return!0}function ru(e,t){if(e.size!==t.size)return!1;for(let r of e.entries())if(!t.has(r[0]))return!1;for(let r of e.entries())if(!be(r[1],t.get(r[0])))return!1;return!0}function nu(e,t){if(e.size!==t.size)return!1;for(let r of e.entries())if(!t.has(r[0]))return!1;return!0}function be(e,t){if(e===t)return!0;if(e&&Oi(e)&&t&&Oi(t)){if(e.constructor!==t.constructor)return!1;if(Array.isArray(e)&&Array.isArray(t))return eu(e,t);if(e instanceof Map&&t instanceof Map)return ru(e,t);if(e instanceof Set&&t instanceof Set)return nu(e,t);if(ArrayBuffer.isView(e)&&ArrayBuffer.isView(t))return tu(e,t);if(Pi(e)&&Pi(t))return e.source===t.source&&e.flags===t.flags;if(e.valueOf!==Object.prototype.valueOf)return e.valueOf()===t.valueOf();if(e.toString!==Object.prototype.toString)return e.toString()===t.toString();let r=Object.keys(e),n=Object.keys(t);if(r.length!==n.length)return!1;for(let o=r.length;o--!==0;)if(!Object.prototype.hasOwnProperty.call(t,r[o]))return!1;for(let o=r.length;o--!==0;){let i=r[o];if(!(i==="_owner"&&e.$$typeof)&&!be(e[i],t[i]))return!1}return!0}return Number.isNaN(e)&&Number.isNaN(t)?!0:e===t}function rn(e){let t=Object.prototype.toString.call(e).slice(8,-1);if(/HTML\w+Element/.test(t))return"HTMLElement";if(au(t))return t}function Be(e){return t=>rn(t)===e}function au(e){return ou.includes(e)}function Vt(e){return t=>typeof t===e}function su(e){return iu.includes(e)}function O(e){if(e===null)return"null";switch(typeof e){case"bigint":return"bigint";case"boolean":return"boolean";case"number":return"number";case"string":return"string";case"symbol":return"symbol";case"undefined":return"undefined"}return O.array(e)?"Array":O.plainFunction(e)?"Function":rn(e)||"Object"}function cu(...e){return e.every(t=>A.string(t)||A.array(t)||A.plainObject(t))}function uu(e,t,r){return ys(e,t)?[e,t].every(A.array)?!e.some(ki(r))&&t.some(ki(r)):[e,t].every(A.plainObject)?!Object.entries(e).some(Ii(r))&&Object.entries(t).some(Ii(r)):t===r:!1}function Ci(e,t,r){let{actual:n,key:o,previous:i,type:a}=r,s=Ue(e,o),c=Ue(t,o),l=[s,c].every(A.number)&&(a==="increased"?s<c:s>c);return A.undefined(n)||(l=l&&c===n),A.undefined(i)||(l=l&&s===i),l}function Ai(e,t,r){let{key:n,type:o,value:i}=r,a=Ue(e,n),s=Ue(t,n),c=o==="added"?a:s,l=o==="added"?s:a;if(!A.nullOrUndefined(i)){if(A.defined(c)){if(A.array(c)||A.plainObject(c))return uu(c,l,i)}else return be(l,i);return!1}return[a,s].every(A.array)?!l.every(ho(c)):[a,s].every(A.plainObject)?pu(Object.keys(c),Object.keys(l)):![a,s].every(u=>A.primitive(u)&&A.defined(u))&&(o==="added"?!A.defined(a)&&A.defined(s):A.defined(a)&&!A.defined(s))}function Ri(e,t,{key:r}={}){let n=Ue(e,r),o=Ue(t,r);if(!ys(n,o))throw new TypeError("Inputs have different types");if(!cu(n,o))throw new TypeError("Inputs don't have length");return[n,o].every(A.plainObject)&&(n=Object.keys(n),o=Object.keys(o)),[n,o]}function Ii(e){return([t,r])=>A.array(e)?be(e,r)||e.some(n=>be(n,r)||A.array(r)&&ho(r)(n)):A.plainObject(e)&&e[t]?!!e[t]&&be(e[t],r):be(e,r)}function pu(e,t){return t.some(r=>!e.includes(r))}function ki(e){return t=>A.array(e)?e.some(r=>be(r,t)||A.array(t)&&ho(t)(r)):be(e,t)}function Xt(e,t){return A.array(e)?e.some(r=>be(r,t)):be(e,t)}function ho(e){return t=>e.some(r=>be(r,t))}function ys(...e){return e.every(A.array)||e.every(A.number)||e.every(A.plainObject)||e.every(A.string)}function Ue(e,t){return A.plainObject(e)||A.array(e)?A.string(t)?t.split(".").reduce((r,n)=>r&&r[n],e):A.number(t)?e[t]:e:e}function Hr(e,t){if([e,t].some(A.nullOrUndefined))throw new Error("Missing required parameters");if(![e,t].every(r=>A.plainObject(r)||A.array(r)))throw new Error("Expected plain objects or array");return{added:(r,n)=>{try{return Ai(e,t,{key:r,type:"added",value:n})}catch{return!1}},changed:(r,n,o)=>{try{let i=Ue(e,r),a=Ue(t,r),s=A.defined(n),c=A.defined(o);if(s||c){let l=c?Xt(o,i):!Xt(n,i),u=Xt(n,a);return l&&u}return[i,a].every(A.array)||[i,a].every(A.plainObject)?!be(i,a):i!==a}catch{return!1}},changedFrom:(r,n,o)=>{if(!A.defined(r))return!1;try{let i=Ue(e,r),a=Ue(t,r),s=A.defined(o);return Xt(n,i)&&(s?Xt(o,a):!s)}catch{return!1}},decreased:(r,n,o)=>{if(!A.defined(r))return!1;try{return Ci(e,t,{key:r,actual:n,previous:o,type:"decreased"})}catch{return!1}},emptied:r=>{try{let[n,o]=Ri(e,t,{key:r});return!!n.length&&!o.length}catch{return!1}},filled:r=>{try{let[n,o]=Ri(e,t,{key:r});return!n.length&&!!o.length}catch{return!1}},increased:(r,n,o)=>{if(!A.defined(r))return!1;try{return Ci(e,t,{key:r,actual:n,previous:o,type:"increased"})}catch{return!1}},removed:(r,n)=>{try{return Ai(e,t,{key:r,type:"removed",value:n})}catch{return!1}}}}function hu(e){return Object.keys(e)}function bs(e,...t){if(!A.plainObject(e))throw new TypeError("Expected an object");let r={};for(let n in e)({}).hasOwnProperty.call(e,n)&&(t.includes(n)||(r[n]=e[n]));return r}function fu(e,...t){if(!A.plainObject(e))throw new TypeError("Expected an object");if(!t.length)return e;let r={};for(let n in e)({}).hasOwnProperty.call(e,n)&&t.includes(n)&&(r[n]=e[n]);return r}function gu(e){var t=!1;return function(){t||(t=!0,window.Promise.resolve().then(function(){t=!1,e()}))}}function yu(e){var t=!1;return function(){t||(t=!0,setTimeout(function(){t=!1,e()},mu))}}function xs(e){var t={};return e&&t.toString.call(e)==="[object Function]"}function Et(e,t){if(e.nodeType!==1)return[];var r=e.ownerDocument.defaultView,n=r.getComputedStyle(e,null);return t?n[t]:n}function fo(e){return e.nodeName==="HTML"?e:e.parentNode||e.host}function hr(e){if(!e)return document.body;switch(e.nodeName){case"HTML":case"BODY":return e.ownerDocument.body;case"#document":return e.body}var t=Et(e),r=t.overflow,n=t.overflowX,o=t.overflowY;return/(auto|scroll|overlay)/.test(r+o+n)?e:hr(fo(e))}function ws(e){return e&&e.referenceNode?e.referenceNode:e}function Ft(e){return e===11?Mi:e===10?Di:Mi||Di}function Mt(e){if(!e)return document.documentElement;for(var t=Ft(10)?document.body:null,r=e.offsetParent||null;r===t&&e.nextElementSibling;)r=(e=e.nextElementSibling).offsetParent;var n=r&&r.nodeName;return!n||n==="BODY"||n==="HTML"?e?e.ownerDocument.documentElement:document.documentElement:["TH","TD","TABLE"].indexOf(r.nodeName)!==-1&&Et(r,"position")==="static"?Mt(r):r}function xu(e){var t=e.nodeName;return t==="BODY"?!1:t==="HTML"||Mt(e.firstElementChild)===e}function qn(e){return e.parentNode!==null?qn(e.parentNode):e}function Ur(e,t){if(!e||!e.nodeType||!t||!t.nodeType)return document.documentElement;var r=e.compareDocumentPosition(t)&Node.DOCUMENT_POSITION_FOLLOWING,n=r?e:t,o=r?t:e,i=document.createRange();i.setStart(n,0),i.setEnd(o,0);var a=i.commonAncestorContainer;if(e!==a&&t!==a||n.contains(o))return xu(a)?a:Mt(a);var s=qn(e);return s.host?Ur(s.host,t):Ur(e,qn(t).host)}function Dt(e){var t=arguments.length>1&&arguments[1]!==void 0?arguments[1]:"top",r=t==="top"?"scrollTop":"scrollLeft",n=e.nodeName;if(n==="BODY"||n==="HTML"){var o=e.ownerDocument.documentElement,i=e.ownerDocument.scrollingElement||o;return i[r]}return e[r]}function wu(e,t){var r=arguments.length>2&&arguments[2]!==void 0?arguments[2]:!1,n=Dt(t,"top"),o=Dt(t,"left"),i=r?-1:1;return e.top+=n*i,e.bottom+=n*i,e.left+=o*i,e.right+=o*i,e}function Li(e,t){var r=t==="x"?"Left":"Top",n=r==="Left"?"Right":"Bottom";return parseFloat(e["border"+r+"Width"])+parseFloat(e["border"+n+"Width"])}function Ni(e,t,r,n){return Math.max(t["offset"+e],t["scroll"+e],r["client"+e],r["offset"+e],r["scroll"+e],Ft(10)?parseInt(r["offset"+e])+parseInt(n["margin"+(e==="Height"?"Top":"Left")])+parseInt(n["margin"+(e==="Height"?"Bottom":"Right")]):0)}function Ts(e){var t=e.body,r=e.documentElement,n=Ft(10)&&getComputedStyle(r);return{height:Ni("Height",t,r,n),width:Ni("Width",t,r,n)}}function st(e){return Ne({},e,{right:e.left+e.width,bottom:e.top+e.height})}function Yn(e){var t={};try{if(Ft(10)){t=e.getBoundingClientRect();var r=Dt(e,"top"),n=Dt(e,"left");t.top+=r,t.left+=n,t.bottom+=r,t.right+=n}else t=e.getBoundingClientRect()}catch{}var o={left:t.left,top:t.top,width:t.right-t.left,height:t.bottom-t.top},i=e.nodeName==="HTML"?Ts(e.ownerDocument):{},a=i.width||e.clientWidth||o.width,s=i.height||e.clientHeight||o.height,c=e.offsetWidth-a,l=e.offsetHeight-s;if(c||l){var u=Et(e);c-=Li(u,"x"),l-=Li(u,"y"),o.width-=c,o.height-=l}return st(o)}function mo(e,t){var r=arguments.length>2&&arguments[2]!==void 0?arguments[2]:!1,n=Ft(10),o=t.nodeName==="HTML",i=Yn(e),a=Yn(t),s=hr(e),c=Et(t),l=parseFloat(c.borderTopWidth),u=parseFloat(c.borderLeftWidth);r&&o&&(a.top=Math.max(a.top,0),a.left=Math.max(a.left,0));var p=st({top:i.top-a.top-l,left:i.left-a.left-u,width:i.width,height:i.height});if(p.marginTop=0,p.marginLeft=0,!n&&o){var d=parseFloat(c.marginTop),h=parseFloat(c.marginLeft);p.top-=l-d,p.bottom-=l-d,p.left-=u-h,p.right-=u-h,p.marginTop=d,p.marginLeft=h}return(n&&!r?t.contains(s):t===s&&s.nodeName!=="BODY")&&(p=wu(p,t)),p}function Su(e){var t=arguments.length>1&&arguments[1]!==void 0?arguments[1]:!1,r=e.ownerDocument.documentElement,n=mo(e,r),o=Math.max(r.clientWidth,window.innerWidth||0),i=Math.max(r.clientHeight,window.innerHeight||0),a=t?0:Dt(r),s=t?0:Dt(r,"left"),c={top:a-n.top+n.marginTop,left:s-n.left+n.marginLeft,width:o,height:i};return st(c)}function Es(e){var t=e.nodeName;if(t==="BODY"||t==="HTML")return!1;if(Et(e,"position")==="fixed")return!0;var r=fo(e);return r?Es(r):!1}function Ss(e){if(!e||!e.parentElement||Ft())return document.documentElement;for(var t=e.parentElement;t&&Et(t,"transform")==="none";)t=t.parentElement;return t||document.documentElement}function go(e,t,r,n){var o=arguments.length>4&&arguments[4]!==void 0?arguments[4]:!1,i={top:0,left:0},a=o?Ss(e):Ur(e,ws(t));if(n==="viewport")i=Su(a,o);else{var s=void 0;n==="scrollParent"?(s=hr(fo(t)),s.nodeName==="BODY"&&(s=e.ownerDocument.documentElement)):n==="window"?s=e.ownerDocument.documentElement:s=n;var c=mo(s,a,o);if(s.nodeName==="HTML"&&!Es(a)){var l=Ts(e.ownerDocument),u=l.height,p=l.width;i.top+=c.top-c.marginTop,i.bottom=u+c.top,i.left+=c.left-c.marginLeft,i.right=p+c.left}else i=c}r=r||0;var d=typeof r=="number";return i.left+=d?r:r.left||0,i.top+=d?r:r.top||0,i.right-=d?r:r.right||0,i.bottom-=d?r:r.bottom||0,i}function Pu(e){var t=e.width,r=e.height;return t*r}function Ps(e,t,r,n,o){var i=arguments.length>5&&arguments[5]!==void 0?arguments[5]:0;if(e.indexOf("auto")===-1)return e;var a=go(r,n,i,o),s={top:{width:a.width,height:t.top-a.top},right:{width:a.right-t.right,height:a.height},bottom:{width:a.width,height:a.bottom-t.bottom},left:{width:t.left-a.left,height:a.height}},c=Object.keys(s).map(function(d){return Ne({key:d},s[d],{area:Pu(s[d])})}).sort(function(d,h){return h.area-d.area}),l=c.filter(function(d){var h=d.width,f=d.height;return h>=r.clientWidth&&f>=r.clientHeight}),u=l.length>0?l[0].key:c[0].key,p=e.split("-")[1];return u+(p?"-"+p:"")}function Os(e,t,r){var n=arguments.length>3&&arguments[3]!==void 0?arguments[3]:null,o=n?Ss(t):Ur(t,ws(r));return mo(r,o,n)}function Cs(e){var t=e.ownerDocument.defaultView,r=t.getComputedStyle(e),n=parseFloat(r.marginTop||0)+parseFloat(r.marginBottom||0),o=parseFloat(r.marginLeft||0)+parseFloat(r.marginRight||0),i={width:e.offsetWidth+o,height:e.offsetHeight+n};return i}function Wr(e){var t={left:"right",right:"left",bottom:"top",top:"bottom"};return e.replace(/left|right|bottom|top/g,function(r){return t[r]})}function As(e,t,r){r=r.split("-")[0];var n=Cs(e),o={width:n.width,height:n.height},i=["right","left"].indexOf(r)!==-1,a=i?"top":"left",s=i?"left":"top",c=i?"height":"width",l=i?"width":"height";return o[a]=t[a]+t[c]/2-n[c]/2,r===s?o[s]=t[s]-n[l]:o[s]=t[Wr(s)],o}function fr(e,t){return Array.prototype.find?e.find(t):e.filter(t)[0]}function Ou(e,t,r){if(Array.prototype.findIndex)return e.findIndex(function(o){return o[t]===r});var n=fr(e,function(o){return o[t]===r});return e.indexOf(n)}function Rs(e,t,r){var n=r===void 0?e:e.slice(0,Ou(e,"name",r));return n.forEach(function(o){o.function&&console.warn("`modifier.function` is deprecated, use `modifier.fn`!");var i=o.function||o.fn;o.enabled&&xs(i)&&(t.offsets.popper=st(t.offsets.popper),t.offsets.reference=st(t.offsets.reference),t=i(t,o))}),t}function Cu(){if(!this.state.isDestroyed){var e={instance:this,styles:{},arrowStyles:{},attributes:{},flipped:!1,offsets:{}};e.offsets.reference=Os(this.state,this.popper,this.reference,this.options.positionFixed),e.placement=Ps(this.options.placement,e.offsets.reference,this.popper,this.reference,this.options.modifiers.flip.boundariesElement,this.options.modifiers.flip.padding),e.originalPlacement=e.placement,e.positionFixed=this.options.positionFixed,e.offsets.popper=As(this.popper,e.offsets.reference,e.placement),e.offsets.popper.position=this.options.positionFixed?"fixed":"absolute",e=Rs(this.modifiers,e),this.state.isCreated?this.options.onUpdate(e):(this.state.isCreated=!0,this.options.onCreate(e))}}function Is(e,t){return e.some(function(r){var n=r.name,o=r.enabled;return o&&n===t})}function yo(e){for(var t=[!1,"ms","Webkit","Moz","O"],r=e.charAt(0).toUpperCase()+e.slice(1),n=0;n<t.length;n++){var o=t[n],i=o?""+o+r:e;if(typeof document.body.style[i]<"u")return i}return null}function Au(){return this.state.isDestroyed=!0,Is(this.modifiers,"applyStyle")&&(this.popper.removeAttribute("x-placement"),this.popper.style.position="",this.popper.style.top="",this.popper.style.left="",this.popper.style.right="",this.popper.style.bottom="",this.popper.style.willChange="",this.popper.style[yo("transform")]=""),this.disableEventListeners(),this.options.removeOnDestroy&&this.popper.parentNode.removeChild(this.popper),this}function ks(e){var t=e.ownerDocument;return t?t.defaultView:window}function Ms(e,t,r,n){var o=e.nodeName==="BODY",i=o?e.ownerDocument.defaultView:e;i.addEventListener(t,r,{passive:!0}),o||Ms(hr(i.parentNode),t,r,n),n.push(i)}function Ru(e,t,r,n){r.updateBound=n,ks(e).addEventListener("resize",r.updateBound,{passive:!0});var o=hr(e);return Ms(o,"scroll",r.updateBound,r.scrollParents),r.scrollElement=o,r.eventsEnabled=!0,r}function Iu(){this.state.eventsEnabled||(this.state=Ru(this.reference,this.options,this.state,this.scheduleUpdate))}function ku(e,t){return ks(e).removeEventListener("resize",t.updateBound),t.scrollParents.forEach(function(r){r.removeEventListener("scroll",t.updateBound)}),t.updateBound=null,t.scrollParents=[],t.scrollElement=null,t.eventsEnabled=!1,t}function Mu(){this.state.eventsEnabled&&(cancelAnimationFrame(this.scheduleUpdate),this.state=ku(this.reference,this.state))}function vo(e){return e!==""&&!isNaN(parseFloat(e))&&isFinite(e)}function Gn(e,t){Object.keys(t).forEach(function(r){var n="";["width","height","top","right","bottom","left"].indexOf(r)!==-1&&vo(t[r])&&(n="px"),e.style[r]=t[r]+n})}function Du(e,t){Object.keys(t).forEach(function(r){var n=t[r];n!==!1?e.setAttribute(r,t[r]):e.removeAttribute(r)})}function Lu(e){return Gn(e.instance.popper,e.styles),Du(e.instance.popper,e.attributes),e.arrowElement&&Object.keys(e.arrowStyles).length&&Gn(e.arrowElement,e.arrowStyles),e}function Nu(e,t,r,n,o){var i=Os(o,t,e,r.positionFixed),a=Ps(r.placement,i,t,e,r.modifiers.flip.boundariesElement,r.modifiers.flip.padding);return t.setAttribute("x-placement",a),Gn(t,{position:r.positionFixed?"fixed":"absolute"}),r}function ju(e,t){var r=e.offsets,n=r.popper,o=r.reference,i=Math.round,a=Math.floor,s=function(x){return x},c=i(o.width),l=i(n.width),u=["left","right"].indexOf(e.placement)!==-1,p=e.placement.indexOf("-")!==-1,d=c%2===l%2,h=c%2===1&&l%2===1,f=t?u||p||d?i:a:s,y=t?i:s;return{left:f(h&&!p&&t?n.left-1:n.left),top:y(n.top),bottom:y(n.bottom),right:f(n.right)}}function Fu(e,t){var r=t.x,n=t.y,o=e.offsets.popper,i=fr(e.instance.modifiers,function(v){return v.name==="applyStyle"}).gpuAcceleration;i!==void 0&&console.warn("WARNING: `gpuAcceleration` option moved to `computeStyle` modifier and will not be supported in future versions of Popper.js!");var a=i!==void 0?i:t.gpuAcceleration,s=Mt(e.instance.popper),c=Yn(s),l={position:o.position},u=ju(e,window.devicePixelRatio<2||!Vu),p=r==="bottom"?"top":"bottom",d=n==="right"?"left":"right",h=yo("transform"),f=void 0,y=void 0;if(p==="bottom"?s.nodeName==="HTML"?y=-s.clientHeight+u.bottom:y=-c.height+u.bottom:y=u.top,d==="right"?s.nodeName==="HTML"?f=-s.clientWidth+u.right:f=-c.width+u.right:f=u.left,a&&h)l[h]="translate3d("+f+"px, "+y+"px, 0)",l[p]=0,l[d]=0,l.willChange="transform";else{var x=p==="bottom"?-1:1,b=d==="right"?-1:1;l[p]=y*x,l[d]=f*b,l.willChange=p+", "+d}var g={"x-placement":e.placement};return e.attributes=Ne({},g,e.attributes),e.styles=Ne({},l,e.styles),e.arrowStyles=Ne({},e.offsets.arrow,e.arrowStyles),e}function Ds(e,t,r){var n=fr(e,function(s){var c=s.name;return c===t}),o=!!n&&e.some(function(s){return s.name===r&&s.enabled&&s.order<n.order});if(!o){var i="`"+t+"`",a="`"+r+"`";console.warn(a+" modifier is required by "+i+" modifier in order to work, be sure to include it before "+i+"!")}return o}function Bu(e,t){var r;if(!Ds(e.instance.modifiers,"arrow","keepTogether"))return e;var n=t.element;if(typeof n=="string"){if(n=e.instance.popper.querySelector(n),!n)return e}else if(!e.instance.popper.contains(n))return console.warn("WARNING: `arrow.element` must be child of its popper element!"),e;var o=e.placement.split("-")[0],i=e.offsets,a=i.popper,s=i.reference,c=["left","right"].indexOf(o)!==-1,l=c?"height":"width",u=c?"Top":"Left",p=u.toLowerCase(),d=c?"left":"top",h=c?"bottom":"right",f=Cs(n)[l];s[h]-f<a[p]&&(e.offsets.popper[p]-=a[p]-(s[h]-f)),s[p]+f>a[h]&&(e.offsets.popper[p]+=s[p]+f-a[h]),e.offsets.popper=st(e.offsets.popper);var y=s[p]+s[l]/2-f/2,x=Et(e.instance.popper),b=parseFloat(x["margin"+u]),g=parseFloat(x["border"+u+"Width"]),v=y-e.offsets.popper[p]-b-g;return v=Math.max(Math.min(a[l]-f,v),0),e.arrowElement=n,e.offsets.arrow=(r={},Lt(r,p,Math.round(v)),Lt(r,d,""),r),e}function zu(e){return e==="end"?"start":e==="start"?"end":e}function ji(e){var t=arguments.length>1&&arguments[1]!==void 0?arguments[1]:!1,r=Rn.indexOf(e),n=Rn.slice(r+1).concat(Rn.slice(0,r));return t?n.reverse():n}function _u(e,t){if(Is(e.instance.modifiers,"inner")||e.flipped&&e.placement===e.originalPlacement)return e;var r=go(e.instance.popper,e.instance.reference,t.padding,t.boundariesElement,e.positionFixed),n=e.placement.split("-")[0],o=Wr(n),i=e.placement.split("-")[1]||"",a=[];switch(t.behavior){case In.FLIP:a=[n,o];break;case In.CLOCKWISE:a=ji(n);break;case In.COUNTERCLOCKWISE:a=ji(n,!0);break;default:a=t.behavior}return a.forEach(function(s,c){if(n!==s||a.length===c+1)return e;n=e.placement.split("-")[0],o=Wr(n);var l=e.offsets.popper,u=e.offsets.reference,p=Math.floor,d=n==="left"&&p(l.right)>p(u.left)||n==="right"&&p(l.left)<p(u.right)||n==="top"&&p(l.bottom)>p(u.top)||n==="bottom"&&p(l.top)<p(u.bottom),h=p(l.left)<p(r.left),f=p(l.right)>p(r.right),y=p(l.top)<p(r.top),x=p(l.bottom)>p(r.bottom),b=n==="left"&&h||n==="right"&&f||n==="top"&&y||n==="bottom"&&x,g=["top","bottom"].indexOf(n)!==-1,v=!!t.flipVariations&&(g&&i==="start"&&h||g&&i==="end"&&f||!g&&i==="start"&&y||!g&&i==="end"&&x),w=!!t.flipVariationsByContent&&(g&&i==="start"&&f||g&&i==="end"&&h||!g&&i==="start"&&x||!g&&i==="end"&&y),T=v||w;(d||b||T)&&(e.flipped=!0,(d||b)&&(n=a[c+1]),T&&(i=zu(i)),e.placement=n+(i?"-"+i:""),e.offsets.popper=Ne({},e.offsets.popper,As(e.instance.popper,e.offsets.reference,e.placement)),e=Rs(e.instance.modifiers,e,"flip"))}),e}function Hu(e){var t=e.offsets,r=t.popper,n=t.reference,o=e.placement.split("-")[0],i=Math.floor,a=["top","bottom"].indexOf(o)!==-1,s=a?"right":"bottom",c=a?"left":"top",l=a?"width":"height";return r[s]<i(n[c])&&(e.offsets.popper[c]=i(n[c])-r[l]),r[c]>i(n[s])&&(e.offsets.popper[c]=i(n[s])),e}function Uu(e,t,r,n){var o=e.match(/((?:\-|\+)?\d*\.?\d*)(.*)/),i=+o[1],a=o[2];if(!i)return e;if(a.indexOf("%")===0){var s=void 0;switch(a){case"%p":s=r;break;case"%":case"%r":default:s=n}var c=st(s);return c[t]/100*i}else if(a==="vh"||a==="vw"){var l=void 0;return a==="vh"?l=Math.max(document.documentElement.clientHeight,window.innerHeight||0):l=Math.max(document.documentElement.clientWidth,window.innerWidth||0),l/100*i}else return i}function Wu(e,t,r,n){var o=[0,0],i=["right","left"].indexOf(n)!==-1,a=e.split(/(\+|\-)/).map(function(u){return u.trim()}),s=a.indexOf(fr(a,function(u){return u.search(/,|\s/)!==-1}));a[s]&&a[s].indexOf(",")===-1&&console.warn("Offsets separated by white space(s) are deprecated, use a comma (,) instead.");var c=/\s*,\s*|\s+/,l=s!==-1?[a.slice(0,s).concat([a[s].split(c)[0]]),[a[s].split(c)[1]].concat(a.slice(s+1))]:[a];return l=l.map(function(u,p){var d=(p===1?!i:i)?"height":"width",h=!1;return u.reduce(function(f,y){return f[f.length-1]===""&&["+","-"].indexOf(y)!==-1?(f[f.length-1]=y,h=!0,f):h?(f[f.length-1]+=y,h=!1,f):f.concat(y)},[]).map(function(f){return Uu(f,d,t,r)})}),l.forEach(function(u,p){u.forEach(function(d,h){vo(d)&&(o[p]+=d*(u[h-1]==="-"?-1:1))})}),o}function qu(e,t){var r=t.offset,n=e.placement,o=e.offsets,i=o.popper,a=o.reference,s=n.split("-")[0],c=void 0;return vo(+r)?c=[+r,0]:c=Wu(r,i,a,s),s==="left"?(i.top+=c[0],i.left-=c[1]):s==="right"?(i.top+=c[0],i.left+=c[1]):s==="top"?(i.left+=c[0],i.top-=c[1]):s==="bottom"&&(i.left+=c[0],i.top+=c[1]),e.popper=i,e}function Yu(e,t){var r=t.boundariesElement||Mt(e.instance.popper);e.instance.reference===r&&(r=Mt(r));var n=yo("transform"),o=e.instance.popper.style,i=o.top,a=o.left,s=o[n];o.top="",o.left="",o[n]="";var c=go(e.instance.popper,e.instance.reference,t.padding,r,e.positionFixed);o.top=i,o.left=a,o[n]=s,t.boundaries=c;var l=t.priority,u=e.offsets.popper,p={primary:function(d){var h=u[d];return u[d]<c[d]&&!t.escapeWithReference&&(h=Math.max(u[d],c[d])),Lt({},d,h)},secondary:function(d){var h=d==="right"?"left":"top",f=u[h];return u[d]>c[d]&&!t.escapeWithReference&&(f=Math.min(u[h],c[d]-(d==="right"?u.width:u.height))),Lt({},h,f)}};return l.forEach(function(d){var h=["left","top"].indexOf(d)!==-1?"primary":"secondary";u=Ne({},u,p[h](d))}),e.offsets.popper=u,e}function Gu(e){var t=e.placement,r=t.split("-")[0],n=t.split("-")[1];if(n){var o=e.offsets,i=o.reference,a=o.popper,s=["bottom","top"].indexOf(r)!==-1,c=s?"left":"top",l=s?"width":"height",u={start:Lt({},c,i[c]),end:Lt({},c,i[c]+i[l]-a[l])};e.offsets.popper=Ne({},a,u[n])}return e}function Xu(e){if(!Ds(e.instance.modifiers,"hide","preventOverflow"))return e;var t=e.offsets.reference,r=fr(e.instance.modifiers,function(n){return n.name==="preventOverflow"}).boundaries;if(t.bottom<r.top||t.left>r.right||t.top>r.bottom||t.right<r.left){if(e.hide===!0)return e;e.hide=!0,e.attributes["x-out-of-boundaries"]=""}else{if(e.hide===!1)return e;e.hide=!1,e.attributes["x-out-of-boundaries"]=!1}return e}function Ku(e){var t=e.placement,r=t.split("-")[0],n=e.offsets,o=n.popper,i=n.reference,a=["left","right"].indexOf(r)!==-1,s=["top","left"].indexOf(r)===-1;return o[a?"left":"top"]=i[r]-(s?o[a?"width":"height"]:0),e.placement=Wr(t),e.offsets.popper=st(o),e}function on(e){var t=Object.prototype.toString.call(e).slice(8,-1);if(/HTML\w+Element/.test(t))return"HTMLElement";if(tp(t))return t}function ze(e){return function(t){return on(t)===e}}function tp(e){return $u.includes(e)}function Bt(e){return function(t){return typeof t===e}}function rp(e){return ep.includes(e)}function C(e){if(e===null)return"null";switch(typeof e){case"bigint":return"bigint";case"boolean":return"boolean";case"number":return"number";case"string":return"string";case"symbol":return"symbol";case"undefined":return"undefined"}if(C.array(e))return"Array";if(C.plainFunction(e))return"Function";var t=on(e);return t||"Object"}function Ns(e){return function(t){return typeof t===e}}function ap(e,t){var r=e.length;if(r!==t.length)return!1;for(var n=r;n--!==0;)if(!we(e[n],t[n]))return!1;return!0}function sp(e,t){if(e.byteLength!==t.byteLength)return!1;for(var r=new DataView(e.buffer),n=new DataView(t.buffer),o=e.byteLength;o--;)if(r.getUint8(o)!==n.getUint8(o))return!1;return!0}function lp(e,t){var r,n,o,i;if(e.size!==t.size)return!1;try{for(var a=Kn(e.entries()),s=a.next();!s.done;s=a.next()){var c=s.value;if(!t.has(c[0]))return!1}}catch(p){r={error:p}}finally{try{s&&!s.done&&(n=a.return)&&n.call(a)}finally{if(r)throw r.error}}try{for(var l=Kn(e.entries()),u=l.next();!u.done;u=l.next()){var c=u.value;if(!we(c[1],t.get(c[0])))return!1}}catch(p){o={error:p}}finally{try{u&&!u.done&&(i=l.return)&&i.call(l)}finally{if(o)throw o.error}}return!0}function cp(e,t){var r,n;if(e.size!==t.size)return!1;try{for(var o=Kn(e.entries()),i=o.next();!i.done;i=o.next()){var a=i.value;if(!t.has(a[0]))return!1}}catch(s){r={error:s}}finally{try{i&&!i.done&&(n=o.return)&&n.call(o)}finally{if(r)throw r.error}}return!0}function we(e,t){if(e===t)return!0;if(e&&Bi(e)&&t&&Bi(t)){if(e.constructor!==t.constructor)return!1;if(Array.isArray(e)&&Array.isArray(t))return ap(e,t);if(e instanceof Map&&t instanceof Map)return lp(e,t);if(e instanceof Set&&t instanceof Set)return cp(e,t);if(ArrayBuffer.isView(e)&&ArrayBuffer.isView(t))return sp(e,t);if(Fi(e)&&Fi(t))return e.source===t.source&&e.flags===t.flags;if(e.valueOf!==Object.prototype.valueOf)return e.valueOf()===t.valueOf();if(e.toString!==Object.prototype.toString)return e.toString()===t.toString();var r=Object.keys(e),n=Object.keys(t);if(r.length!==n.length)return!1;for(var o=r.length;o--!==0;)if(!Object.prototype.hasOwnProperty.call(t,r[o]))return!1;for(var o=r.length;o--!==0;){var i=r[o];if(!(i==="_owner"&&e.$$typeof)&&!we(e[i],t[i]))return!1}return!0}return Number.isNaN(e)&&Number.isNaN(t)?!0:e===t}function up(){for(var e=[],t=0;t<arguments.length;t++)e[t]=arguments[t];return e.every(function(r){return k.string(r)||k.array(r)||k.plainObject(r)})}function pp(e,t,r){return js(e,t)?[e,t].every(k.array)?!e.some(Wi(r))&&t.some(Wi(r)):[e,t].every(k.plainObject)?!Object.entries(e).some(Ui(r))&&Object.entries(t).some(Ui(r)):t===r:!1}function zi(e,t,r){var n=r.actual,o=r.key,i=r.previous,a=r.type,s=We(e,o),c=We(t,o),l=[s,c].every(k.number)&&(a==="increased"?s<c:s>c);return k.undefined(n)||(l=l&&c===n),k.undefined(i)||(l=l&&s===i),l}function _i(e,t,r){var n=r.key,o=r.type,i=r.value,a=We(e,n),s=We(t,n),c=o==="added"?a:s,l=o==="added"?s:a;if(!k.nullOrUndefined(i)){if(k.defined(c)){if(k.array(c)||k.plainObject(c))return pp(c,l,i)}else return we(l,i);return!1}return[a,s].every(k.array)?!l.every(bo(c)):[a,s].every(k.plainObject)?dp(Object.keys(c),Object.keys(l)):![a,s].every(function(u){return k.primitive(u)&&k.defined(u)})&&(o==="added"?!k.defined(a)&&k.defined(s):k.defined(a)&&!k.defined(s))}function Hi(e,t,r){var n=r===void 0?{}:r,o=n.key,i=We(e,o),a=We(t,o);if(!js(i,a))throw new TypeError("Inputs have different types");if(!up(i,a))throw new TypeError("Inputs don't have length");return[i,a].every(k.plainObject)&&(i=Object.keys(i),a=Object.keys(a)),[i,a]}function Ui(e){return function(t){var r=t[0],n=t[1];return k.array(e)?we(e,n)||e.some(function(o){return we(o,n)||k.array(n)&&bo(n)(o)}):k.plainObject(e)&&e[r]?!!e[r]&&we(e[r],n):we(e,n)}}function dp(e,t){return t.some(function(r){return!e.includes(r)})}function Wi(e){return function(t){return k.array(e)?e.some(function(r){return we(r,t)||k.array(t)&&bo(t)(r)}):we(e,t)}}function Kt(e,t){return k.array(e)?e.some(function(r){return we(r,t)}):we(e,t)}function bo(e){return function(t){return e.some(function(r){return we(r,t)})}}function js(){for(var e=[],t=0;t<arguments.length;t++)e[t]=arguments[t];return e.every(k.array)||e.every(k.number)||e.every(k.plainObject)||e.every(k.string)}function We(e,t){if(k.plainObject(e)||k.array(e)){if(k.string(t)){var r=t.split(".");return r.reduce(function(n,o){return n&&n[o]},e)}return k.number(t)?e[t]:e}return e}function hp(e,t){if([e,t].some(k.nullOrUndefined))throw new Error("Missing required parameters");if(![e,t].every(function(p){return k.plainObject(p)||k.array(p)}))throw new Error("Expected plain objects or array");var r=function(p,d){try{return _i(e,t,{key:p,type:"added",value:d})}catch{return!1}},n=function(p,d,h){try{var f=We(e,p),y=We(t,p),x=k.defined(d),b=k.defined(h);if(x||b){var g=b?Kt(h,f):!Kt(d,f),v=Kt(d,y);return g&&v}return[f,y].every(k.array)||[f,y].every(k.plainObject)?!we(f,y):f!==y}catch{return!1}},o=function(p,d,h){if(!k.defined(p))return!1;try{var f=We(e,p),y=We(t,p),x=k.defined(h);return Kt(d,f)&&(x?Kt(h,y):!x)}catch{return!1}},i=function(p,d){return k.defined(p)?(console.warn("`changedTo` is deprecated! Replace it with `change`"),n(p,d)):!1},a=function(p,d,h){if(!k.defined(p))return!1;try{return zi(e,t,{key:p,actual:d,previous:h,type:"decreased"})}catch{return!1}},s=function(p){try{var d=Hi(e,t,{key:p}),h=d[0],f=d[1];return!!h.length&&!f.length}catch{return!1}},c=function(p){try{var d=Hi(e,t,{key:p}),h=d[0],f=d[1];return!h.length&&!!f.length}catch{return!1}},l=function(p,d,h){if(!k.defined(p))return!1;try{return zi(e,t,{key:p,actual:d,previous:h,type:"increased"})}catch{return!1}},u=function(p,d){try{return _i(e,t,{key:p,type:"removed",value:d})}catch{return!1}};return{added:r,changed:n,changedFrom:o,changedTo:i,decreased:a,emptied:s,filled:c,increased:l,removed:u}}function qi(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(o){return Object.getOwnPropertyDescriptor(e,o).enumerable})),r.push.apply(r,n)}return r}function re(e){for(var t=1;t<arguments.length;t++){var r=arguments[t]!=null?arguments[t]:{};t%2?qi(Object(r),!0).forEach(function(n){ge(e,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):qi(Object(r)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(r,n))})}return e}function mr(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function Yi(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,Fs(n.key),n)}}function gr(e,t,r){return t&&Yi(e.prototype,t),r&&Yi(e,r),Object.defineProperty(e,"prototype",{writable:!1}),e}function ge(e,t,r){return t=Fs(t),t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function yr(e,t){if(typeof t!="function"&&t!==null)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&Qn(e,t)}function qr(e){return qr=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(t){return t.__proto__||Object.getPrototypeOf(t)},qr(e)}function Qn(e,t){return Qn=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(r,n){return r.__proto__=n,r},Qn(e,t)}function fp(){if(typeof Reflect>"u"||!Reflect.construct||Reflect.construct.sham)return!1;if(typeof Proxy=="function")return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){})),!0}catch{return!1}}function mp(e,t){if(e==null)return{};var r={},n=Object.keys(e),o,i;for(i=0;i<n.length;i++)o=n[i],!(t.indexOf(o)>=0)&&(r[o]=e[o]);return r}function Vs(e,t){if(e==null)return{};var r=mp(e,t),n,o;if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(e);for(o=0;o<i.length;o++)n=i[o],!(t.indexOf(n)>=0)&&Object.prototype.propertyIsEnumerable.call(e,n)&&(r[n]=e[n])}return r}function Ge(e){if(e===void 0)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}function gp(e,t){if(t&&(typeof t=="object"||typeof t=="function"))return t;if(t!==void 0)throw new TypeError("Derived constructors may only return object or undefined");return Ge(e)}function vr(e){var t=fp();return function(){var r=qr(e),n;if(t){var o=qr(this).constructor;n=Reflect.construct(r,arguments,o)}else n=r.apply(this,arguments);return gp(this,n)}}function yp(e,t){if(typeof e!="object"||e===null)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t||"default");if(typeof n!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(t==="string"?String:Number)(e)}function Fs(e){var t=yp(e,"string");return typeof t=="symbol"?t:String(t)}function wp(e,t,r,n){return typeof e=="boolean"?e:typeof e=="function"?e(t,r,n):e?!!e:!1}function Tp(e,t){return Object.hasOwnProperty.call(e,t)}function Ep(e,t,r,n){return n?new Error(n):new Error("Required ".concat(e[t]," `").concat(t,"` was not specified in `").concat(r,"`."))}function Sp(e,t){if(typeof e!="function")throw new TypeError(bp);if(t&&typeof t!="string")throw new TypeError(xp)}function Gi(e,t,r){return Sp(e,r),function(n,o,i){for(var a=arguments.length,s=new Array(a>3?a-3:0),c=3;c<a;c++)s[c-3]=arguments[c];return wp(t,n,o,i)?Tp(n,o)?e.apply(void 0,[n,o,i].concat(s)):Ep(n,o,i,r):e.apply(void 0,[n,o,i].concat(s))}}function He(){return!!(typeof window<"u"&&window.document&&window.document.createElement)}function kn(){return"ontouchstart"in window&&/Mobi/.test(navigator.userAgent)}function kr(e){var t=e.title,r=e.data,n=e.warn,o=n===void 0?!1:n,i=e.debug,a=i===void 0?!1:i,s=o?console.warn||console.error:console.log;a&&t&&r&&(console.groupCollapsed("%creact-floater: ".concat(t),"color: #9b00ff; font-weight: bold; font-size: 12px;"),Array.isArray(r)?r.forEach(function(c){k.plainObject(c)&&c.key?s.apply(console,[c.key,c.value]):s.apply(console,[c])}):s.apply(console,[r]),console.groupEnd())}function Pp(e,t,r){var n=arguments.length>3&&arguments[3]!==void 0?arguments[3]:!1;e.addEventListener(t,r,n)}function Op(e,t,r){var n=arguments.length>3&&arguments[3]!==void 0?arguments[3]:!1;e.removeEventListener(t,r,n)}function Cp(e,t,r){var n=arguments.length>3&&arguments[3]!==void 0?arguments[3]:!1,o;o=function(i){r(i),Op(e,t,o)},Pp(e,t,o,n)}function Xi(){}function _s(e){var t=e.handleClick,r=e.styles,n=r.color,o=r.height,i=r.width,a=Vs(r,Ap);return m.createElement("button",{"aria-label":"close",onClick:t,style:a,type:"button"},m.createElement("svg",{width:"".concat(i,"px"),height:"".concat(o,"px"),viewBox:"0 0 18 18",version:"1.1",xmlns:"http://www.w3.org/2000/svg",preserveAspectRatio:"xMidYMid"},m.createElement("g",null,m.createElement("path",{d:"M8.13911129,9.00268191 L0.171521827,17.0258467 C-0.0498027049,17.248715 -0.0498027049,17.6098394 0.171521827,17.8327545 C0.28204354,17.9443526 0.427188206,17.9998706 0.572051765,17.9998706 C0.71714958,17.9998706 0.862013139,17.9443526 0.972581703,17.8327545 L9.0000937,9.74924618 L17.0276057,17.8327545 C17.1384085,17.9443526 17.2832721,17.9998706 17.4281356,17.9998706 C17.5729992,17.9998706 17.718097,17.9443526 17.8286656,17.8327545 C18.0499901,17.6098862 18.0499901,17.2487618 17.8286656,17.0258467 L9.86135722,9.00268191 L17.8340066,0.973848225 C18.0553311,0.750979934 18.0553311,0.389855532 17.8340066,0.16694039 C17.6126821,-0.0556467968 17.254037,-0.0556467968 17.0329467,0.16694039 L9.00042166,8.25611765 L0.967006424,0.167268345 C0.745681892,-0.0553188426 0.387317931,-0.0553188426 0.165993399,0.167268345 C-0.0553311331,0.390136635 -0.0553311331,0.751261038 0.165993399,0.974176179 L8.13920499,9.00268191 L8.13911129,9.00268191 Z",fill:n}))))}function Hs(e){var t=e.content,r=e.footer,n=e.handleClick,o=e.open,i=e.positionWrapper,a=e.showCloseButton,s=e.title,c=e.styles,l={content:m.isValidElement(t)?t:m.createElement("div",{className:"__floater__content",style:c.content},t)};return s&&(l.title=m.isValidElement(s)?s:m.createElement("div",{className:"__floater__title",style:c.title},s)),r&&(l.footer=m.isValidElement(r)?r:m.createElement("div",{className:"__floater__footer",style:c.footer},r)),(a||i)&&!k.boolean(o)&&(l.close=m.createElement(_s,{styles:c.close,handleClick:n})),m.createElement("div",{className:"__floater__container",style:c.container},l.close,l.title,l.content,l.footer)}function Ip(e){var t=(0,Xn.default)(Rp,e.options||{});return{wrapper:{cursor:"help",display:"inline-flex",flexDirection:"column",zIndex:t.zIndex},wrapperPosition:{left:-1e3,position:"absolute",top:-1e3,visibility:"hidden"},floater:{display:"inline-block",filter:"drop-shadow(0 0 3px rgba(0, 0, 0, 0.3))",maxWidth:300,opacity:0,position:"relative",transition:"opacity 0.3s",visibility:"hidden",zIndex:t.zIndex},floaterOpening:{opacity:1,visibility:"visible"},floaterWithAnimation:{opacity:1,transition:"opacity 0.3s, transform 0.2s",visibility:"visible"},floaterWithComponent:{maxWidth:"100%"},floaterClosing:{opacity:0,visibility:"visible"},floaterCentered:{left:"50%",position:"fixed",top:"50%",transform:"translate(-50%, -50%)"},container:{backgroundColor:"#fff",color:"#666",minHeight:60,minWidth:200,padding:20,position:"relative",zIndex:10},title:{borderBottom:"1px solid #555",color:"#555",fontSize:18,marginBottom:5,paddingBottom:6,paddingRight:18},content:{fontSize:15},close:{backgroundColor:"transparent",border:0,borderRadius:0,color:"#555",fontSize:0,height:15,outline:"none",padding:10,position:"absolute",right:0,top:0,width:15,WebkitAppearance:"none"},footer:{borderTop:"1px solid #ccc",fontSize:13,marginTop:10,paddingTop:5},arrow:{color:"#fff",display:"inline-flex",length:16,margin:8,position:"absolute",spread:32},options:t}}function nt(){return!!(typeof window<"u"&&window.document&&window.document.createElement)}function qs(e){return e?e.getBoundingClientRect():null}function jp(){let{body:e,documentElement:t}=document;return!e||!t?0:Math.max(e.scrollHeight,e.offsetHeight,t.clientHeight,t.scrollHeight,t.offsetHeight)}function it(e){return typeof e=="string"?document.querySelector(e):e}function Vp(e){return!e||e.nodeType!==1?null:getComputedStyle(e)}function an(e,t,r){if(!e)return xt();let n=(0,vs.default)(e);if(n){if(n.isSameNode(xt()))return r?document:xt();if(!(n.scrollHeight>n.offsetHeight)&&!t)return n.style.overflow="initial",xt()}return n}function br(e,t){if(!e)return!1;let r=an(e,t);return r?!r.isSameNode(xt()):!1}function Fp(e){return e.offsetParent!==document.body}function lr(e,t="fixed"){if(!e||!(e instanceof HTMLElement))return!1;let{nodeName:r}=e,n=Vp(e);return r==="BODY"||r==="HTML"?!1:n&&n.position===t?!0:e.parentNode?lr(e.parentNode,t):!1}function Bp(e){var t;if(!e)return!1;let r=e;for(;r&&r!==document.body;){if(r instanceof HTMLElement){let{display:n,visibility:o}=getComputedStyle(r);if(n==="none"||o==="hidden")return!1}r=(t=r.parentElement)!=null?t:null}return!0}function zp(e,t,r){var n;let o=qs(e),i=an(e,r),a=br(e,r),s=0,c=(n=o?.top)!=null?n:0;return i instanceof HTMLElement&&(s=i.scrollTop,!a&&!lr(e)&&(c+=s),i.isSameNode(xt())||(c+=xt().scrollTop)),Math.floor(c-t)}function _p(e,t,r){var n;if(!e)return 0;let{offsetTop:o=0,scrollTop:i=0}=(n=(0,vs.default)(e))!=null?n:{},a=e.getBoundingClientRect().top+i;o&&(br(e,r)||Fp(e))&&(a-=o);let s=Math.floor(a-t);return s<0?0:s}function xt(){var e;return(e=document.scrollingElement)!=null?e:document.documentElement}function Hp(e,t){let{duration:r,element:n}=t;return new Promise((o,i)=>{let{scrollTop:a}=n,s=e>a?e-a:a-e;du.default.top(n,e,{duration:s<100?50:r},c=>c&&c.message!=="Element already at target scroll position"?i(c):o())})}function Ys(e=navigator.userAgent){let t=e;return typeof window>"u"?t="node":document.documentMode?t="ie":/Edge/.test(e)?t="edge":window.opera||e.includes(" OPR/")?t="opera":typeof window.InstallTrigger<"u"?t="firefox":window.chrome?t="chrome":/(Version\/([\d._]+).*Safari|CriOS|FxiOS| Mobile\/)/.test(e)&&(t="safari"),t}function ot(e){let t=[],r=n=>{if(typeof n=="string"||typeof n=="number")t.push(n);else if(Array.isArray(n))n.forEach(o=>r(o));else if(Jo(n)){let{children:o}=n.props;Array.isArray(o)?o.forEach(i=>r(i)):r(o)}};return r(e),t.join(" ").trim()}function Up(e,t){return!A.plainObject(e)||!A.array(t)?!1:Object.keys(e).every(r=>t.includes(r))}function Wp(e){let t=/^#?([\da-f])([\da-f])([\da-f])$/i,r=e.replace(t,(o,i,a,s)=>i+i+a+a+s+s),n=/^#?([\da-f]{2})([\da-f]{2})([\da-f]{2})$/i.exec(r);return n?[parseInt(n[1],16),parseInt(n[2],16),parseInt(n[3],16)]:[]}function Ki(e){return e.disableBeacon||e.placement==="center"}function Qi(){return!["chrome","safari","firefox","opera"].includes(Ys())}function lt({data:e,debug:t=!1,title:r,warn:n=!1}){let o=n?console.warn||console.error:console.log;t&&(r&&e?(console.groupCollapsed(`%creact-joyride: ${r}`,"color: #ff0044; font-weight: bold; font-size: 12px;"),Array.isArray(e)?e.forEach(i=>{A.plainObject(i)&&i.key?o.apply(console,[i.key,i.value]):o.apply(console,[i])}):o.apply(console,[e]),console.groupEnd()):console.error("Missing title or data props"))}function qp(e){let{isFirstStep:t,lifecycle:r,previousLifecycle:n,scrollToFirstStep:o,step:i,target:a}=e;return!i.disableScrolling&&(!t||o||r===B.TOOLTIP)&&i.placement!=="center"&&(!i.isFixed||!lr(a))&&n!==r&&[B.BEACON,B.TOOLTIP].includes(r)}function Qp(e,t){var r,n,o,i;let{floaterProps:a,styles:s}=e,c=(0,An.default)(s??{},(r=t?.styles)!=null?r:{}),l=(0,An.default)(Kp,c.options||{}),{width:u}=l;window.innerWidth>480&&(u=380),"width"in l&&(u=typeof l.width=="number"&&window.innerWidth<l.width?window.innerWidth-30:l.width);let p={bottom:0,left:0,overflow:"hidden",position:"absolute",right:0,top:0,zIndex:l.zIndex},d={beacon:{...Zt,display:"inline-block",height:l.beaconSize,position:"relative",width:l.beaconSize,zIndex:l.zIndex},beaconInner:{animation:"joyride-beacon-inner 1.2s infinite ease-in-out",backgroundColor:l.primaryColor,borderRadius:"50%",display:"block",height:"50%",left:"50%",opacity:.7,position:"absolute",top:"50%",transform:"translate(-50%, -50%)",width:"50%"},beaconOuter:{animation:"joyride-beacon-outer 1.2s infinite ease-in-out",backgroundColor:`rgba(${Wp(l.primaryColor).join(",")}, 0.2)`,border:`2px solid ${l.primaryColor}`,borderRadius:"50%",boxSizing:"border-box",display:"block",height:"100%",left:0,opacity:.9,position:"absolute",top:0,transformOrigin:"center",width:"100%"},tooltip:{backgroundColor:l.backgroundColor,borderRadius:5,boxSizing:"border-box",color:l.textColor,fontSize:16,maxWidth:"100%",padding:15,position:"relative",width:u},tooltipContainer:{lineHeight:1.4,textAlign:"center"},tooltipTitle:{fontSize:18,margin:0},tooltipContent:{padding:"20px 10px"},tooltipFooter:{alignItems:"center",display:"flex",justifyContent:"flex-end",marginTop:15},tooltipFooterSpacer:{flex:1},buttonNext:{...Zt,backgroundColor:l.primaryColor,borderRadius:4,color:"#fff"},buttonBack:{...Zt,color:l.primaryColor,marginLeft:"auto",marginRight:5},buttonClose:{...Zt,color:l.textColor,height:14,padding:15,position:"absolute",right:0,top:0,width:14},buttonSkip:{...Zt,color:l.textColor,fontSize:14},overlay:{...p,backgroundColor:l.overlayColor,mixBlendMode:"hard-light"},overlayLegacy:{...p},overlayLegacyCenter:{...p,backgroundColor:l.overlayColor},spotlight:{...Ji,backgroundColor:"gray"},spotlightLegacy:{...Ji,boxShadow:`0 0 0 9999px ${l.overlayColor}, ${l.spotlightShadow}`},floaterStyles:{arrow:{color:(i=(o=(n=a?.styles)==null?void 0:n.arrow)==null?void 0:o.color)!=null?i:l.arrowColor},options:{zIndex:l.zIndex+100}},options:l};return(0,An.default)(d,c)}function Jp(e){return fu(e,"beaconComponent","disableCloseOnEsc","disableOverlay","disableOverlayClose","disableScrolling","disableScrollParentFix","floaterProps","hideBackButton","hideCloseButton","locale","showProgress","showSkipButton","spotlightClicks","spotlightPadding","styles","tooltipComponent")}function $t(e,t){var r,n,o,i,a,s;let c=e??{},l=Ir.default.all([Gp,Jp(t),c],{isMergeableObject:A.plainObject}),u=Qp(t,l),p=br(it(l.target),l.disableScrollParentFix),d=Ir.default.all([Yp,(r=t.floaterProps)!=null?r:{},(n=l.floaterProps)!=null?n:{}]);return d.offset=l.offset,d.styles=(0,Ir.default)((o=d.styles)!=null?o:{},u.floaterStyles),d.offset+=(a=(i=t.spotlightPadding)!=null?i:l.spotlightPadding)!=null?a:0,l.placementBeacon&&d.wrapperOptions&&(d.wrapperOptions.placement=l.placementBeacon),p&&d.options.preventOverflow&&(d.options.preventOverflow.boundariesElement="window"),{...l,locale:Ir.default.all([Gs,(s=t.locale)!=null?s:{},l.locale||{}]),floaterProps:d,styles:bs(u,"floaterStyles")}}function Xs(e,t=!1){return A.plainObject(e)?e.target?!0:(lt({title:"validateStep",data:"target is missing from the step",warn:!0,debug:t}),!1):(lt({title:"validateStep",data:"step must be an object",warn:!0,debug:t}),!1)}function Zi(e,t=!1){return A.array(e)?e.every(r=>Xs(r,t)):(lt({title:"validateSteps",data:"steps must be an array",warn:!0,debug:t}),!1)}function $p(e){return new Zp(e)}function rd({styles:e}){return X("div",{key:"JoyrideSpotlight",className:"react-joyride__spotlight","data-test-id":"spotlight",style:e})}function ad({styles:e,...t}){let{color:r,height:n,width:o,...i}=e;return m.createElement("button",{style:i,type:"button",...t},m.createElement("svg",{height:typeof n=="number"?`${n}px`:n,preserveAspectRatio:"xMidYMid",version:"1.1",viewBox:"0 0 18 18",width:typeof o=="number"?`${o}px`:o,xmlns:"http://www.w3.org/2000/svg"},m.createElement("g",null,m.createElement("path",{d:"M8.13911129,9.00268191 L0.171521827,17.0258467 C-0.0498027049,17.248715 -0.0498027049,17.6098394 0.171521827,17.8327545 C0.28204354,17.9443526 0.427188206,17.9998706 0.572051765,17.9998706 C0.71714958,17.9998706 0.862013139,17.9443526 0.972581703,17.8327545 L9.0000937,9.74924618 L17.0276057,17.8327545 C17.1384085,17.9443526 17.2832721,17.9998706 17.4281356,17.9998706 C17.5729992,17.9998706 17.718097,17.9443526 17.8286656,17.8327545 C18.0499901,17.6098862 18.0499901,17.2487618 17.8286656,17.0258467 L9.86135722,9.00268191 L17.8340066,0.973848225 C18.0553311,0.750979934 18.0553311,0.389855532 17.8340066,0.16694039 C17.6126821,-0.0556467968 17.254037,-0.0556467968 17.0329467,0.16694039 L9.00042166,8.25611765 L0.967006424,0.167268345 C0.745681892,-0.0553188426 0.387317931,-0.0553188426 0.165993399,0.167268345 C-0.0553311331,0.390136635 -0.0553311331,0.751261038 0.165993399,0.974176179 L8.13920499,9.00268191 L8.13911129,9.00268191 Z",fill:r}))))}function ld(e){let{backProps:t,closeProps:r,continuous:n,index:o,isLastStep:i,primaryProps:a,size:s,skipProps:c,step:l,tooltipProps:u}=e,{content:p,hideBackButton:d,hideCloseButton:h,hideFooter:f,locale:y,showProgress:x,showSkipButton:b,styles:g,title:v}=l,{back:w,close:T,last:F,next:M,skip:D}=y,V={primary:T};return n&&(V.primary=i?F:M,x&&(V.primary=X("span",null,V.primary," (",o+1,"/",s,")"))),b&&!i&&(V.skip=X("button",{"aria-live":"off","data-test-id":"button-skip",style:g.buttonSkip,type:"button",...c},D)),!d&&o>0&&(V.back=X("button",{"data-test-id":"button-back",style:g.buttonBack,type:"button",...t},w)),V.close=!h&&X(sd,{"data-test-id":"button-close",styles:g.buttonClose,...r}),X("div",{key:"JoyrideTooltip","aria-label":ot(v)||ot(p),className:"react-joyride__tooltip",style:g.tooltip,...u},X("div",{style:g.tooltipContainer},v&&X("h1",{"aria-label":ot(v),style:g.tooltipTitle},v),X("div",{style:g.tooltipContent},p)),!f&&X("div",{style:g.tooltipFooter},X("div",{style:g.tooltipFooterSpacer},V.skip),V.back,X("button",{"data-test-id":"button-primary",style:g.buttonNext,type:"button",...a},V.primary)),V.close)}function hd({targetSelector:e}){return ue(()=>{let t=document.querySelector(e);if(t){t.style.animation="pulsate 3s infinite",t.style.transformOrigin="center",t.style.animationTimingFunction="ease-in-out";let r=`
        @keyframes pulsate {
          0% {
            box-shadow: 0 0 0 0 rgba(2, 156, 253, 0.7), 0 0 0 0 rgba(2, 156, 253, 0.4);
          }
          50% {
            box-shadow: 0 0 0 20px rgba(2, 156, 253, 0), 0 0 0 40px rgba(2, 156, 253, 0);
          }
          100% {
            box-shadow: 0 0 0 0 rgba(2, 156, 253, 0), 0 0 0 0 rgba(2, 156, 253, 0);
          }
        }
      `,n=document.createElement("style");n.id="sb-onboarding-pulsating-effect",n.innerHTML=r,document.head.appendChild(n)}return()=>{let r=document.querySelector("#sb-onboarding-pulsating-effect");r&&r.remove(),t&&(t.style.animation="auto")}},[e]),null}function Js({top:e=0,left:t=0,width:r=window.innerWidth,height:n=window.innerHeight,colors:o=["#CA90FF","#FC521F","#66BF3C","#FF4785","#FFAE00","#1EA7FD"],...i}){let[a]=xe(()=>{let s=document.createElement("div");return s.setAttribute("id","confetti-container"),s.setAttribute("style","position: fixed; top: 0; left: 0; width: 100%; height: 100%; pointer-events: none; z-index: 9999;"),s});return ue(()=>(document.body.appendChild(a),()=>{document.body.removeChild(a)}),[]),Ht(m.createElement(fd,{top:e,left:t,width:r,height:n},m.createElement(mi,{colors:o,drawShape:gd,...i})),a)}function md(e,t){return Math.floor(Math.random()*(t-e))+e}function gd(e){switch(this.shape=this.shape||md(1,6),this.shape){case 2:{let t=this.w/2,r=this.h/2;e.moveTo(-t+2,-r),e.lineTo(t-2,-r),e.arcTo(t,-r,t,-r+2,2),e.lineTo(t,r-2),e.arcTo(t,r,t-2,r,2),e.lineTo(-t+2,r),e.arcTo(-t,r,-t,r-2,2),e.lineTo(-t,-r+2),e.arcTo(-t,-r,-t+2,-r,2);break}case 3:{e.rect(-4,-4,8,16),e.rect(-12,-4,24,8);break}case 4:{e.rect(-4,-4,8,16),e.rect(-4,-4,24,8);break}case 1:{e.arc(0,0,this.radius,0,2*Math.PI);break}case 5:{e.moveTo(16,4),e.lineTo(4,24),e.lineTo(24,24);break}case 6:{e.arc(4,-4,4,-Math.PI/2,0),e.lineTo(4,0);break}}e.closePath(),e.fill()}function Fd({api:e,isFinalStep:t,onFirstTourDone:r,onLastTourDone:n,codeSnippets:o}){let[i,a]=xe(),s=wn();ue(()=>{e.once(ni,()=>{a(3)})},[]);let c=$e(()=>document.querySelector("#root div[role=main]")||document.querySelector("#storybook-panel-root"),[]),l=t?[{target:"#example-button--warning",title:"Congratulations!",content:m.createElement(m.Fragment,null,"You just created your first story. Continue setting up your project to write stories for your own components."),placement:"right",disableOverlay:!0,disableBeacon:!0,floaterProps:{disableAnimation:!0},onNextButtonClick(){n()}}]:[{target:"#storybook-explorer-tree > div",title:"Storybook is built from stories",content:m.createElement(m.Fragment,null,"Storybook stories represent the key states of each of your components.",m.createElement("br",null),m.createElement("br",null),o?.filename&&m.createElement(m.Fragment,null,"We automatically added four stories for this Button component in this example file:",m.createElement("br",null),m.createElement(Fr,null,o.filename))),placement:"right",disableBeacon:!0,styles:{spotlight:{transform:"translateY(30px)"}},floaterProps:{disableAnimation:!0}},{target:"#storybook-preview-iframe",title:"Storybook previews are interactive",content:"Whenever you modify code or stories, Storybook automatically updates how it previews your components.",placement:"bottom",styles:{spotlight:{borderRadius:0}}},{target:c,title:"Interactive story playground",content:m.createElement(m.Fragment,null,"See how a story renders with different data and state without touching code.",m.createElement("br",null),m.createElement("br",null),"Try it out by pressing this button.",m.createElement(hd,{targetSelector:"#control-primary"})),placement:"right",spotlightClicks:!0,floaterProps:{target:"#control-primary",options:{preventOverflow:{boundariesElement:"window"}}},hideNextButton:!0},{target:"#control-primary",title:"Congratulations!",content:m.createElement(m.Fragment,null,"You learned how to control your stories interactively. Now let's explore how to write your first story.",m.createElement(Js,{numberOfPieces:800,recycle:!1,tweenDuration:2e4})),placement:"right",floaterProps:{options:{preventOverflow:{boundariesElement:"window"}}},disableOverlay:!0}];return m.createElement(dd,{steps:l,continuous:!0,stepIndex:i,spotlightPadding:0,hideBackButton:!0,disableCloseOnEsc:!0,disableOverlayClose:!0,disableScrolling:!0,hideCloseButton:!0,callback:u=>{!t&&u.status===W.FINISHED&&r()},floaterProps:{options:{offset:{offset:"0, 6"}},styles:{floater:{padding:0,paddingLeft:8,paddingTop:8,filter:s.base==="light"?"drop-shadow(0px 5px 5px rgba(0,0,0,0.05)) drop-shadow(0 1px 3px rgba(0,0,0,0.1))":"drop-shadow(#fff5 0px 0px 0.5px) drop-shadow(#fff5 0px 0px 0.5px)"}}},tooltipComponent:Ed,styles:{overlay:{mixBlendMode:"unset",backgroundColor:"none"},spotlight:{backgroundColor:"none",border:`solid 2px ${s.color.secondary}`,boxShadow:"0px 0px 0px 9999px rgba(0,0,0,0.4)"},options:{zIndex:1e4,primaryColor:s.color.secondary,arrowColor:s.base==="dark"?"#292A2C":s.color.lightest}}})}function Bd(){return m.createElement("svg",{width:"32px",height:"40px",viewBox:"0 0 256 319",preserveAspectRatio:"xMidYMid"},m.createElement("defs",null,m.createElement("path",{d:"M9.87245893,293.324145 L0.0114611411,30.5732167 C-0.314208957,21.8955842 6.33948896,14.5413918 15.0063196,13.9997149 L238.494389,0.0317105427 C247.316188,-0.519651867 254.914637,6.18486163 255.466,15.0066607 C255.486773,15.339032 255.497167,15.6719708 255.497167,16.0049907 L255.497167,302.318596 C255.497167,311.157608 248.331732,318.323043 239.492719,318.323043 C239.253266,318.323043 239.013844,318.317669 238.774632,318.306926 L25.1475605,308.712253 C16.8276309,308.338578 10.1847994,301.646603 9.87245893,293.324145 L9.87245893,293.324145 Z",id:"path-1"})),m.createElement("g",null,m.createElement("mask",{id:"mask-2",fill:"white"},m.createElement("use",{xlinkHref:"#path-1"})),m.createElement("use",{fill:"#FF4785",fillRule:"nonzero",xlinkHref:"#path-1"}),m.createElement("path",{d:"M188.665358,39.126973 L190.191903,2.41148534 L220.883535,0 L222.205755,37.8634126 C222.251771,39.1811466 221.22084,40.2866846 219.903106,40.3327009 C219.338869,40.3524045 218.785907,40.1715096 218.342409,39.8221376 L206.506729,30.4984116 L192.493574,41.1282444 C191.443077,41.9251106 189.945493,41.7195021 189.148627,40.6690048 C188.813185,40.2267976 188.6423,39.6815326 188.665358,39.126973 Z M149.413703,119.980309 C149.413703,126.206975 191.355678,123.222696 196.986019,118.848893 C196.986019,76.4467826 174.234041,54.1651411 132.57133,54.1651411 C90.9086182,54.1651411 67.5656805,76.7934542 67.5656805,110.735941 C67.5656805,169.85244 147.345341,170.983856 147.345341,203.229219 C147.345341,212.280549 142.913138,217.654777 133.162291,217.654777 C120.456641,217.654777 115.433477,211.165914 116.024438,189.103298 C116.024438,184.317101 67.5656805,182.824962 66.0882793,189.103298 C62.3262146,242.56887 95.6363019,257.990394 133.753251,257.990394 C170.688279,257.990394 199.645341,238.303123 199.645341,202.663511 C199.645341,139.304202 118.683759,141.001326 118.683759,109.604526 C118.683759,96.8760922 128.139127,95.178968 133.753251,95.178968 C139.662855,95.178968 150.300143,96.2205679 149.413703,119.980309 Z",fill:"#FFFFFF",fillRule:"nonzero",mask:"url(#mask-2)"})))}function th(e){let{debounce:t,scroll:r,polyfill:n,offsetSize:o}=e===void 0?{debounce:0,scroll:!1,offsetSize:!1}:e,i=n||(typeof window>"u"?class{}:window.ResizeObserver);if(!i)throw new Error("This browser does not support ResizeObserver out of the box. See: https://github.com/react-spring/react-use-measure/#resize-observer-polyfills");let[a,s]=xe({left:0,top:0,width:0,height:0,bottom:0,right:0,x:0,y:0}),c=Ot({element:null,scrollContainers:null,resizeObserver:null,lastBounds:a}),l=t?typeof t=="number"?t:t.scroll:null,u=t?typeof t=="number"?t:t.resize:null,p=Ot(!1);ue(()=>(p.current=!0,()=>void(p.current=!1)));let[d,h,f]=$e(()=>{let g=()=>{if(!c.current.element)return;let{left:v,top:w,width:T,height:F,bottom:M,right:D,x:V,y:ne}=c.current.element.getBoundingClientRect(),pe={left:v,top:w,width:T,height:F,bottom:M,right:D,x:V,y:ne};c.current.element instanceof HTMLElement&&o&&(pe.height=c.current.element.offsetHeight,pe.width=c.current.element.offsetWidth),Object.freeze(pe),p.current&&!ih(c.current.lastBounds,pe)&&s(c.current.lastBounds=pe)};return[g,u?(0,ea.default)(g,u):g,l?(0,ea.default)(g,l):g]},[s,o,l,u]);function y(){c.current.scrollContainers&&(c.current.scrollContainers.forEach(g=>g.removeEventListener("scroll",f,!0)),c.current.scrollContainers=null),c.current.resizeObserver&&(c.current.resizeObserver.disconnect(),c.current.resizeObserver=null)}function x(){c.current.element&&(c.current.resizeObserver=new i(f),c.current.resizeObserver.observe(c.current.element),r&&c.current.scrollContainers&&c.current.scrollContainers.forEach(g=>g.addEventListener("scroll",f,{capture:!0,passive:!0})))}let b=g=>{!g||g===c.current.element||(y(),c.current.element=g,c.current.scrollContainers=Zs(g),x())};return nh(f,!!r),rh(h),ue(()=>{y(),x()},[r,f,h]),ue(()=>y,[]),[b,a,d]}function rh(e){ue(()=>{let t=e;return window.addEventListener("resize",t),()=>void window.removeEventListener("resize",t)},[e])}function nh(e,t){ue(()=>{if(t){let r=e;return window.addEventListener("scroll",r,{capture:!0,passive:!0}),()=>void window.removeEventListener("scroll",r,!0)}},[e,t])}function Zs(e){let t=[];if(!e||e===document.body)return t;let{overflow:r,overflowX:n,overflowY:o}=window.getComputedStyle(e);return[r,n,o].some(i=>i==="auto"||i==="scroll")&&t.push(e),[...t,...Zs(e.parentElement)]}function lh(e){let t=new ta,r=new ta,n=0,o=!1,i=!1,a=new WeakSet,s={schedule:(c,l=!1,u=!1)=>{let p=u&&o,d=p?t:r;return l&&a.add(c),d.add(c)&&p&&o&&(n=t.order.length),c},cancel:c=>{r.remove(c),a.delete(c)},process:c=>{if(o){i=!0;return}if(o=!0,[t,r]=[r,t],r.clear(),n=t.order.length,n)for(let l=0;l<n;l++){let u=t.order[l];a.has(u)&&(s.schedule(u),e()),u(c)}o=!1,i&&(i=!1,s.process(c))}};return s}function nl(e,t){let r=!1,n=!0,o={delta:0,timestamp:0,isProcessing:!1},i=Dr.reduce((l,u)=>(l[u]=lh(()=>r=!0),l),{}),a=l=>{i[l].process(o)},s=()=>{let l=performance.now();r=!1,o.delta=n?1e3/60:Math.max(Math.min(l-o.timestamp,ch),1),o.timestamp=l,o.isProcessing=!0,Dr.forEach(a),o.isProcessing=!1,r&&t&&(n=!1,e(s))},c=()=>{r=!0,n=!0,o.isProcessing||e(s)};return{schedule:Dr.reduce((l,u)=>{let p=i[u];return l[u]=(d,h=!1,f=!1)=>(r||c(),p.schedule(d,h,f)),l},{}),cancel:l=>Dr.forEach(u=>i[u].cancel(l)),state:o,steps:i}}function uh(e,t,r,n){let{visualElement:o}=ve(sn),i=ve(el),a=ve(wo),s=ve($s).reducedMotion,c=Ot();n=n||i.renderer,!c.current&&n&&(c.current=n(e,{visualState:t,parent:o,props:r,presenceContext:a,blockInitialAnimation:a?a.initial===!1:!1,reducedMotionConfig:s}));let l=c.current;ei(()=>{l&&l.update(r,a)});let u=Ot(!!(r[tl]&&!window.HandoffComplete));return ah(()=>{l&&(Eo.postRender(l.render),u.current&&l.animationState&&l.animationState.animateChanges())}),ue(()=>{l&&(l.updateFeatures(),!u.current&&l.animationState&&l.animationState.animateChanges(),u.current&&(u.current=!1,window.HandoffComplete=!0))}),l}function At(e){return e&&typeof e=="object"&&Object.prototype.hasOwnProperty.call(e,"current")}function ph(e,t,r){return Ut(n=>{n&&e.mount&&e.mount(n),t&&(n?t.mount(n):t.unmount()),r&&(typeof r=="function"?r(n):At(r)&&(r.current=n))},[t])}function cr(e){return typeof e=="string"||Array.isArray(e)}function cn(e){return e!==null&&typeof e=="object"&&typeof e.start=="function"}function un(e){return cn(e.animate)||Po.some(t=>cr(e[t]))}function ol(e){return!!(un(e)||e.variants)}function dh(e,t){if(un(e)){let{initial:r,animate:n}=e;return{initial:r===!1||cr(r)?r:void 0,animate:cr(n)?n:void 0}}return e.inherit!==!1?t:{}}function hh(e){let{initial:t,animate:r}=dh(e,ve(sn));return $e(()=>({initial:t,animate:r}),[ra(t),ra(r)])}function ra(e){return Array.isArray(e)?e.join(" "):e}function fh(e){for(let t in e)ur[t]={...ur[t],...e[t]}}function gh({preloadedFeatures:e,createVisualElement:t,useRender:r,useVisualState:n,Component:o}){e&&fh(e);function i(s,c){let l,u={...ve($s),...s,layoutId:yh(s)},{isStatic:p}=u,d=hh(s),h=n(s,p);if(!p&&ln){d.visualElement=uh(o,h,u,t);let f=ve(al),y=ve(el).strict;d.visualElement&&(l=d.visualElement.loadFeatures(u,y,e,f))}return X(sn.Provider,{value:d},l&&d.visualElement?X(l,{visualElement:d.visualElement,...u}):null,r(o,s,ph(h,d.visualElement,c),h,p,d.visualElement))}let a=Or(i);return a[mh]=o,a}function yh({layoutId:e}){let t=ve(il).id;return t&&e!==void 0?t+"-"+e:e}function vh(e){function t(n,o={}){return gh(e(n,o))}if(typeof Proxy>"u")return t;let r=new Map;return new Proxy(t,{get:(n,o)=>(r.has(o)||r.set(o,t(o)),r.get(o))})}function Oo(e){return typeof e!="string"||e.includes("-")?!1:!!(bh.indexOf(e)>-1||/[A-Z]/.test(e))}function xh(e){Object.assign(Yr,e)}function sl(e,{layout:t,layoutId:r}){return St.has(e)||e.startsWith("origin")||(t||r!==void 0)&&(!!Yr[e]||e==="opacity")}function Eh(e,{enableHardwareAcceleration:t=!0,allowTransformNone:r=!0},n,o){let i="";for(let a=0;a<Th;a++){let s=xr[a];if(e[s]!==void 0){let c=wh[s]||s;i+=`${c}(${e[s]}) `}}return t&&!e.z&&(i+="translateZ(0)"),i=i.trim(),o?i=o(e,n?"":i):r&&n&&(i="none"),i}function wr(e){return typeof e=="string"}function Ao(e,t,r,n){let{style:o,vars:i,transform:a,transformOrigin:s}=e,c=!1,l=!1,u=!0;for(let p in t){let d=t[p];if(cl(p)){i[p]=d;continue}let h=ul[p],f=Oh(d,h);if(St.has(p)){if(c=!0,a[p]=f,!u)continue;d!==(h.default||0)&&(u=!1)}else p.startsWith("origin")?(l=!0,s[p]=f):o[p]=f}if(t.transform||(c||n?o.transform=Eh(e.transform,r,u,n):o.transform&&(o.transform="none")),l){let{originX:p="50%",originY:d="50%",originZ:h=0}=s;o.transformOrigin=`${p} ${d} ${h}`}}function pl(e,t,r){for(let n in t)!Te(t[n])&&!sl(n,r)&&(e[n]=t[n])}function kh({transformTemplate:e},t,r){return $e(()=>{let n=Ro();return Ao(n,t,{enableHardwareAcceleration:!r},e),Object.assign({},n.vars,n.style)},[t])}function Mh(e,t,r){let n=e.style||{},o={};return pl(o,n,e),Object.assign(o,kh(e,t,r)),o}function Dh(e,t,r){let n={},o=Mh(e,t,r);return e.drag&&e.dragListener!==!1&&(n.draggable=!1,o.userSelect=o.WebkitUserSelect=o.WebkitTouchCallout="none",o.touchAction=e.drag===!0?"none":`pan-${e.drag==="x"?"y":"x"}`),e.tabIndex===void 0&&(e.onTap||e.onTapStart||e.whileTap)&&(n.tabIndex=0),n.style=o,n}function Xr(e){return e.startsWith("while")||e.startsWith("drag")&&e!=="draggable"||e.startsWith("layout")||e.startsWith("onTap")||e.startsWith("onPan")||e.startsWith("onLayout")||Lh.has(e)}function Nh(e){e&&(dl=t=>t.startsWith("on")?!Xr(t):e(t))}function jh(e,t,r){let n={};for(let o in e)o==="values"&&typeof e.values=="object"||(dl(o)||r===!0&&Xr(o)||!t&&!Xr(o)||e.draggable&&o.startsWith("onDrag"))&&(n[o]=e[o]);return n}function aa(e,t,r){return typeof e=="string"?e:L.transform(t+r*e)}function Vh(e,t,r){let n=aa(t,e.x,e.width),o=aa(r,e.y,e.height);return`${n} ${o}`}function zh(e,t,r=1,n=0,o=!0){e.pathLength=1;let i=o?Fh:Bh;e[i.offset]=L.transform(-n);let a=L.transform(t),s=L.transform(r);e[i.array]=`${a} ${s}`}function Io(e,{attrX:t,attrY:r,attrScale:n,originX:o,originY:i,pathLength:a,pathSpacing:s=1,pathOffset:c=0,...l},u,p,d){if(Ao(e,l,u,d),p){e.style.viewBox&&(e.attrs.viewBox=e.style.viewBox);return}e.attrs=e.style,e.style={};let{attrs:h,style:f,dimensions:y}=e;h.transform&&(y&&(f.transform=h.transform),delete h.transform),y&&(o!==void 0||i!==void 0||f.transform)&&(f.transformOrigin=Vh(y,o!==void 0?o:.5,i!==void 0?i:.5)),t!==void 0&&(h.x=t),r!==void 0&&(h.y=r),n!==void 0&&(h.scale=n),a!==void 0&&zh(h,a,s,c,!1)}function _h(e,t,r,n){let o=$e(()=>{let i=hl();return Io(i,t,{enableHardwareAcceleration:!1},ko(n),e.transformTemplate),{...i.attrs,style:{...i.style}}},[t]);if(e.style){let i={};pl(i,e.style,e),o.style={...i,...o.style}}return o}function Hh(e=!1){return(t,r,n,{latestValues:o},i)=>{let a=(Oo(t)?_h:Dh)(r,o,i,t),s=jh(r,typeof t=="string",e),c=t!==mn?{...s,...a,ref:n}:{},{children:l}=r,u=$e(()=>Te(l)?l.get():l,[l]);return X(t,{...c,children:u})}}function fl(e,{style:t,vars:r},n,o){Object.assign(e.style,t,o&&o.getProjectionStyles(n));for(let i in r)e.style.setProperty(i,r[i])}function gl(e,t,r,n){fl(e,t,void 0,n);for(let o in t.attrs)e.setAttribute(ml.has(o)?o:To(o),t.attrs[o])}function Mo(e,t){let{style:r}=e,n={};for(let o in r)(Te(r[o])||t.style&&Te(t.style[o])||sl(o,e))&&(n[o]=r[o]);return n}function yl(e,t){let r=Mo(e,t);for(let n in e)if(Te(e[n])||Te(t[n])){let o=xr.indexOf(n)!==-1?"attr"+n.charAt(0).toUpperCase()+n.substring(1):n;r[o]=e[n]}return r}function Do(e,t,r,n={},o={}){return typeof t=="function"&&(t=t(r!==void 0?r:e.custom,n,o)),typeof t=="string"&&(t=e.variants&&e.variants[t]),typeof t=="function"&&(t=t(r!==void 0?r:e.custom,n,o)),t}function Uh(e){let t=Ot(null);return t.current===null&&(t.current=e()),t.current}function Br(e){let t=Te(e)?e.get():e;return Wh(t)?t.toValue():t}function Yh({scrapeMotionValuesFromProps:e,createRenderState:t,onMount:r},n,o,i){let a={latestValues:Gh(n,o,i,e),renderState:t()};return r&&(a.mount=s=>r(n,s,a)),a}function Gh(e,t,r,n){let o={},i=n(e,{});for(let d in i)o[d]=Br(i[d]);let{initial:a,animate:s}=e,c=un(e),l=ol(e);t&&l&&!c&&e.inherit!==!1&&(a===void 0&&(a=t.initial),s===void 0&&(s=t.animate));let u=r?r.initial===!1:!1;u=u||a===!1;let p=u?s:a;return p&&typeof p!="boolean"&&!cn(p)&&(Array.isArray(p)?p:[p]).forEach(d=>{let h=Do(e,d);if(!h)return;let{transitionEnd:f,transition:y,...x}=h;for(let b in x){let g=x[b];if(Array.isArray(g)){let v=u?g.length-1:0;g=g[v]}g!==null&&(o[b]=g)}for(let b in f)o[b]=f[b]}),o}function Qh(e,{forwardMotionProps:t=!1},r,n){return{...Oo(e)?Xh:Kh,preloadedFeatures:r,useRender:Hh(t),createVisualElement:n,Component:e}}function Xe(e,t,r,n={passive:!0}){return e.addEventListener(t,r,n),()=>e.removeEventListener(t,r)}function pn(e,t="page"){return{point:{x:e[t+"X"],y:e[t+"Y"]}}}function Ke(e,t,r,n){return Xe(e,t,Jh(r),n)}function xl(e){let t=null;return()=>{let r=()=>{t=null};return t===null?(t=e,r):!1}}function wl(e){let t=!1;if(e==="y")t=la();else if(e==="x")t=sa();else{let r=sa(),n=la();r&&n?t=()=>{r(),n()}:(r&&r(),n&&n())}return t}function Tl(){let e=wl(!0);return e?(e(),!1):!0}function ca(e,t){let r="pointer"+(t?"enter":"leave"),n="onHover"+(t?"Start":"End"),o=(i,a)=>{if(i.pointerType==="touch"||Tl())return;let s=e.getProps();e.animationState&&s.whileHover&&e.animationState.setActive("whileHover",t),s[n]&&ie.update(()=>s[n](i,a))};return Ke(e.current,r,o,{passive:!e.getProps()[n]})}function Ln(e,t){if(!t)return;let r=new PointerEvent("pointer"+e);t(r,pn(r))}function of({root:e,...t}){let r=e||document;Nn.has(r)||Nn.set(r,{});let n=Nn.get(r),o=JSON.stringify(t);return n[o]||(n[o]=new IntersectionObserver(nf,{root:e,...t})),n[o]}function af(e,t,r){let n=of(t);return Jn.set(e,r),n.observe(e),()=>{Jn.delete(e),n.unobserve(e)}}function cf({viewport:e={}},{viewport:t={}}={}){return r=>e[r]!==t[r]}function Sl(e,t){if(!Array.isArray(t))return!1;let r=t.length;if(r!==e.length)return!1;for(let n=0;n<r;n++)if(t[n]!==e[n])return!1;return!0}function pf(e){let t={};return e.values.forEach((r,n)=>t[n]=r.get()),t}function df(e){let t={};return e.values.forEach((r,n)=>t[n]=r.getVelocity()),t}function dn(e,t,r){let n=e.getProps();return Do(n,t,r!==void 0?r:n.custom,pf(e),df(e))}function Ol(e){return!!(!e||typeof e=="string"&&Cl[e]||Pl(e)||Array.isArray(e)&&e.every(Ol))}function Al(e){if(e)return Pl(e)?rr(e):Array.isArray(e)?e.map(Al):Cl[e]}function ff(e,t,r,{delay:n=0,duration:o,repeat:i=0,repeatType:a="loop",ease:s,times:c}={}){let l={[t]:r};c&&(l.offset=c);let u=Al(s);return Array.isArray(u)&&(l.easing=u),e.animate(l,{delay:n,duration:o,easing:Array.isArray(u)?"linear":u,fill:"both",iterations:i+1,direction:a==="reverse"?"alternate":"normal"})}function mf(e,{repeat:t,repeatType:r="loop"}){let n=t&&r!=="loop"&&t%2===1?0:e.length-1;return e[n]}function vf(e,t,r,n,o){let i,a,s=0;do a=t+(r-t)/2,i=Rl(a,n,o)-e,i>0?r=a:t=a;while(Math.abs(i)>gf&&++s<yf);return a}function Sr(e,t,r,n){if(e===t&&r===n)return le;let o=i=>vf(i,0,1,e,r);return i=>i===0||i===1?i:Rl(o(i),t,n)}function jn(e,t,r){return r<0&&(r+=1),r>1&&(r-=1),r<1/6?e+(t-e)*6*r:r<1/2?t:r<2/3?e+(t-e)*(2/3-r)*6:e}function Pf({hue:e,saturation:t,lightness:r,alpha:n}){e/=360,t/=100,r/=100;let o=0,i=0,a=0;if(!t)o=i=a=r;else{let s=r<.5?r*(1+t):r+t-r*t,c=2*r-s;o=jn(c,s,e+1/3),i=jn(c,s,e),a=jn(c,s,e-1/3)}return{red:Math.round(o*255),green:Math.round(i*255),blue:Math.round(a*255),alpha:n}}function Cf(e){let t="",r="",n="",o="";return e.length>5?(t=e.substring(1,3),r=e.substring(3,5),n=e.substring(5,7),o=e.substring(7,9)):(t=e.substring(1,2),r=e.substring(2,3),n=e.substring(3,4),o=e.substring(4,5),t+=t,r+=r,n+=n,o+=o),{red:parseInt(t,16),green:parseInt(r,16),blue:parseInt(n,16),alpha:o?parseInt(o,16)/255:1}}function da(e){let t=Rf(e);Fe(!!t,`'${e}' is not an animatable color. Use the equivalent color code instead.`);let r=t.parse(e);return t===Rt&&(r=Pf(r)),r}function If(e){var t,r;return isNaN(e)&&wr(e)&&(((t=e.match(Co))===null||t===void 0?void 0:t.length)||0)+(((r=e.match(Ch))===null||r===void 0?void 0:r.length)||0)>0}function Qr(e){let t=e.toString(),r=t.match(ma)||[],n=[],o={color:[],number:[],var:[]},i=[];for(let s=0;s<r.length;s++){let c=r[s];me.test(c)?(o.color.push(s),i.push(Vl),n.push(me.parse(c))):c.startsWith(Mf)?(o.var.push(s),i.push(kf),n.push(c)):(o.number.push(s),i.push(jl),n.push(parseFloat(c)))}let a=t.replace(ma,fa).split(fa);return{values:n,split:a,indexes:o,types:i}}function Fl(e){return Qr(e).values}function Bl(e){let{split:t,types:r}=Qr(e),n=t.length;return o=>{let i="";for(let a=0;a<n;a++)if(i+=t[a],o[a]!==void 0){let s=r[a];s===jl?i+=ir(o[a]):s===Vl?i+=me.transform(o[a]):i+=o[a]}return i}}function Lf(e){let t=Fl(e);return Bl(e)(t.map(Df))}function $n(e,t){return r=>r>0?t:e}function Nf(e,t){return r=>oe(e,t,r)}function Vo(e){return typeof e=="number"?Nf:typeof e=="string"?Gr(e)?$n:me.test(e)?ha:Ff:Array.isArray(e)?zl:typeof e=="object"?me.test(e)?ha:jf:$n}function zl(e,t){let r=[...e],n=r.length,o=e.map((i,a)=>Vo(i)(i,t[a]));return i=>{for(let a=0;a<n;a++)r[a]=o[a](i);return r}}function jf(e,t){let r={...e,...t},n={};for(let o in r)e[o]!==void 0&&t[o]!==void 0&&(n[o]=Vo(e[o])(e[o],t[o]));return o=>{for(let i in n)r[i]=n[i](o);return r}}function Vf(e,t){var r;let n=[],o={color:0,var:0,number:0};for(let i=0;i<t.values.length;i++){let a=t.types[i],s=e.indexes[a][o[a]],c=(r=e.values[s])!==null&&r!==void 0?r:0;n[i]=c,o[a]++}return n}function _l(e,t,r){return typeof e=="number"&&typeof t=="number"&&typeof r=="number"?oe(e,t,r):Vo(e)(e,t)}function Bf(e,t,r){let n=[],o=r||_l,i=e.length-1;for(let a=0;a<i;a++){let s=o(e[a],e[a+1]);if(t){let c=Array.isArray(t)?t[a]||le:t;s=Qe(c,s)}n.push(s)}return n}function zf(e,t,{clamp:r=!0,ease:n,mixer:o}={}){let i=e.length;if(Fe(i===t.length,"Both input and output ranges must be the same length"),i===1)return()=>t[0];e[0]>e[i-1]&&(e=[...e].reverse(),t=[...t].reverse());let a=Bf(t,n,o),s=a.length,c=l=>{let u=0;if(s>1)for(;u<e.length-2&&!(l<e[u+1]);u++);let p=pr(e[u],e[u+1],l);return a[u](p)};return r?l=>c(ct(e[0],e[i-1],l)):c}function _f(e,t){let r=e[e.length-1];for(let n=1;n<=t;n++){let o=pr(0,t,n);e.push(oe(r,1,o))}}function Hf(e){let t=[0];return _f(t,e.length-1),t}function Uf(e,t){return e.map(r=>r*t)}function Wf(e,t){return e.map(()=>t||Il).splice(0,e.length-1)}function Jr({duration:e=300,keyframes:t,times:r,ease:n="easeInOut"}){let o=wf(n)?n.map(pa):pa(n),i={done:!1,value:t[0]},a=Uf(r&&r.length===t.length?r:Hf(t),e),s=zf(a,t,{ease:Array.isArray(o)?o:Wf(t,o)});return{calculatedDuration:e,next:c=>(i.value=s(c),i.done=c>=e,i)}}function Hl(e,t){return t?e*(1e3/t):0}function Ul(e,t,r){let n=Math.max(t-qf,0);return Hl(r-e(n),t-n)}function Kf({duration:e=800,bounce:t=.25,velocity:r=0,mass:n=1}){let o,i;Er(e<=at(ga),"Spring duration must be 10 seconds or less");let a=1-t;a=ct(Gf,Xf,a),e=ct(Yf,ga,Je(e)),a<1?(o=l=>{let u=l*a,p=u*e,d=u-r,h=eo(l,a),f=Math.exp(-p);return Bn-d/h*f},i=l=>{let u=l*a*e,p=u*r+r,d=Math.pow(a,2)*Math.pow(l,2)*e,h=Math.exp(-u),f=eo(Math.pow(l,2),a);return(-o(l)+Bn>0?-1:1)*((p-d)*h)/f}):(o=l=>{let u=Math.exp(-l*e),p=(l-r)*e+1;return-Bn+u*p},i=l=>{let u=Math.exp(-l*e),p=(r-l)*(e*e);return u*p});let s=5/e,c=Jf(o,i,s);if(e=at(e),isNaN(c))return{stiffness:100,damping:10,duration:e};{let l=Math.pow(c,2)*n;return{stiffness:l,damping:a*2*Math.sqrt(n*l),duration:e}}}function Jf(e,t,r){let n=r;for(let o=1;o<Qf;o++)n=n-e(n)/t(n);return n}function eo(e,t){return e*Math.sqrt(1-t*t)}function ya(e,t){return t.some(r=>e[r]!==void 0)}function em(e){let t={velocity:0,stiffness:100,damping:10,mass:1,isResolvedFromDuration:!1,...e};if(!ya(e,$f)&&ya(e,Zf)){let r=Kf(e);t={...t,...r,mass:1},t.isResolvedFromDuration=!0}return t}function Wl({keyframes:e,restDelta:t,restSpeed:r,...n}){let o=e[0],i=e[e.length-1],a={done:!1,value:o},{stiffness:s,damping:c,mass:l,duration:u,velocity:p,isResolvedFromDuration:d}=em({...n,velocity:-Je(n.velocity||0)}),h=p||0,f=c/(2*Math.sqrt(s*l)),y=i-o,x=Je(Math.sqrt(s/l)),b=Math.abs(y)<5;r||(r=b?.01:2),t||(t=b?.005:.5);let g;if(f<1){let v=eo(x,f);g=w=>{let T=Math.exp(-f*x*w);return i-T*((h+f*x*y)/v*Math.sin(v*w)+y*Math.cos(v*w))}}else if(f===1)g=v=>i-Math.exp(-x*v)*(y+(h+x*y)*v);else{let v=x*Math.sqrt(f*f-1);g=w=>{let T=Math.exp(-f*x*w),F=Math.min(v*w,300);return i-T*((h+f*x*y)*Math.sinh(F)+v*y*Math.cosh(F))/v}}return{calculatedDuration:d&&u||null,next:v=>{let w=g(v);if(d)a.done=v>=u;else{let T=h;v!==0&&(f<1?T=Ul(g,v,w):T=0);let F=Math.abs(T)<=r,M=Math.abs(i-w)<=t;a.done=F&&M}return a.value=a.done?i:w,a}}}function va({keyframes:e,velocity:t=0,power:r=.8,timeConstant:n=325,bounceDamping:o=10,bounceStiffness:i=500,modifyTarget:a,min:s,max:c,restDelta:l=.5,restSpeed:u}){let p=e[0],d={done:!1,value:p},h=D=>s!==void 0&&D<s||c!==void 0&&D>c,f=D=>s===void 0?c:c===void 0||Math.abs(s-D)<Math.abs(c-D)?s:c,y=r*t,x=p+y,b=a===void 0?x:a(x);b!==x&&(y=b-p);let g=D=>-y*Math.exp(-D/n),v=D=>b+g(D),w=D=>{let V=g(D),ne=v(D);d.done=Math.abs(V)<=l,d.value=d.done?b:ne},T,F,M=D=>{h(d.value)&&(T=D,F=Wl({keyframes:[d.value,f(d.value)],velocity:Ul(v,D,d.value),damping:o,stiffness:i,restDelta:l,restSpeed:u}))};return M(0),{calculatedDuration:null,next:D=>{let V=!1;return!F&&T===void 0&&(V=!0,w(D),M(D)),T!==void 0&&D>T?F.next(D-T):(!V&&w(D),d)}}}function tm(){zr=void 0}function ba(e){let t=0,r=50,n=e.next(t);for(;!n.done&&t<2e4;)t+=r,n=e.next(t);return t>=2e4?1/0:t}function Zr({autoplay:e=!0,delay:t=0,driver:r=rm,keyframes:n,type:o="keyframes",repeat:i=0,repeatDelay:a=0,repeatType:s="loop",onPlay:c,onStop:l,onComplete:u,onUpdate:p,...d}){let h=1,f=!1,y,x,b=()=>{x=new Promise(te=>{y=te})};b();let g,v=nm[o]||Jr,w;v!==Jr&&typeof n[0]!="number"&&(Fe(n.length===2,`Only two keyframes currently supported with spring and inertia animations. Trying to animate ${n}`),w=Qe(om,_l(n[0],n[1])),n=[0,100]);let T=v({...d,keyframes:n}),F;s==="mirror"&&(F=v({...d,keyframes:[...n].reverse(),velocity:-(d.velocity||0)}));let M="idle",D=null,V=null,ne=null;T.calculatedDuration===null&&i&&(T.calculatedDuration=ba(T));let{calculatedDuration:pe}=T,Ie=1/0,Ee=1/0;pe!==null&&(Ie=pe+a,Ee=Ie*(i+1)-a);let ce=0,ae=te=>{if(V===null)return;h>0&&(V=Math.min(V,te)),h<0&&(V=Math.min(te-Ee/h,V)),D!==null?ce=D:ce=Math.round(te-V)*h;let E=ce-t*(h>=0?1:-1),R=h>=0?E<0:E>Ee;ce=Math.max(E,0),M==="finished"&&D===null&&(ce=Ee);let U=ce,_=T;if(i){let z=Math.min(ce,Ee)/Ie,q=Math.floor(z),Y=z%1;!Y&&z>=1&&(Y=1),Y===1&&q--,q=Math.min(q,i+1),q%2&&(s==="reverse"?(Y=1-Y,a&&(Y-=a/Ie)):s==="mirror"&&(_=F)),U=ct(0,1,Y)*Ie}let N=R?{done:!1,value:n[0]}:_.next(U);w&&(N.value=w(N.value));let{done:G}=N;!R&&pe!==null&&(G=h>=0?ce>=Ee:ce<=0);let H=D===null&&(M==="finished"||M==="running"&&G);return p&&p(N.value),H&&he(),N},ye=()=>{g&&g.stop(),g=void 0},_e=()=>{M="idle",ye(),y(),b(),V=ne=null},he=()=>{M="finished",u&&u(),ye(),y()},je=()=>{if(f)return;g||(g=r(ae));let te=g.now();c&&c(),D!==null?V=te-D:(!V||M==="finished")&&(V=te),M==="finished"&&b(),ne=V,D=null,M="running",g.start()};e&&je();let Pt={then(te,E){return x.then(te,E)},get time(){return Je(ce)},set time(te){te=at(te),ce=te,D!==null||!g||h===0?D=te:V=g.now()-te/h},get duration(){let te=T.calculatedDuration===null?ba(T):T.calculatedDuration;return Je(te)},get speed(){return h},set speed(te){te===h||!g||(h=te,Pt.time=Je(ce))},get state(){return M},play:je,pause:()=>{M="paused",D=ce},stop:()=>{f=!0,M!=="idle"&&(M="idle",l&&l(),_e())},cancel:()=>{ne!==null&&ae(ne),_e()},complete:()=>{M="finished"},sample:te=>(V=0,ae(te))};return Pt}function im(e){let t;return()=>(t===void 0&&(t=e()),t)}function um(e,t,{onUpdate:r,onComplete:n,...o}){if(!(am()&&sm.has(t)&&!o.repeatDelay&&o.repeatType!=="mirror"&&o.damping!==0&&o.type!=="inertia"))return!1;let i=!1,a,s,c=!1,l=()=>{s=new Promise(b=>{a=b})};l();let{keyframes:u,duration:p=300,ease:d,times:h}=o;if(cm(t,o)){let b=Zr({...o,repeat:0,delay:0}),g={done:!1,value:u[0]},v=[],w=0;for(;!g.done&&w<lm;)g=b.sample(w),v.push(g.value),w+=Nr;h=void 0,u=v,p=w-Nr,d="linear"}let f=ff(e.owner.current,t,u,{...o,duration:p,ease:d,times:h}),y=()=>{c=!1,f.cancel()},x=()=>{c=!0,ie.update(y),a(),l()};return f.onfinish=()=>{c||(e.set(mf(u,o)),n&&n(),x())},{then(b,g){return s.then(b,g)},attachTimeline(b){return f.timeline=b,f.onfinish=null,le},get time(){return Je(f.currentTime||0)},set time(b){f.currentTime=at(b)},get speed(){return f.playbackRate},set speed(b){f.playbackRate=b},get duration(){return Je(p)},play:()=>{i||(f.play(),Ze(y))},pause:()=>f.pause(),stop:()=>{if(i=!0,f.playState==="idle")return;let{currentTime:b}=f;if(b){let g=Zr({...o,autoplay:!1});e.setWithVelocity(g.sample(b-Nr).value,g.sample(b).value,Nr)}x()},complete:()=>{c||f.finish()},cancel:x}}function pm({keyframes:e,delay:t,onUpdate:r,onComplete:n}){let o=()=>(r&&r(e[e.length-1]),n&&n(),{time:0,speed:1,duration:0,play:le,pause:le,stop:le,then:i=>(i(),Promise.resolve()),cancel:le,complete:le});return t?Zr({keyframes:[0,1],duration:0,delay:t,onComplete:o}):o()}function vm(e){let[t,r]=e.slice(0,-1).split("(");if(t==="drop-shadow")return e;let[n]=r.match(Co)||[];if(!n)return e;let o=r.replace(n,""),i=ym.has(t)?1:0;return n!==r&&(i*=100),t+"("+i+o+")"}function ql(e,t){let r=Fo(e);return r!==ro&&(r=ut),r.getAnimatableNone?r.getAnimatableNone(t):void 0}function wm(e){if(typeof e=="number")return e===0;if(e!==null)return e==="none"||e==="0"||Yl(e)}function Tm(e,t,r,n){let o=to(t,r),i;Array.isArray(r)?i=[...r]:i=[null,r];let a=n.from!==void 0?n.from:e.get(),s,c=[];for(let l=0;l<i.length;l++)i[l]===null&&(i[l]=l===0?a:i[l-1]),wm(i[l])&&c.push(l),typeof i[l]=="string"&&i[l]!=="none"&&i[l]!=="0"&&(s=i[l]);if(o&&c.length&&s)for(let l=0;l<c.length;l++){let u=c[l];i[u]=ql(t,s)}return i}function Em({when:e,delay:t,delayChildren:r,staggerChildren:n,staggerDirection:o,repeat:i,repeatType:a,repeatDelay:s,from:c,elapsed:l,...u}){return!!Object.keys(u).length}function Bo(e,t){return e[t]||e.default||e}function $r(e){return!!(Te(e)&&e.add)}function _o(e,t){e.indexOf(t)===-1&&e.push(t)}function Ho(e,t){let r=e.indexOf(t);r>-1&&e.splice(r,1)}function Wo(e,t,r){e||xa.has(t)||(console.warn(t),r&&console.warn(r),xa.add(t))}function Nt(e,t){return new Pm(e,t)}function Rm(e,t,r){e.hasValue(t)?e.getValue(t).set(r):e.addValue(t,Nt(r))}function Im(e,t){let r=dn(e,t),{transitionEnd:n={},transition:o={},...i}=r?e.makeTargetAnimatable(r,!1):{};i={...i,...n};for(let a in i){let s=qh(i[a]);Rm(e,a,s)}}function km(e,t,r){var n,o;let i=Object.keys(t).filter(s=>!e.hasValue(s)),a=i.length;if(a)for(let s=0;s<a;s++){let c=i[s],l=t[c],u=null;Array.isArray(l)&&(u=l[0]),u===null&&(u=(o=(n=r[c])!==null&&n!==void 0?n:e.readValue(c))!==null&&o!==void 0?o:t[c]),u!=null&&(typeof u=="string"&&(Gl(u)||Yl(u))?u=parseFloat(u):!Am(u)&&ut.test(l)&&(u=ql(c,l)),e.addValue(c,Nt(u,{owner:e})),r[c]===void 0&&(r[c]=u),u!==null&&e.setBaseTarget(c,u))}}function Mm(e,t){return t?(t[e]||t.default||t).from:void 0}function Dm(e,t,r){let n={};for(let o in e){let i=Mm(o,t);if(i!==void 0)n[o]=i;else{let a=r.getValue(o);a&&(n[o]=a.get())}}return n}function Lm({protectedKeys:e,needsAnimating:t},r){let n=e.hasOwnProperty(r)&&t[r]!==!0;return t[r]=!1,n}function Nm(e,t){let r=e.get();if(Array.isArray(t)){for(let n=0;n<t.length;n++)if(t[n]!==r)return!0}else return r!==t}function Ql(e,t,{delay:r=0,transitionOverride:n,type:o}={}){let{transition:i=e.getDefaultTransition(),transitionEnd:a,...s}=e.makeTargetAnimatable(t),c=e.getValue("willChange");n&&(i=n);let l=[],u=o&&e.animationState&&e.animationState.getState()[o];for(let p in s){let d=e.getValue(p),h=s[p];if(!d||h===void 0||u&&Lm(u,p))continue;let f={delay:r,elapsed:0,...Bo(i||{},p)};if(window.HandoffAppearAnimations){let b=e.getProps()[tl];if(b){let g=window.HandoffAppearAnimations(b,p,d,ie);g!==null&&(f.elapsed=g,f.isHandoff=!0)}}let y=!f.isHandoff&&!Nm(d,h);if(f.type==="spring"&&(d.getVelocity()||f.velocity)&&(y=!1),d.animation&&(y=!1),y)continue;d.start(zo(p,d,h,e.shouldReduceMotion&&St.has(p)?{type:!1}:f));let x=d.animation;$r(c)&&(c.add(p),x.then(()=>c.remove(p))),l.push(x)}return a&&Promise.all(l).then(()=>{a&&Im(e,a)}),l}function no(e,t,r={}){let n=dn(e,t,r.custom),{transition:o=e.getDefaultTransition()||{}}=n||{};r.transitionOverride&&(o=r.transitionOverride);let i=n?()=>Promise.all(Ql(e,n,r)):()=>Promise.resolve(),a=e.variantChildren&&e.variantChildren.size?(c=0)=>{let{delayChildren:l=0,staggerChildren:u,staggerDirection:p}=o;return jm(e,t,l+c,u,p,r)}:()=>Promise.resolve(),{when:s}=o;if(s){let[c,l]=s==="beforeChildren"?[i,a]:[a,i];return c().then(()=>l())}else return Promise.all([i(),a(r.delay)])}function jm(e,t,r=0,n=0,o=1,i){let a=[],s=(e.variantChildren.size-1)*n,c=o===1?(l=0)=>l*n:(l=0)=>s-l*n;return Array.from(e.variantChildren).sort(Vm).forEach((l,u)=>{l.notify("AnimationStart",t),a.push(no(l,t,{...i,delay:r+c(u)}).then(()=>l.notify("AnimationComplete",t)))}),Promise.all(a)}function Vm(e,t){return e.sortNodePosition(t)}function Fm(e,t,r={}){e.notify("AnimationStart",t);let n;if(Array.isArray(t)){let o=t.map(i=>no(e,i,r));n=Promise.all(o)}else if(typeof t=="string")n=no(e,t,r);else{let o=typeof t=="function"?dn(e,t,r.custom):t;n=Promise.all(Ql(e,o,r))}return n.then(()=>e.notify("AnimationComplete",t))}function _m(e){return t=>Promise.all(t.map(({animation:r,options:n})=>Fm(e,r,n)))}function Hm(e){let t=_m(e),r=Wm(),n=!0,o=(c,l)=>{let u=dn(e,l);if(u){let{transition:p,transitionEnd:d,...h}=u;c={...c,...h,...d}}return c};function i(c){t=c(e)}function a(c,l){let u=e.getProps(),p=e.getVariantContext(!0)||{},d=[],h=new Set,f={},y=1/0;for(let b=0;b<zm;b++){let g=Bm[b],v=r[g],w=u[g]!==void 0?u[g]:p[g],T=cr(w),F=g===l?v.isActive:null;F===!1&&(y=b);let M=w===p[g]&&w!==u[g]&&T;if(M&&n&&e.manuallyAnimateOnMount&&(M=!1),v.protectedKeys={...f},!v.isActive&&F===null||!w&&!v.prevProp||cn(w)||typeof w=="boolean")continue;let D=Um(v.prevProp,w)||g===l&&v.isActive&&!M&&T||b>y&&T,V=!1,ne=Array.isArray(w)?w:[w],pe=ne.reduce(o,{});F===!1&&(pe={});let{prevResolvedValues:Ie={}}=v,Ee={...Ie,...pe},ce=ae=>{D=!0,h.has(ae)&&(V=!0,h.delete(ae)),v.needsAnimating[ae]=!0};for(let ae in Ee){let ye=pe[ae],_e=Ie[ae];if(f.hasOwnProperty(ae))continue;let he=!1;Kr(ye)&&Kr(_e)?he=!Sl(ye,_e):he=ye!==_e,he?ye!==void 0?ce(ae):h.add(ae):ye!==void 0&&h.has(ae)?ce(ae):v.protectedKeys[ae]=!0}v.prevProp=w,v.prevResolvedValues=pe,v.isActive&&(f={...f,...pe}),n&&e.blockInitialAnimation&&(D=!1),D&&(!M||V)&&d.push(...ne.map(ae=>({animation:ae,options:{type:g,...c}})))}if(h.size){let b={};h.forEach(g=>{let v=e.getBaseTarget(g);v!==void 0&&(b[g]=v)}),d.push({animation:b})}let x=!!d.length;return n&&(u.initial===!1||u.initial===u.animate)&&!e.manuallyAnimateOnMount&&(x=!1),n=!1,x?t(d):Promise.resolve()}function s(c,l,u){var p;if(r[c].isActive===l)return Promise.resolve();(p=e.variantChildren)===null||p===void 0||p.forEach(h=>{var f;return(f=h.animationState)===null||f===void 0?void 0:f.setActive(c,l)}),r[c].isActive=l;let d=a(u,c);for(let h in r)r[h].protectedKeys={};return d}return{animateChanges:a,setActive:s,setAnimateFunction:i,getState:()=>r}}function Um(e,t){return typeof t=="string"?t!==e:Array.isArray(t)?!Sl(t,e):!1}function yt(e=!1){return{isActive:e,protectedKeys:{},needsAnimating:{},prevResolvedValues:{}}}function Wm(){return{animate:yt(!0),whileInView:yt(),whileHover:yt(),whileTap:yt(),whileDrag:yt(),whileFocus:yt(),exit:yt()}}function Km(e,t){let r=Ta(e.x,t.x),n=Ta(e.y,t.y);return Math.sqrt(r**2+n**2)}function zn(e,t){return t?{point:t(e.point)}:e}function Ea(e,t){return{x:e.x-t.x,y:e.y-t.y}}function _n({point:e},t){return{point:e,delta:Ea(e,Zl(t)),offset:Ea(e,Qm(t)),velocity:Jm(t,.1)}}function Qm(e){return e[0]}function Zl(e){return e[e.length-1]}function Jm(e,t){if(e.length<2)return{x:0,y:0};let r=e.length-1,n=null,o=Zl(e);for(;r>=0&&(n=e[r],!(o.timestamp-n.timestamp>at(t)));)r--;if(!n)return{x:0,y:0};let i=Je(o.timestamp-n.timestamp);if(i===0)return{x:0,y:0};let a={x:(o.x-n.x)/i,y:(o.y-n.y)/i};return a.x===1/0&&(a.x=0),a.y===1/0&&(a.y=0),a}function Ae(e){return e.max-e.min}function oo(e,t=0,r=.01){return Math.abs(e-t)<=r}function Sa(e,t,r,n=.5){e.origin=n,e.originPoint=oe(t.min,t.max,e.origin),e.scale=Ae(r)/Ae(t),(oo(e.scale,1,1e-4)||isNaN(e.scale))&&(e.scale=1),e.translate=oe(r.min,r.max,e.origin)-e.originPoint,(oo(e.translate)||isNaN(e.translate))&&(e.translate=0)}function ar(e,t,r,n){Sa(e.x,t.x,r.x,n?n.originX:void 0),Sa(e.y,t.y,r.y,n?n.originY:void 0)}function Pa(e,t,r){e.min=r.min+t.min,e.max=e.min+Ae(t)}function Zm(e,t,r){Pa(e.x,t.x,r.x),Pa(e.y,t.y,r.y)}function Oa(e,t,r){e.min=t.min-r.min,e.max=e.min+Ae(t)}function sr(e,t,r){Oa(e.x,t.x,r.x),Oa(e.y,t.y,r.y)}function $m(e,{min:t,max:r},n){return t!==void 0&&e<t?e=n?oe(t,e,n.min):Math.max(e,t):r!==void 0&&e>r&&(e=n?oe(r,e,n.max):Math.min(e,r)),e}function Ca(e,t,r){return{min:t!==void 0?e.min+t:void 0,max:r!==void 0?e.max+r-(e.max-e.min):void 0}}function eg(e,{top:t,left:r,bottom:n,right:o}){return{x:Ca(e.x,r,o),y:Ca(e.y,t,n)}}function Aa(e,t){let r=t.min-e.min,n=t.max-e.max;return t.max-t.min<e.max-e.min&&([r,n]=[n,r]),{min:r,max:n}}function tg(e,t){return{x:Aa(e.x,t.x),y:Aa(e.y,t.y)}}function rg(e,t){let r=.5,n=Ae(e),o=Ae(t);return o>n?r=pr(t.min,t.max-n,e.min):n>o&&(r=pr(e.min,e.max-o,t.min)),ct(0,1,r)}function ng(e,t){let r={};return t.min!==void 0&&(r.min=t.min-e.min),t.max!==void 0&&(r.max=t.max-e.min),r}function og(e=io){return e===!1?e=0:e===!0&&(e=io),{x:Ra(e,"left","right"),y:Ra(e,"top","bottom")}}function Ra(e,t,r){return{min:Ia(e,t),max:Ia(e,r)}}function Ia(e,t){return typeof e=="number"?e:e[t]||0}function Le(e){return[e("x"),e("y")]}function $l({top:e,left:t,right:r,bottom:n}){return{x:{min:t,max:r},y:{min:e,max:n}}}function ig({x:e,y:t}){return{top:t.min,right:e.max,bottom:t.max,left:e.min}}function ag(e,t){if(!t)return e;let r=t({x:e.left,y:e.top}),n=t({x:e.right,y:e.bottom});return{top:r.y,left:r.x,bottom:n.y,right:n.x}}function Hn(e){return e===void 0||e===1}function ao({scale:e,scaleX:t,scaleY:r}){return!Hn(e)||!Hn(t)||!Hn(r)}function vt(e){return ao(e)||ec(e)||e.z||e.rotate||e.rotateX||e.rotateY}function ec(e){return Da(e.x)||Da(e.y)}function Da(e){return e&&e!=="0%"}function en(e,t,r){let n=e-r,o=t*n;return r+o}function La(e,t,r,n,o){return o!==void 0&&(e=en(e,o,n)),en(e,r,n)+t}function so(e,t=0,r=1,n,o){e.min=La(e.min,t,r,n,o),e.max=La(e.max,t,r,n,o)}function tc(e,{x:t,y:r}){so(e.x,t.translate,t.scale,t.originPoint),so(e.y,r.translate,r.scale,r.originPoint)}function sg(e,t,r,n=!1){let o=r.length;if(!o)return;t.x=t.y=1;let i,a;for(let s=0;s<o;s++){i=r[s],a=i.projectionDelta;let c=i.instance;c&&c.style&&c.style.display==="contents"||(n&&i.options.layoutScroll&&i.scroll&&i!==i.root&&kt(e,{x:-i.scroll.offset.x,y:-i.scroll.offset.y}),a&&(t.x*=a.x.scale,t.y*=a.y.scale,tc(e,a)),n&&vt(i.latestValues)&&kt(e,i.latestValues))}t.x=Na(t.x),t.y=Na(t.y)}function Na(e){return Number.isInteger(e)||e>1.0000000000001||e<.999999999999?e:1}function rt(e,t){e.min=e.min+t,e.max=e.max+t}function ja(e,t,[r,n,o]){let i=t[o]!==void 0?t[o]:.5,a=oe(e.min,e.max,i);so(e,t[r],t[n],a,t.scale)}function kt(e,t){ja(e.x,t,lg),ja(e.y,t,cg)}function rc(e,t){return $l(ag(e.getBoundingClientRect(),t))}function ug(e,t,r){let n=rc(e,r),{scroll:o}=t;return o&&(rt(n.x,o.offset.x),rt(n.y,o.offset.y)),n}function jr(e,t,r){return(t===!0||t===e)&&(r===null||r===e)}function hg(e,t=10){let r=null;return Math.abs(e.y)>t?r="y":Math.abs(e.x)>t&&(r="x"),r}function gg(){let e=ve(wo);if(e===null)return[!0,null];let{isPresent:t,onExitComplete:r,register:n}=e,o=$o();return ue(()=>n(o),[]),!t&&r?[!1,()=>r&&r(o)]:[!0]}function Fa(e,t){return t.max===t.min?0:e/(t.max-t.min)*100}function oc(e){let[t,r]=gg(),n=ve(il);return m.createElement(vg,{...e,layoutGroup:n,switchLayoutGroup:ve(al),isPresent:t,safeToRemove:r})}function wg(e,t,r,n,o,i){o?(e.opacity=oe(0,r.opacity!==void 0?r.opacity:1,Tg(n)),e.opacityExit=oe(t.opacity!==void 0?t.opacity:1,0,Eg(n))):i&&(e.opacity=oe(t.opacity!==void 0?t.opacity:1,r.opacity!==void 0?r.opacity:1,n));for(let a=0;a<xg;a++){let s=`border${ic[a]}Radius`,c=_a(t,s),l=_a(r,s);c===void 0&&l===void 0||(c||(c=0),l||(l=0),c===0||l===0||za(c)===za(l)?(e[s]=Math.max(oe(Ba(c),Ba(l),n),0),(qe.test(l)||qe.test(c))&&(e[s]+="%")):e[s]=l)}(t.rotate||r.rotate)&&(e.rotate=oe(t.rotate||0,r.rotate||0,n))}function _a(e,t){return e[t]!==void 0?e[t]:e.borderRadius}function ac(e,t,r){return n=>n<e?0:n>t?1:r(pr(e,t,n))}function Ha(e,t){e.min=t.min,e.max=t.max}function Me(e,t){Ha(e.x,t.x),Ha(e.y,t.y)}function Ua(e,t,r,n,o){return e-=t,e=en(e,1/r,n),o!==void 0&&(e=en(e,1/o,n)),e}function Sg(e,t=0,r=1,n=.5,o,i=e,a=e){if(qe.test(t)&&(t=parseFloat(t),t=oe(a.min,a.max,t/100)-a.min),typeof t!="number")return;let s=oe(i.min,i.max,n);e===i&&(s-=t),e.min=Ua(e.min,t,r,s,o),e.max=Ua(e.max,t,r,s,o)}function Wa(e,t,[r,n,o],i,a){Sg(e,t[r],t[n],t[o],t.scale,i,a)}function qa(e,t,r,n){Wa(e.x,t,Pg,r?r.x:void 0,n?n.x:void 0),Wa(e.y,t,Og,r?r.y:void 0,n?n.y:void 0)}function Ya(e){return e.translate===0&&e.scale===1}function sc(e){return Ya(e.x)&&Ya(e.y)}function Cg(e,t){return e.x.min===t.x.min&&e.x.max===t.x.max&&e.y.min===t.y.min&&e.y.max===t.y.max}function lc(e,t){return Math.round(e.x.min)===Math.round(t.x.min)&&Math.round(e.x.max)===Math.round(t.x.max)&&Math.round(e.y.min)===Math.round(t.y.min)&&Math.round(e.y.max)===Math.round(t.y.max)}function Ga(e){return Ae(e.x)/Ae(e.y)}function Xa(e,t,r){let n="",o=e.x.translate/t.x,i=e.y.translate/t.y;if((o||i)&&(n=`translate3d(${o}px, ${i}px, 0) `),(t.x!==1||t.y!==1)&&(n+=`scale(${1/t.x}, ${1/t.y}) `),r){let{rotate:c,rotateX:l,rotateY:u}=r;c&&(n+=`rotate(${c}deg) `),l&&(n+=`rotateX(${l}deg) `),u&&(n+=`rotateY(${u}deg) `)}let a=e.x.scale*t.x,s=e.y.scale*t.y;return(a!==1||s!==1)&&(n+=`scale(${a}, ${s})`),n||"none"}function kg(e,t){let r=Tt.now(),n=({timestamp:o})=>{let i=o-r;i>=t&&(Ze(n),e(i-t))};return ie.read(n,!0),()=>Ze(n)}function Mg(e){window.MotionDebug&&window.MotionDebug.record(e)}function Dg(e){return e instanceof SVGElement&&e.tagName!=="svg"}function Lg(e,t,r){let n=Te(e)?e:Nt(e);return n.start(zo("",n,t,r)),n.animation}function cc({attachResizeListener:e,defaultParent:t,measureScroll:r,checkIsScrollRoot:n,resetTransform:o}){return class{constructor(i={},a=t?.()){this.id=jg++,this.animationId=0,this.children=new Set,this.options={},this.isTreeAnimating=!1,this.isAnimationBlocked=!1,this.isLayoutDirty=!1,this.isProjectionDirty=!1,this.isSharedProjectionDirty=!1,this.isTransformDirty=!1,this.updateManuallyBlocked=!1,this.updateBlockedByResize=!1,this.isUpdating=!1,this.isSVG=!1,this.needsReset=!1,this.shouldResetTransform=!1,this.treeScale={x:1,y:1},this.eventHandlers=new Map,this.hasTreeAnimated=!1,this.updateScheduled=!1,this.projectionUpdateScheduled=!1,this.checkUpdateFailed=()=>{this.isUpdating&&(this.isUpdating=!1,this.clearAllSnapshots())},this.updateProjection=()=>{this.projectionUpdateScheduled=!1,bt.totalNodes=bt.resolvedTargetDeltas=bt.recalculatedProjection=0,this.nodes.forEach(Bg),this.nodes.forEach(Wg),this.nodes.forEach(qg),this.nodes.forEach(zg),Mg(bt)},this.hasProjected=!1,this.isVisible=!0,this.animationProgress=0,this.sharedNodes=new Map,this.latestValues=i,this.root=a?a.root||a:this,this.path=a?[...a.path,a]:[],this.parent=a,this.depth=a?a.depth+1:0;for(let s=0;s<this.path.length;s++)this.path[s].shouldResetTransform=!0;this.root===this&&(this.nodes=new Ig)}addEventListener(i,a){return this.eventHandlers.has(i)||this.eventHandlers.set(i,new Uo),this.eventHandlers.get(i).add(a)}notifyListeners(i,...a){let s=this.eventHandlers.get(i);s&&s.notify(...a)}hasListeners(i){return this.eventHandlers.has(i)}mount(i,a=this.root.hasTreeAnimated){if(this.instance)return;this.isSVG=Dg(i),this.instance=i;let{layoutId:s,layout:c,visualElement:l}=this.options;if(l&&!l.current&&l.mount(i),this.root.nodes.add(this),this.parent&&this.parent.children.add(this),a&&(c||s)&&(this.isLayoutDirty=!0),e){let u,p=()=>this.root.updateBlockedByResize=!1;e(i,()=>{this.root.updateBlockedByResize=!0,u&&u(),u=kg(p,250),_r.hasAnimatedSinceResize&&(_r.hasAnimatedSinceResize=!1,this.nodes.forEach(Za))})}s&&this.root.registerSharedNode(s,this),this.options.animate!==!1&&l&&(s||c)&&this.addEventListener("didUpdate",({delta:u,hasLayoutChanged:p,hasRelativeTargetChanged:d,layout:h})=>{if(this.isTreeAnimationBlocked()){this.target=void 0,this.relativeTarget=void 0;return}let f=this.options.transition||l.getDefaultTransition()||Qg,{onLayoutAnimationStart:y,onLayoutAnimationComplete:x}=l.getProps(),b=!this.targetLayout||!lc(this.targetLayout,h)||d,g=!p&&d;if(this.options.layoutRoot||this.resumeFrom&&this.resumeFrom.instance||g||p&&(b||!this.currentAnimation)){this.resumeFrom&&(this.resumingFrom=this.resumeFrom,this.resumingFrom.resumingFrom=void 0),this.setAnimationOrigin(u,g);let v={...Bo(f,"layout"),onPlay:y,onComplete:x};(l.shouldReduceMotion||this.options.layoutRoot)&&(v.delay=0,v.type=!1),this.startAnimation(v)}else p||Za(this),this.isLead()&&this.options.onExitComplete&&this.options.onExitComplete();this.targetLayout=h})}unmount(){this.options.layoutId&&this.willUpdate(),this.root.nodes.remove(this);let i=this.getStack();i&&i.remove(this),this.parent&&this.parent.children.delete(this),this.instance=void 0,Ze(this.updateProjection)}blockUpdate(){this.updateManuallyBlocked=!0}unblockUpdate(){this.updateManuallyBlocked=!1}isUpdateBlocked(){return this.updateManuallyBlocked||this.updateBlockedByResize}isTreeAnimationBlocked(){return this.isAnimationBlocked||this.parent&&this.parent.isTreeAnimationBlocked()||!1}startUpdate(){this.isUpdateBlocked()||(this.isUpdating=!0,this.nodes&&this.nodes.forEach(Yg),this.animationId++)}getTransformTemplate(){let{visualElement:i}=this.options;return i&&i.getProps().transformTemplate}willUpdate(i=!0){if(this.root.hasTreeAnimated=!0,this.root.isUpdateBlocked()){this.options.onExitComplete&&this.options.onExitComplete();return}if(!this.root.isUpdating&&this.root.startUpdate(),this.isLayoutDirty)return;this.isLayoutDirty=!0;for(let l=0;l<this.path.length;l++){let u=this.path[l];u.shouldResetTransform=!0,u.updateScroll("snapshot"),u.options.layoutRoot&&u.willUpdate(!1)}let{layoutId:a,layout:s}=this.options;if(a===void 0&&!s)return;let c=this.getTransformTemplate();this.prevTransformTemplateValue=c?c(this.latestValues,""):void 0,this.updateSnapshot(),i&&this.notifyListeners("willUpdate")}update(){if(this.updateScheduled=!1,this.isUpdateBlocked()){this.unblockUpdate(),this.clearAllSnapshots(),this.nodes.forEach(Ja);return}this.isUpdating||this.nodes.forEach(Hg),this.isUpdating=!1,this.nodes.forEach(Ug),this.nodes.forEach(Vg),this.nodes.forEach(Fg),this.clearAllSnapshots();let i=Tt.now();de.delta=ct(0,1e3/60,i-de.timestamp),de.timestamp=i,de.isProcessing=!0,Dn.update.process(de),Dn.preRender.process(de),Dn.render.process(de),de.isProcessing=!1}didUpdate(){this.updateScheduled||(this.updateScheduled=!0,Eo.read(()=>this.update()))}clearAllSnapshots(){this.nodes.forEach(_g),this.sharedNodes.forEach(Gg)}scheduleUpdateProjection(){this.projectionUpdateScheduled||(this.projectionUpdateScheduled=!0,ie.preRender(this.updateProjection,!1,!0))}scheduleCheckAfterUnmount(){ie.postRender(()=>{this.isLayoutDirty?this.root.didUpdate():this.root.checkUpdateFailed()})}updateSnapshot(){this.snapshot||!this.instance||(this.snapshot=this.measure())}updateLayout(){if(!this.instance||(this.updateScroll(),!(this.options.alwaysMeasureLayout&&this.isLead())&&!this.isLayoutDirty))return;if(this.resumeFrom&&!this.resumeFrom.instance)for(let s=0;s<this.path.length;s++)this.path[s].updateScroll();let i=this.layout;this.layout=this.measure(!1),this.layoutCorrected=se(),this.isLayoutDirty=!1,this.projectionDelta=void 0,this.notifyListeners("measure",this.layout.layoutBox);let{visualElement:a}=this.options;a&&a.notify("LayoutMeasure",this.layout.layoutBox,i?i.layoutBox:void 0)}updateScroll(i="measure"){let a=!!(this.options.layoutScroll&&this.instance);this.scroll&&this.scroll.animationId===this.root.animationId&&this.scroll.phase===i&&(a=!1),a&&(this.scroll={animationId:this.root.animationId,phase:i,isRoot:n(this.instance),offset:r(this.instance)})}resetTransform(){if(!o)return;let i=this.isLayoutDirty||this.shouldResetTransform,a=this.projectionDelta&&!sc(this.projectionDelta),s=this.getTransformTemplate(),c=s?s(this.latestValues,""):void 0,l=c!==this.prevTransformTemplateValue;i&&(a||vt(this.latestValues)||l)&&(o(this.instance,c),this.shouldResetTransform=!1,this.scheduleRender())}measure(i=!0){let a=this.measurePageBox(),s=this.removeElementScroll(a);return i&&(s=this.removeTransform(s)),Jg(s),{animationId:this.root.animationId,measuredBox:a,layoutBox:s,latestValues:{},source:this.id}}measurePageBox(){let{visualElement:i}=this.options;if(!i)return se();let a=i.measureViewportBox(),{scroll:s}=this.root;return s&&(rt(a.x,s.offset.x),rt(a.y,s.offset.y)),a}removeElementScroll(i){let a=se();Me(a,i);for(let s=0;s<this.path.length;s++){let c=this.path[s],{scroll:l,options:u}=c;if(c!==this.root&&l&&u.layoutScroll){if(l.isRoot){Me(a,i);let{scroll:p}=this.root;p&&(rt(a.x,-p.offset.x),rt(a.y,-p.offset.y))}rt(a.x,l.offset.x),rt(a.y,l.offset.y)}}return a}applyTransform(i,a=!1){let s=se();Me(s,i);for(let c=0;c<this.path.length;c++){let l=this.path[c];!a&&l.options.layoutScroll&&l.scroll&&l!==l.root&&kt(s,{x:-l.scroll.offset.x,y:-l.scroll.offset.y}),vt(l.latestValues)&&kt(s,l.latestValues)}return vt(this.latestValues)&&kt(s,this.latestValues),s}removeTransform(i){let a=se();Me(a,i);for(let s=0;s<this.path.length;s++){let c=this.path[s];if(!c.instance||!vt(c.latestValues))continue;ao(c.latestValues)&&c.updateSnapshot();let l=se(),u=c.measurePageBox();Me(l,u),qa(a,c.latestValues,c.snapshot?c.snapshot.layoutBox:void 0,l)}return vt(this.latestValues)&&qa(a,this.latestValues),a}setTargetDelta(i){this.targetDelta=i,this.root.scheduleUpdateProjection(),this.isProjectionDirty=!0}setOptions(i){this.options={...this.options,...i,crossfade:i.crossfade!==void 0?i.crossfade:!0}}clearMeasurements(){this.scroll=void 0,this.layout=void 0,this.snapshot=void 0,this.prevTransformTemplateValue=void 0,this.targetDelta=void 0,this.target=void 0,this.isLayoutDirty=!1}forceRelativeParentToResolveTarget(){this.relativeParent&&this.relativeParent.resolvedRelativeTargetAt!==de.timestamp&&this.relativeParent.resolveTargetDelta(!0)}resolveTargetDelta(i=!1){var a;let s=this.getLead();this.isProjectionDirty||(this.isProjectionDirty=s.isProjectionDirty),this.isTransformDirty||(this.isTransformDirty=s.isTransformDirty),this.isSharedProjectionDirty||(this.isSharedProjectionDirty=s.isSharedProjectionDirty);let c=!!this.resumingFrom||this!==s;if(!(i||c&&this.isSharedProjectionDirty||this.isProjectionDirty||!((a=this.parent)===null||a===void 0)&&a.isProjectionDirty||this.attemptToResolveRelativeTarget))return;let{layout:l,layoutId:u}=this.options;if(!(!this.layout||!(l||u))){if(this.resolvedRelativeTargetAt=de.timestamp,!this.targetDelta&&!this.relativeTarget){let p=this.getClosestProjectingParent();p&&p.layout&&this.animationProgress!==1?(this.relativeParent=p,this.forceRelativeParentToResolveTarget(),this.relativeTarget=se(),this.relativeTargetOrigin=se(),sr(this.relativeTargetOrigin,this.layout.layoutBox,p.layout.layoutBox),Me(this.relativeTarget,this.relativeTargetOrigin)):this.relativeParent=this.relativeTarget=void 0}if(!(!this.relativeTarget&&!this.targetDelta)){if(this.target||(this.target=se(),this.targetWithTransforms=se()),this.relativeTarget&&this.relativeTargetOrigin&&this.relativeParent&&this.relativeParent.target?(this.forceRelativeParentToResolveTarget(),Zm(this.target,this.relativeTarget,this.relativeParent.target)):this.targetDelta?(this.resumingFrom?this.target=this.applyTransform(this.layout.layoutBox):Me(this.target,this.layout.layoutBox),tc(this.target,this.targetDelta)):Me(this.target,this.layout.layoutBox),this.attemptToResolveRelativeTarget){this.attemptToResolveRelativeTarget=!1;let p=this.getClosestProjectingParent();p&&!!p.resumingFrom==!!this.resumingFrom&&!p.options.layoutScroll&&p.target&&this.animationProgress!==1?(this.relativeParent=p,this.forceRelativeParentToResolveTarget(),this.relativeTarget=se(),this.relativeTargetOrigin=se(),sr(this.relativeTargetOrigin,this.target,p.target),Me(this.relativeTarget,this.relativeTargetOrigin)):this.relativeParent=this.relativeTarget=void 0}bt.resolvedTargetDeltas++}}}getClosestProjectingParent(){if(!(!this.parent||ao(this.parent.latestValues)||ec(this.parent.latestValues)))return this.parent.isProjecting()?this.parent:this.parent.getClosestProjectingParent()}isProjecting(){return!!((this.relativeTarget||this.targetDelta||this.options.layoutRoot)&&this.layout)}calcProjection(){var i;let a=this.getLead(),s=!!this.resumingFrom||this!==a,c=!0;if((this.isProjectionDirty||!((i=this.parent)===null||i===void 0)&&i.isProjectionDirty)&&(c=!1),s&&(this.isSharedProjectionDirty||this.isTransformDirty)&&(c=!1),this.resolvedRelativeTargetAt===de.timestamp&&(c=!1),c)return;let{layout:l,layoutId:u}=this.options;if(this.isTreeAnimating=!!(this.parent&&this.parent.isTreeAnimating||this.currentAnimation||this.pendingAnimation),this.isTreeAnimating||(this.targetDelta=this.relativeTarget=void 0),!this.layout||!(l||u))return;Me(this.layoutCorrected,this.layout.layoutBox);let p=this.treeScale.x,d=this.treeScale.y;sg(this.layoutCorrected,this.treeScale,this.path,s),a.layout&&!a.target&&(this.treeScale.x!==1||this.treeScale.y!==1)&&(a.target=a.layout.layoutBox,a.targetWithTransforms=se());let{target:h}=a;if(!h){this.projectionTransform&&(this.projectionDelta=It(),this.projectionTransform="none",this.scheduleRender());return}this.projectionDelta||(this.projectionDelta=It(),this.projectionDeltaWithTransform=It());let f=this.projectionTransform;ar(this.projectionDelta,this.layoutCorrected,h,this.latestValues),this.projectionTransform=Xa(this.projectionDelta,this.treeScale),(this.projectionTransform!==f||this.treeScale.x!==p||this.treeScale.y!==d)&&(this.hasProjected=!0,this.scheduleRender(),this.notifyListeners("projectionUpdate",h)),bt.recalculatedProjection++}hide(){this.isVisible=!1}show(){this.isVisible=!0}scheduleRender(i=!0){if(this.options.scheduleRender&&this.options.scheduleRender(),i){let a=this.getStack();a&&a.scheduleRender()}this.resumingFrom&&!this.resumingFrom.instance&&(this.resumingFrom=void 0)}setAnimationOrigin(i,a=!1){let s=this.snapshot,c=s?s.latestValues:{},l={...this.latestValues},u=It();(!this.relativeParent||!this.relativeParent.options.layoutRoot)&&(this.relativeTarget=this.relativeTargetOrigin=void 0),this.attemptToResolveRelativeTarget=!a;let p=se(),d=s?s.source:void 0,h=this.layout?this.layout.source:void 0,f=d!==h,y=this.getStack(),x=!y||y.members.length<=1,b=!!(f&&!x&&this.options.crossfade===!0&&!this.path.some(Kg));this.animationProgress=0;let g;this.mixTargetDelta=v=>{let w=v/1e3;$a(u.x,i.x,w),$a(u.y,i.y,w),this.setTargetDelta(u),this.relativeTarget&&this.relativeTargetOrigin&&this.layout&&this.relativeParent&&this.relativeParent.layout&&(sr(p,this.layout.layoutBox,this.relativeParent.layout.layoutBox),Xg(this.relativeTarget,this.relativeTargetOrigin,p,w),g&&Cg(this.relativeTarget,g)&&(this.isProjectionDirty=!1),g||(g=se()),Me(g,this.relativeTarget)),f&&(this.animationValues=l,wg(l,c,this.latestValues,w,b,x)),this.root.scheduleUpdateProjection(),this.scheduleRender(),this.animationProgress=w},this.mixTargetDelta(this.options.layoutRoot?1e3:0)}startAnimation(i){this.notifyListeners("animationStart"),this.currentAnimation&&this.currentAnimation.stop(),this.resumingFrom&&this.resumingFrom.currentAnimation&&this.resumingFrom.currentAnimation.stop(),this.pendingAnimation&&(Ze(this.pendingAnimation),this.pendingAnimation=void 0),this.pendingAnimation=ie.update(()=>{_r.hasAnimatedSinceResize=!0,this.currentAnimation=Lg(0,Qa,{...i,onUpdate:a=>{this.mixTargetDelta(a),i.onUpdate&&i.onUpdate(a)},onComplete:()=>{i.onComplete&&i.onComplete(),this.completeAnimation()}}),this.resumingFrom&&(this.resumingFrom.currentAnimation=this.currentAnimation),this.pendingAnimation=void 0})}completeAnimation(){this.resumingFrom&&(this.resumingFrom.currentAnimation=void 0,this.resumingFrom.preserveOpacity=void 0);let i=this.getStack();i&&i.exitAnimationComplete(),this.resumingFrom=this.currentAnimation=this.animationValues=void 0,this.notifyListeners("animationComplete")}finishAnimation(){this.currentAnimation&&(this.mixTargetDelta&&this.mixTargetDelta(Qa),this.currentAnimation.stop()),this.completeAnimation()}applyTransformsToTarget(){let i=this.getLead(),{targetWithTransforms:a,target:s,layout:c,latestValues:l}=i;if(!(!a||!s||!c)){if(this!==i&&this.layout&&c&&uc(this.options.animationType,this.layout.layoutBox,c.layoutBox)){s=this.target||se();let u=Ae(this.layout.layoutBox.x);s.x.min=i.target.x.min,s.x.max=s.x.min+u;let p=Ae(this.layout.layoutBox.y);s.y.min=i.target.y.min,s.y.max=s.y.min+p}Me(a,s),kt(a,l),ar(this.projectionDeltaWithTransform,this.layoutCorrected,a,l)}}registerSharedNode(i,a){this.sharedNodes.has(i)||this.sharedNodes.set(i,new Ag),this.sharedNodes.get(i).add(a);let s=a.options.initialPromotionConfig;a.promote({transition:s?s.transition:void 0,preserveFollowOpacity:s&&s.shouldPreserveFollowOpacity?s.shouldPreserveFollowOpacity(a):void 0})}isLead(){let i=this.getStack();return i?i.lead===this:!0}getLead(){var i;let{layoutId:a}=this.options;return a?((i=this.getStack())===null||i===void 0?void 0:i.lead)||this:this}getPrevLead(){var i;let{layoutId:a}=this.options;return a?(i=this.getStack())===null||i===void 0?void 0:i.prevLead:void 0}getStack(){let{layoutId:i}=this.options;if(i)return this.root.sharedNodes.get(i)}promote({needsReset:i,transition:a,preserveFollowOpacity:s}={}){let c=this.getStack();c&&c.promote(this,s),i&&(this.projectionDelta=void 0,this.needsReset=!0),a&&this.setOptions({transition:a})}relegate(){let i=this.getStack();return i?i.relegate(this):!1}resetRotation(){let{visualElement:i}=this.options;if(!i)return;let a=!1,{latestValues:s}=i;if((s.rotate||s.rotateX||s.rotateY||s.rotateZ)&&(a=!0),!a)return;let c={};for(let l=0;l<Ka.length;l++){let u="rotate"+Ka[l];s[u]&&(c[u]=s[u],i.setStaticValue(u,0))}i.render();for(let l in c)i.setStaticValue(l,c[l]);i.scheduleRender()}getProjectionStyles(i){var a,s;if(!this.instance||this.isSVG)return;if(!this.isVisible)return Ng;let c={visibility:""},l=this.getTransformTemplate();if(this.needsReset)return this.needsReset=!1,c.opacity="",c.pointerEvents=Br(i?.pointerEvents)||"",c.transform=l?l(this.latestValues,""):"none",c;let u=this.getLead();if(!this.projectionDelta||!this.layout||!u.target){let f={};return this.options.layoutId&&(f.opacity=this.latestValues.opacity!==void 0?this.latestValues.opacity:1,f.pointerEvents=Br(i?.pointerEvents)||""),this.hasProjected&&!vt(this.latestValues)&&(f.transform=l?l({},""):"none",this.hasProjected=!1),f}let p=u.animationValues||u.latestValues;this.applyTransformsToTarget(),c.transform=Xa(this.projectionDeltaWithTransform,this.treeScale,p),l&&(c.transform=l(p,c.transform));let{x:d,y:h}=this.projectionDelta;c.transformOrigin=`${d.origin*100}% ${h.origin*100}% 0`,u.animationValues?c.opacity=u===this?(s=(a=p.opacity)!==null&&a!==void 0?a:this.latestValues.opacity)!==null&&s!==void 0?s:1:this.preserveOpacity?this.latestValues.opacity:p.opacityExit:c.opacity=u===this?p.opacity!==void 0?p.opacity:"":p.opacityExit!==void 0?p.opacityExit:0;for(let f in Yr){if(p[f]===void 0)continue;let{correct:y,applyTo:x}=Yr[f],b=c.transform==="none"?p[f]:y(p[f],u);if(x){let g=x.length;for(let v=0;v<g;v++)c[x[v]]=b}else c[f]=b}return this.options.layoutId&&(c.pointerEvents=u===this?Br(i?.pointerEvents)||"":"none"),c}clearSnapshot(){this.resumeFrom=this.snapshot=void 0}resetTree(){this.root.nodes.forEach(i=>{var a;return(a=i.currentAnimation)===null||a===void 0?void 0:a.stop()}),this.root.nodes.forEach(Ja),this.root.sharedNodes.clear()}}}function Vg(e){e.updateLayout()}function Fg(e){var t;let r=((t=e.resumeFrom)===null||t===void 0?void 0:t.snapshot)||e.snapshot;if(e.isLead()&&e.layout&&r&&e.hasListeners("didUpdate")){let{layoutBox:n,measuredBox:o}=e.layout,{animationType:i}=e.options,a=r.source!==e.layout.source;i==="size"?Le(p=>{let d=a?r.measuredBox[p]:r.layoutBox[p],h=Ae(d);d.min=n[p].min,d.max=d.min+h}):uc(i,r.layoutBox,n)&&Le(p=>{let d=a?r.measuredBox[p]:r.layoutBox[p],h=Ae(n[p]);d.max=d.min+h,e.relativeTarget&&!e.currentAnimation&&(e.isProjectionDirty=!0,e.relativeTarget[p].max=e.relativeTarget[p].min+h)});let s=It();ar(s,n,r.layoutBox);let c=It();a?ar(c,e.applyTransform(o,!0),r.measuredBox):ar(c,n,r.layoutBox);let l=!sc(s),u=!1;if(!e.resumeFrom){let p=e.getClosestProjectingParent();if(p&&!p.resumeFrom){let{snapshot:d,layout:h}=p;if(d&&h){let f=se();sr(f,r.layoutBox,d.layoutBox);let y=se();sr(y,n,h.layoutBox),lc(f,y)||(u=!0),p.options.layoutRoot&&(e.relativeTarget=y,e.relativeTargetOrigin=f,e.relativeParent=p)}}}e.notifyListeners("didUpdate",{layout:n,snapshot:r,delta:c,layoutDelta:s,hasLayoutChanged:l,hasRelativeTargetChanged:u})}else if(e.isLead()){let{onExitComplete:n}=e.options;n&&n()}e.options.transition=void 0}function Bg(e){bt.totalNodes++,e.parent&&(e.isProjecting()||(e.isProjectionDirty=e.parent.isProjectionDirty),e.isSharedProjectionDirty||(e.isSharedProjectionDirty=!!(e.isProjectionDirty||e.parent.isProjectionDirty||e.parent.isSharedProjectionDirty)),e.isTransformDirty||(e.isTransformDirty=e.parent.isTransformDirty))}function zg(e){e.isProjectionDirty=e.isSharedProjectionDirty=e.isTransformDirty=!1}function _g(e){e.clearSnapshot()}function Ja(e){e.clearMeasurements()}function Hg(e){e.isLayoutDirty=!1}function Ug(e){let{visualElement:t}=e.options;t&&t.getProps().onBeforeLayoutMeasure&&t.notify("BeforeLayoutMeasure"),e.resetTransform()}function Za(e){e.finishAnimation(),e.targetDelta=e.relativeTarget=e.target=void 0,e.isProjectionDirty=!0}function Wg(e){e.resolveTargetDelta()}function qg(e){e.calcProjection()}function Yg(e){e.resetRotation()}function Gg(e){e.removeLeadSnapshot()}function $a(e,t,r){e.translate=oe(t.translate,0,r),e.scale=oe(t.scale,1,r),e.origin=t.origin,e.originPoint=t.originPoint}function es(e,t,r,n){e.min=oe(t.min,r.min,n),e.max=oe(t.max,r.max,n)}function Xg(e,t,r,n){es(e.x,t.x,r.x,n),es(e.y,t.y,r.y,n)}function Kg(e){return e.animationValues&&e.animationValues.opacityExit!==void 0}function ns(e){e.min=rs(e.min),e.max=rs(e.max)}function Jg(e){ns(e.x),ns(e.y)}function uc(e,t,r){return e==="position"||e==="preserve-aspect"&&!oo(Ga(t),Ga(r),.2)}function ty(e){let t=ey.exec(e);if(!t)return[,];let[,r,n]=t;return[r,n]}function lo(e,t,r=1){Fe(r<=ry,`Max CSS variable fallback depth detected in property "${e}". This may indicate a circular fallback dependency.`);let[n,o]=ty(e);if(!n)return;let i=window.getComputedStyle(t).getPropertyValue(n);if(i){let a=i.trim();return Gl(a)?parseFloat(a):a}else return Gr(o)?lo(o,t,r+1):o}function ny(e,{...t},r){let n=e.current;if(!(n instanceof Element))return{target:t,transitionEnd:r};r&&(r={...r}),e.values.forEach(o=>{let i=o.get();if(!Gr(i))return;let a=lo(i,n);a&&o.set(a)});for(let o in t){let i=t[o];if(!Gr(i))continue;let a=lo(i,n);a&&(t[o]=a,r||(r={}),r[o]===void 0&&(r[o]=i))}return{target:t,transitionEnd:r}}function ly(e){let t=[];return sy.forEach(r=>{let n=e.getValue(r);n!==void 0&&(t.push([r,n.get()]),n.set(r.startsWith("scale")?1:0))}),t.length&&e.render(),t}function py(e,t,r,n){return iy(t)?uy(e,t,r,n):{target:t,transitionEnd:n}}function hy(){if(hc.current=!0,!!ln)if(window.matchMedia){let e=window.matchMedia("(prefers-reduced-motion)"),t=()=>co.current=e.matches;e.addListener(t),t()}else co.current=!1}function fy(e,t,r){let{willChange:n}=t;for(let o in t){let i=t[o],a=r[o];if(Te(i))e.addValue(o,i),$r(n)&&n.add(o),Wo(i.version==="11.0.6",`Attempting to mix Framer Motion versions ${i.version} with 11.0.6 may not work as expected.`);else if(Te(a))e.addValue(o,Nt(i,{owner:e})),$r(n)&&n.remove(o);else if(a!==i)if(e.hasValue(o)){let s=e.getValue(o);!s.hasAnimated&&s.set(i)}else{let s=e.getStaticValue(o);e.addValue(o,Nt(s!==void 0?s:i,{owner:e}))}}for(let o in r)t[o]===void 0&&e.removeValue(o);return t}function vy(e){return window.getComputedStyle(e)}function Vy(){let[e,t]=xe(null);return ue(()=>{(async()=>{try{let r=(await(await fetch("/index.json")).json()).entries["example-button--primary"].importPath;t({data:r,error:null})}catch(r){t({data:null,error:r})}})()},[]),e}function Xy(){let[e,t]=xe({data:null,error:null});return ue(()=>{(async()=>{try{let r=(await(await fetch("/project.json")).json())?.language==="javascript"?qy:Gy;t({data:r,error:null})}catch(r){t({data:null,error:r})}})()},[]),e}function Qy({api:e}){let[t,r]=xe(!0),[n,o]=xe(!1),[i,a]=xe("1:Welcome"),{data:s}=Xy(),c=Ut(()=>{let l=new URL(window.location.href),u=decodeURIComponent(l.searchParams.get("path"));l.search=`?path=${u}&onboarding=false`,history.replaceState({},"",l.href),e.setQueryParams({onboarding:"false"}),r(!1)},[r,e]);return ue(()=>{e.emit(nr,{step:"1:Welcome",type:"telemetry"})},[]),ue(()=>{i!=="1:Welcome"&&e.emit(nr,{step:i,type:"telemetry"})},[e,i]),ue(()=>{let l;return i==="4:VisitNewStory"&&(o(!0),l=window.setTimeout(()=>{a("5:ConfigureYourProject")},2e3)),()=>{clearTimeout(l)}},[i]),ue(()=>{let l=e.getCurrentStoryData()?.id;if(e.setQueryParams({onboarding:"true"}),l!=="example-button--primary")try{e.selectStory("example-button--primary",void 0,{ref:void 0})}catch{}},[]),t?m.createElement(xn,{theme:Ky},t&&n&&m.createElement(Js,{numberOfPieces:800,recycle:!1,tweenDuration:2e4,onConfettiComplete:l=>{l?.reset(),o(!1)}}),t&&i==="1:Welcome"&&m.createElement(eh,{onProceed:()=>{a("2:StorybookTour")},skipOnboarding:()=>{c(),e.emit(nr,{step:"X:SkippedOnboarding",where:"WelcomeModal",type:"telemetry"})}}),t&&(i==="2:StorybookTour"||i==="5:ConfigureYourProject")&&m.createElement(Fd,{api:e,isFinalStep:i==="5:ConfigureYourProject",onFirstTourDone:()=>{a("3:WriteYourStory")},codeSnippets:s||void 0,onLastTourDone:()=>{try{e.selectStory("configure-your-project--docs")}catch{}e.emit(nr,{step:"6:FinishedOnboarding",type:"telemetry"}),c()}}),t&&i==="3:WriteYourStory"&&s&&m.createElement(Uy,{api:e,codeSnippets:s,addonsStore:Cr,onFinish:()=>{e.selectStory("example-button--warning"),a("4:VisitNewStory")},skipOnboarding:c})):null}var Dc,tn,Lc,uo,Nc,jc,ls,Re,Vc,cs,pt,Fc,Bc,zc,po,_c,us,Hc,ps,ds,Uc,Wc,qc,Yc,Gc,hs,Kc,fs,Ei,Si,ms,Qc,Jc,Zc,Pi,Oi,$c,ou,iu,lu,A,du,vs,Ir,An,S,dr,mu,vu,bu,Mi,Di,Tu,Eu,Lt,Ne,Vu,Ls,Rn,In,Qu,Ju,nn,Vi,Xn,Zu,$u,ep,k,np,op,Fi,Bi,ip,Kn,vp,bp,xp,K,Qt,Bs,zs,Ap,Us,Ws,Rp,kp,Mp,xo,Dp,Lp,Np,I,J,Ve,B,W,Jt,Yp,Gs,Gp,Xp,Kp,Zt,Ji,Ks,$i,Zp,ed,td,nd,od,id,sd,cd,ud,pd,Qs,dd,fd,yd,De,vd,bd,xd,wd,Td,Ed,Sd,Pd,Od,Cd,Ad,Rd,Fr,Mn,Id,kd,Md,Dd,Ld,Nd,jd,Mr,Vd,zd,_d,Hd,Ud,Wd,qd,Yd,Gd,Xd,Kd,Qd,Jd,Zd,$d,eh,ea,oh,ih,$s,sn,wo,ln,ah,el,To,sh,tl,rl,ta,Dr,ch,Eo,US,So,Po,na,ur,il,al,mh,bh,Yr,xr,St,Te,wh,Th,ll,cl,Sh,Gr,Ph,Oh,ct,zt,or,Lr,ir,Co,Ch,Ah,Tr,tt,qe,L,Rh,Ih,oa,ia,ul,Ro,Lh,dl,Fh,Bh,hl,ko,ml,Kr,Wh,qh,vl,le,ie,Ze,de,Dn,Xh,Kh,bl,Jh,Zh,Qe,sa,la,dt,$h,ef,El,tf,Jn,Nn,rf,nf,sf,lf,uf,Er,Fe,at,Je,hf,Pl,rr,Cl,Rl,gf,yf,bf,xf,Il,wf,kl,Ml,Lo,Dl,Tf,Ll,No,Ef,Sf,ua,pa,pr,oe,jo,Nl,Of,Vn,wt,Zn,Rt,Fn,Af,Rf,ha,me,jl,Vl,kf,Mf,fa,ma,Df,ut,Ff,qf,Bn,Yf,ga,Gf,Xf,Qf,Zf,$f,zr,Tt,rm,nm,om,am,sm,Nr,lm,cm,dm,hm,fm,mm,gm,to,ym,bm,ro,xm,Fo,Yl,zo,Gl,Uo,xa,wa,Sm,Pm,Xl,Om,Kl,er,Cm,Am,Bm,zm,qm,Ym,Gm,Xm,Ta,Jl,io,ka,It,Ma,se,lg,cg,nc,pg,dg,fg,Va,mg,_r,tr,yg,vg,bg,ic,xg,Ba,za,Tg,Eg,Pg,Og,Ag,Rg,Ig,Ka,Ng,Qa,jg,bt,Qg,ts,rs,Zg,Un,pc,$g,ey,ry,oy,dc,iy,Vr,os,is,ay,sy,jt,cy,uy,dy,co,hc,as,fc,my,ss,gy,yy,mc,by,xy,wy,Ty,Ey,_t,Sy,Py,Oy,Cy,Ay,Ry,Iy,ky,My,Dy,Ly,Ny,jy,Wn,Fy,By,zy,_y,Hy,nr,Uy,Wy,qy,Yy,Gy,Ky,yc=ke(()=>{Z();$();ee();Wt();Wt();si();yn();Pr();Pr();bn();gi();yi();Ti();Dc=Object.create,tn=Object.defineProperty,Lc=Object.getOwnPropertyDescriptor,uo=Object.getOwnPropertyNames,Nc=Object.getPrototypeOf,jc=Object.prototype.hasOwnProperty,ls=(e,t)=>function(){return e&&(t=(0,e[uo(e)[0]])(e=0)),t},Re=(e,t)=>function(){return t||(0,e[uo(e)[0]])((t={exports:{}}).exports,t),t.exports},Vc=(e,t)=>{for(var r in t)tn(e,r,{get:t[r],enumerable:!0})},cs=(e,t,r,n)=>{if(t&&typeof t=="object"||typeof t=="function")for(let o of uo(t))!jc.call(e,o)&&o!==r&&tn(e,o,{get:()=>t[o],enumerable:!(n=Lc(t,o))||n.enumerable});return e},pt=(e,t,r)=>(r=e!=null?Dc(Nc(e)):{},cs(t||!e||!e.__esModule?tn(r,"default",{value:e,enumerable:!0}):r,e)),Fc=e=>cs(tn({},"__esModule",{value:!0}),e),Bc=Re({"../../node_modules/scroll/index.js"(e,t){var r=new Error("Element already at target scroll position"),n=new Error("Scroll cancelled"),o=Math.min,i=Date.now;t.exports={left:a("scrollLeft"),top:a("scrollTop")};function a(l){return function(u,p,d,h){d=d||{},typeof d=="function"&&(h=d,d={}),typeof h!="function"&&(h=c);var f=i(),y=u[l],x=d.ease||s,b=isNaN(d.duration)?350:+d.duration,g=!1;return y===p?h(r,u[l]):requestAnimationFrame(w),v;function v(){g=!0}function w(T){if(g)return h(n,u[l]);var F=i(),M=o(1,(F-f)/b),D=x(M);u[l]=D*(p-y)+y,M<1?requestAnimationFrame(w):requestAnimationFrame(function(){h(null,u[l])})}}}function s(l){return .5*(1-Math.cos(Math.PI*l))}function c(){}}}),zc=Re({"../../node_modules/scrollparent/scrollparent.js"(e,t){(function(r,n){typeof define=="function"&&define.amd?define([],n):typeof t=="object"&&t.exports?t.exports=n():r.Scrollparent=n()})(e,function(){function r(o){var i=getComputedStyle(o,null).getPropertyValue("overflow");return i.indexOf("scroll")>-1||i.indexOf("auto")>-1}function n(o){if(o instanceof HTMLElement||o instanceof SVGElement){for(var i=o.parentNode;i.parentNode;){if(r(i))return i;i=i.parentNode}return document.scrollingElement||document.documentElement}}return n})}}),po=Re({"../../node_modules/deepmerge/dist/cjs.js"(e,t){var r=function(v){return n(v)&&!o(v)};function n(v){return!!v&&typeof v=="object"}function o(v){var w=Object.prototype.toString.call(v);return w==="[object RegExp]"||w==="[object Date]"||s(v)}var i=typeof Symbol=="function"&&Symbol.for,a=i?Symbol.for("react.element"):60103;function s(v){return v.$$typeof===a}function c(v){return Array.isArray(v)?[]:{}}function l(v,w){return w.clone!==!1&&w.isMergeableObject(v)?b(c(v),v,w):v}function u(v,w,T){return v.concat(w).map(function(F){return l(F,T)})}function p(v,w){if(!w.customMerge)return b;var T=w.customMerge(v);return typeof T=="function"?T:b}function d(v){return Object.getOwnPropertySymbols?Object.getOwnPropertySymbols(v).filter(function(w){return Object.propertyIsEnumerable.call(v,w)}):[]}function h(v){return Object.keys(v).concat(d(v))}function f(v,w){try{return w in v}catch{return!1}}function y(v,w){return f(v,w)&&!(Object.hasOwnProperty.call(v,w)&&Object.propertyIsEnumerable.call(v,w))}function x(v,w,T){var F={};return T.isMergeableObject(v)&&h(v).forEach(function(M){F[M]=l(v[M],T)}),h(w).forEach(function(M){y(v,M)||(f(v,M)&&T.isMergeableObject(w[M])?F[M]=p(M,T)(v[M],w[M],T):F[M]=l(w[M],T))}),F}function b(v,w,T){T=T||{},T.arrayMerge=T.arrayMerge||u,T.isMergeableObject=T.isMergeableObject||r,T.cloneUnlessOtherwiseSpecified=l;var F=Array.isArray(w),M=Array.isArray(v),D=F===M;return D?F?T.arrayMerge(v,w,T):x(v,w,T):l(w,T)}b.all=function(v,w){if(!Array.isArray(v))throw new Error("first argument should be an array");return v.reduce(function(T,F){return b(T,F,w)},{})};var g=b;t.exports=g}}),_c=Re({"../../node_modules/react-is/cjs/react-is.development.js"(e){(function(){var t=typeof Symbol=="function"&&Symbol.for,r=t?Symbol.for("react.element"):60103,n=t?Symbol.for("react.portal"):60106,o=t?Symbol.for("react.fragment"):60107,i=t?Symbol.for("react.strict_mode"):60108,a=t?Symbol.for("react.profiler"):60114,s=t?Symbol.for("react.provider"):60109,c=t?Symbol.for("react.context"):60110,l=t?Symbol.for("react.async_mode"):60111,u=t?Symbol.for("react.concurrent_mode"):60111,p=t?Symbol.for("react.forward_ref"):60112,d=t?Symbol.for("react.suspense"):60113,h=t?Symbol.for("react.suspense_list"):60120,f=t?Symbol.for("react.memo"):60115,y=t?Symbol.for("react.lazy"):60116,x=t?Symbol.for("react.block"):60121,b=t?Symbol.for("react.fundamental"):60117,g=t?Symbol.for("react.responder"):60118,v=t?Symbol.for("react.scope"):60119;function w(P){return typeof P=="string"||typeof P=="function"||P===o||P===u||P===a||P===i||P===d||P===h||typeof P=="object"&&P!==null&&(P.$$typeof===y||P.$$typeof===f||P.$$typeof===s||P.$$typeof===c||P.$$typeof===p||P.$$typeof===b||P.$$typeof===g||P.$$typeof===v||P.$$typeof===x)}function T(P){if(typeof P=="object"&&P!==null){var fe=P.$$typeof;switch(fe){case r:var Ye=P.type;switch(Ye){case l:case u:case o:case a:case i:case d:return Ye;default:var qo=Ye&&Ye.$$typeof;switch(qo){case c:case p:case y:case f:case s:return qo;default:return fe}}case n:return fe}}}var F=l,M=u,D=c,V=s,ne=r,pe=p,Ie=o,Ee=y,ce=f,ae=n,ye=a,_e=i,he=d,je=!1;function Pt(P){return je||(je=!0,console.warn("The ReactIs.isAsyncMode() alias has been deprecated, and will be removed in React 17+. Update your code to use ReactIs.isConcurrentMode() instead. It has the exact same API.")),te(P)||T(P)===l}function te(P){return T(P)===u}function E(P){return T(P)===c}function R(P){return T(P)===s}function U(P){return typeof P=="object"&&P!==null&&P.$$typeof===r}function _(P){return T(P)===p}function N(P){return T(P)===o}function G(P){return T(P)===y}function H(P){return T(P)===f}function z(P){return T(P)===n}function q(P){return T(P)===a}function Y(P){return T(P)===i}function Q(P){return T(P)===d}e.AsyncMode=F,e.ConcurrentMode=M,e.ContextConsumer=D,e.ContextProvider=V,e.Element=ne,e.ForwardRef=pe,e.Fragment=Ie,e.Lazy=Ee,e.Memo=ce,e.Portal=ae,e.Profiler=ye,e.StrictMode=_e,e.Suspense=he,e.isAsyncMode=Pt,e.isConcurrentMode=te,e.isContextConsumer=E,e.isContextProvider=R,e.isElement=U,e.isForwardRef=_,e.isFragment=N,e.isLazy=G,e.isMemo=H,e.isPortal=z,e.isProfiler=q,e.isStrictMode=Y,e.isSuspense=Q,e.isValidElementType=w,e.typeOf=T})()}}),us=Re({"../../node_modules/react-is/index.js"(e,t){t.exports=_c()}}),Hc=Re({"../../node_modules/object-assign/index.js"(e,t){var r=Object.getOwnPropertySymbols,n=Object.prototype.hasOwnProperty,o=Object.prototype.propertyIsEnumerable;function i(s){if(s==null)throw new TypeError("Object.assign cannot be called with null or undefined");return Object(s)}function a(){try{if(!Object.assign)return!1;var s=new String("abc");if(s[5]="de",Object.getOwnPropertyNames(s)[0]==="5")return!1;for(var c={},l=0;l<10;l++)c["_"+String.fromCharCode(l)]=l;var u=Object.getOwnPropertyNames(c).map(function(d){return c[d]});if(u.join("")!=="**********")return!1;var p={};return"abcdefghijklmnopqrst".split("").forEach(function(d){p[d]=d}),Object.keys(Object.assign({},p)).join("")==="abcdefghijklmnopqrst"}catch{return!1}}t.exports=a()?Object.assign:function(s,c){for(var l,u=i(s),p,d=1;d<arguments.length;d++){l=Object(arguments[d]);for(var h in l)n.call(l,h)&&(u[h]=l[h]);if(r){p=r(l);for(var f=0;f<p.length;f++)o.call(l,p[f])&&(u[p[f]]=l[p[f]])}}return u}}}),ps=Re({"../../node_modules/prop-types/lib/ReactPropTypesSecret.js"(e,t){var r="SECRET_DO_NOT_PASS_THIS_OR_YOU_WILL_BE_FIRED";t.exports=r}}),ds=Re({"../../node_modules/prop-types/lib/has.js"(e,t){t.exports=Function.call.bind(Object.prototype.hasOwnProperty)}}),Uc=Re({"../../node_modules/prop-types/checkPropTypes.js"(e,t){var r=function(){};n=ps(),o={},i=ds(),r=function(s){var c="Warning: "+s;typeof console<"u"&&console.error(c);try{throw new Error(c)}catch{}};var n,o,i;function a(s,c,l,u,p){for(var d in s)if(i(s,d)){var h;try{if(typeof s[d]!="function"){var f=Error((u||"React class")+": "+l+" type `"+d+"` is invalid; it must be a function, usually from the `prop-types` package, but received `"+typeof s[d]+"`.This often happens because of typos such as `PropTypes.function` instead of `PropTypes.func`.");throw f.name="Invariant Violation",f}h=s[d](c,d,u,l,null,n)}catch(x){h=x}if(h&&!(h instanceof Error)&&r((u||"React class")+": type specification of "+l+" `"+d+"` is invalid; the type checker function must return `null` or an `Error` but returned a "+typeof h+". You may have forgotten to pass an argument to the type checker creator (arrayOf, instanceOf, objectOf, oneOf, oneOfType, and shape all require an argument)."),h instanceof Error&&!(h.message in o)){o[h.message]=!0;var y=p?p():"";r("Failed "+l+" type: "+h.message+(y??""))}}}a.resetWarningCache=function(){o={}},t.exports=a}}),Wc=Re({"../../node_modules/prop-types/factoryWithTypeCheckers.js"(e,t){var r=us(),n=Hc(),o=ps(),i=ds(),a=Uc(),s=function(){};s=function(l){var u="Warning: "+l;typeof console<"u"&&console.error(u);try{throw new Error(u)}catch{}};function c(){return null}t.exports=function(l,u){var p=typeof Symbol=="function"&&Symbol.iterator,d="@@iterator";function h(E){var R=E&&(p&&E[p]||E[d]);if(typeof R=="function")return R}var f="<<anonymous>>",y={array:v("array"),bigint:v("bigint"),bool:v("boolean"),func:v("function"),number:v("number"),object:v("object"),string:v("string"),symbol:v("symbol"),any:w(),arrayOf:T,element:F(),elementType:M(),instanceOf:D,node:Ie(),objectOf:ne,oneOf:V,oneOfType:pe,shape:ce,exact:ae};function x(E,R){return E===R?E!==0||1/E===1/R:E!==E&&R!==R}function b(E,R){this.message=E,this.data=R&&typeof R=="object"?R:{},this.stack=""}b.prototype=Error.prototype;function g(E){var R={},U=0;function _(G,H,z,q,Y,Q,P){if(q=q||f,Q=Q||z,P!==o){if(u){var fe=new Error("Calling PropTypes validators directly is not supported by the `prop-types` package. Use `PropTypes.checkPropTypes()` to call them. Read more at http://fb.me/use-check-prop-types");throw fe.name="Invariant Violation",fe}else if(typeof console<"u"){var Ye=q+":"+z;!R[Ye]&&U<3&&(s("You are manually calling a React.PropTypes validation function for the `"+Q+"` prop on `"+q+"`. This is deprecated and will throw in the standalone `prop-types` package. You may be seeing this warning due to a third-party PropTypes library. See https://fb.me/react-warning-dont-call-proptypes for details."),R[Ye]=!0,U++)}}return H[z]==null?G?H[z]===null?new b("The "+Y+" `"+Q+"` is marked as required "+("in `"+q+"`, but its value is `null`.")):new b("The "+Y+" `"+Q+"` is marked as required in "+("`"+q+"`, but its value is `undefined`.")):null:E(H,z,q,Y,Q)}var N=_.bind(null,!1);return N.isRequired=_.bind(null,!0),N}function v(E){function R(U,_,N,G,H,z){var q=U[_],Y=he(q);if(Y!==E){var Q=je(q);return new b("Invalid "+G+" `"+H+"` of type "+("`"+Q+"` supplied to `"+N+"`, expected ")+("`"+E+"`."),{expectedType:E})}return null}return g(R)}function w(){return g(c)}function T(E){function R(U,_,N,G,H){if(typeof E!="function")return new b("Property `"+H+"` of component `"+N+"` has invalid PropType notation inside arrayOf.");var z=U[_];if(!Array.isArray(z)){var q=he(z);return new b("Invalid "+G+" `"+H+"` of type "+("`"+q+"` supplied to `"+N+"`, expected an array."))}for(var Y=0;Y<z.length;Y++){var Q=E(z,Y,N,G,H+"["+Y+"]",o);if(Q instanceof Error)return Q}return null}return g(R)}function F(){function E(R,U,_,N,G){var H=R[U];if(!l(H)){var z=he(H);return new b("Invalid "+N+" `"+G+"` of type "+("`"+z+"` supplied to `"+_+"`, expected a single ReactElement."))}return null}return g(E)}function M(){function E(R,U,_,N,G){var H=R[U];if(!r.isValidElementType(H)){var z=he(H);return new b("Invalid "+N+" `"+G+"` of type "+("`"+z+"` supplied to `"+_+"`, expected a single ReactElement type."))}return null}return g(E)}function D(E){function R(U,_,N,G,H){if(!(U[_]instanceof E)){var z=E.name||f,q=te(U[_]);return new b("Invalid "+G+" `"+H+"` of type "+("`"+q+"` supplied to `"+N+"`, expected ")+("instance of `"+z+"`."))}return null}return g(R)}function V(E){if(!Array.isArray(E))return arguments.length>1?s("Invalid arguments supplied to oneOf, expected an array, got "+arguments.length+" arguments. A common mistake is to write oneOf(x, y, z) instead of oneOf([x, y, z])."):s("Invalid argument supplied to oneOf, expected an array."),c;function R(U,_,N,G,H){for(var z=U[_],q=0;q<E.length;q++)if(x(z,E[q]))return null;var Y=JSON.stringify(E,function(Q,P){var fe=je(P);return fe==="symbol"?String(P):P});return new b("Invalid "+G+" `"+H+"` of value `"+String(z)+"` "+("supplied to `"+N+"`, expected one of "+Y+"."))}return g(R)}function ne(E){function R(U,_,N,G,H){if(typeof E!="function")return new b("Property `"+H+"` of component `"+N+"` has invalid PropType notation inside objectOf.");var z=U[_],q=he(z);if(q!=="object")return new b("Invalid "+G+" `"+H+"` of type "+("`"+q+"` supplied to `"+N+"`, expected an object."));for(var Y in z)if(i(z,Y)){var Q=E(z,Y,N,G,H+"."+Y,o);if(Q instanceof Error)return Q}return null}return g(R)}function pe(E){if(!Array.isArray(E))return s("Invalid argument supplied to oneOfType, expected an instance of array."),c;for(var R=0;R<E.length;R++){var U=E[R];if(typeof U!="function")return s("Invalid argument supplied to oneOfType. Expected an array of check functions, but received "+Pt(U)+" at index "+R+"."),c}function _(N,G,H,z,q){for(var Y=[],Q=0;Q<E.length;Q++){var P=E[Q],fe=P(N,G,H,z,q,o);if(fe==null)return null;fe.data&&i(fe.data,"expectedType")&&Y.push(fe.data.expectedType)}var Ye=Y.length>0?", expected one of type ["+Y.join(", ")+"]":"";return new b("Invalid "+z+" `"+q+"` supplied to "+("`"+H+"`"+Ye+"."))}return g(_)}function Ie(){function E(R,U,_,N,G){return ye(R[U])?null:new b("Invalid "+N+" `"+G+"` supplied to "+("`"+_+"`, expected a ReactNode."))}return g(E)}function Ee(E,R,U,_,N){return new b((E||"React class")+": "+R+" type `"+U+"."+_+"` is invalid; it must be a function, usually from the `prop-types` package, but received `"+N+"`.")}function ce(E){function R(U,_,N,G,H){var z=U[_],q=he(z);if(q!=="object")return new b("Invalid "+G+" `"+H+"` of type `"+q+"` "+("supplied to `"+N+"`, expected `object`."));for(var Y in E){var Q=E[Y];if(typeof Q!="function")return Ee(N,G,H,Y,je(Q));var P=Q(z,Y,N,G,H+"."+Y,o);if(P)return P}return null}return g(R)}function ae(E){function R(U,_,N,G,H){var z=U[_],q=he(z);if(q!=="object")return new b("Invalid "+G+" `"+H+"` of type `"+q+"` "+("supplied to `"+N+"`, expected `object`."));var Y=n({},U[_],E);for(var Q in Y){var P=E[Q];if(i(E,Q)&&typeof P!="function")return Ee(N,G,H,Q,je(P));if(!P)return new b("Invalid "+G+" `"+H+"` key `"+Q+"` supplied to `"+N+"`.\nBad object: "+JSON.stringify(U[_],null,"  ")+`
Valid keys: `+JSON.stringify(Object.keys(E),null,"  "));var fe=P(z,Q,N,G,H+"."+Q,o);if(fe)return fe}return null}return g(R)}function ye(E){switch(typeof E){case"number":case"string":case"undefined":return!0;case"boolean":return!E;case"object":if(Array.isArray(E))return E.every(ye);if(E===null||l(E))return!0;var R=h(E);if(R){var U=R.call(E),_;if(R!==E.entries){for(;!(_=U.next()).done;)if(!ye(_.value))return!1}else for(;!(_=U.next()).done;){var N=_.value;if(N&&!ye(N[1]))return!1}}else return!1;return!0;default:return!1}}function _e(E,R){return E==="symbol"?!0:R?R["@@toStringTag"]==="Symbol"||typeof Symbol=="function"&&R instanceof Symbol:!1}function he(E){var R=typeof E;return Array.isArray(E)?"array":E instanceof RegExp?"object":_e(R,E)?"symbol":R}function je(E){if(typeof E>"u"||E===null)return""+E;var R=he(E);if(R==="object"){if(E instanceof Date)return"date";if(E instanceof RegExp)return"regexp"}return R}function Pt(E){var R=je(E);switch(R){case"array":case"object":return"an "+R;case"boolean":case"date":case"regexp":return"a "+R;default:return R}}function te(E){return!E.constructor||!E.constructor.name?f:E.constructor.name}return y.checkPropTypes=a,y.resetWarningCache=a.resetWarningCache,y.PropTypes=y,y}}}),qc=Re({"../../node_modules/prop-types/index.js"(e,t){r=us(),n=!0,t.exports=Wc()(r.isElement,n);var r,n}}),Yc=Re({"../../node_modules/react-innertext/index.js"(e,t){var r=function(i){return Object.prototype.hasOwnProperty.call(i,"props")},n=function(i,a){return i+o(a)},o=function(i){return i===null||typeof i=="boolean"||typeof i>"u"?"":typeof i=="number"?i.toString():typeof i=="string"?i:Array.isArray(i)?i.reduce(n,""):r(i)&&Object.prototype.hasOwnProperty.call(i.props,"children")?o(i.props.children):""};o.default=o,t.exports=o}}),Gc=Re({"../../node_modules/debounce/index.js"(e,t){function r(n,o,i){var a,s,c,l,u;o==null&&(o=100);function p(){var h=Date.now()-l;h<o&&h>=0?a=setTimeout(p,o-h):(a=null,i||(u=n.apply(c,s),c=s=null))}var d=function(){c=this,s=arguments,l=Date.now();var h=i&&!a;return a||(a=setTimeout(p,o)),h&&(u=n.apply(c,s),c=s=null),u};return d.clear=function(){a&&(clearTimeout(a),a=null)},d.flush=function(){a&&(u=n.apply(c,s),c=s=null,clearTimeout(a),a=null)},d}r.debounce=r,t.exports=r}});Kc=ls({"../../node_modules/framer-motion/node_modules/@emotion/memoize/dist/memoize.browser.esm.js"(){hs=Xc}}),fs={};Vc(fs,{default:()=>ms});Qc=ls({"../../node_modules/framer-motion/node_modules/@emotion/is-prop-valid/dist/is-prop-valid.browser.esm.js"(){Kc(),Ei=/^((children|dangerouslySetInnerHTML|key|ref|autoFocus|defaultValue|defaultChecked|innerHTML|suppressContentEditableWarning|suppressHydrationWarning|valueLink|accept|acceptCharset|accessKey|action|allow|allowUserMedia|allowPaymentRequest|allowFullScreen|allowTransparency|alt|async|autoComplete|autoPlay|capture|cellPadding|cellSpacing|challenge|charSet|checked|cite|classID|className|cols|colSpan|content|contentEditable|contextMenu|controls|controlsList|coords|crossOrigin|data|dateTime|decoding|default|defer|dir|disabled|disablePictureInPicture|download|draggable|encType|form|formAction|formEncType|formMethod|formNoValidate|formTarget|frameBorder|headers|height|hidden|high|href|hrefLang|htmlFor|httpEquiv|id|inputMode|integrity|is|keyParams|keyType|kind|label|lang|list|loading|loop|low|marginHeight|marginWidth|max|maxLength|media|mediaGroup|method|min|minLength|multiple|muted|name|nonce|noValidate|open|optimum|pattern|placeholder|playsInline|poster|preload|profile|radioGroup|readOnly|referrerPolicy|rel|required|reversed|role|rows|rowSpan|sandbox|scope|scoped|scrolling|seamless|selected|shape|size|sizes|slot|span|spellCheck|src|srcDoc|srcLang|srcSet|start|step|style|summary|tabIndex|target|title|type|useMap|value|width|wmode|wrap|about|datatype|inlist|prefix|property|resource|typeof|vocab|autoCapitalize|autoCorrect|autoSave|color|inert|itemProp|itemScope|itemType|itemID|itemRef|on|results|security|unselectable|accentHeight|accumulate|additive|alignmentBaseline|allowReorder|alphabetic|amplitude|arabicForm|ascent|attributeName|attributeType|autoReverse|azimuth|baseFrequency|baselineShift|baseProfile|bbox|begin|bias|by|calcMode|capHeight|clip|clipPathUnits|clipPath|clipRule|colorInterpolation|colorInterpolationFilters|colorProfile|colorRendering|contentScriptType|contentStyleType|cursor|cx|cy|d|decelerate|descent|diffuseConstant|direction|display|divisor|dominantBaseline|dur|dx|dy|edgeMode|elevation|enableBackground|end|exponent|externalResourcesRequired|fill|fillOpacity|fillRule|filter|filterRes|filterUnits|floodColor|floodOpacity|focusable|fontFamily|fontSize|fontSizeAdjust|fontStretch|fontStyle|fontVariant|fontWeight|format|from|fr|fx|fy|g1|g2|glyphName|glyphOrientationHorizontal|glyphOrientationVertical|glyphRef|gradientTransform|gradientUnits|hanging|horizAdvX|horizOriginX|ideographic|imageRendering|in|in2|intercept|k|k1|k2|k3|k4|kernelMatrix|kernelUnitLength|kerning|keyPoints|keySplines|keyTimes|lengthAdjust|letterSpacing|lightingColor|limitingConeAngle|local|markerEnd|markerMid|markerStart|markerHeight|markerUnits|markerWidth|mask|maskContentUnits|maskUnits|mathematical|mode|numOctaves|offset|opacity|operator|order|orient|orientation|origin|overflow|overlinePosition|overlineThickness|panose1|paintOrder|pathLength|patternContentUnits|patternTransform|patternUnits|pointerEvents|points|pointsAtX|pointsAtY|pointsAtZ|preserveAlpha|preserveAspectRatio|primitiveUnits|r|radius|refX|refY|renderingIntent|repeatCount|repeatDur|requiredExtensions|requiredFeatures|restart|result|rotate|rx|ry|scale|seed|shapeRendering|slope|spacing|specularConstant|specularExponent|speed|spreadMethod|startOffset|stdDeviation|stemh|stemv|stitchTiles|stopColor|stopOpacity|strikethroughPosition|strikethroughThickness|string|stroke|strokeDasharray|strokeDashoffset|strokeLinecap|strokeLinejoin|strokeMiterlimit|strokeOpacity|strokeWidth|surfaceScale|systemLanguage|tableValues|targetX|targetY|textAnchor|textDecoration|textRendering|textLength|to|transform|u1|u2|underlinePosition|underlineThickness|unicode|unicodeBidi|unicodeRange|unitsPerEm|vAlphabetic|vHanging|vIdeographic|vMathematical|values|vectorEffect|version|vertAdvY|vertOriginX|vertOriginY|viewBox|viewTarget|visibility|widths|wordSpacing|writingMode|x|xHeight|x1|x2|xChannelSelector|xlinkActuate|xlinkArcrole|xlinkHref|xlinkRole|xlinkShow|xlinkTitle|xlinkType|xmlBase|xmlns|xmlnsXlink|xmlLang|xmlSpace|y|y1|y2|yChannelSelector|z|zoomAndPan|for|class|autofocus)|(([Dd][Aa][Tt][Aa]|[Aa][Rr][Ii][Aa]|x)-.*))$/,Si=hs(function(e){return Ei.test(e)||e.charCodeAt(0)===111&&e.charCodeAt(1)===110&&e.charCodeAt(2)<91}),ms=Si}});Jc=gs("function"),Zc=e=>e===null,Pi=e=>Object.prototype.toString.call(e).slice(8,-1)==="RegExp",Oi=e=>!$c(e)&&!Zc(e)&&(Jc(e)||typeof e=="object"),$c=gs("undefined");ou=["Array","ArrayBuffer","AsyncFunction","AsyncGenerator","AsyncGeneratorFunction","Date","Error","Function","Generator","GeneratorFunction","HTMLElement","Map","Object","Promise","RegExp","Set","WeakMap","WeakSet"],iu=["bigint","boolean","null","number","string","symbol","undefined"];lu=["innerHTML","ownerDocument","style","attributes","nodeValue"];O.array=Array.isArray;O.arrayOf=(e,t)=>!O.array(e)&&!O.function(t)?!1:e.every(r=>t(r));O.asyncGeneratorFunction=e=>rn(e)==="AsyncGeneratorFunction";O.asyncFunction=Be("AsyncFunction");O.bigint=Vt("bigint");O.boolean=e=>e===!0||e===!1;O.date=Be("Date");O.defined=e=>!O.undefined(e);O.domElement=e=>O.object(e)&&!O.plainObject(e)&&e.nodeType===1&&O.string(e.nodeName)&&lu.every(t=>t in e);O.empty=e=>O.string(e)&&e.length===0||O.array(e)&&e.length===0||O.object(e)&&!O.map(e)&&!O.set(e)&&Object.keys(e).length===0||O.set(e)&&e.size===0||O.map(e)&&e.size===0;O.error=Be("Error");O.function=Vt("function");O.generator=e=>O.iterable(e)&&O.function(e.next)&&O.function(e.throw);O.generatorFunction=Be("GeneratorFunction");O.instanceOf=(e,t)=>!e||!t?!1:Object.getPrototypeOf(e)===t.prototype;O.iterable=e=>!O.nullOrUndefined(e)&&O.function(e[Symbol.iterator]);O.map=Be("Map");O.nan=e=>Number.isNaN(e);O.null=e=>e===null;O.nullOrUndefined=e=>O.null(e)||O.undefined(e);O.number=e=>Vt("number")(e)&&!O.nan(e);O.numericString=e=>O.string(e)&&e.length>0&&!Number.isNaN(Number(e));O.object=e=>!O.nullOrUndefined(e)&&(O.function(e)||typeof e=="object");O.oneOf=(e,t)=>O.array(e)?e.indexOf(t)>-1:!1;O.plainFunction=Be("Function");O.plainObject=e=>{if(rn(e)!=="Object")return!1;let t=Object.getPrototypeOf(e);return t===null||t===Object.getPrototypeOf({})};O.primitive=e=>O.null(e)||su(typeof e);O.promise=Be("Promise");O.propertyOf=(e,t,r)=>{if(!O.object(e)||!t)return!1;let n=e[t];return O.function(r)?r(n):O.defined(n)};O.regexp=Be("RegExp");O.set=Be("Set");O.string=Vt("string");O.symbol=Vt("symbol");O.undefined=Vt("undefined");O.weakMap=Be("WeakMap");O.weakSet=Be("WeakSet");A=O;du=pt(Bc(),1),vs=pt(zc(),1);Ir=pt(po(),1),An=pt(po(),1),S=pt(qc()),dr=typeof window<"u"&&typeof document<"u"&&typeof navigator<"u",mu=function(){for(var e=["Edge","Trident","Firefox"],t=0;t<e.length;t+=1)if(dr&&navigator.userAgent.indexOf(e[t])>=0)return 1;return 0}();vu=dr&&window.Promise,bu=vu?gu:yu;Mi=dr&&!!(window.MSInputMethodContext&&document.documentMode),Di=dr&&/MSIE 10/.test(navigator.userAgent);Tu=function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")},Eu=function(){function e(t,r){for(var n=0;n<r.length;n++){var o=r[n];o.enumerable=o.enumerable||!1,o.configurable=!0,"value"in o&&(o.writable=!0),Object.defineProperty(t,o.key,o)}}return function(t,r,n){return r&&e(t.prototype,r),n&&e(t,n),t}}(),Lt=function(e,t,r){return t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e},Ne=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e};Vu=dr&&/Firefox/i.test(navigator.userAgent);Ls=["auto-start","auto","auto-end","top-start","top","top-end","right-start","right","right-end","bottom-end","bottom","bottom-start","left-end","left","left-start"],Rn=Ls.slice(3);In={FLIP:"flip",CLOCKWISE:"clockwise",COUNTERCLOCKWISE:"counterclockwise"};Qu={shift:{order:100,enabled:!0,fn:Gu},offset:{order:200,enabled:!0,fn:qu,offset:0},preventOverflow:{order:300,enabled:!0,fn:Yu,priority:["left","right","top","bottom"],padding:5,boundariesElement:"scrollParent"},keepTogether:{order:400,enabled:!0,fn:Hu},arrow:{order:500,enabled:!0,fn:Bu,element:"[x-arrow]"},flip:{order:600,enabled:!0,fn:_u,behavior:"flip",padding:5,boundariesElement:"viewport",flipVariations:!1,flipVariationsByContent:!1},inner:{order:700,enabled:!1,fn:Ku},hide:{order:800,enabled:!0,fn:Xu},computeStyle:{order:850,enabled:!0,fn:Fu,gpuAcceleration:!0,x:"bottom",y:"right"},applyStyle:{order:900,enabled:!0,fn:Lu,onLoad:Nu,gpuAcceleration:void 0}},Ju={placement:"bottom",positionFixed:!1,eventsEnabled:!0,removeOnDestroy:!1,onCreate:function(){},onUpdate:function(){},modifiers:Qu},nn=function(){function e(t,r){var n=this,o=arguments.length>2&&arguments[2]!==void 0?arguments[2]:{};Tu(this,e),this.scheduleUpdate=function(){return requestAnimationFrame(n.update)},this.update=bu(this.update.bind(this)),this.options=Ne({},e.Defaults,o),this.state={isDestroyed:!1,isCreated:!1,scrollParents:[]},this.reference=t&&t.jquery?t[0]:t,this.popper=r&&r.jquery?r[0]:r,this.options.modifiers={},Object.keys(Ne({},e.Defaults.modifiers,o.modifiers)).forEach(function(a){n.options.modifiers[a]=Ne({},e.Defaults.modifiers[a]||{},o.modifiers?o.modifiers[a]:{})}),this.modifiers=Object.keys(this.options.modifiers).map(function(a){return Ne({name:a},n.options.modifiers[a])}).sort(function(a,s){return a.order-s.order}),this.modifiers.forEach(function(a){a.enabled&&xs(a.onLoad)&&a.onLoad(n.reference,n.popper,n.options,a,n.state)}),this.update();var i=this.options.eventsEnabled;i&&this.enableEventListeners(),this.state.eventsEnabled=i}return Eu(e,[{key:"update",value:function(){return Cu.call(this)}},{key:"destroy",value:function(){return Au.call(this)}},{key:"enableEventListeners",value:function(){return Iu.call(this)}},{key:"disableEventListeners",value:function(){return Mu.call(this)}}]),e}();nn.Utils=window.PopperUtils;nn.placements=Ls;nn.Defaults=Ju;Vi=nn,Xn=pt(po()),Zu=["innerHTML","ownerDocument","style","attributes","nodeValue"],$u=["Array","ArrayBuffer","AsyncFunction","AsyncGenerator","AsyncGeneratorFunction","Date","Error","Function","Generator","GeneratorFunction","HTMLElement","Map","Object","Promise","RegExp","Set","WeakMap","WeakSet"],ep=["bigint","boolean","null","number","string","symbol","undefined"];C.array=Array.isArray;C.arrayOf=function(e,t){return!C.array(e)&&!C.function(t)?!1:e.every(function(r){return t(r)})};C.asyncGeneratorFunction=function(e){return on(e)==="AsyncGeneratorFunction"};C.asyncFunction=ze("AsyncFunction");C.bigint=Bt("bigint");C.boolean=function(e){return e===!0||e===!1};C.date=ze("Date");C.defined=function(e){return!C.undefined(e)};C.domElement=function(e){return C.object(e)&&!C.plainObject(e)&&e.nodeType===1&&C.string(e.nodeName)&&Zu.every(function(t){return t in e})};C.empty=function(e){return C.string(e)&&e.length===0||C.array(e)&&e.length===0||C.object(e)&&!C.map(e)&&!C.set(e)&&Object.keys(e).length===0||C.set(e)&&e.size===0||C.map(e)&&e.size===0};C.error=ze("Error");C.function=Bt("function");C.generator=function(e){return C.iterable(e)&&C.function(e.next)&&C.function(e.throw)};C.generatorFunction=ze("GeneratorFunction");C.instanceOf=function(e,t){return!e||!t?!1:Object.getPrototypeOf(e)===t.prototype};C.iterable=function(e){return!C.nullOrUndefined(e)&&C.function(e[Symbol.iterator])};C.map=ze("Map");C.nan=function(e){return Number.isNaN(e)};C.null=function(e){return e===null};C.nullOrUndefined=function(e){return C.null(e)||C.undefined(e)};C.number=function(e){return Bt("number")(e)&&!C.nan(e)};C.numericString=function(e){return C.string(e)&&e.length>0&&!Number.isNaN(Number(e))};C.object=function(e){return!C.nullOrUndefined(e)&&(C.function(e)||typeof e=="object")};C.oneOf=function(e,t){return C.array(e)?e.indexOf(t)>-1:!1};C.plainFunction=ze("Function");C.plainObject=function(e){if(on(e)!=="Object")return!1;var t=Object.getPrototypeOf(e);return t===null||t===Object.getPrototypeOf({})};C.primitive=function(e){return C.null(e)||rp(typeof e)};C.promise=ze("Promise");C.propertyOf=function(e,t,r){if(!C.object(e)||!t)return!1;var n=e[t];return C.function(r)?r(n):C.defined(n)};C.regexp=ze("RegExp");C.set=ze("Set");C.string=Bt("string");C.symbol=Bt("symbol");C.undefined=Bt("undefined");C.weakMap=ze("WeakMap");C.weakSet=ze("WeakSet");k=C;np=Ns("function"),op=function(e){return e===null},Fi=function(e){return Object.prototype.toString.call(e).slice(8,-1)==="RegExp"},Bi=function(e){return!ip(e)&&!op(e)&&(np(e)||typeof e=="object")},ip=Ns("undefined"),Kn=function(e){var t=typeof Symbol=="function"&&Symbol.iterator,r=t&&e[t],n=0;if(r)return r.call(e);if(e&&typeof e.length=="number")return{next:function(){return e&&n>=e.length&&(e=void 0),{value:e&&e[n++],done:!e}}};throw new TypeError(t?"Object is not iterable.":"Symbol.iterator is not defined.")};vp={flip:{padding:20},preventOverflow:{padding:10}},bp="The typeValidator argument must be a function with the signature function(props, propName, componentName).",xp="The error message is optional, but must be a string if provided.";K={INIT:"init",IDLE:"idle",OPENING:"opening",OPEN:"open",CLOSING:"closing",ERROR:"error"},Qt=ht.createPortal!==void 0;Bs=function(e){yr(r,e);var t=vr(r);function r(){return mr(this,r),t.apply(this,arguments)}return gr(r,[{key:"componentDidMount",value:function(){He()&&(this.node||this.appendNode(),Qt||this.renderPortal())}},{key:"componentDidUpdate",value:function(){He()&&(Qt||this.renderPortal())}},{key:"componentWillUnmount",value:function(){!He()||!this.node||(Qt||ht.unmountComponentAtNode(this.node),this.node&&this.node.parentNode===document.body&&(document.body.removeChild(this.node),this.node=void 0))}},{key:"appendNode",value:function(){var n=this.props,o=n.id,i=n.zIndex;this.node||(this.node=document.createElement("div"),o&&(this.node.id=o),i&&(this.node.style.zIndex=i),document.body.appendChild(this.node))}},{key:"renderPortal",value:function(){if(!He())return null;var n=this.props,o=n.children,i=n.setRef;if(this.node||this.appendNode(),Qt)return ht.createPortal(o,this.node);var a=ht.unstable_renderSubtreeIntoContainer(this,o.length>1?m.createElement("div",null,o):o[0],this.node);return i(a),null}},{key:"renderReact16",value:function(){var n=this.props,o=n.hasChildren,i=n.placement,a=n.target;return o?this.renderPortal():a||i==="center"?this.renderPortal():null}},{key:"render",value:function(){return Qt?this.renderReact16():null}}]),r}(m.Component);ge(Bs,"propTypes",{children:S.default.oneOfType([S.default.element,S.default.array]),hasChildren:S.default.bool,id:S.default.oneOfType([S.default.string,S.default.number]),placement:S.default.string,setRef:S.default.func.isRequired,target:S.default.oneOfType([S.default.object,S.default.string]),zIndex:S.default.number});zs=function(e){yr(r,e);var t=vr(r);function r(){return mr(this,r),t.apply(this,arguments)}return gr(r,[{key:"parentStyle",get:function(){var n=this.props,o=n.placement,i=n.styles,a=i.arrow.length,s={pointerEvents:"none",position:"absolute",width:"100%"};return o.startsWith("top")?(s.bottom=0,s.left=0,s.right=0,s.height=a):o.startsWith("bottom")?(s.left=0,s.right=0,s.top=0,s.height=a):o.startsWith("left")?(s.right=0,s.top=0,s.bottom=0):o.startsWith("right")&&(s.left=0,s.top=0),s}},{key:"render",value:function(){var n=this.props,o=n.placement,i=n.setArrowRef,a=n.styles,s=a.arrow,c=s.color,l=s.display,u=s.length,p=s.margin,d=s.position,h=s.spread,f={display:l,position:d},y,x=h,b=u;return o.startsWith("top")?(y="0,0 ".concat(x/2,",").concat(b," ").concat(x,",0"),f.bottom=0,f.marginLeft=p,f.marginRight=p):o.startsWith("bottom")?(y="".concat(x,",").concat(b," ").concat(x/2,",0 0,").concat(b),f.top=0,f.marginLeft=p,f.marginRight=p):o.startsWith("left")?(b=h,x=u,y="0,0 ".concat(x,",").concat(b/2," 0,").concat(b),f.right=0,f.marginTop=p,f.marginBottom=p):o.startsWith("right")&&(b=h,x=u,y="".concat(x,",").concat(b," ").concat(x,",0 0,").concat(b/2),f.left=0,f.marginTop=p,f.marginBottom=p),m.createElement("div",{className:"__floater__arrow",style:this.parentStyle},m.createElement("span",{ref:i,style:f},m.createElement("svg",{width:x,height:b,version:"1.1",xmlns:"http://www.w3.org/2000/svg"},m.createElement("polygon",{points:y,fill:c}))))}}]),r}(m.Component);ge(zs,"propTypes",{placement:S.default.string.isRequired,setArrowRef:S.default.func.isRequired,styles:S.default.object.isRequired});Ap=["color","height","width"];_s.propTypes={handleClick:S.default.func.isRequired,styles:S.default.object.isRequired};Hs.propTypes={content:S.default.node.isRequired,footer:S.default.node,handleClick:S.default.func.isRequired,open:S.default.bool,positionWrapper:S.default.bool.isRequired,showCloseButton:S.default.bool.isRequired,styles:S.default.object.isRequired,title:S.default.node};Us=function(e){yr(r,e);var t=vr(r);function r(){return mr(this,r),t.apply(this,arguments)}return gr(r,[{key:"style",get:function(){var n=this.props,o=n.disableAnimation,i=n.component,a=n.placement,s=n.hideArrow,c=n.status,l=n.styles,u=l.arrow.length,p=l.floater,d=l.floaterCentered,h=l.floaterClosing,f=l.floaterOpening,y=l.floaterWithAnimation,x=l.floaterWithComponent,b={};return s||(a.startsWith("top")?b.padding="0 0 ".concat(u,"px"):a.startsWith("bottom")?b.padding="".concat(u,"px 0 0"):a.startsWith("left")?b.padding="0 ".concat(u,"px 0 0"):a.startsWith("right")&&(b.padding="0 0 0 ".concat(u,"px"))),[K.OPENING,K.OPEN].indexOf(c)!==-1&&(b=re(re({},b),f)),c===K.CLOSING&&(b=re(re({},b),h)),c===K.OPEN&&!o&&(b=re(re({},b),y)),a==="center"&&(b=re(re({},b),d)),i&&(b=re(re({},b),x)),re(re({},p),b)}},{key:"render",value:function(){var n=this.props,o=n.component,i=n.handleClick,a=n.hideArrow,s=n.setFloaterRef,c=n.status,l={},u=["__floater"];return o?m.isValidElement(o)?l.content=m.cloneElement(o,{closeFn:i}):l.content=o({closeFn:i}):l.content=m.createElement(Hs,this.props),c===K.OPEN&&u.push("__floater__open"),a||(l.arrow=m.createElement(zs,this.props)),m.createElement("div",{ref:s,className:u.join(" "),style:this.style},m.createElement("div",{className:"__floater__body"},l.content,l.arrow))}}]),r}(m.Component);ge(Us,"propTypes",{component:S.default.oneOfType([S.default.func,S.default.element]),content:S.default.node,disableAnimation:S.default.bool.isRequired,footer:S.default.node,handleClick:S.default.func.isRequired,hideArrow:S.default.bool.isRequired,open:S.default.bool,placement:S.default.string.isRequired,positionWrapper:S.default.bool.isRequired,setArrowRef:S.default.func.isRequired,setFloaterRef:S.default.func.isRequired,showCloseButton:S.default.bool,status:S.default.string.isRequired,styles:S.default.object.isRequired,title:S.default.node});Ws=function(e){yr(r,e);var t=vr(r);function r(){return mr(this,r),t.apply(this,arguments)}return gr(r,[{key:"render",value:function(){var n=this.props,o=n.children,i=n.handleClick,a=n.handleMouseEnter,s=n.handleMouseLeave,c=n.setChildRef,l=n.setWrapperRef,u=n.style,p=n.styles,d;if(o)if(m.Children.count(o)===1)if(!m.isValidElement(o))d=m.createElement("span",null,o);else{var h=k.function(o.type)?"innerRef":"ref";d=m.cloneElement(m.Children.only(o),ge({},h,c))}else d=o;return d?m.createElement("span",{ref:l,style:re(re({},p),u),onClick:i,onMouseEnter:a,onMouseLeave:s},d):null}}]),r}(m.Component);ge(Ws,"propTypes",{children:S.default.node,handleClick:S.default.func.isRequired,handleMouseEnter:S.default.func.isRequired,handleMouseLeave:S.default.func.isRequired,setChildRef:S.default.func.isRequired,setWrapperRef:S.default.func.isRequired,style:S.default.object,styles:S.default.object.isRequired});Rp={zIndex:100};kp=["arrow","flip","offset"],Mp=["position","top","right","bottom","left"],xo=function(e){yr(r,e);var t=vr(r);function r(n){var o;return mr(this,r),o=t.call(this,n),ge(Ge(o),"setArrowRef",function(i){o.arrowRef=i}),ge(Ge(o),"setChildRef",function(i){o.childRef=i}),ge(Ge(o),"setFloaterRef",function(i){o.floaterRef=i}),ge(Ge(o),"setWrapperRef",function(i){o.wrapperRef=i}),ge(Ge(o),"handleTransitionEnd",function(){var i=o.state.status,a=o.props.callback;o.wrapperPopper&&o.wrapperPopper.instance.update(),o.setState({status:i===K.OPENING?K.OPEN:K.IDLE},function(){var s=o.state.status;a(s===K.OPEN?"open":"close",o.props)})}),ge(Ge(o),"handleClick",function(){var i=o.props,a=i.event,s=i.open;if(!k.boolean(s)){var c=o.state,l=c.positionWrapper,u=c.status;(o.event==="click"||o.event==="hover"&&l)&&(kr({title:"click",data:[{event:a,status:u===K.OPEN?"closing":"opening"}],debug:o.debug}),o.toggle())}}),ge(Ge(o),"handleMouseEnter",function(){var i=o.props,a=i.event,s=i.open;if(!(k.boolean(s)||kn())){var c=o.state.status;o.event==="hover"&&c===K.IDLE&&(kr({title:"mouseEnter",data:[{key:"originalEvent",value:a}],debug:o.debug}),clearTimeout(o.eventDelayTimeout),o.toggle())}}),ge(Ge(o),"handleMouseLeave",function(){var i=o.props,a=i.event,s=i.eventDelay,c=i.open;if(!(k.boolean(c)||kn())){var l=o.state,u=l.status,p=l.positionWrapper;o.event==="hover"&&(kr({title:"mouseLeave",data:[{key:"originalEvent",value:a}],debug:o.debug}),s?[K.OPENING,K.OPEN].indexOf(u)!==-1&&!p&&!o.eventDelayTimeout&&(o.eventDelayTimeout=setTimeout(function(){delete o.eventDelayTimeout,o.toggle()},s*1e3)):o.toggle(K.IDLE))}}),o.state={currentPlacement:n.placement,needsUpdate:!1,positionWrapper:n.wrapperOptions.position&&!!n.target,status:K.INIT,statusWrapper:K.INIT},o._isMounted=!1,o.hasMounted=!1,He()&&window.addEventListener("load",function(){o.popper&&o.popper.instance.update(),o.wrapperPopper&&o.wrapperPopper.instance.update()}),o}return gr(r,[{key:"componentDidMount",value:function(){if(He()){var n=this.state.positionWrapper,o=this.props,i=o.children,a=o.open,s=o.target;this._isMounted=!0,kr({title:"init",data:{hasChildren:!!i,hasTarget:!!s,isControlled:k.boolean(a),positionWrapper:n,target:this.target,floater:this.floaterRef},debug:this.debug}),this.hasMounted||(this.initPopper(),this.hasMounted=!0),!i&&s&&k.boolean(a)}}},{key:"componentDidUpdate",value:function(n,o){if(He()){var i=this.props,a=i.autoOpen,s=i.open,c=i.target,l=i.wrapperOptions,u=hp(o,this.state),p=u.changedFrom,d=u.changed;if(n.open!==s){var h;k.boolean(s)&&(h=s?K.OPENING:K.CLOSING),this.toggle(h)}(n.wrapperOptions.position!==l.position||n.target!==c)&&this.changeWrapperPosition(this.props),d("status",K.IDLE)&&s?this.toggle(K.OPEN):p("status",K.INIT,K.IDLE)&&a&&this.toggle(K.OPEN),this.popper&&d("status",K.OPENING)&&this.popper.instance.update(),this.floaterRef&&(d("status",K.OPENING)||d("status",K.CLOSING))&&Cp(this.floaterRef,"transitionend",this.handleTransitionEnd),d("needsUpdate",!0)&&this.rebuildPopper()}}},{key:"componentWillUnmount",value:function(){He()&&(this._isMounted=!1,this.popper&&this.popper.instance.destroy(),this.wrapperPopper&&this.wrapperPopper.instance.destroy())}},{key:"initPopper",value:function(){var n=this,o=arguments.length>0&&arguments[0]!==void 0?arguments[0]:this.target,i=this.state.positionWrapper,a=this.props,s=a.disableFlip,c=a.getPopper,l=a.hideArrow,u=a.offset,p=a.placement,d=a.wrapperOptions,h=p==="top"||p==="bottom"?"flip":["right","bottom-end","top-end","left","top-start","bottom-start"];if(p==="center")this.setState({status:K.IDLE});else if(o&&this.floaterRef){var f=this.options,y=f.arrow,x=f.flip,b=f.offset,g=Vs(f,kp);new Vi(o,this.floaterRef,{placement:p,modifiers:re({arrow:re({enabled:!l,element:this.arrowRef},y),flip:re({enabled:!s,behavior:h},x),offset:re({offset:"0, ".concat(u,"px")},b)},g),onCreate:function(w){var T;if(n.popper=w,!((T=n.floaterRef)!==null&&T!==void 0&&T.isConnected)){n.setState({needsUpdate:!0});return}c(w,"floater"),n._isMounted&&n.setState({currentPlacement:w.placement,status:K.IDLE}),p!==w.placement&&setTimeout(function(){w.instance.update()},1)},onUpdate:function(w){n.popper=w;var T=n.state.currentPlacement;n._isMounted&&w.placement!==T&&n.setState({currentPlacement:w.placement})}})}if(i){var v=k.undefined(d.offset)?0:d.offset;new Vi(this.target,this.wrapperRef,{placement:d.placement||p,modifiers:{arrow:{enabled:!1},offset:{offset:"0, ".concat(v,"px")},flip:{enabled:!1}},onCreate:function(w){n.wrapperPopper=w,n._isMounted&&n.setState({statusWrapper:K.IDLE}),c(w,"wrapper"),p!==w.placement&&setTimeout(function(){w.instance.update()},1)}})}}},{key:"rebuildPopper",value:function(){var n=this;this.floaterRefInterval=setInterval(function(){var o;(o=n.floaterRef)!==null&&o!==void 0&&o.isConnected&&(clearInterval(n.floaterRefInterval),n.setState({needsUpdate:!1}),n.initPopper())},50)}},{key:"changeWrapperPosition",value:function(n){var o=n.target,i=n.wrapperOptions;this.setState({positionWrapper:i.position&&!!o})}},{key:"toggle",value:function(n){var o=this.state.status,i=o===K.OPEN?K.CLOSING:K.OPENING;k.undefined(n)||(i=n),this.setState({status:i})}},{key:"debug",get:function(){var n=this.props.debug;return n||He()&&"ReactFloaterDebug"in window&&!!window.ReactFloaterDebug}},{key:"event",get:function(){var n=this.props,o=n.disableHoverToClick,i=n.event;return i==="hover"&&kn()&&!o?"click":i}},{key:"options",get:function(){var n=this.props.options;return(0,Xn.default)(vp,n||{})}},{key:"styles",get:function(){var n=this,o=this.state,i=o.status,a=o.positionWrapper,s=o.statusWrapper,c=this.props.styles,l=(0,Xn.default)(Ip(c),c);if(a){var u;[K.IDLE].indexOf(i)===-1||[K.IDLE].indexOf(s)===-1?u=l.wrapperPosition:u=this.wrapperPopper.styles,l.wrapper=re(re({},l.wrapper),u)}if(this.target){var p=window.getComputedStyle(this.target);this.wrapperStyles?l.wrapper=re(re({},l.wrapper),this.wrapperStyles):["relative","static"].indexOf(p.position)===-1&&(this.wrapperStyles={},a||(Mp.forEach(function(d){n.wrapperStyles[d]=p[d]}),l.wrapper=re(re({},l.wrapper),this.wrapperStyles),this.target.style.position="relative",this.target.style.top="auto",this.target.style.right="auto",this.target.style.bottom="auto",this.target.style.left="auto"))}return l}},{key:"target",get:function(){if(!He())return null;var n=this.props.target;return n?k.domElement(n)?n:document.querySelector(n):this.childRef||this.wrapperRef}},{key:"render",value:function(){var n=this.state,o=n.currentPlacement,i=n.positionWrapper,a=n.status,s=this.props,c=s.children,l=s.component,u=s.content,p=s.disableAnimation,d=s.footer,h=s.hideArrow,f=s.id,y=s.open,x=s.showCloseButton,b=s.style,g=s.target,v=s.title,w=m.createElement(Ws,{handleClick:this.handleClick,handleMouseEnter:this.handleMouseEnter,handleMouseLeave:this.handleMouseLeave,setChildRef:this.setChildRef,setWrapperRef:this.setWrapperRef,style:b,styles:this.styles.wrapper},c),T={};return i?T.wrapperInPortal=w:T.wrapperAsChildren=w,m.createElement("span",null,m.createElement(Bs,{hasChildren:!!c,id:f,placement:o,setRef:this.setFloaterRef,target:g,zIndex:this.styles.options.zIndex},m.createElement(Us,{component:l,content:u,disableAnimation:p,footer:d,handleClick:this.handleClick,hideArrow:h||o==="center",open:y,placement:o,positionWrapper:i,setArrowRef:this.setArrowRef,setFloaterRef:this.setFloaterRef,showCloseButton:x,status:a,styles:this.styles,title:v}),T.wrapperInPortal),T.wrapperAsChildren)}}]),r}(m.Component);ge(xo,"propTypes",{autoOpen:S.default.bool,callback:S.default.func,children:S.default.node,component:Gi(S.default.oneOfType([S.default.func,S.default.element]),function(e){return!e.content}),content:Gi(S.default.node,function(e){return!e.component}),debug:S.default.bool,disableAnimation:S.default.bool,disableFlip:S.default.bool,disableHoverToClick:S.default.bool,event:S.default.oneOf(["hover","click"]),eventDelay:S.default.number,footer:S.default.node,getPopper:S.default.func,hideArrow:S.default.bool,id:S.default.oneOfType([S.default.string,S.default.number]),offset:S.default.number,open:S.default.bool,options:S.default.object,placement:S.default.oneOf(["top","top-start","top-end","bottom","bottom-start","bottom-end","left","left-start","left-end","right","right-start","right-end","auto","center"]),showCloseButton:S.default.bool,style:S.default.object,styles:S.default.object,target:S.default.oneOfType([S.default.object,S.default.string]),title:S.default.node,wrapperOptions:S.default.shape({offset:S.default.number,placement:S.default.oneOf(["top","top-start","top-end","bottom","bottom-start","bottom-end","left","left-start","left-end","right","right-start","right-end","auto"]),position:S.default.bool})});ge(xo,"defaultProps",{autoOpen:!1,callback:Xi,debug:!1,disableAnimation:!1,disableFlip:!1,disableHoverToClick:!1,event:"click",eventDelay:.4,getPopper:Xi,hideArrow:!1,offset:15,placement:"bottom",showCloseButton:!1,styles:{},target:null,wrapperOptions:{position:!1}});Dp=pt(Yc(),1),Lp=Object.defineProperty,Np=(e,t,r)=>t in e?Lp(e,t,{enumerable:!0,configurable:!0,writable:!0,value:r}):e[t]=r,I=(e,t,r)=>(Np(e,typeof t!="symbol"?t+"":t,r),r),J={INIT:"init",START:"start",STOP:"stop",RESET:"reset",PREV:"prev",NEXT:"next",GO:"go",CLOSE:"close",SKIP:"skip",UPDATE:"update"},Ve={TOUR_START:"tour:start",STEP_BEFORE:"step:before",BEACON:"beacon",TOOLTIP:"tooltip",STEP_AFTER:"step:after",TOUR_END:"tour:end",TOUR_STATUS:"tour:status",TARGET_NOT_FOUND:"error:target_not_found",ERROR:"error"},B={INIT:"init",READY:"ready",BEACON:"beacon",TOOLTIP:"tooltip",COMPLETE:"complete",ERROR:"error"},W={IDLE:"idle",READY:"ready",WAITING:"waiting",RUNNING:"running",PAUSED:"paused",SKIPPED:"skipped",FINISHED:"finished",ERROR:"error"};Jt=Ht!==void 0;Yp={options:{preventOverflow:{boundariesElement:"scrollParent"}},wrapperOptions:{offset:-18,position:!0}},Gs={back:"Back",close:"Close",last:"Last",next:"Next",open:"Open the dialog",skip:"Skip"},Gp={event:"click",placement:"bottom",offset:10,disableBeacon:!1,disableCloseOnEsc:!1,disableOverlay:!1,disableOverlayClose:!1,disableScrollParentFix:!1,disableScrolling:!1,hideBackButton:!1,hideCloseButton:!1,hideFooter:!1,isFixed:!1,locale:Gs,showProgress:!1,showSkipButton:!1,spotlightClicks:!1,spotlightPadding:10},Xp={continuous:!1,debug:!1,disableCloseOnEsc:!1,disableOverlay:!1,disableOverlayClose:!1,disableScrolling:!1,disableScrollParentFix:!1,getHelpers:void 0,hideBackButton:!1,run:!0,scrollOffset:20,scrollDuration:300,scrollToFirstStep:!1,showSkipButton:!1,showProgress:!1,spotlightClicks:!1,spotlightPadding:10,steps:[]},Kp={arrowColor:"#fff",backgroundColor:"#fff",beaconSize:36,overlayColor:"rgba(0, 0, 0, 0.5)",primaryColor:"#f04",spotlightShadow:"0 0 15px rgba(0, 0, 0, 0.5)",textColor:"#333",width:380,zIndex:100},Zt={backgroundColor:"transparent",border:0,borderRadius:0,color:"#555",cursor:"pointer",fontSize:16,lineHeight:1,padding:8,WebkitAppearance:"none"},Ji={borderRadius:4,position:"absolute"};Ks={action:"init",controlled:!1,index:0,lifecycle:B.INIT,origin:null,size:0,status:W.IDLE},$i=hu(bs(Ks,"controlled","size")),Zp=class{constructor(e){I(this,"beaconPopper"),I(this,"tooltipPopper"),I(this,"data",new Map),I(this,"listener"),I(this,"store",new Map),I(this,"addListener",o=>{this.listener=o}),I(this,"setSteps",o=>{let{size:i,status:a}=this.getState(),s={size:o.length,status:a};this.data.set("steps",o),a===W.WAITING&&!i&&o.length&&(s.status=W.RUNNING),this.setState(s)}),I(this,"getPopper",o=>o==="beacon"?this.beaconPopper:this.tooltipPopper),I(this,"setPopper",(o,i)=>{o==="beacon"?this.beaconPopper=i:this.tooltipPopper=i}),I(this,"cleanupPoppers",()=>{this.beaconPopper=null,this.tooltipPopper=null}),I(this,"close",(o=null)=>{let{index:i,status:a}=this.getState();a===W.RUNNING&&this.setState({...this.getNextState({action:J.CLOSE,index:i+1,origin:o})})}),I(this,"go",o=>{let{controlled:i,status:a}=this.getState();if(i||a!==W.RUNNING)return;let s=this.getSteps()[o];this.setState({...this.getNextState({action:J.GO,index:o}),status:s?a:W.FINISHED})}),I(this,"info",()=>this.getState()),I(this,"next",()=>{let{index:o,status:i}=this.getState();i===W.RUNNING&&this.setState(this.getNextState({action:J.NEXT,index:o+1}))}),I(this,"open",()=>{let{status:o}=this.getState();o===W.RUNNING&&this.setState({...this.getNextState({action:J.UPDATE,lifecycle:B.TOOLTIP})})}),I(this,"prev",()=>{let{index:o,status:i}=this.getState();i===W.RUNNING&&this.setState({...this.getNextState({action:J.PREV,index:o-1})})}),I(this,"reset",(o=!1)=>{let{controlled:i}=this.getState();i||this.setState({...this.getNextState({action:J.RESET,index:0}),status:o?W.RUNNING:W.READY})}),I(this,"skip",()=>{let{status:o}=this.getState();o===W.RUNNING&&this.setState({action:J.SKIP,lifecycle:B.INIT,status:W.SKIPPED})}),I(this,"start",o=>{let{index:i,size:a}=this.getState();this.setState({...this.getNextState({action:J.START,index:A.number(o)?o:i},!0),status:a?W.RUNNING:W.WAITING})}),I(this,"stop",(o=!1)=>{let{index:i,status:a}=this.getState();[W.FINISHED,W.SKIPPED].includes(a)||this.setState({...this.getNextState({action:J.STOP,index:i+(o?1:0)}),status:W.PAUSED})}),I(this,"update",o=>{var i,a;if(!Up(o,$i))throw new Error(`State is not valid. Valid keys: ${$i.join(", ")}`);this.setState({...this.getNextState({...this.getState(),...o,action:(i=o.action)!=null?i:J.UPDATE,origin:(a=o.origin)!=null?a:null},!0)})});let{continuous:t=!1,stepIndex:r,steps:n=[]}=e??{};this.setState({action:J.INIT,controlled:A.number(r),continuous:t,index:A.number(r)?r:0,lifecycle:B.INIT,origin:null,status:n.length?W.READY:W.IDLE},!0),this.beaconPopper=null,this.tooltipPopper=null,this.listener=null,this.setSteps(n)}getState(){return this.store.size?{action:this.store.get("action")||"",controlled:this.store.get("controlled")||!1,index:parseInt(this.store.get("index"),10),lifecycle:this.store.get("lifecycle")||"",origin:this.store.get("origin")||null,size:this.store.get("size")||0,status:this.store.get("status")||""}:{...Ks}}getNextState(e,t=!1){var r,n,o,i,a;let{action:s,controlled:c,index:l,size:u,status:p}=this.getState(),d=A.number(e.index)?e.index:l,h=c&&!t?l:Math.min(Math.max(d,0),u);return{action:(r=e.action)!=null?r:s,controlled:c,index:h,lifecycle:(n=e.lifecycle)!=null?n:B.INIT,origin:(o=e.origin)!=null?o:null,size:(i=e.size)!=null?i:u,status:h===u?W.FINISHED:(a=e.status)!=null?a:p}}getSteps(){let e=this.data.get("steps");return Array.isArray(e)?e:[]}hasUpdatedState(e){let t=JSON.stringify(e),r=JSON.stringify(this.getState());return t!==r}setState(e,t=!1){let r=this.getState(),{action:n,index:o,lifecycle:i,origin:a=null,size:s,status:c}={...r,...e};this.store.set("action",n),this.store.set("index",o),this.store.set("lifecycle",i),this.store.set("origin",a),this.store.set("size",s),this.store.set("status",c),t&&(this.store.set("controlled",e.controlled),this.store.set("continuous",e.continuous)),this.listener&&this.hasUpdatedState(r)&&this.listener(this.getState())}getHelpers(){return{close:this.close,go:this.go,info:this.info,next:this.next,open:this.open,prev:this.prev,reset:this.reset,skip:this.skip}}};ed=class{constructor(e,t){if(I(this,"element"),I(this,"options"),I(this,"canBeTabbed",r=>{let{tabIndex:n}=r;return n===null||n<0?!1:this.canHaveFocus(r)}),I(this,"canHaveFocus",r=>{let n=/input|select|textarea|button|object/,o=r.nodeName.toLowerCase();return(n.test(o)&&!r.getAttribute("disabled")||o==="a"&&!!r.getAttribute("href"))&&this.isVisible(r)}),I(this,"findValidTabElements",()=>[].slice.call(this.element.querySelectorAll("*"),0).filter(this.canBeTabbed)),I(this,"handleKeyDown",r=>{let{code:n="Tab"}=this.options;r.code===n&&this.interceptTab(r)}),I(this,"interceptTab",r=>{r.preventDefault();let n=this.findValidTabElements(),{shiftKey:o}=r;if(!n.length)return;let i=document.activeElement?n.indexOf(document.activeElement):0;i===-1||!o&&i+1===n.length?i=0:o&&i===0?i=n.length-1:i+=o?-1:1,n[i].focus()}),I(this,"isHidden",r=>{let n=r.offsetWidth<=0&&r.offsetHeight<=0,o=window.getComputedStyle(r);return n&&!r.innerHTML?!0:n&&o.getPropertyValue("overflow")!=="visible"||o.getPropertyValue("display")==="none"}),I(this,"isVisible",r=>{let n=r;for(;n;)if(n instanceof HTMLElement){if(n===document.body)break;if(this.isHidden(n))return!1;n=n.parentNode}return!0}),I(this,"removeScope",()=>{window.removeEventListener("keydown",this.handleKeyDown)}),I(this,"checkFocus",r=>{document.activeElement!==r&&(r.focus(),window.requestAnimationFrame(()=>this.checkFocus(r)))}),I(this,"setFocus",()=>{let{selector:r}=this.options;if(!r)return;let n=this.element.querySelector(r);n&&window.requestAnimationFrame(()=>this.checkFocus(n))}),!(e instanceof HTMLElement))throw new TypeError("Invalid parameter: element must be an HTMLElement");this.element=e,this.options=t,window.addEventListener("keydown",this.handleKeyDown,!1),this.setFocus()}},td=class extends ft{constructor(e){if(super(e),I(this,"beacon",null),I(this,"setBeaconRef",n=>{this.beacon=n}),e.beaconComponent)return;let t=document.head||document.getElementsByTagName("head")[0],r=document.createElement("style");r.id="joyride-beacon-animation",e.nonce&&r.setAttribute("nonce",e.nonce),r.appendChild(document.createTextNode(`
        @keyframes joyride-beacon-inner {
          20% {
            opacity: 0.9;
          }
        
          90% {
            opacity: 0.7;
          }
        }
        
        @keyframes joyride-beacon-outer {
          0% {
            transform: scale(1);
          }
        
          45% {
            opacity: 0.7;
            transform: scale(0.75);
          }
        
          100% {
            opacity: 0.9;
            transform: scale(1);
          }
        }
      `)),t.appendChild(r)}componentDidMount(){let{shouldFocus:e}=this.props;A.domElement(this.beacon)||console.warn("beacon is not a valid DOM element"),setTimeout(()=>{A.domElement(this.beacon)&&e&&this.beacon.focus()},0)}componentWillUnmount(){let e=document.getElementById("joyride-beacon-animation");e?.parentNode&&e.parentNode.removeChild(e)}render(){let{beaconComponent:e,continuous:t,index:r,isLastStep:n,locale:o,onClickOrHover:i,size:a,step:s,styles:c}=this.props,l=A.string(o.open)?o.open:(0,Dp.default)(o.open),u={"aria-label":l,onClick:i,onMouseEnter:i,ref:this.setBeaconRef,title:l},p;return e?p=X(e,{continuous:t,index:r,isLastStep:n,size:a,step:s,...u}):p=X("button",{key:"JoyrideBeacon",className:"react-joyride__beacon","data-test-id":"button-beacon",style:c.beacon,type:"button",...u},X("span",{style:c.beaconInner}),X("span",{style:c.beaconOuter})),p}};nd=rd,od=class extends ft{constructor(){super(...arguments),I(this,"isActive",!1),I(this,"resizeTimeout"),I(this,"scrollTimeout"),I(this,"scrollParent"),I(this,"state",{isScrolling:!1,mouseOverSpotlight:!1,showSpotlight:!0}),I(this,"handleMouseMove",e=>{let{mouseOverSpotlight:t}=this.state,{height:r,left:n,position:o,top:i,width:a}=this.spotlightStyles,s=o==="fixed"?e.clientY:e.pageY,c=o==="fixed"?e.clientX:e.pageX,l=s>=i&&s<=i+r,u=c>=n&&c<=n+a&&l;u!==t&&this.updateState({mouseOverSpotlight:u})}),I(this,"handleScroll",()=>{let{target:e}=this.props,t=it(e);if(this.scrollParent!==document){let{isScrolling:r}=this.state;r||this.updateState({isScrolling:!0,showSpotlight:!1}),clearTimeout(this.scrollTimeout),this.scrollTimeout=window.setTimeout(()=>{this.updateState({isScrolling:!1,showSpotlight:!0})},50)}else lr(t,"sticky")&&this.updateState({})}),I(this,"handleResize",()=>{clearTimeout(this.resizeTimeout),this.resizeTimeout=window.setTimeout(()=>{this.isActive&&this.forceUpdate()},100)})}componentDidMount(){let{debug:e,disableScrolling:t,disableScrollParentFix:r=!1,target:n}=this.props,o=it(n);this.scrollParent=an(o??document.body,r,!0),this.isActive=!0,!t&&br(o,!0)&&lt({title:"step has a custom scroll parent and can cause trouble with scrolling",data:[{key:"parent",value:this.scrollParent}],debug:e}),window.addEventListener("resize",this.handleResize)}componentDidUpdate(e){var t;let{lifecycle:r,spotlightClicks:n}=this.props,{changed:o}=Hr(e,this.props);o("lifecycle",B.TOOLTIP)&&((t=this.scrollParent)==null||t.addEventListener("scroll",this.handleScroll,{passive:!0}),setTimeout(()=>{let{isScrolling:i}=this.state;i||this.updateState({showSpotlight:!0})},100)),(o("spotlightClicks")||o("disableOverlay")||o("lifecycle"))&&(n&&r===B.TOOLTIP?window.addEventListener("mousemove",this.handleMouseMove,!1):r!==B.TOOLTIP&&window.removeEventListener("mousemove",this.handleMouseMove))}componentWillUnmount(){var e;this.isActive=!1,window.removeEventListener("mousemove",this.handleMouseMove),window.removeEventListener("resize",this.handleResize),clearTimeout(this.resizeTimeout),clearTimeout(this.scrollTimeout),(e=this.scrollParent)==null||e.removeEventListener("scroll",this.handleScroll)}get spotlightStyles(){var e,t,r;let{showSpotlight:n}=this.state,{disableScrollParentFix:o=!1,spotlightClicks:i,spotlightPadding:a=0,styles:s,target:c}=this.props,l=it(c),u=qs(l),p=lr(l),d=zp(l,a,o);return{...Qi()?s.spotlightLegacy:s.spotlight,height:Math.round(((e=u?.height)!=null?e:0)+a*2),left:Math.round(((t=u?.left)!=null?t:0)-a),opacity:n?1:0,pointerEvents:i?"none":"auto",position:p?"fixed":"absolute",top:d,transition:"opacity 0.2s",width:Math.round(((r=u?.width)!=null?r:0)+a*2)}}updateState(e){this.isActive&&this.setState(t=>({...t,...e}))}render(){let{mouseOverSpotlight:e,showSpotlight:t}=this.state,{disableOverlay:r,disableOverlayClose:n,lifecycle:o,onClickOverlay:i,placement:a,styles:s}=this.props;if(r||o!==B.TOOLTIP)return null;let c=s.overlay;Qi()&&(c=a==="center"?s.overlayLegacyCenter:s.overlayLegacy);let l={cursor:n?"default":"pointer",height:jp(),pointerEvents:e?"none":"auto",...c},u=a!=="center"&&t&&X(nd,{styles:this.spotlightStyles});if(Ys()==="safari"){let{mixBlendMode:p,zIndex:d,...h}=l;u=X("div",{style:{...h}},u),delete l.backgroundColor}return X("div",{className:"react-joyride__overlay","data-test-id":"overlay",onClick:i,role:"presentation",style:l},u)}},id=class extends ft{constructor(){super(...arguments),I(this,"node",null)}componentDidMount(){let{id:e}=this.props;nt()&&(this.node=document.createElement("div"),this.node.id=e,document.body.appendChild(this.node),Jt||this.renderReact15())}componentDidUpdate(){nt()&&(Jt||this.renderReact15())}componentWillUnmount(){!nt()||!this.node||(Jt||Go(this.node),this.node.parentNode===document.body&&(document.body.removeChild(this.node),this.node=null))}renderReact15(){if(!nt())return;let{children:e}=this.props;this.node&&Xo(this,e,this.node)}renderReact16(){if(!nt()||!Jt)return null;let{children:e}=this.props;return this.node?Ht(e,this.node):null}render(){return Jt?this.renderReact16():null}};sd=ad;cd=ld,ud=class extends ft{constructor(){super(...arguments),I(this,"handleClickBack",e=>{e.preventDefault();let{helpers:t}=this.props;t.prev()}),I(this,"handleClickClose",e=>{e.preventDefault();let{helpers:t}=this.props;t.close("button_close")}),I(this,"handleClickPrimary",e=>{e.preventDefault();let{continuous:t,helpers:r}=this.props;if(!t){r.close("button_primary");return}r.next()}),I(this,"handleClickSkip",e=>{e.preventDefault();let{helpers:t}=this.props;t.skip()}),I(this,"getElementsProps",()=>{let{continuous:e,isLastStep:t,setTooltipRef:r,step:n}=this.props,o=ot(n.locale.back),i=ot(n.locale.close),a=ot(n.locale.last),s=ot(n.locale.next),c=ot(n.locale.skip),l=e?s:i;return t&&(l=a),{backProps:{"aria-label":o,"data-action":"back",onClick:this.handleClickBack,role:"button",title:o},closeProps:{"aria-label":i,"data-action":"close",onClick:this.handleClickClose,role:"button",title:i},primaryProps:{"aria-label":l,"data-action":"primary",onClick:this.handleClickPrimary,role:"button",title:l},skipProps:{"aria-label":c,"data-action":"skip",onClick:this.handleClickSkip,role:"button",title:c},tooltipProps:{"aria-modal":!0,ref:r,role:"alertdialog"}}})}render(){let{continuous:e,index:t,isLastStep:r,setTooltipRef:n,size:o,step:i}=this.props,{beaconComponent:a,tooltipComponent:s,...c}=i,l;if(s){let u={...this.getElementsProps(),continuous:e,index:t,isLastStep:r,size:o,step:c,setTooltipRef:n};l=X(s,{...u})}else l=X(cd,{...this.getElementsProps(),continuous:e,index:t,isLastStep:r,size:o,step:i});return l}},pd=class extends ft{constructor(){super(...arguments),I(this,"scope",null),I(this,"tooltip",null),I(this,"handleClickHoverBeacon",e=>{let{step:t,store:r}=this.props;e.type==="mouseenter"&&t.event!=="hover"||r.update({lifecycle:B.TOOLTIP})}),I(this,"handleClickOverlay",()=>{let{helpers:e,step:t}=this.props;t.disableOverlayClose||e.close("overlay")}),I(this,"setTooltipRef",e=>{this.tooltip=e}),I(this,"setPopper",(e,t)=>{var r;let{action:n,lifecycle:o,step:i,store:a}=this.props;t==="wrapper"?a.setPopper("beacon",e):a.setPopper("tooltip",e),a.getPopper("beacon")&&a.getPopper("tooltip")&&o===B.INIT&&a.update({action:n,lifecycle:B.READY}),(r=i.floaterProps)!=null&&r.getPopper&&i.floaterProps.getPopper(e,t)}),I(this,"renderTooltip",e=>{let{continuous:t,helpers:r,index:n,size:o,step:i}=this.props;return X(ud,{continuous:t,helpers:r,index:n,isLastStep:n+1===o,setTooltipRef:this.setTooltipRef,size:o,step:i,...e})})}componentDidMount(){let{debug:e,index:t}=this.props;lt({title:`step:${t}`,data:[{key:"props",value:this.props}],debug:e})}componentDidUpdate(e){var t;let{action:r,callback:n,continuous:o,controlled:i,debug:a,helpers:s,index:c,lifecycle:l,status:u,step:p,store:d}=this.props,{changed:h,changedFrom:f}=Hr(e,this.props),y=s.info(),x=o&&r!==J.CLOSE&&(c>0||r===J.PREV),b=h("action")||h("index")||h("lifecycle")||h("status"),g=f("lifecycle",[B.TOOLTIP,B.INIT],B.INIT),v=h("action",[J.NEXT,J.PREV,J.SKIP,J.CLOSE]),w=i&&c===e.index;if(v&&(g||w)&&n({...y,index:e.index,lifecycle:B.COMPLETE,step:e.step,type:Ve.STEP_AFTER}),p.placement==="center"&&u===W.RUNNING&&h("index")&&r!==J.START&&l===B.INIT&&d.update({lifecycle:B.READY}),b){let T=it(p.target),F=!!T;F&&Bp(T)?(f("status",W.READY,W.RUNNING)||f("lifecycle",B.INIT,B.READY))&&n({...y,step:p,type:Ve.STEP_BEFORE}):(console.warn(F?"Target not visible":"Target not mounted",p),n({...y,type:Ve.TARGET_NOT_FOUND,step:p}),i||d.update({index:c+(r===J.PREV?-1:1)}))}f("lifecycle",B.INIT,B.READY)&&d.update({lifecycle:Ki(p)||x?B.TOOLTIP:B.BEACON}),h("index")&&lt({title:`step:${l}`,data:[{key:"props",value:this.props}],debug:a}),h("lifecycle",B.BEACON)&&n({...y,step:p,type:Ve.BEACON}),h("lifecycle",B.TOOLTIP)&&(n({...y,step:p,type:Ve.TOOLTIP}),this.tooltip&&(this.scope=new ed(this.tooltip,{selector:"[data-action=primary]"}),this.scope.setFocus())),f("lifecycle",[B.TOOLTIP,B.INIT],B.INIT)&&((t=this.scope)==null||t.removeScope(),d.cleanupPoppers())}componentWillUnmount(){var e;(e=this.scope)==null||e.removeScope()}get open(){let{lifecycle:e,step:t}=this.props;return Ki(t)||e===B.TOOLTIP}render(){let{continuous:e,debug:t,index:r,lifecycle:n,nonce:o,shouldScroll:i,size:a,step:s}=this.props,c=it(s.target);return!Xs(s)||!A.domElement(c)?null:X("div",{key:`JoyrideStep-${r}`,className:"react-joyride__step"},X(id,{id:"react-joyride-portal"},X(od,{...s,debug:t,lifecycle:n,onClickOverlay:this.handleClickOverlay})),X(xo,{...s.floaterProps,component:this.renderTooltip,debug:t,getPopper:this.setPopper,id:`react-joyride-step-${r}`,open:this.open,placement:s.placement,target:s.target},X(td,{beaconComponent:s.beaconComponent,continuous:e,index:r,isLastStep:r+1===a,locale:s.locale,nonce:o,onClickOrHover:this.handleClickHoverBeacon,shouldFocus:i,size:a,step:s,styles:s.styles})))}},Qs=class extends ft{constructor(e){super(e),I(this,"helpers"),I(this,"store"),I(this,"callback",a=>{let{callback:s}=this.props;A.function(s)&&s(a)}),I(this,"handleKeyboard",a=>{let{index:s,lifecycle:c}=this.state,{steps:l}=this.props,u=l[s];c===B.TOOLTIP&&a.code==="Escape"&&u&&!u.disableCloseOnEsc&&this.store.close("keyboard")}),I(this,"syncState",a=>{this.setState(a)});let{debug:t,getHelpers:r,run:n,stepIndex:o}=e;this.store=$p({...e,controlled:n&&A.number(o)}),this.helpers=this.store.getHelpers();let{addListener:i}=this.store;lt({title:"init",data:[{key:"props",value:this.props},{key:"state",value:this.state}],debug:t}),i(this.syncState),r&&r(this.helpers),this.state=this.store.getState()}componentDidMount(){if(!nt())return;let{debug:e,disableCloseOnEsc:t,run:r,steps:n}=this.props,{start:o}=this.store;Zi(n,e)&&r&&o(),t||document.body.addEventListener("keydown",this.handleKeyboard,{passive:!0})}componentDidUpdate(e,t){if(!nt())return;let{action:r,controlled:n,index:o,lifecycle:i,status:a}=this.state,{debug:s,run:c,stepIndex:l,steps:u}=this.props,{stepIndex:p,steps:d}=e,{reset:h,setSteps:f,start:y,stop:x,update:b}=this.store,{changed:g}=Hr(e,this.props),{changed:v,changedFrom:w}=Hr(t,this.state),T=$t(u[o],this.props),F=!be(d,u),M=A.number(l)&&g("stepIndex"),D=it(T.target);if(F&&(Zi(u,s)?f(u):console.warn("Steps are not valid",u)),g("run")&&(c?y(l):x()),M){let ne=A.number(p)&&p<l?J.NEXT:J.PREV;r===J.STOP&&(ne=J.START),[W.FINISHED,W.SKIPPED].includes(a)||b({action:r===J.CLOSE?J.CLOSE:ne,index:l,lifecycle:B.INIT})}!n&&a===W.RUNNING&&o===0&&!D&&(this.store.update({index:o+1}),this.callback({...this.state,type:Ve.TARGET_NOT_FOUND,step:T}));let V={...this.state,index:o,step:T};if(v("action",[J.NEXT,J.PREV,J.SKIP,J.CLOSE])&&v("status",W.PAUSED)){let ne=$t(u[t.index],this.props);this.callback({...V,index:t.index,lifecycle:B.COMPLETE,step:ne,type:Ve.STEP_AFTER})}if(v("status",[W.FINISHED,W.SKIPPED])){let ne=$t(u[t.index],this.props);n||this.callback({...V,index:t.index,lifecycle:B.COMPLETE,step:ne,type:Ve.STEP_AFTER}),this.callback({...V,type:Ve.TOUR_END,step:ne,index:t.index}),h()}else w("status",[W.IDLE,W.READY],W.RUNNING)?this.callback({...V,type:Ve.TOUR_START}):(v("status")||v("action",J.RESET))&&this.callback({...V,type:Ve.TOUR_STATUS});this.scrollToStep(t),T.placement==="center"&&a===W.RUNNING&&i===B.INIT&&this.store.update({lifecycle:B.READY})}componentWillUnmount(){let{disableCloseOnEsc:e}=this.props;e||document.body.removeEventListener("keydown",this.handleKeyboard)}scrollToStep(e){let{index:t,lifecycle:r,status:n}=this.state,{debug:o,disableScrollParentFix:i=!1,scrollDuration:a,scrollOffset:s=20,scrollToFirstStep:c=!1,steps:l}=this.props,u=$t(l[t],this.props),p=it(u.target),d=qp({isFirstStep:t===0,lifecycle:r,previousLifecycle:e.lifecycle,scrollToFirstStep:c,step:u,target:p});if(n===W.RUNNING&&d){let h=br(p,i),f=an(p,i),y=Math.floor(_p(p,s,i))||0;lt({title:"scrollToStep",data:[{key:"index",value:t},{key:"lifecycle",value:r},{key:"status",value:n}],debug:o});let x=this.store.getPopper("beacon"),b=this.store.getPopper("tooltip");if(r===B.BEACON&&x){let{offsets:g,placement:v}=x;!["bottom"].includes(v)&&!h&&(y=Math.floor(g.popper.top-s))}else if(r===B.TOOLTIP&&b){let{flipped:g,offsets:v,placement:w}=b;["top","right","left"].includes(w)&&!g&&!h?y=Math.floor(v.popper.top-s):y-=u.spotlightPadding}y=y>=0?y:0,n===W.RUNNING&&Hp(y,{element:f,duration:a}).then(()=>{setTimeout(()=>{var g;(g=this.store.getPopper("tooltip"))==null||g.instance.update()},10)})}}render(){if(!nt())return null;let{index:e,status:t}=this.state,{continuous:r=!1,debug:n=!1,nonce:o,scrollToFirstStep:i=!1,steps:a}=this.props,s;if(t===W.RUNNING&&a[e]){let c=$t(a[e],this.props);s=X(pd,{...this.state,callback:this.callback,continuous:r,debug:n,helpers:this.helpers,nonce:o,shouldScroll:!c.disableScrolling&&(e!==0||i),step:c,store:this.store})}return X("div",{className:"react-joyride"},s)}};I(Qs,"defaultProps",Xp);dd=Qs;fd=j.div(({width:e,height:t,left:r,top:n})=>({width:`${e}px`,height:`${t}px`,left:`${r}px`,top:`${n}px`,position:"relative",overflow:"hidden"}));yd=j.button`
  all: unset;
  box-sizing: border-box;
  border: 0;
  border-radius: 0.25rem;
  cursor: pointer;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: 0 0.75rem;
  background: ${({theme:e,variant:t})=>t==="primary"?e.color.secondary:t==="secondary"?e.color.lighter:t==="outline"?"transparent":e.color.secondary};
  color: ${({theme:e,variant:t})=>t==="primary"?e.color.lightest:t==="secondary"||t==="outline"?e.darkest:e.color.lightest};
  box-shadow: ${({variant:e})=>e==="primary"?"none":e==="secondary"||e==="outline"?"#D9E8F2 0 0 0 1px inset":"none"};
  height: 32px;
  font-size: 0.8125rem;
  font-weight: 700;
  font-family: ${({theme:e})=>e.typography.fonts.base};
  transition: background-color, box-shadow, opacity;
  transition-duration: 0.16s;
  transition-timing-function: ease-in-out;
  text-decoration: none;

  &:hover {
    background-color: ${({variant:e})=>e==="primary"?"#0b94eb":e==="secondary"?"#eef4f9":e==="outline"?"transparent":"#0b94eb"};
  }

  &:focus {
    box-shadow: ${({variant:e})=>e==="primary"?"inset 0 0 0 1px rgba(0, 0, 0, 0.2)":e==="secondary"||e==="outline"?"inset 0 0 0 1px #0b94eb":"inset 0 0 0 2px rgba(0, 0, 0, 0.1)"};
  }
`,De=Or(function({children:e,onClick:t,variant:r="primary",...n},o){return m.createElement(yd,{ref:o,onClick:t,variant:r,...n},e)}),vd=j.div`
  background: ${({theme:e})=>e.base==="dark"?"#292A2C":e.color.lightest};
  width: 260px;
  padding: 15px;
  border-radius: 5px;
`,bd=j.div`
  display: flex;
  flex-direction: column;
  align-items: flex-start;
`,xd=j.div`
  font-size: 13px;
  line-height: 18px;
  font-weight: 700;
  color: ${({theme:e})=>e.color.defaultText};
`,wd=j.p`
  font-size: 13px;
  line-height: 18px;
  text-align: start;
  color: ${({theme:e})=>e.color.defaultText};
  margin: 0;
  margin-top: 5px;
`,Td=j.div`
  display: flex;
  justify-content: flex-end;
  margin-top: 15px;
`,Ed=({step:e,primaryProps:t,tooltipProps:r})=>m.createElement(vd,{...r},m.createElement(bd,null,e.title&&m.createElement(xd,null,e.title),m.createElement(wd,null,e.content)),!e.hideNextButton&&m.createElement(Td,{id:"buttonNext"},m.createElement(De,{...t,...e.onNextButtonClick?{onClick:e.onNextButtonClick}:{}},"Next"))),Sd=j(Ct)``,Pd=j.div`
  display: flex;
  flex-direction: row;
  height: 100%;
  max-height: 85vh;

  &:focus-visible {
    outline: none;
  }
`,Od=j.div`
  position: relative;
  flex: 1;
  display: flex;
  flex-direction: column;
  background: white;
  font-family: ${({theme:e})=>e.typography.fonts.base};
`,Cd=j.div`
  position: relative;
  z-index: 1;
  box-sizing: border-box;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 15px;
  border-bottom: 1px solid rgba(0, 0, 0, 0.1);
  height: 44px;
`,Ad=j.div`
  display: flex;
  align-items: center;
  gap: 5px;
  font-size: 13px;
  line-height: 18px;
  font-weight: bold;
  color: ${({theme:e})=>e.color.darkest};
`,Rd=j.div`
  font-size: 13px;
  line-height: 18px;
  padding: 15px;
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  justify-content: space-between;
  color: ${({theme:e})=>e.color.darker};

  h3 {
    font-size: 13px;
    line-height: 18px;
    font-weight: bold;
    padding: 0;
    margin: 0;
  }
`,Fr=j.span(({theme:e})=>({display:"inline-flex",borderRadius:3,padding:"0 5px",marginBottom:-2,opacity:.8,fontFamily:e.typography.fonts.mono,fontSize:11,border:e.base==="dark"?e.color.darkest:e.color.lightest,color:e.base==="dark"?e.color.lightest:e.color.darkest,backgroundColor:e.base==="dark"?"black":e.color.light,boxSizing:"border-box",lineHeight:"17px"})),Mn=j.img`
  max-width: 100%;
  margin-top: 1em;
`,Id=j.div`
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 0;
  overflow: hidden;
  z-index: 0;
  pointer-events: none;
`,kd=gt`
  0% { transform: translate(0px, 0px) }
  50% { transform: translate(120px, 0px) }
  100% { transform: translate(0px, 0px) }
`,Md=j.div`
  position: absolute;
  width: 350px;
  height: 350px;
  left: -160px;
  top: -260px;
  background: radial-gradient(
    circle at center,
    rgba(255, 119, 119, 1) 0%,
    rgba(255, 119, 119, 0) 70%
  );
  animation: ${kd} 8s linear infinite;
  animation-timing-function: ease-in-out;
  z-index: 2;
`,Dd=gt`
  0% { transform: translate(0px, 0px) }
  33% { transform: translate(-64px, 0px) }
  66% { transform: translate(120px, 0px) }
  100% { transform: translate(0px, 0px) }
`,Ld=j.div`
  position: absolute;
  width: 350px;
  height: 350px;
  left: -54px;
  top: -250px;
  background: radial-gradient(
    circle at center,
    rgba(253, 255, 147, 1) 0%,
    rgba(253, 255, 147, 0) 70%
  );
  animation: ${Dd} 12s linear infinite;
  animation-timing-function: ease-in-out;
  z-index: 3;
`,Nd=gt`
  0% { transform: translate(0px, 0px) }
  50% { transform: translate(-120px, 0px) }
  100% { transform: translate(0px, 0px) }
`,jd=j.div`
  position: absolute;
  width: 350px;
  height: 350px;
  left: 150px;
  top: -220px;
  background: radial-gradient(
    circle at center,
    rgba(119, 255, 247, 0.8) 0%,
    rgba(119, 255, 247, 0) 70%
  );
  animation: ${Nd} 4s linear infinite;
  animation-timing-function: ease-in-out;
  z-index: 4;
`,Mr=j.div`
  box-sizing: border-box;
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
  margin-top: 4px;
`,Vd=j.div`
  margin-bottom: 4px;
`;zd=j(Ct)`
  background: white;
`,_d=j.div`
  border-radius: 5px;
  display: flex;
  flex-direction: column;
  align-items: center;
  height: 100%;
  justify-content: space-between;
`,Hd=j.div`
  display: flex;
  flex: 1;
  flex-direction: column;
  align-items: center;
  justify-content: center;
`,Ud=j.h1`
  margin: 0;
  margin-top: 20px;
  margin-bottom: 5px;
  color: ${({theme:e})=>e.color.darkest};
  font-weight: ${({theme:e})=>e.typography.weight.bold};
  font-size: ${({theme:e})=>e.typography.size.m1}px;
  line-height: ${({theme:e})=>e.typography.size.m3}px;
`,Wd=j.p`
  margin: 0;
  margin-bottom: 20px;
  max-width: 320px;
  text-align: center;
  font-size: ${({theme:e})=>e.typography.size.s2}px;
  font-weight: ${({theme:e})=>e.typography.weight.regular};
  line-height: ${({theme:e})=>e.typography.size.m1}px;
  color: ${({theme:e})=>e.color.darker};
`,qd=j.button`
  all: unset;
  cursor: pointer;
  font-size: 13px;
  color: #798186;
  padding-bottom: 20px;

  &:focus-visible {
    outline: auto;
  }
`,Yd=j(vi)`
  margin-left: 2px;
  height: 10px;
`,Gd=j.div`
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: -1;
  overflow: hidden;
`,Xd=gt`
  0% { transform: translate(0px, 0px) }
  50% { transform: translate(-200px, 0px) }
  100% { transform: translate(0px, 0px) }
`,Kd=j.div`
  position: absolute;
  width: 1200px;
  height: 1200px;
  left: -200px;
  top: -900px;
  background: radial-gradient(
    circle at center,
    rgba(253, 255, 147, 1) 0%,
    rgba(253, 255, 147, 0) 70%
  );
  animation: ${Xd} 4s linear infinite;
  animation-timing-function: ease-in-out;
  z-index: 3;
`,Qd=gt`
  0% { transform: translate(0px, 0px) }
  50% { transform: translate(400px, 0px) }
  100% { transform: translate(0px, 0px) }
`,Jd=j.div`
  position: absolute;
  width: 1200px;
  height: 1200px;
  left: -600px;
  top: -840px;
  background: radial-gradient(
    circle at center,
    rgba(255, 119, 119, 1) 0%,
    rgba(255, 119, 119, 0) 70%
  );
  animation: ${Qd} 6s linear infinite;
  animation-timing-function: ease-in-out;
  z-index: 2;
`,Zd=gt`
  0% { transform: translate(600px, -40px) }
  50% { transform: translate(600px, -200px) }
  100% { transform: translate(600px, -40px) }
`,$d=j.div`
  position: absolute;
  width: 1200px;
  height: 1200px;
  left: -600px;
  top: -840px;
  background: radial-gradient(
    circle at center,
    rgba(119, 255, 247, 0.8) 0%,
    rgba(119, 255, 247, 0) 70%
  );
  animation: ${Zd} 4s linear infinite;
  animation-timing-function: ease-in-out;
  z-index: 4;
`;j.h2`
  color: #000;
  font-weight: 700;
  font-size: 20px;
  line-height: 20px;
`;j.p`
  font-size: 14px;
  font-weight: 400;
  line-height: 20px;
  color: #454e54;
`;eh=({onProceed:e,skipOnboarding:t,container:r})=>m.createElement("div",{style:{zIndex:10}},m.createElement(zd,{width:540,height:430,defaultOpen:!0,container:r},m.createElement(_d,{"data-chromatic":"ignore"},m.createElement(Hd,null,m.createElement(Bd,null),m.createElement(Ud,null,"Welcome to Storybook"),m.createElement(Wd,null,"Storybook helps you develop UI components faster. Learn the basics in a few simple steps."),m.createElement(De,{style:{marginTop:4},onClick:e},"Start your 3 minute tour")),m.createElement(qd,{onClick:t},"Skip tour",m.createElement(Yd,null)),m.createElement(Gd,null,m.createElement(Kd,null),m.createElement(Jd,null),m.createElement($d,null))))),ea=pt(Gc());oh=["x","y","top","bottom","left","right","width","height"],ih=(e,t)=>oh.every(r=>e[r]===t[r]),$s=mt({transformPagePoint:e=>e,isStatic:!1,reducedMotion:"never"}),sn=mt({}),wo=mt(null),ln=typeof document<"u",ah=ln?gn:ue,el=mt({strict:!1}),To=e=>e.replace(/([a-z])([A-Z])/g,"$1-$2").toLowerCase(),sh="framerAppearId",tl="data-"+To(sh),rl={skipAnimations:!1,useManualTiming:!1},ta=class{constructor(){this.order=[],this.scheduled=new Set}add(e){if(!this.scheduled.has(e))return this.scheduled.add(e),this.order.push(e),!0}remove(e){let t=this.order.indexOf(e);t!==-1&&(this.order.splice(t,1),this.scheduled.delete(e))}clear(){this.order.length=0,this.scheduled.clear()}};Dr=["prepare","read","update","preRender","render","postRender"],ch=40;({schedule:Eo,cancel:US}=nl(queueMicrotask,!1));So=["animate","whileInView","whileFocus","whileHover","whileTap","whileDrag","exit"],Po=["initial",...So];na={animation:["animate","variants","whileHover","whileTap","exit","whileInView","whileFocus","whileDrag"],exit:["exit"],drag:["drag","dragControls"],focus:["whileFocus"],hover:["whileHover","onHoverStart","onHoverEnd"],tap:["whileTap","onTap","onTapStart","onTapCancel"],pan:["onPan","onPanStart","onPanSessionStart","onPanEnd"],inView:["whileInView","onViewportEnter","onViewportLeave"],layout:["layout","layoutId"]},ur={};for(let e in na)ur[e]={isEnabled:t=>na[e].some(r=>!!t[r])};il=mt({}),al=mt({}),mh=Symbol.for("motionComponentSymbol");bh=["animate","circle","defs","desc","ellipse","g","image","line","filter","marker","mask","metadata","path","pattern","polygon","polyline","rect","stop","switch","symbol","svg","text","tspan","use","view"];Yr={};xr=["transformPerspective","x","y","z","translateX","translateY","translateZ","scale","scaleX","scaleY","rotate","rotateX","rotateY","rotateZ","skew","skewX","skewY"],St=new Set(xr);Te=e=>!!(e&&e.getVelocity),wh={x:"translateX",y:"translateY",z:"translateZ",transformPerspective:"perspective"},Th=xr.length;ll=e=>t=>typeof t=="string"&&t.startsWith(e),cl=ll("--"),Sh=ll("var(--"),Gr=e=>Sh(e)&&Ph.test(e),Ph=/var\s*\(\s*--[\w-]+(\s*,\s*(?:(?:[^)(]|\((?:[^)(]+|\([^)(]*\))*\))*)+)?\s*\)$/i,Oh=(e,t)=>t&&typeof e=="number"?t.transform(e):e,ct=(e,t,r)=>r>t?t:r<e?e:r,zt={test:e=>typeof e=="number",parse:parseFloat,transform:e=>e},or={...zt,transform:e=>ct(0,1,e)},Lr={...zt,default:1},ir=e=>Math.round(e*1e5)/1e5,Co=/(-)?([\d]*\.?[\d])+/g,Ch=/(#[0-9a-f]{3,8}|(rgb|hsl)a?\((-?[\d\.]+%?[,\s]+){2}(-?[\d\.]+%?)\s*[\,\/]?\s*[\d\.]*%?\))/gi,Ah=/^(#[0-9a-f]{3,8}|(rgb|hsl)a?\((-?[\d\.]+%?[,\s]+){2}(-?[\d\.]+%?)\s*[\,\/]?\s*[\d\.]*%?\))$/i;Tr=e=>({test:t=>wr(t)&&t.endsWith(e)&&t.split(" ").length===1,parse:parseFloat,transform:t=>`${t}${e}`}),tt=Tr("deg"),qe=Tr("%"),L=Tr("px"),Rh=Tr("vh"),Ih=Tr("vw"),oa={...qe,parse:e=>qe.parse(e)/100,transform:e=>qe.transform(e*100)},ia={...zt,transform:Math.round},ul={borderWidth:L,borderTopWidth:L,borderRightWidth:L,borderBottomWidth:L,borderLeftWidth:L,borderRadius:L,radius:L,borderTopLeftRadius:L,borderTopRightRadius:L,borderBottomRightRadius:L,borderBottomLeftRadius:L,width:L,maxWidth:L,height:L,maxHeight:L,size:L,top:L,right:L,bottom:L,left:L,padding:L,paddingTop:L,paddingRight:L,paddingBottom:L,paddingLeft:L,margin:L,marginTop:L,marginRight:L,marginBottom:L,marginLeft:L,rotate:tt,rotateX:tt,rotateY:tt,rotateZ:tt,scale:Lr,scaleX:Lr,scaleY:Lr,scaleZ:Lr,skew:tt,skewX:tt,skewY:tt,distance:L,translateX:L,translateY:L,translateZ:L,x:L,y:L,z:L,perspective:L,transformPerspective:L,opacity:or,originX:oa,originY:oa,originZ:L,zIndex:ia,fillOpacity:or,strokeOpacity:or,numOctaves:ia};Ro=()=>({style:{},transform:{},transformOrigin:{},vars:{}});Lh=new Set(["animate","exit","variants","initial","style","values","variants","transition","transformTemplate","custom","inherit","onBeforeLayoutMeasure","onAnimationStart","onAnimationComplete","onUpdate","onDragStart","onDrag","onDragEnd","onMeasureDragConstraints","onDirectionLock","onDragTransitionEnd","_dragX","_dragY","onHoverStart","onHoverEnd","onViewportEnter","onViewportLeave","globalTapTarget","ignoreStrict","viewport"]);dl=e=>!Xr(e);try{Nh((Qc(),Fc(fs)).default)}catch{}Fh={offset:"stroke-dashoffset",array:"stroke-dasharray"},Bh={offset:"strokeDashoffset",array:"strokeDasharray"};hl=()=>({...Ro(),attrs:{}}),ko=e=>typeof e=="string"&&e.toLowerCase()==="svg";ml=new Set(["baseFrequency","diffuseConstant","kernelMatrix","kernelUnitLength","keySplines","keyTimes","limitingConeAngle","markerHeight","markerWidth","numOctaves","targetX","targetY","surfaceScale","specularConstant","specularExponent","stdDeviation","tableValues","viewBox","gradientTransform","pathLength","startOffset","textLength","lengthAdjust"]);Kr=e=>Array.isArray(e),Wh=e=>!!(e&&typeof e=="object"&&e.mix&&e.toValue),qh=e=>Kr(e)?e[e.length-1]||0:e;vl=e=>(t,r)=>{let n=ve(sn),o=ve(wo),i=()=>Yh(e,t,n,o);return r?i():Uh(i)};le=e=>e,{schedule:ie,cancel:Ze,state:de,steps:Dn}=nl(typeof requestAnimationFrame<"u"?requestAnimationFrame:le,!0),Xh={useVisualState:vl({scrapeMotionValuesFromProps:yl,createRenderState:hl,onMount:(e,t,{renderState:r,latestValues:n})=>{ie.read(()=>{try{r.dimensions=typeof t.getBBox=="function"?t.getBBox():t.getBoundingClientRect()}catch{r.dimensions={x:0,y:0,width:0,height:0}}}),ie.render(()=>{Io(r,n,{enableHardwareAcceleration:!1},ko(t.tagName),e.transformTemplate),gl(t,r)})}})},Kh={useVisualState:vl({scrapeMotionValuesFromProps:Mo,createRenderState:Ro})};bl=e=>e.pointerType==="mouse"?typeof e.button!="number"||e.button<=0:e.isPrimary!==!1;Jh=e=>t=>bl(t)&&e(t,pn(t));Zh=(e,t)=>r=>t(e(r)),Qe=(...e)=>e.reduce(Zh);sa=xl("dragHorizontal"),la=xl("dragVertical");dt=class{constructor(e){this.isMounted=!1,this.node=e}update(){}};$h=class extends dt{mount(){this.unmount=Qe(ca(this.node,!0),ca(this.node,!1))}unmount(){}},ef=class extends dt{constructor(){super(...arguments),this.isActive=!1}onFocus(){let e=!1;try{e=this.node.current.matches(":focus-visible")}catch{e=!0}!e||!this.node.animationState||(this.node.animationState.setActive("whileFocus",!0),this.isActive=!0)}onBlur(){!this.isActive||!this.node.animationState||(this.node.animationState.setActive("whileFocus",!1),this.isActive=!1)}mount(){this.unmount=Qe(Xe(this.node.current,"focus",()=>this.onFocus()),Xe(this.node.current,"blur",()=>this.onBlur()))}unmount(){}},El=(e,t)=>t?e===t?!0:El(e,t.parentElement):!1;tf=class extends dt{constructor(){super(...arguments),this.removeStartListeners=le,this.removeEndListeners=le,this.removeAccessibleListeners=le,this.startPointerPress=(e,t)=>{if(this.isPressing)return;this.removeEndListeners();let r=this.node.getProps(),n=Ke(window,"pointerup",(i,a)=>{if(!this.checkPressEnd())return;let{onTap:s,onTapCancel:c,globalTapTarget:l}=this.node.getProps();ie.update(()=>{!l&&!El(this.node.current,i.target)?c&&c(i,a):s&&s(i,a)})},{passive:!(r.onTap||r.onPointerUp)}),o=Ke(window,"pointercancel",(i,a)=>this.cancelPress(i,a),{passive:!(r.onTapCancel||r.onPointerCancel)});this.removeEndListeners=Qe(n,o),this.startPress(e,t)},this.startAccessiblePress=()=>{let e=o=>{if(o.key!=="Enter"||this.isPressing)return;let i=a=>{a.key!=="Enter"||!this.checkPressEnd()||Ln("up",(s,c)=>{let{onTap:l}=this.node.getProps();l&&ie.update(()=>l(s,c))})};this.removeEndListeners(),this.removeEndListeners=Xe(this.node.current,"keyup",i),Ln("down",(a,s)=>{this.startPress(a,s)})},t=Xe(this.node.current,"keydown",e),r=()=>{this.isPressing&&Ln("cancel",(o,i)=>this.cancelPress(o,i))},n=Xe(this.node.current,"blur",r);this.removeAccessibleListeners=Qe(t,n)}}startPress(e,t){this.isPressing=!0;let{onTapStart:r,whileTap:n}=this.node.getProps();n&&this.node.animationState&&this.node.animationState.setActive("whileTap",!0),r&&ie.update(()=>r(e,t))}checkPressEnd(){return this.removeEndListeners(),this.isPressing=!1,this.node.getProps().whileTap&&this.node.animationState&&this.node.animationState.setActive("whileTap",!1),!Tl()}cancelPress(e,t){if(!this.checkPressEnd())return;let{onTapCancel:r}=this.node.getProps();r&&ie.update(()=>r(e,t))}mount(){let e=this.node.getProps(),t=Ke(e.globalTapTarget?window:this.node.current,"pointerdown",this.startPointerPress,{passive:!(e.onTapStart||e.onPointerStart)}),r=Xe(this.node.current,"focus",this.startAccessiblePress);this.removeStartListeners=Qe(t,r)}unmount(){this.removeStartListeners(),this.removeEndListeners(),this.removeAccessibleListeners()}},Jn=new WeakMap,Nn=new WeakMap,rf=e=>{let t=Jn.get(e.target);t&&t(e)},nf=e=>{e.forEach(rf)};sf={some:0,all:1},lf=class extends dt{constructor(){super(...arguments),this.hasEnteredView=!1,this.isInView=!1}startObserver(){this.unmount();let{viewport:e={}}=this.node.getProps(),{root:t,margin:r,amount:n="some",once:o}=e,i={root:t?t.current:void 0,rootMargin:r,threshold:typeof n=="number"?n:sf[n]},a=s=>{let{isIntersecting:c}=s;if(this.isInView===c||(this.isInView=c,o&&!c&&this.hasEnteredView))return;c&&(this.hasEnteredView=!0),this.node.animationState&&this.node.animationState.setActive("whileInView",c);let{onViewportEnter:l,onViewportLeave:u}=this.node.getProps(),p=c?l:u;p&&p(s)};return af(this.node.current,i,a)}mount(){this.startObserver()}update(){if(typeof IntersectionObserver>"u")return;let{props:e,prevProps:t}=this.node;["amount","margin","root"].some(cf(e,t))&&this.startObserver()}unmount(){}};uf={inView:{Feature:lf},tap:{Feature:tf},focus:{Feature:ef},hover:{Feature:$h}};Er=le,Fe=le;Er=(e,t)=>{!e&&typeof console<"u"&&console.warn(t)},Fe=(e,t)=>{if(!e)throw new Error(t)};at=e=>e*1e3,Je=e=>e/1e3,hf={current:!1},Pl=e=>Array.isArray(e)&&typeof e[0]=="number";rr=([e,t,r,n])=>`cubic-bezier(${e}, ${t}, ${r}, ${n})`,Cl={linear:"linear",ease:"ease",easeIn:"ease-in",easeOut:"ease-out",easeInOut:"ease-in-out",circIn:rr([0,.65,.55,1]),circOut:rr([.55,0,1,.45]),backIn:rr([.31,.01,.66,-.59]),backOut:rr([.33,1.53,.69,.99])};Rl=(e,t,r)=>(((1-3*r+3*t)*e+(3*r-6*t))*e+3*t)*e,gf=1e-7,yf=12;bf=Sr(.42,0,1,1),xf=Sr(0,0,.58,1),Il=Sr(.42,0,.58,1),wf=e=>Array.isArray(e)&&typeof e[0]!="number",kl=e=>t=>t<=.5?e(2*t)/2:(2-e(2*(1-t)))/2,Ml=e=>t=>1-e(1-t),Lo=e=>1-Math.sin(Math.acos(e)),Dl=Ml(Lo),Tf=kl(Lo),Ll=Sr(.33,1.53,.69,.99),No=Ml(Ll),Ef=kl(No),Sf=e=>(e*=2)<1?.5*No(e):.5*(2-Math.pow(2,-10*(e-1))),ua={linear:le,easeIn:bf,easeInOut:Il,easeOut:xf,circIn:Lo,circInOut:Tf,circOut:Dl,backIn:No,backInOut:Ef,backOut:Ll,anticipate:Sf},pa=e=>{if(Array.isArray(e)){Fe(e.length===4,"Cubic bezier arrays must contain four numerical values.");let[t,r,n,o]=e;return Sr(t,r,n,o)}else if(typeof e=="string")return Fe(ua[e]!==void 0,`Invalid easing type '${e}'`),ua[e];return e},pr=(e,t,r)=>{let n=t-e;return n===0?1:(r-e)/n},oe=(e,t,r)=>e+(t-e)*r;jo=(e,t)=>r=>!!(wr(r)&&Ah.test(r)&&r.startsWith(e)||t&&Object.prototype.hasOwnProperty.call(r,t)),Nl=(e,t,r)=>n=>{if(!wr(n))return n;let[o,i,a,s]=n.match(Co);return{[e]:parseFloat(o),[t]:parseFloat(i),[r]:parseFloat(a),alpha:s!==void 0?parseFloat(s):1}},Of=e=>ct(0,255,e),Vn={...zt,transform:e=>Math.round(Of(e))},wt={test:jo("rgb","red"),parse:Nl("red","green","blue"),transform:({red:e,green:t,blue:r,alpha:n=1})=>"rgba("+Vn.transform(e)+", "+Vn.transform(t)+", "+Vn.transform(r)+", "+ir(or.transform(n))+")"};Zn={test:jo("#"),parse:Cf,transform:wt.transform},Rt={test:jo("hsl","hue"),parse:Nl("hue","saturation","lightness"),transform:({hue:e,saturation:t,lightness:r,alpha:n=1})=>"hsla("+Math.round(e)+", "+qe.transform(ir(t))+", "+qe.transform(ir(r))+", "+ir(or.transform(n))+")"},Fn=(e,t,r)=>{let n=e*e,o=r*(t*t-n)+n;return o<0?0:Math.sqrt(o)},Af=[Zn,wt,Rt],Rf=e=>Af.find(t=>t.test(e));ha=(e,t)=>{let r=da(e),n=da(t),o={...r};return i=>(o.red=Fn(r.red,n.red,i),o.green=Fn(r.green,n.green,i),o.blue=Fn(r.blue,n.blue,i),o.alpha=oe(r.alpha,n.alpha,i),wt.transform(o))},me={test:e=>wt.test(e)||Zn.test(e)||Rt.test(e),parse:e=>wt.test(e)?wt.parse(e):Rt.test(e)?Rt.parse(e):Zn.parse(e),transform:e=>wr(e)?e:e.hasOwnProperty("red")?wt.transform(e):Rt.transform(e)};jl="number",Vl="color",kf="var",Mf="var(",fa="${}",ma=/(var\s*\(\s*--[\w-]+(\s*,\s*(?:(?:[^)(]|\((?:[^)(]+|\([^)(]*\))*\))*)+)?\s*\))|(#[0-9a-f]{3,8}|(rgb|hsl)a?\((-?[\d\.]+%?[,\s]+){2}(-?[\d\.]+%?)\s*[\,\/]?\s*[\d\.]*%?\))|((-)?([\d]*\.?[\d])+)/gi;Df=e=>typeof e=="number"?0:e;ut={test:If,parse:Fl,createTransformer:Bl,getAnimatableNone:Lf};Ff=(e,t)=>{let r=ut.createTransformer(t),n=Qr(e),o=Qr(t);return n.indexes.var.length===o.indexes.var.length&&n.indexes.color.length===o.indexes.color.length&&n.indexes.number.length>=o.indexes.number.length?Qe(zl(Vf(n,o),o.values),r):(Er(!0,`Complex values '${e}' and '${t}' too different to mix. Ensure all colors are of the same type, and that each contains the same quantity of number and color values. Falling back to instant transition.`),$n(e,t))};qf=5;Bn=.001,Yf=.01,ga=10,Gf=.05,Xf=1;Qf=12;Zf=["duration","bounce"],$f=["stiffness","damping","mass"];Tt={now:()=>(zr===void 0&&Tt.set(de.isProcessing||rl.useManualTiming?de.timestamp:performance.now()),zr),set:e=>{zr=e,queueMicrotask(tm)}},rm=e=>{let t=({timestamp:r})=>e(r);return{start:()=>ie.update(t,!0),stop:()=>Ze(t),now:()=>de.isProcessing?de.timestamp:Tt.now()}};nm={decay:va,inertia:va,tween:Jr,keyframes:Jr,spring:Wl},om=e=>e/100;am=im(()=>Object.hasOwnProperty.call(Element.prototype,"animate")),sm=new Set(["opacity","clipPath","filter","transform"]),Nr=10,lm=2e4,cm=(e,t)=>t.type==="spring"||e==="backgroundColor"||!Ol(t.ease);dm={type:"spring",stiffness:500,damping:25,restSpeed:10},hm=e=>({type:"spring",stiffness:550,damping:e===0?2*Math.sqrt(550):30,restSpeed:10}),fm={type:"keyframes",duration:.8},mm={type:"keyframes",ease:[.25,.1,.35,1],duration:.3},gm=(e,{keyframes:t})=>t.length>2?fm:St.has(e)?e.startsWith("scale")?hm(t[1]):dm:mm,to=(e,t)=>e==="zIndex"?!1:!!(typeof t=="number"||Array.isArray(t)||typeof t=="string"&&(ut.test(t)||t==="0")&&!t.startsWith("url(")),ym=new Set(["brightness","contrast","saturate","opacity"]);bm=/([a-z-]*)\(.*?\)/g,ro={...ut,getAnimatableNone:e=>{let t=e.match(bm);return t?t.map(vm).join(" "):e}},xm={...ul,color:me,backgroundColor:me,outlineColor:me,fill:me,stroke:me,borderColor:me,borderTopColor:me,borderRightColor:me,borderBottomColor:me,borderLeftColor:me,filter:ro,WebkitFilter:ro},Fo=e=>xm[e];Yl=e=>/^0[^.\s]+$/.test(e);zo=(e,t,r,n={})=>o=>{let i=Bo(n,e)||{},a=i.delay||n.delay||0,{elapsed:s=0}=n;s=s-at(a);let c=Tm(t,e,r,i),l=c[0],u=c[c.length-1],p=to(e,l),d=to(e,u);Er(p===d,`You are trying to animate ${e} from "${l}" to "${u}". ${l} is not an animatable value - to enable this animation set ${l} to a value animatable to ${u} via the \`style\` property.`);let h={keyframes:c,velocity:t.getVelocity(),ease:"easeOut",...i,delay:-s,onUpdate:f=>{t.set(f),i.onUpdate&&i.onUpdate(f)},onComplete:()=>{o(),i.onComplete&&i.onComplete()}};if(Em(i)||(h={...h,...gm(e,h)}),h.duration&&(h.duration=at(h.duration)),h.repeatDelay&&(h.repeatDelay=at(h.repeatDelay)),!p||!d||hf.current||i.type===!1||rl.skipAnimations)return pm(h);if(!n.isHandoff&&t.owner&&t.owner.current instanceof HTMLElement&&!t.owner.getProps().onUpdate){let f=um(t,e,h);if(f)return f}return Zr(h)};Gl=e=>/^\-?\d*\.?\d+$/.test(e);Uo=class{constructor(){this.subscriptions=[]}add(e){return _o(this.subscriptions,e),()=>Ho(this.subscriptions,e)}notify(e,t,r){let n=this.subscriptions.length;if(n)if(n===1)this.subscriptions[0](e,t,r);else for(let o=0;o<n;o++){let i=this.subscriptions[o];i&&i(e,t,r)}}getSize(){return this.subscriptions.length}clear(){this.subscriptions.length=0}},xa=new Set;wa=30,Sm=e=>!isNaN(parseFloat(e)),Pm=class{constructor(e,t={}){this.version="11.0.6",this.canTrackVelocity=!1,this.events={},this.updateAndNotify=(r,n=!0)=>{let o=Tt.now();this.updatedAt!==o&&this.setPrevFrameValue(),this.prev=this.current,this.setCurrent(r),this.current!==this.prev&&this.events.change&&this.events.change.notify(this.current),n&&this.events.renderRequest&&this.events.renderRequest.notify(this.current)},this.hasAnimated=!1,this.setCurrent(e),this.canTrackVelocity=Sm(this.current),this.owner=t.owner}setCurrent(e){this.current=e,this.updatedAt=Tt.now()}setPrevFrameValue(e=this.current){this.prevFrameValue=e,this.prevUpdatedAt=this.updatedAt}onChange(e){return Wo(!1,'value.onChange(callback) is deprecated. Switch to value.on("change", callback).'),this.on("change",e)}on(e,t){this.events[e]||(this.events[e]=new Uo);let r=this.events[e].add(t);return e==="change"?()=>{r(),ie.read(()=>{this.events.change.getSize()||this.stop()})}:r}clearListeners(){for(let e in this.events)this.events[e].clear()}attach(e,t){this.passiveEffect=e,this.stopPassiveEffect=t}set(e,t=!0){!t||!this.passiveEffect?this.updateAndNotify(e,t):this.passiveEffect(e,this.updateAndNotify)}setWithVelocity(e,t,r){this.set(t),this.prev=void 0,this.prevFrameValue=e,this.prevUpdatedAt=this.updatedAt-r}jump(e){this.updateAndNotify(e),this.prev=e,this.prevUpdatedAt=this.prevFrameValue=void 0,this.stop(),this.stopPassiveEffect&&this.stopPassiveEffect()}get(){return this.current}getPrevious(){return this.prev}getVelocity(){let e=Tt.now();if(!this.canTrackVelocity||this.prevFrameValue===void 0||e-this.updatedAt>wa)return 0;let t=Math.min(this.updatedAt-this.prevUpdatedAt,wa);return Hl(parseFloat(this.current)-parseFloat(this.prevFrameValue),t)}start(e){return this.stop(),new Promise(t=>{this.hasAnimated=!0,this.animation=e(t),this.events.animationStart&&this.events.animationStart.notify()}).then(()=>{this.events.animationComplete&&this.events.animationComplete.notify(),this.clearAnimation()})}stop(){this.animation&&(this.animation.stop(),this.events.animationCancel&&this.events.animationCancel.notify()),this.clearAnimation()}isAnimating(){return!!this.animation}clearAnimation(){delete this.animation}destroy(){this.clearListeners(),this.stop(),this.stopPassiveEffect&&this.stopPassiveEffect()}};Xl=e=>t=>t.test(e),Om={test:e=>e==="auto",parse:e=>e},Kl=[zt,L,qe,tt,Ih,Rh,Om],er=e=>Kl.find(Xl(e)),Cm=[...Kl,me,ut],Am=e=>Cm.find(Xl(e));Bm=[...So].reverse(),zm=So.length;qm=class extends dt{constructor(e){super(e),e.animationState||(e.animationState=Hm(e))}updateAnimationControlsSubscription(){let{animate:e}=this.node.getProps();this.unmount(),cn(e)&&(this.unmount=e.subscribe(this.node))}mount(){this.updateAnimationControlsSubscription()}update(){let{animate:e}=this.node.getProps(),{animate:t}=this.node.prevProps||{};e!==t&&this.updateAnimationControlsSubscription()}unmount(){}},Ym=0,Gm=class extends dt{constructor(){super(...arguments),this.id=Ym++}update(){if(!this.node.presenceContext)return;let{isPresent:e,onExitComplete:t,custom:r}=this.node.presenceContext,{isPresent:n}=this.node.prevPresenceContext||{};if(!this.node.animationState||e===n)return;let o=this.node.animationState.setActive("exit",!e,{custom:r??this.node.getProps().custom});t&&!e&&o.then(()=>t(this.id))}mount(){let{register:e}=this.node.presenceContext||{};e&&(this.unmount=e(this.id))}unmount(){}},Xm={animation:{Feature:qm},exit:{Feature:Gm}},Ta=(e,t)=>Math.abs(e-t);Jl=class{constructor(e,t,{transformPagePoint:r,contextWindow:n,dragSnapToOrigin:o=!1}={}){if(this.startEvent=null,this.lastMoveEvent=null,this.lastMoveEventInfo=null,this.handlers={},this.contextWindow=window,this.updatePoint=()=>{if(!(this.lastMoveEvent&&this.lastMoveEventInfo))return;let u=_n(this.lastMoveEventInfo,this.history),p=this.startEvent!==null,d=Km(u.offset,{x:0,y:0})>=3;if(!p&&!d)return;let{point:h}=u,{timestamp:f}=de;this.history.push({...h,timestamp:f});let{onStart:y,onMove:x}=this.handlers;p||(y&&y(this.lastMoveEvent,u),this.startEvent=this.lastMoveEvent),x&&x(this.lastMoveEvent,u)},this.handlePointerMove=(u,p)=>{this.lastMoveEvent=u,this.lastMoveEventInfo=zn(p,this.transformPagePoint),ie.update(this.updatePoint,!0)},this.handlePointerUp=(u,p)=>{this.end();let{onEnd:d,onSessionEnd:h,resumeAnimation:f}=this.handlers;if(this.dragSnapToOrigin&&f&&f(),!(this.lastMoveEvent&&this.lastMoveEventInfo))return;let y=_n(u.type==="pointercancel"?this.lastMoveEventInfo:zn(p,this.transformPagePoint),this.history);this.startEvent&&d&&d(u,y),h&&h(u,y)},!bl(e))return;this.dragSnapToOrigin=o,this.handlers=t,this.transformPagePoint=r,this.contextWindow=n||window;let i=pn(e),a=zn(i,this.transformPagePoint),{point:s}=a,{timestamp:c}=de;this.history=[{...s,timestamp:c}];let{onSessionStart:l}=t;l&&l(e,_n(a,this.history)),this.removeListeners=Qe(Ke(this.contextWindow,"pointermove",this.handlePointerMove),Ke(this.contextWindow,"pointerup",this.handlePointerUp),Ke(this.contextWindow,"pointercancel",this.handlePointerUp))}updateHandlers(e){this.handlers=e}end(){this.removeListeners&&this.removeListeners(),Ze(this.updatePoint)}};io=.35;ka=()=>({translate:0,scale:1,origin:0,originPoint:0}),It=()=>({x:ka(),y:ka()}),Ma=()=>({min:0,max:0}),se=()=>({x:Ma(),y:Ma()});lg=["x","scaleX","originX"],cg=["y","scaleY","originY"];nc=({current:e})=>e?e.ownerDocument.defaultView:null,pg=new WeakMap,dg=class{constructor(e){this.openGlobalLock=null,this.isDragging=!1,this.currentDirection=null,this.originPoint={x:0,y:0},this.constraints=!1,this.hasMutatedConstraints=!1,this.elastic=se(),this.visualElement=e}start(e,{snapToCursor:t=!1}={}){let{presenceContext:r}=this.visualElement;if(r&&r.isPresent===!1)return;let n=l=>{let{dragSnapToOrigin:u}=this.getProps();u?this.pauseAnimation():this.stopAnimation(),t&&this.snapToCursor(pn(l,"page").point)},o=(l,u)=>{let{drag:p,dragPropagation:d,onDragStart:h}=this.getProps();if(p&&!d&&(this.openGlobalLock&&this.openGlobalLock(),this.openGlobalLock=wl(p),!this.openGlobalLock))return;this.isDragging=!0,this.currentDirection=null,this.resolveConstraints(),this.visualElement.projection&&(this.visualElement.projection.isAnimationBlocked=!0,this.visualElement.projection.target=void 0),Le(y=>{let x=this.getAxisMotionValue(y).get()||0;if(qe.test(x)){let{projection:b}=this.visualElement;if(b&&b.layout){let g=b.layout.layoutBox[y];g&&(x=Ae(g)*(parseFloat(x)/100))}}this.originPoint[y]=x}),h&&ie.update(()=>h(l,u),!1,!0);let{animationState:f}=this.visualElement;f&&f.setActive("whileDrag",!0)},i=(l,u)=>{let{dragPropagation:p,dragDirectionLock:d,onDirectionLock:h,onDrag:f}=this.getProps();if(!p&&!this.openGlobalLock)return;let{offset:y}=u;if(d&&this.currentDirection===null){this.currentDirection=hg(y),this.currentDirection!==null&&h&&h(this.currentDirection);return}this.updateAxis("x",u.point,y),this.updateAxis("y",u.point,y),this.visualElement.render(),f&&f(l,u)},a=(l,u)=>this.stop(l,u),s=()=>Le(l=>{var u;return this.getAnimationState(l)==="paused"&&((u=this.getAxisMotionValue(l).animation)===null||u===void 0?void 0:u.play())}),{dragSnapToOrigin:c}=this.getProps();this.panSession=new Jl(e,{onSessionStart:n,onStart:o,onMove:i,onSessionEnd:a,resumeAnimation:s},{transformPagePoint:this.visualElement.getTransformPagePoint(),dragSnapToOrigin:c,contextWindow:nc(this.visualElement)})}stop(e,t){let r=this.isDragging;if(this.cancel(),!r)return;let{velocity:n}=t;this.startAnimation(n);let{onDragEnd:o}=this.getProps();o&&ie.update(()=>o(e,t))}cancel(){this.isDragging=!1;let{projection:e,animationState:t}=this.visualElement;e&&(e.isAnimationBlocked=!1),this.panSession&&this.panSession.end(),this.panSession=void 0;let{dragPropagation:r}=this.getProps();!r&&this.openGlobalLock&&(this.openGlobalLock(),this.openGlobalLock=null),t&&t.setActive("whileDrag",!1)}updateAxis(e,t,r){let{drag:n}=this.getProps();if(!r||!jr(e,n,this.currentDirection))return;let o=this.getAxisMotionValue(e),i=this.originPoint[e]+r[e];this.constraints&&this.constraints[e]&&(i=$m(i,this.constraints[e],this.elastic[e])),o.set(i)}resolveConstraints(){var e;let{dragConstraints:t,dragElastic:r}=this.getProps(),n=this.visualElement.projection&&!this.visualElement.projection.layout?this.visualElement.projection.measure(!1):(e=this.visualElement.projection)===null||e===void 0?void 0:e.layout,o=this.constraints;t&&At(t)?this.constraints||(this.constraints=this.resolveRefConstraints()):t&&n?this.constraints=eg(n.layoutBox,t):this.constraints=!1,this.elastic=og(r),o!==this.constraints&&n&&this.constraints&&!this.hasMutatedConstraints&&Le(i=>{this.getAxisMotionValue(i)&&(this.constraints[i]=ng(n.layoutBox[i],this.constraints[i]))})}resolveRefConstraints(){let{dragConstraints:e,onMeasureDragConstraints:t}=this.getProps();if(!e||!At(e))return!1;let r=e.current;Fe(r!==null,"If `dragConstraints` is set as a React ref, that ref must be passed to another component's `ref` prop.");let{projection:n}=this.visualElement;if(!n||!n.layout)return!1;let o=ug(r,n.root,this.visualElement.getTransformPagePoint()),i=tg(n.layout.layoutBox,o);if(t){let a=t(ig(i));this.hasMutatedConstraints=!!a,a&&(i=$l(a))}return i}startAnimation(e){let{drag:t,dragMomentum:r,dragElastic:n,dragTransition:o,dragSnapToOrigin:i,onDragTransitionEnd:a}=this.getProps(),s=this.constraints||{},c=Le(l=>{if(!jr(l,t,this.currentDirection))return;let u=s&&s[l]||{};i&&(u={min:0,max:0});let p=n?200:1e6,d=n?40:1e7,h={type:"inertia",velocity:r?e[l]:0,bounceStiffness:p,bounceDamping:d,timeConstant:750,restDelta:1,restSpeed:10,...o,...u};return this.startAxisValueAnimation(l,h)});return Promise.all(c).then(a)}startAxisValueAnimation(e,t){let r=this.getAxisMotionValue(e);return r.start(zo(e,r,0,t))}stopAnimation(){Le(e=>this.getAxisMotionValue(e).stop())}pauseAnimation(){Le(e=>{var t;return(t=this.getAxisMotionValue(e).animation)===null||t===void 0?void 0:t.pause()})}getAnimationState(e){var t;return(t=this.getAxisMotionValue(e).animation)===null||t===void 0?void 0:t.state}getAxisMotionValue(e){let t="_drag"+e.toUpperCase(),r=this.visualElement.getProps();return r[t]||this.visualElement.getValue(e,(r.initial?r.initial[e]:void 0)||0)}snapToCursor(e){Le(t=>{let{drag:r}=this.getProps();if(!jr(t,r,this.currentDirection))return;let{projection:n}=this.visualElement,o=this.getAxisMotionValue(t);if(n&&n.layout){let{min:i,max:a}=n.layout.layoutBox[t];o.set(e[t]-oe(i,a,.5))}})}scalePositionWithinConstraints(){if(!this.visualElement.current)return;let{drag:e,dragConstraints:t}=this.getProps(),{projection:r}=this.visualElement;if(!At(t)||!r||!this.constraints)return;this.stopAnimation();let n={x:0,y:0};Le(i=>{let a=this.getAxisMotionValue(i);if(a){let s=a.get();n[i]=rg({min:s,max:s},this.constraints[i])}});let{transformTemplate:o}=this.visualElement.getProps();this.visualElement.current.style.transform=o?o({},""):"none",r.root&&r.root.updateScroll(),r.updateLayout(),this.resolveConstraints(),Le(i=>{if(!jr(i,e,null))return;let a=this.getAxisMotionValue(i),{min:s,max:c}=this.constraints[i];a.set(oe(s,c,n[i]))})}addListeners(){if(!this.visualElement.current)return;pg.set(this.visualElement,this);let e=this.visualElement.current,t=Ke(e,"pointerdown",s=>{let{drag:c,dragListener:l=!0}=this.getProps();c&&l&&this.start(s)}),r=()=>{let{dragConstraints:s}=this.getProps();At(s)&&(this.constraints=this.resolveRefConstraints())},{projection:n}=this.visualElement,o=n.addEventListener("measure",r);n&&!n.layout&&(n.root&&n.root.updateScroll(),n.updateLayout()),r();let i=Xe(window,"resize",()=>this.scalePositionWithinConstraints()),a=n.addEventListener("didUpdate",({delta:s,hasLayoutChanged:c})=>{this.isDragging&&c&&(Le(l=>{let u=this.getAxisMotionValue(l);u&&(this.originPoint[l]+=s[l].translate,u.set(u.get()+s[l].translate))}),this.visualElement.render())});return()=>{i(),t(),o(),a&&a()}}getProps(){let e=this.visualElement.getProps(),{drag:t=!1,dragDirectionLock:r=!1,dragPropagation:n=!1,dragConstraints:o=!1,dragElastic:i=io,dragMomentum:a=!0}=e;return{...e,drag:t,dragDirectionLock:r,dragPropagation:n,dragConstraints:o,dragElastic:i,dragMomentum:a}}};fg=class extends dt{constructor(e){super(e),this.removeGroupControls=le,this.removeListeners=le,this.controls=new dg(e)}mount(){let{dragControls:e}=this.node.getProps();e&&(this.removeGroupControls=e.subscribe(this.controls)),this.removeListeners=this.controls.addListeners()||le}unmount(){this.removeGroupControls(),this.removeListeners()}},Va=e=>(t,r)=>{e&&ie.update(()=>e(t,r))},mg=class extends dt{constructor(){super(...arguments),this.removePointerDownListener=le}onPointerDown(e){this.session=new Jl(e,this.createPanHandlers(),{transformPagePoint:this.node.getTransformPagePoint(),contextWindow:nc(this.node)})}createPanHandlers(){let{onPanSessionStart:e,onPanStart:t,onPan:r,onPanEnd:n}=this.node.getProps();return{onSessionStart:Va(e),onStart:Va(t),onMove:r,onEnd:(o,i)=>{delete this.session,n&&ie.update(()=>n(o,i))}}}mount(){this.removePointerDownListener=Ke(this.node.current,"pointerdown",e=>this.onPointerDown(e))}update(){this.session&&this.session.updateHandlers(this.createPanHandlers())}unmount(){this.removePointerDownListener(),this.session&&this.session.end()}};_r={hasAnimatedSinceResize:!0,hasEverUpdated:!1};tr={correct:(e,t)=>{if(!t.target)return e;if(typeof e=="string")if(L.test(e))e=parseFloat(e);else return e;let r=Fa(e,t.target.x),n=Fa(e,t.target.y);return`${r}% ${n}%`}},yg={correct:(e,{treeScale:t,projectionDelta:r})=>{let n=e,o=ut.parse(e);if(o.length>5)return n;let i=ut.createTransformer(e),a=typeof o[0]!="number"?1:0,s=r.x.scale*t.x,c=r.y.scale*t.y;o[0+a]/=s,o[1+a]/=c;let l=oe(s,c,.5);return typeof o[2+a]=="number"&&(o[2+a]/=l),typeof o[3+a]=="number"&&(o[3+a]/=l),i(o)}},vg=class extends m.Component{componentDidMount(){let{visualElement:e,layoutGroup:t,switchLayoutGroup:r,layoutId:n}=this.props,{projection:o}=e;xh(bg),o&&(t.group&&t.group.add(o),r&&r.register&&n&&r.register(o),o.root.didUpdate(),o.addEventListener("animationComplete",()=>{this.safeToRemove()}),o.setOptions({...o.options,onExitComplete:()=>this.safeToRemove()})),_r.hasEverUpdated=!0}getSnapshotBeforeUpdate(e){let{layoutDependency:t,visualElement:r,drag:n,isPresent:o}=this.props,i=r.projection;return i&&(i.isPresent=o,n||e.layoutDependency!==t||t===void 0?i.willUpdate():this.safeToRemove(),e.isPresent!==o&&(o?i.promote():i.relegate()||ie.postRender(()=>{let a=i.getStack();(!a||!a.members.length)&&this.safeToRemove()}))),null}componentDidUpdate(){let{projection:e}=this.props.visualElement;e&&(e.root.didUpdate(),Eo.postRender(()=>{!e.currentAnimation&&e.isLead()&&this.safeToRemove()}))}componentWillUnmount(){let{visualElement:e,layoutGroup:t,switchLayoutGroup:r}=this.props,{projection:n}=e;n&&(n.scheduleCheckAfterUnmount(),t&&t.group&&t.group.remove(n),r&&r.deregister&&r.deregister(n))}safeToRemove(){let{safeToRemove:e}=this.props;e&&e()}render(){return null}};bg={borderRadius:{...tr,applyTo:["borderTopLeftRadius","borderTopRightRadius","borderBottomLeftRadius","borderBottomRightRadius"]},borderTopLeftRadius:tr,borderTopRightRadius:tr,borderBottomLeftRadius:tr,borderBottomRightRadius:tr,boxShadow:yg},ic=["TopLeft","TopRight","BottomLeft","BottomRight"],xg=ic.length,Ba=e=>typeof e=="string"?parseFloat(e):e,za=e=>typeof e=="number"||L.test(e);Tg=ac(0,.5,Dl),Eg=ac(.5,.95,le);Pg=["x","scaleX","originX"],Og=["y","scaleY","originY"];Ag=class{constructor(){this.members=[]}add(e){_o(this.members,e),e.scheduleRender()}remove(e){if(Ho(this.members,e),e===this.prevLead&&(this.prevLead=void 0),e===this.lead){let t=this.members[this.members.length-1];t&&this.promote(t)}}relegate(e){let t=this.members.findIndex(n=>e===n);if(t===0)return!1;let r;for(let n=t;n>=0;n--){let o=this.members[n];if(o.isPresent!==!1){r=o;break}}return r?(this.promote(r),!0):!1}promote(e,t){let r=this.lead;if(e!==r&&(this.prevLead=r,this.lead=e,e.show(),r)){r.instance&&r.scheduleRender(),e.scheduleRender(),e.resumeFrom=r,t&&(e.resumeFrom.preserveOpacity=!0),r.snapshot&&(e.snapshot=r.snapshot,e.snapshot.latestValues=r.animationValues||r.latestValues),e.root&&e.root.isUpdating&&(e.isLayoutDirty=!0);let{crossfade:n}=e.options;n===!1&&r.hide()}}exitAnimationComplete(){this.members.forEach(e=>{let{options:t,resumingFrom:r}=e;t.onExitComplete&&t.onExitComplete(),r&&r.options.onExitComplete&&r.options.onExitComplete()})}scheduleRender(){this.members.forEach(e=>{e.instance&&e.scheduleRender(!1)})}removeLeadSnapshot(){this.lead&&this.lead.snapshot&&(this.lead.snapshot=void 0)}};Rg=(e,t)=>e.depth-t.depth,Ig=class{constructor(){this.children=[],this.isDirty=!1}add(e){_o(this.children,e),this.isDirty=!0}remove(e){Ho(this.children,e),this.isDirty=!0}forEach(e){this.isDirty&&this.children.sort(Rg),this.isDirty=!1,this.children.forEach(e)}};Ka=["","X","Y","Z"],Ng={visibility:"hidden"},Qa=1e3,jg=0,bt={type:"projectionFrame",totalNodes:0,resolvedTargetDeltas:0,recalculatedProjection:0};Qg={duration:.45,ease:[.4,0,.1,1]},ts=e=>typeof navigator<"u"&&navigator.userAgent&&navigator.userAgent.toLowerCase().includes(e),rs=ts("applewebkit/")&&!ts("chrome/")?Math.round:le;Zg=cc({attachResizeListener:(e,t)=>Xe(e,"resize",t),measureScroll:()=>({x:document.documentElement.scrollLeft||document.body.scrollLeft,y:document.documentElement.scrollTop||document.body.scrollTop}),checkIsScrollRoot:()=>!0}),Un={current:void 0},pc=cc({measureScroll:e=>({x:e.scrollLeft,y:e.scrollTop}),defaultParent:()=>{if(!Un.current){let e=new Zg({});e.mount(window),e.setOptions({layoutScroll:!0}),Un.current=e}return Un.current},resetTransform:(e,t)=>{e.style.transform=t!==void 0?t:"none"},checkIsScrollRoot:e=>window.getComputedStyle(e).position==="fixed"}),$g={pan:{Feature:mg},drag:{Feature:fg,ProjectionNode:pc,MeasureLayout:oc}},ey=/var\((--[a-zA-Z0-9-_]+),? ?([a-zA-Z0-9 ()%#.,-]+)?\)/;ry=4;oy=new Set(["width","height","top","left","right","bottom","x","y","translateX","translateY"]),dc=e=>oy.has(e),iy=e=>Object.keys(e).some(dc),Vr=e=>e===zt||e===L,os=(e,t)=>parseFloat(e.split(", ")[t]),is=(e,t)=>(r,{transform:n})=>{if(n==="none"||!n)return 0;let o=n.match(/^matrix3d\((.+)\)$/);if(o)return os(o[1],t);{let i=n.match(/^matrix\((.+)\)$/);return i?os(i[1],e):0}},ay=new Set(["x","y","z"]),sy=xr.filter(e=>!ay.has(e));jt={width:({x:e},{paddingLeft:t="0",paddingRight:r="0"})=>e.max-e.min-parseFloat(t)-parseFloat(r),height:({y:e},{paddingTop:t="0",paddingBottom:r="0"})=>e.max-e.min-parseFloat(t)-parseFloat(r),top:(e,{top:t})=>parseFloat(t),left:(e,{left:t})=>parseFloat(t),bottom:({y:e},{top:t})=>parseFloat(t)+(e.max-e.min),right:({x:e},{left:t})=>parseFloat(t)+(e.max-e.min),x:is(4,13),y:is(5,14)};jt.translateX=jt.x;jt.translateY=jt.y;cy=(e,t,r)=>{let n=t.measureViewportBox(),o=t.current,i=getComputedStyle(o),{display:a}=i,s={};a==="none"&&t.setStaticValue("display",e.display||"block"),r.forEach(l=>{s[l]=jt[l](n,i)}),t.render();let c=t.measureViewportBox();return r.forEach(l=>{let u=t.getValue(l);u&&u.jump(s[l]),e[l]=jt[l](c,i)}),e},uy=(e,t,r={},n={})=>{t={...t},n={...n};let o=Object.keys(t).filter(dc),i=[],a=!1,s=[];if(o.forEach(c=>{let l=e.getValue(c);if(!e.hasValue(c))return;let u=r[c],p=er(u),d=t[c],h;if(Kr(d)){let f=d.length,y=d[0]===null?1:0;u=d[y],p=er(u);for(let x=y;x<f&&d[x]!==null;x++)h?Fe(er(d[x])===h,"All keyframes must be of the same type"):(h=er(d[x]),Fe(h===p||Vr(p)&&Vr(h),"Keyframes must be of the same dimension as the current value"))}else h=er(d);if(p!==h)if(Vr(p)&&Vr(h)){let f=l.get();typeof f=="string"&&l.set(parseFloat(f)),typeof d=="string"?t[c]=parseFloat(d):Array.isArray(d)&&h===L&&(t[c]=d.map(parseFloat))}else p?.transform&&h?.transform&&(u===0||d===0)?u===0?l.set(h.transform(u)):t[c]=p.transform(d):(a||(i=ly(e),a=!0),s.push(c),n[c]=n[c]!==void 0?n[c]:t[c],l.jump(d))}),s.length){let c=s.indexOf("height")>=0?window.pageYOffset:null,l=cy(t,e,s);return i.length&&i.forEach(([u,p])=>{e.getValue(u).set(p)}),e.render(),ln&&c!==null&&window.scrollTo({top:c}),{target:l,transitionEnd:n}}else return{target:t,transitionEnd:n}};dy=(e,t,r,n)=>{let o=ny(e,t,n);return t=o.target,n=o.transitionEnd,py(e,t,r,n)},co={current:null},hc={current:!1};as=new WeakMap,fc=Object.keys(ur),my=fc.length,ss=["AnimationStart","AnimationComplete","Update","BeforeLayoutMeasure","LayoutMeasure","LayoutAnimationStart","LayoutAnimationComplete"],gy=Po.length,yy=class{constructor({parent:e,props:t,presenceContext:r,reducedMotionConfig:n,visualState:o},i={}){this.current=null,this.children=new Set,this.isVariantNode=!1,this.isControllingVariants=!1,this.shouldReduceMotion=null,this.values=new Map,this.features={},this.valueSubscriptions=new Map,this.prevMotionValues={},this.events={},this.propEventSubscriptions={},this.notifyUpdate=()=>this.notify("Update",this.latestValues),this.render=()=>{this.current&&(this.triggerBuild(),this.renderInstance(this.current,this.renderState,this.props.style,this.projection))},this.scheduleRender=()=>ie.render(this.render,!1,!0);let{latestValues:a,renderState:s}=o;this.latestValues=a,this.baseTarget={...a},this.initialValues=t.initial?{...a}:{},this.renderState=s,this.parent=e,this.props=t,this.presenceContext=r,this.depth=e?e.depth+1:0,this.reducedMotionConfig=n,this.options=i,this.isControllingVariants=un(t),this.isVariantNode=ol(t),this.isVariantNode&&(this.variantChildren=new Set),this.manuallyAnimateOnMount=!!(e&&e.current);let{willChange:c,...l}=this.scrapeMotionValuesFromProps(t,{});for(let u in l){let p=l[u];a[u]!==void 0&&Te(p)&&(p.set(a[u],!1),$r(c)&&c.add(u))}}scrapeMotionValuesFromProps(e,t){return{}}mount(e){this.current=e,as.set(e,this),this.projection&&!this.projection.instance&&this.projection.mount(e),this.parent&&this.isVariantNode&&!this.isControllingVariants&&(this.removeFromVariantTree=this.parent.addVariantChild(this)),this.values.forEach((t,r)=>this.bindToMotionValue(r,t)),hc.current||hy(),this.shouldReduceMotion=this.reducedMotionConfig==="never"?!1:this.reducedMotionConfig==="always"?!0:co.current,Wo(this.shouldReduceMotion!==!0,"You have Reduced Motion enabled on your device. Animations may not appear as expected."),this.parent&&this.parent.children.add(this),this.update(this.props,this.presenceContext)}unmount(){as.delete(this.current),this.projection&&this.projection.unmount(),Ze(this.notifyUpdate),Ze(this.render),this.valueSubscriptions.forEach(e=>e()),this.removeFromVariantTree&&this.removeFromVariantTree(),this.parent&&this.parent.children.delete(this);for(let e in this.events)this.events[e].clear();for(let e in this.features)this.features[e].unmount();this.current=null}bindToMotionValue(e,t){let r=St.has(e),n=t.on("change",i=>{this.latestValues[e]=i,this.props.onUpdate&&ie.update(this.notifyUpdate,!1,!0),r&&this.projection&&(this.projection.isTransformDirty=!0)}),o=t.on("renderRequest",this.scheduleRender);this.valueSubscriptions.set(e,()=>{n(),o()})}sortNodePosition(e){return!this.current||!this.sortInstanceNodePosition||this.type!==e.type?0:this.sortInstanceNodePosition(this.current,e.current)}loadFeatures({children:e,...t},r,n,o){let i,a;if(n&&r){let s="You have rendered a `motion` component within a `LazyMotion` component. This will break tree shaking. Import and render a `m` component instead.";t.ignoreStrict?Er(!1,s):Fe(!1,s)}for(let s=0;s<my;s++){let c=fc[s],{isEnabled:l,Feature:u,ProjectionNode:p,MeasureLayout:d}=ur[c];p&&(i=p),l(t)&&(!this.features[c]&&u&&(this.features[c]=new u(this)),d&&(a=d))}if((this.type==="html"||this.type==="svg")&&!this.projection&&i){this.projection=new i(this.latestValues,this.parent&&this.parent.projection);let{layoutId:s,layout:c,drag:l,dragConstraints:u,layoutScroll:p,layoutRoot:d}=t;this.projection.setOptions({layoutId:s,layout:c,alwaysMeasureLayout:!!l||u&&At(u),visualElement:this,scheduleRender:()=>this.scheduleRender(),animationType:typeof c=="string"?c:"both",initialPromotionConfig:o,layoutScroll:p,layoutRoot:d})}return a}updateFeatures(){for(let e in this.features){let t=this.features[e];t.isMounted?t.update():(t.mount(),t.isMounted=!0)}}triggerBuild(){this.build(this.renderState,this.latestValues,this.options,this.props)}measureViewportBox(){return this.current?this.measureInstanceViewportBox(this.current,this.props):se()}getStaticValue(e){return this.latestValues[e]}setStaticValue(e,t){this.latestValues[e]=t}makeTargetAnimatable(e,t=!0){return this.makeTargetAnimatableFromInstance(e,t)}update(e,t){(e.transformTemplate||this.props.transformTemplate)&&this.scheduleRender(),this.prevProps=this.props,this.props=e,this.prevPresenceContext=this.presenceContext,this.presenceContext=t;for(let r=0;r<ss.length;r++){let n=ss[r];this.propEventSubscriptions[n]&&(this.propEventSubscriptions[n](),delete this.propEventSubscriptions[n]);let o=e["on"+n];o&&(this.propEventSubscriptions[n]=this.on(n,o))}this.prevMotionValues=fy(this,this.scrapeMotionValuesFromProps(e,this.prevProps),this.prevMotionValues),this.handleChildMotionValue&&this.handleChildMotionValue()}getProps(){return this.props}getVariant(e){return this.props.variants?this.props.variants[e]:void 0}getDefaultTransition(){return this.props.transition}getTransformPagePoint(){return this.props.transformPagePoint}getClosestVariantNode(){return this.isVariantNode?this:this.parent?this.parent.getClosestVariantNode():void 0}getVariantContext(e=!1){if(e)return this.parent?this.parent.getVariantContext():void 0;if(!this.isControllingVariants){let r=this.parent?this.parent.getVariantContext()||{}:{};return this.props.initial!==void 0&&(r.initial=this.props.initial),r}let t={};for(let r=0;r<gy;r++){let n=Po[r],o=this.props[n];(cr(o)||o===!1)&&(t[n]=o)}return t}addVariantChild(e){let t=this.getClosestVariantNode();if(t)return t.variantChildren&&t.variantChildren.add(e),()=>t.variantChildren.delete(e)}addValue(e,t){t!==this.values.get(e)&&(this.removeValue(e),this.bindToMotionValue(e,t)),this.values.set(e,t),this.latestValues[e]=t.get()}removeValue(e){this.values.delete(e);let t=this.valueSubscriptions.get(e);t&&(t(),this.valueSubscriptions.delete(e)),delete this.latestValues[e],this.removeValueFromRenderState(e,this.renderState)}hasValue(e){return this.values.has(e)}getValue(e,t){if(this.props.values&&this.props.values[e])return this.props.values[e];let r=this.values.get(e);return r===void 0&&t!==void 0&&(r=Nt(t,{owner:this}),this.addValue(e,r)),r}readValue(e){var t;return this.latestValues[e]!==void 0||!this.current?this.latestValues[e]:(t=this.getBaseTargetFromProps(this.props,e))!==null&&t!==void 0?t:this.readValueFromInstance(this.current,e,this.options)}setBaseTarget(e,t){this.baseTarget[e]=t}getBaseTarget(e){var t;let{initial:r}=this.props,n=typeof r=="string"||typeof r=="object"?(t=Do(this.props,r))===null||t===void 0?void 0:t[e]:void 0;if(r&&n!==void 0)return n;let o=this.getBaseTargetFromProps(this.props,e);return o!==void 0&&!Te(o)?o:this.initialValues[e]!==void 0&&n===void 0?void 0:this.baseTarget[e]}on(e,t){return this.events[e]||(this.events[e]=new Uo),this.events[e].add(t)}notify(e,...t){this.events[e]&&this.events[e].notify(...t)}},mc=class extends yy{sortInstanceNodePosition(e,t){return e.compareDocumentPosition(t)&2?1:-1}getBaseTargetFromProps(e,t){return e.style?e.style[t]:void 0}removeValueFromRenderState(e,{vars:t,style:r}){delete t[e],delete r[e]}makeTargetAnimatableFromInstance({transition:e,transitionEnd:t,...r},n){let o=Dm(r,e||{},this);if(n){km(this,r,o);let i=dy(this,r,o,t);t=i.transitionEnd,r=i.target}return{transition:e,transitionEnd:t,...r}}};by=class extends mc{constructor(){super(...arguments),this.type="html"}readValueFromInstance(e,t){if(St.has(t)){let r=Fo(t);return r&&r.default||0}else{let r=vy(e),n=(cl(t)?r.getPropertyValue(t):r[t])||0;return typeof n=="string"?n.trim():n}}measureInstanceViewportBox(e,{transformPagePoint:t}){return rc(e,t)}build(e,t,r,n){Ao(e,t,r,n.transformTemplate)}scrapeMotionValuesFromProps(e,t){return Mo(e,t)}handleChildMotionValue(){this.childSubscription&&(this.childSubscription(),delete this.childSubscription);let{children:e}=this.props;Te(e)&&(this.childSubscription=e.on("change",t=>{this.current&&(this.current.textContent=`${t}`)}))}renderInstance(e,t,r,n){fl(e,t,r,n)}},xy=class extends mc{constructor(){super(...arguments),this.type="svg",this.isSVGTag=!1}getBaseTargetFromProps(e,t){return e[t]}readValueFromInstance(e,t){if(St.has(t)){let r=Fo(t);return r&&r.default||0}return t=ml.has(t)?t:To(t),e.getAttribute(t)}measureInstanceViewportBox(){return se()}scrapeMotionValuesFromProps(e,t){return yl(e,t)}build(e,t,r,n){Io(e,t,r,this.isSVGTag,n.transformTemplate)}renderInstance(e,t,r,n){gl(e,t,r,n)}mount(e){this.isSVGTag=ko(e.tagName),super.mount(e)}},wy=(e,t)=>Oo(e)?new xy(t,{enableHardwareAcceleration:!1}):new by(t,{enableHardwareAcceleration:!0}),Ty={layout:{ProjectionNode:pc,MeasureLayout:oc}},Ey={...Xm,...uf,...$g,...Ty},_t=vh((e,t)=>Qh(e,t,Ey,wy)),Sy=j(_t.div)`
  position: relative;
  z-index: 2;
`,Py=j(_t.div)`
  position: relative;
  padding-top: 10px;
  padding-bottom: 10px;
`;j(_t.div)`
  position: relative;
  padding-top: 12px;
  padding-bottom: 12px;
`;Oy=j.div`
  position: relative;
  box-sizing: border-box;
  background: #171c23;
  width: ${({width:e})=>e}px;
  height: 100%;
  overflow: hidden;
  padding-left: 15px;
  padding-right: 15px;
  padding-top: 4px;
  border-left: ${({theme:e})=>e.base==="dark"?1:0}px solid #fff2;
  border-bottom: ${({theme:e})=>e.base==="dark"?1:0}px solid #fff2;
  border-top: ${({theme:e})=>e.base==="dark"?1:0}px solid #fff2;
  border-radius: 6px 0 0 6px;
  overflow: hidden;

  && {
    pre {
      background: transparent !important;
      margin: 0 !important;
      padding: 0 !important;
    }
  }
`,Cy=j(_t.div)`
  background: #143046;
  position: absolute;
  z-index: 1;
  left: 0;
  top: 44px;
  width: 100%;
  height: 81px;
`,Ay=j(_t.div)`
  position: relative;
  padding-top: 12px;
  padding-bottom: 12px;
  min-height: 57px;
`,Ry={default:{filter:"grayscale(1)",opacity:.5},active:{filter:"grayscale(0)",opacity:1}},Iy=Or(function({active:e,content:t,open:r},n){let o={fontSize:"0.8125rem",lineHeight:"1.1875rem"};return m.createElement(Ay,{ref:n,initial:"default",animate:e?"active":"default","aria-hidden":!e,variants:Ry,transition:{ease:"easeInOut",duration:.6}},t.map(({toggle:i,snippet:a},s)=>m.createElement(mn,{key:s},i===void 0&&m.createElement(Gt,{language:"typescript",customStyle:o},a),i&&!r&&m.createElement(Gt,{language:"typescript",customStyle:o},"  // ..."),i&&r&&m.createElement(_t.div,{initial:{opacity:0},animate:{opacity:1},transition:{delay:.4}},m.createElement(Gt,{language:"typescript",customStyle:o,codeTagProps:{style:{paddingLeft:"15px"}}},a)))))}),ky=({activeStep:e,data:t,width:r,filename:n})=>{let[o,i]=xe([]),a=$e(()=>t.map(()=>Qo()),[t]),s=u=>{let p=0;for(let d=0;d<u;d++)p-=a[d].current.getBoundingClientRect().height;return p},c=Ut(()=>{let u=t.flatMap((p,d)=>{let h=a[d].current.getBoundingClientRect().height,f=[{yPos:s(d),backdropHeight:h,index:d,open:!1}];return p.length>1&&f.push({yPos:s(d),backdropHeight:h,index:d,open:!0}),f});i(u)},[t]);gn(()=>{let u=new ResizeObserver(()=>{c()});return a.forEach(p=>{u.observe(p.current)}),()=>{u.disconnect()}},[]);let l={fontSize:"0.8125rem",lineHeight:"1.1875rem"};return m.createElement(Oy,{width:r},m.createElement(xn,{theme:ii(ai.dark)},m.createElement(Sy,{animate:{y:o[e]?.yPos??0},transition:{ease:"easeInOut",duration:.4}},m.createElement(Py,null,m.createElement(Gt,{language:"typescript",customStyle:l},"// "+n)),t.map((u,p)=>m.createElement(Iy,{key:p,ref:a[p],active:o[e]?.index===p,open:o[e]?.index>p?!0:o[e]?.open??!1,content:u})))),m.createElement(Cy,{initial:{height:81},animate:{height:o[e]?.backdropHeight??81},transition:{ease:"easeInOut",duration:.4},className:"syntax-highlighter-backdrop"}))},My=j.ul(()=>({display:"flex",flexDirection:"column",rowGap:16,padding:0,margin:0})),Dy=({children:e})=>m.createElement(My,null,e),Ly=j.li(()=>({display:"flex",alignItems:"flex-start",columnGap:12})),Ny=j.div`
  font-family: ${({theme:e})=>e.typography.fonts.base};
  color: ${({theme:e})=>e.color.darker};
  font-size: 13px;
  line-height: 18px;
  margin-top: 2px;
`,jy=j.div(({isCompleted:e,theme:t})=>({display:"flex",alignItems:"center",justifyContent:"center",border:`1px solid ${e?"transparent":t.color.medium}`,width:20,height:20,flexShrink:0,borderRadius:"50%",backgroundColor:e?t.color.green:"white",fontFamily:t.typography.fonts.base,fontSize:10,fontWeight:600,color:t.color.dark})),Wn=({children:e,index:t,isCompleted:r})=>m.createElement(Ly,null,m.createElement(jy,{"aria-label":r?"complete":"not complete",isCompleted:r},r?m.createElement(xi,{width:10,height:10,color:"white"}):t),m.createElement(Ny,null,e));Fy=(e,t,r)=>{let[n,o]=xe(null);return ue(()=>{if(e){let i=()=>{r.getChannel().once(ti,()=>{let s=t.getData("example-button--warning");o(s?{data:!0,error:null}:{data:!1,error:null})})},a=r.getChannel();return t.getData("example-button--warning")?o({data:!0,error:null}):a.on(vn,i),()=>{a.off(vn,i)}}},[e]),n},By=(e,t)=>{let[r,n]=xe(null),o=document.querySelector(`.${e}`);return ue(()=>{if(t){let i=new ResizeObserver(()=>{o&&n({top:o.offsetTop,left:o.offsetLeft,height:o.offsetHeight,width:o.offsetWidth})});return i.observe(o),()=>{i.disconnect()}}},[e,t]),r},zy="data:image/png;base64,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",_y="data:image/png;base64,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",Hy="data:image/png;base64,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",nr="STORYBOOK_ADDON_ONBOARDING_CHANNEL",Uy=({onFinish:e,api:t,addonsStore:r,skipOnboarding:n,codeSnippets:o,container:i})=>{let[a,s]=xe("imports"),c=wn(),l={imports:0,meta:1,story:2,args:3,customStory:4},[u,p]=xe(!1),[d,h]=th(),f=Vy(),y=Fy(a==="customStory",t,r),x=By("syntax-highlighter-backdrop",a==="customStory"),b=o?.language==="javascript",g=()=>{let w=o.code[3][0].snippet;navigator.clipboard.writeText(w.replace("// Copy the code below","")),p(!0)},v=Ut(()=>{t.emit(nr,{step:"X:SkippedOnboarding",where:`HowToWriteAStoryModal:${a}`,type:"telemetry"})},[t,a]);return m.createElement(Sd,{width:740,height:430,container:i,defaultOpen:!0},m.createElement(Pd,null,o?m.createElement(ky,{activeStep:l[a]||0,data:o.code,width:480,filename:o.filename}):null,a==="customStory"&&x&&!y?.data&&m.createElement(De,{ref:d,onClick:()=>g(),style:{position:"absolute",opacity:h.width?1:0,top:x.top+x.height-45,left:x.left+x.width-(h.width??0)-10,zIndex:1e3}},u?"Copied to clipboard":"Copy code"),m.createElement(Od,null,m.createElement(Id,null,m.createElement(Md,null),m.createElement(Ld,null),m.createElement(jd,null)),m.createElement(Cd,null,m.createElement(Ct.Title,{asChild:!0},m.createElement(Ad,null,m.createElement(bi,{width:13}),m.createElement("span",null,"How to write a story"))),m.createElement(Ct.Dialog.Close,{onClick:v,asChild:!0},m.createElement(wi,{style:{cursor:"pointer"},width:13,onClick:n,color:c.color.darkest}))),m.createElement(Ct.Description,{asChild:!0},m.createElement(Rd,null,a==="imports"&&m.createElement(m.Fragment,null,m.createElement("div",null,m.createElement("h3",null,"Imports"),b?m.createElement("p",null,"Import a component. In this case, the Button component."):m.createElement(m.Fragment,null,m.createElement("p",null,"First, import ",m.createElement(Fr,null,"Meta")," and"," ",m.createElement(Fr,null,"StoryObj")," for type safety and autocompletion in TypeScript stories."),m.createElement("p",null,"Next, import a component. In this case, the Button component."))),m.createElement(De,{style:{marginTop:4},onClick:()=>{s("meta")}},"Next")),a==="meta"&&m.createElement(m.Fragment,null,m.createElement("div",null,m.createElement("h3",null,"Meta"),m.createElement("p",null,"The default export, Meta, contains metadata about this component's stories. The title field (optional) controls where stories appear in the sidebar."),m.createElement(Mn,{width:"204",alt:"Title property pointing to Storybook's sidebar",src:zy})),m.createElement(Mr,null,m.createElement(De,{variant:"secondary",onClick:()=>s("imports")},"Previous"),m.createElement(De,{onClick:()=>s("story")},"Next"))),a==="story"&&m.createElement(m.Fragment,null,m.createElement("div",null,m.createElement("h3",null,"Story"),m.createElement("p",null,"Each named export is a story. Its contents specify how the story is rendered in addition to other configuration options."),m.createElement(Mn,{width:"190",alt:"Story export pointing to the sidebar entry of the story",src:_y})),m.createElement(Mr,null,m.createElement(De,{variant:"secondary",onClick:()=>s("meta")},"Previous"),m.createElement(De,{onClick:()=>s("args")},"Next"))),a==="args"&&m.createElement(m.Fragment,null,m.createElement("div",null,m.createElement("h3",null,"Args"),m.createElement("p",null,"Args are inputs that are passed to the component, which Storybook uses to render the component in different states. In React, args = props. They also specify the initial control values for the story."),m.createElement(Mn,{alt:"Args mapped to their controls in Storybook",width:"253",src:Hy})),m.createElement(Mr,null,m.createElement(De,{variant:"secondary",onClick:()=>s("story")},"Previous"),m.createElement(De,{onClick:()=>s("customStory")},"Next"))),a==="customStory"&&(y?.error?null:m.createElement(m.Fragment,null,m.createElement("div",null,m.createElement("h3",null,"Create your first story"),m.createElement("p",null,"Now it's your turn. See how easy it is to create your first story by following these steps below."),m.createElement(Dy,null,m.createElement(Wn,{isCompleted:u||!!y?.data,index:1},"Copy the Warning story."),m.createElement(Wn,{isCompleted:!!y?.data,index:2},m.createElement(Vd,null,"Open the Button story in your current working directory."),f?.data&&m.createElement(Fr,null,f.data.replaceAll("/","/\u200B").replaceAll("\\","\\\u200B"))),m.createElement(Wn,{isCompleted:!!y?.data,index:3},"Paste it at the bottom of the file and save."))),m.createElement(Mr,null,m.createElement(De,{variant:"secondary",onClick:()=>s("args")},"Previous"),y?.data?m.createElement(De,{onClick:()=>e()},"Go to story"):null))))))))},Wy={filename:"Button.stories.js",language:"typescript",code:[[{snippet:"import { Button } from './Button';"}],[{snippet:`export default {
      title: 'Example/Button',
      component: Button,
      // ...
    };`}],[{snippet:"export const Primary = {"},{snippet:`args: {
        primary: true,
        label: 'Click',
        background: 'red'
      }`,toggle:!0},{snippet:"};"}],[{snippet:`// Copy the code below
export const Warning = {
  args: {
    primary: true,
    label: 'Delete now',
    backgroundColor: 'red',
  }
};`}]]},qy=Wy,Yy={filename:"Button.stories.ts",language:"typescript",code:[[{snippet:`import type { Meta, StoryObj } from '@storybook/react';
      
      import { Button } from './Button';`}],[{snippet:`const meta: Meta<typeof Button> = {
        title: 'Example/Button',
        component: Button,
        // ...
      };
          
      export default meta;`}],[{snippet:`type Story = StoryObj<Button>;
        
      export const Primary: Story = {`},{snippet:`args: {
          primary: true,
          label: 'Click',
          background: 'red'
        }`,toggle:!0},{snippet:"};"}],[{snippet:`// Copy the code below
  export const Warning: Story = {
    args: {
      primary: true,
      label: 'Delete now',
      backgroundColor: 'red',
    }
  };`}]]},Gy=Yy;Ky=oi()});Z();$();ee();Z();$();ee();Pr();Wt();yn();bn();var Jy=Zo(()=>Promise.resolve().then(()=>(yc(),gc)));Cr.register("@storybook/addon-onboarding",async e=>{let t=e.getUrlState(),r=t.path==="/onboarding"||t.queryParams.onboarding==="true";e.once(ri,()=>{if(!(e.getData("example-button--primary")||document.getElementById("example-button--primary"))){console.warn("[@storybook/addon-onboarding] It seems like you have finished the onboarding experience in Storybook! Therefore this addon is not necessary anymore and will not be loaded. You are free to remove it from your project. More info: https://github.com/storybookjs/storybook/tree/next/code/addons/onboarding#uninstalling");return}if(!r||window.innerWidth<730)return;e.togglePanel(!0),e.togglePanelPosition("bottom"),e.setSelectedPanel("addon-controls");let n=document.createElement("div");n.id="storybook-addon-onboarding",document.body.appendChild(n),ht.render(m.createElement(Ko,{fallback:m.createElement("div",null,"Loading...")},m.createElement(Jy,{api:e})),n)})});})();
}catch(e){ console.error("[Storybook] One of your manager-entries failed: " + import.meta.url, e); }
