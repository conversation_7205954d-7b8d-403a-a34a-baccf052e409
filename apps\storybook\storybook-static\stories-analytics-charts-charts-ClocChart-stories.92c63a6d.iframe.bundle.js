"use strict";(self.webpackChunk_cloc_storybook=self.webpackChunk_cloc_storybook||[]).push([[8359],{"./src/stories/analytics-charts/charts/ClocChart.stories.tsx":(__unused_webpack_module,__webpack_exports__,__webpack_require__)=>{var _Default_parameters,_Default_parameters_docs,_Default_parameters1,_Default_parameters_docs1,_Default_parameters2,_BarVertical_parameters,_BarVertical_parameters_docs,_BarVertical_parameters1,_BarVertical_parameters_docs1,_BarVertical_parameters2,_AreaChart_parameters,_AreaChart_parameters_docs,_AreaChart_parameters1,_AreaChart_parameters_docs1,_AreaChart_parameters2,_LineChart_parameters,_LineChart_parameters_docs,_LineChart_parameters1,_LineChart_parameters_docs1,_LineChart_parameters2,_PieChart_parameters,_PieChart_parameters_docs,_PieChart_parameters1,_PieChart_parameters_docs1,_PieChart_parameters2,_RadarChart_parameters,_RadarChart_parameters_docs,_RadarChart_parameters1,_RadarChart_parameters_docs1,_RadarChart_parameters2,_RadialChart_parameters,_RadialChart_parameters_docs,_RadialChart_parameters1,_RadialChart_parameters_docs1,_RadialChart_parameters2,_TooltipChart_parameters,_TooltipChart_parameters_docs,_TooltipChart_parameters1,_TooltipChart_parameters_docs1,_TooltipChart_parameters2;__webpack_require__.r(__webpack_exports__),__webpack_require__.d(__webpack_exports__,{AreaChart:()=>AreaChart,BarVertical:()=>BarVertical,Default:()=>Default,LineChart:()=>LineChart,PieChart:()=>PieChart,RadarChart:()=>RadarChart,RadialChart:()=>RadialChart,TooltipChart:()=>TooltipChart,__namedExportsOrder:()=>__namedExportsOrder,default:()=>__WEBPACK_DEFAULT_EXPORT__});const __WEBPACK_DEFAULT_EXPORT__={title:"Charts & Reports/Charts/Cloc Chart",component:__webpack_require__("../../packages/toolkit/atoms/dist/index.es.js").S8R,parameters:{layout:"centered",docs:{description:{component:"\nClocChart is a comprehensive chart component that provides multiple visualization types for time tracking and productivity data. It integrates seamlessly with the ClocProvider context to display real-time data with professional styling and interactive features.\n\n### Key Capabilities\n\n- **8 Chart Types**: Bar, bar-vertical, area, line, pie, radar, radial, and tooltip charts for diverse visualization needs\n- **Context Integration**: Automatically retrieves data from ClocProvider context with real-time updates\n- **Loading Management**: Built-in loading states with overlay spinner during data fetching\n- **Theme Compatibility**: Seamless integration with Cloc theme system for consistent styling\n- **Responsive Design**: Minimum width of 400px with flexible container adaptation\n- **Interactive Features**: Hover effects, tooltips, and data exploration capabilities\n\n### Data Flow\n\nThe component uses the `useClocContext()` hook to access:\n- `report` - Chart data transformed using the `transformData()` utility\n- `reportLoading` - Loading state for displaying spinner overlay\n- `appliedTheme` - Current theme configuration for styling\n- `config` - Component configuration settings\n\n### Chart Type Selection\n\nEach chart type is optimized for specific data visualization scenarios:\n- **Bar/Bar-Vertical**: Ideal for comparing discrete values and categories\n- **Area/Line**: Perfect for time series data and trend analysis\n- **Pie**: Best for showing proportional relationships and percentages\n- **Radar**: Excellent for multi-dimensional comparisons and performance metrics\n- **Radial**: Great for progress indicators and goal tracking\n- **Tooltip**: Enhanced interactivity for detailed data exploration\n\n### Technical Implementation\n\nThe component renders different chart types based on the `type` prop, with each chart receiving transformed data and theme configuration. Loading states are managed through the SpinOverlayLoader component for consistent user experience.\n                "}}},argTypes:{type:{control:"select",options:["bar","bar-vertical","area","pie","line","radar","radial","tooltip"],description:"The type of chart to display",table:{type:{summary:"ChartType"},defaultValue:{summary:"bar"}}},className:{control:"text",description:"Additional CSS classes to apply to the chart container",table:{type:{summary:"string"},defaultValue:{summary:"undefined"}}}}},Default={args:{type:"bar"},parameters:{docs:{description:{story:"The default ClocChart component with horizontal bar visualization. Perfect for comparing discrete values and showing data relationships in a clear, easy-to-read format."}}}},BarVertical={args:{type:"bar-vertical"},parameters:{docs:{description:{story:"Vertical bar chart (column chart) visualization ideal for displaying time series data and comparing values across categories with traditional column format."}}}},AreaChart={args:{type:"area"},parameters:{docs:{description:{story:"Area chart visualization with filled regions, perfect for trend analysis and showing cumulative data over time periods with visual emphasis on data volume."}}}},LineChart={args:{type:"line"},parameters:{docs:{description:{story:"Clean line chart visualization ideal for time series analysis and trend identification. Shows data changes over time with clear, minimal visual design."}}}},PieChart={args:{type:"pie"},parameters:{docs:{description:{story:"Pie chart visualization for displaying proportional data and percentages. Ideal for showing how different categories contribute to the total dataset."}}}},RadarChart={args:{type:"radar"},parameters:{docs:{description:{story:"Multi-dimensional radar (spider) chart for comparing multiple variables simultaneously. Perfect for performance metrics and multi-criteria analysis."}}}},RadialChart={args:{type:"radial"},parameters:{docs:{description:{story:"Circular radial chart visualization ideal for progress indicators, goal tracking, and completion percentages with modern, clean design."}}}},TooltipChart={args:{type:"tooltip"},parameters:{docs:{description:{story:"Interactive chart with enhanced tooltip functionality for detailed data exploration. Provides comprehensive information on hover for in-depth analysis."}}}};Default.parameters={...Default.parameters,docs:{...null===(_Default_parameters=Default.parameters)||void 0===_Default_parameters?void 0:_Default_parameters.docs,source:{originalSource:"{\n  args: {\n    type: 'bar'\n  },\n  parameters: {\n    docs: {\n      description: {\n        story: 'The default ClocChart component with horizontal bar visualization. Perfect for comparing discrete values and showing data relationships in a clear, easy-to-read format.'\n      }\n    }\n  }\n}",...null===(_Default_parameters1=Default.parameters)||void 0===_Default_parameters1||null===(_Default_parameters_docs=_Default_parameters1.docs)||void 0===_Default_parameters_docs?void 0:_Default_parameters_docs.source},description:{story:"Default bar chart visualization showing horizontal bars for data comparison.\r\nIdeal for comparing values across different categories or time periods.",...null===(_Default_parameters2=Default.parameters)||void 0===_Default_parameters2||null===(_Default_parameters_docs1=_Default_parameters2.docs)||void 0===_Default_parameters_docs1?void 0:_Default_parameters_docs1.description}}},BarVertical.parameters={...BarVertical.parameters,docs:{...null===(_BarVertical_parameters=BarVertical.parameters)||void 0===_BarVertical_parameters?void 0:_BarVertical_parameters.docs,source:{originalSource:"{\n  args: {\n    type: 'bar-vertical'\n  },\n  parameters: {\n    docs: {\n      description: {\n        story: 'Vertical bar chart (column chart) visualization ideal for displaying time series data and comparing values across categories with traditional column format.'\n      }\n    }\n  }\n}",...null===(_BarVertical_parameters1=BarVertical.parameters)||void 0===_BarVertical_parameters1||null===(_BarVertical_parameters_docs=_BarVertical_parameters1.docs)||void 0===_BarVertical_parameters_docs?void 0:_BarVertical_parameters_docs.source},description:{story:"Vertical bar chart with traditional column display format.\r\nExcellent for time series data and categorical comparisons.",...null===(_BarVertical_parameters2=BarVertical.parameters)||void 0===_BarVertical_parameters2||null===(_BarVertical_parameters_docs1=_BarVertical_parameters2.docs)||void 0===_BarVertical_parameters_docs1?void 0:_BarVertical_parameters_docs1.description}}},AreaChart.parameters={...AreaChart.parameters,docs:{...null===(_AreaChart_parameters=AreaChart.parameters)||void 0===_AreaChart_parameters?void 0:_AreaChart_parameters.docs,source:{originalSource:"{\n  args: {\n    type: 'area'\n  },\n  parameters: {\n    docs: {\n      description: {\n        story: 'Area chart visualization with filled regions, perfect for trend analysis and showing cumulative data over time periods with visual emphasis on data volume.'\n      }\n    }\n  }\n}",...null===(_AreaChart_parameters1=AreaChart.parameters)||void 0===_AreaChart_parameters1||null===(_AreaChart_parameters_docs=_AreaChart_parameters1.docs)||void 0===_AreaChart_parameters_docs?void 0:_AreaChart_parameters_docs.source},description:{story:"Area chart with filled regions for trend analysis and cumulative data.\r\nPerfect for showing data trends over time with visual emphasis.",...null===(_AreaChart_parameters2=AreaChart.parameters)||void 0===_AreaChart_parameters2||null===(_AreaChart_parameters_docs1=_AreaChart_parameters2.docs)||void 0===_AreaChart_parameters_docs1?void 0:_AreaChart_parameters_docs1.description}}},LineChart.parameters={...LineChart.parameters,docs:{...null===(_LineChart_parameters=LineChart.parameters)||void 0===_LineChart_parameters?void 0:_LineChart_parameters.docs,source:{originalSource:"{\n  args: {\n    type: 'line'\n  },\n  parameters: {\n    docs: {\n      description: {\n        story: 'Clean line chart visualization ideal for time series analysis and trend identification. Shows data changes over time with clear, minimal visual design.'\n      }\n    }\n  }\n}",...null===(_LineChart_parameters1=LineChart.parameters)||void 0===_LineChart_parameters1||null===(_LineChart_parameters_docs=_LineChart_parameters1.docs)||void 0===_LineChart_parameters_docs?void 0:_LineChart_parameters_docs.source},description:{story:"Line chart for clean trend visualization and time series analysis.\r\nIdeal for showing data changes and patterns over time.",...null===(_LineChart_parameters2=LineChart.parameters)||void 0===_LineChart_parameters2||null===(_LineChart_parameters_docs1=_LineChart_parameters2.docs)||void 0===_LineChart_parameters_docs1?void 0:_LineChart_parameters_docs1.description}}},PieChart.parameters={...PieChart.parameters,docs:{...null===(_PieChart_parameters=PieChart.parameters)||void 0===_PieChart_parameters?void 0:_PieChart_parameters.docs,source:{originalSource:"{\n  args: {\n    type: 'pie'\n  },\n  parameters: {\n    docs: {\n      description: {\n        story: 'Pie chart visualization for displaying proportional data and percentages. Ideal for showing how different categories contribute to the total dataset.'\n      }\n    }\n  }\n}",...null===(_PieChart_parameters1=PieChart.parameters)||void 0===_PieChart_parameters1||null===(_PieChart_parameters_docs=_PieChart_parameters1.docs)||void 0===_PieChart_parameters_docs?void 0:_PieChart_parameters_docs.source},description:{story:"Pie chart for proportional data display and percentage visualization.\r\nPerfect for showing parts of a whole and data distribution.",...null===(_PieChart_parameters2=PieChart.parameters)||void 0===_PieChart_parameters2||null===(_PieChart_parameters_docs1=_PieChart_parameters2.docs)||void 0===_PieChart_parameters_docs1?void 0:_PieChart_parameters_docs1.description}}},RadarChart.parameters={...RadarChart.parameters,docs:{...null===(_RadarChart_parameters=RadarChart.parameters)||void 0===_RadarChart_parameters?void 0:_RadarChart_parameters.docs,source:{originalSource:"{\n  args: {\n    type: 'radar'\n  },\n  parameters: {\n    docs: {\n      description: {\n        story: 'Multi-dimensional radar (spider) chart for comparing multiple variables simultaneously. Perfect for performance metrics and multi-criteria analysis.'\n      }\n    }\n  }\n}",...null===(_RadarChart_parameters1=RadarChart.parameters)||void 0===_RadarChart_parameters1||null===(_RadarChart_parameters_docs=_RadarChart_parameters1.docs)||void 0===_RadarChart_parameters_docs?void 0:_RadarChart_parameters_docs.source},description:{story:"Radar chart for multi-dimensional data comparison and performance metrics.\r\nExcellent for comparing multiple variables across different entities.",...null===(_RadarChart_parameters2=RadarChart.parameters)||void 0===_RadarChart_parameters2||null===(_RadarChart_parameters_docs1=_RadarChart_parameters2.docs)||void 0===_RadarChart_parameters_docs1?void 0:_RadarChart_parameters_docs1.description}}},RadialChart.parameters={...RadialChart.parameters,docs:{...null===(_RadialChart_parameters=RadialChart.parameters)||void 0===_RadialChart_parameters?void 0:_RadialChart_parameters.docs,source:{originalSource:"{\n  args: {\n    type: 'radial'\n  },\n  parameters: {\n    docs: {\n      description: {\n        story: 'Circular radial chart visualization ideal for progress indicators, goal tracking, and completion percentages with modern, clean design.'\n      }\n    }\n  }\n}",...null===(_RadialChart_parameters1=RadialChart.parameters)||void 0===_RadialChart_parameters1||null===(_RadialChart_parameters_docs=_RadialChart_parameters1.docs)||void 0===_RadialChart_parameters_docs?void 0:_RadialChart_parameters_docs.source},description:{story:"Radial chart for circular progress visualization and goal tracking.\r\nGreat for showing completion percentages and progress indicators.",...null===(_RadialChart_parameters2=RadialChart.parameters)||void 0===_RadialChart_parameters2||null===(_RadialChart_parameters_docs1=_RadialChart_parameters2.docs)||void 0===_RadialChart_parameters_docs1?void 0:_RadialChart_parameters_docs1.description}}},TooltipChart.parameters={...TooltipChart.parameters,docs:{...null===(_TooltipChart_parameters=TooltipChart.parameters)||void 0===_TooltipChart_parameters?void 0:_TooltipChart_parameters.docs,source:{originalSource:"{\n  args: {\n    type: 'tooltip'\n  },\n  parameters: {\n    docs: {\n      description: {\n        story: 'Interactive chart with enhanced tooltip functionality for detailed data exploration. Provides comprehensive information on hover for in-depth analysis.'\n      }\n    }\n  }\n}",...null===(_TooltipChart_parameters1=TooltipChart.parameters)||void 0===_TooltipChart_parameters1||null===(_TooltipChart_parameters_docs=_TooltipChart_parameters1.docs)||void 0===_TooltipChart_parameters_docs?void 0:_TooltipChart_parameters_docs.source},description:{story:"Enhanced tooltip chart with interactive data exploration features.\r\nProvides detailed information on hover for comprehensive data analysis.",...null===(_TooltipChart_parameters2=TooltipChart.parameters)||void 0===_TooltipChart_parameters2||null===(_TooltipChart_parameters_docs1=_TooltipChart_parameters2.docs)||void 0===_TooltipChart_parameters_docs1?void 0:_TooltipChart_parameters_docs1.description}}};const __namedExportsOrder=["Default","BarVertical","AreaChart","LineChart","PieChart","RadarChart","RadialChart","TooltipChart"]}}]);