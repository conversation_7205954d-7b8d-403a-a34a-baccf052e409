"use strict";(self.webpackChunk_cloc_storybook=self.webpackChunk_cloc_storybook||[]).push([[6769],{"./src/stories/analytics-charts/reports/ClocReport.stories.tsx":(__unused_webpack_module,__webpack_exports__,__webpack_require__)=>{var _Default_parameters,_Default_parameters_docs,_Default_parameters1,_Default_parameters_docs1,_Default_parameters2,_SmallSize_parameters,_SmallSize_parameters_docs,_SmallSize_parameters1,_SmallSize_parameters_docs1,_SmallSize_parameters2,_LargeSize_parameters,_LargeSize_parameters_docs,_LargeSize_parameters1,_LargeSize_parameters_docs1,_LargeSize_parameters2,_BorderedVariant_parameters,_BorderedVariant_parameters_docs,_BorderedVariant_parameters1,_BorderedVariant_parameters_docs1,_BorderedVariant_parameters2,_AreaChartReport_parameters,_AreaChartReport_parameters_docs,_AreaChartReport_parameters1,_AreaChartReport_parameters_docs1,_AreaChartReport_parameters2,_TooltipChartReport_parameters,_TooltipChartReport_parameters_docs,_TooltipChartReport_parameters1,_TooltipChartReport_parameters_docs1,_TooltipChartReport_parameters2,_LineChartReport_parameters,_LineChartReport_parameters_docs,_LineChartReport_parameters1,_LineChartReport_parameters_docs1,_LineChartReport_parameters2,_BarVerticalChartReport_parameters,_BarVerticalChartReport_parameters_docs,_BarVerticalChartReport_parameters1,_BarVerticalChartReport_parameters_docs1,_BarVerticalChartReport_parameters2,_BarChartReport_parameters,_BarChartReport_parameters_docs,_BarChartReport_parameters1,_BarChartReport_parameters_docs1,_BarChartReport_parameters2,_PieChartReport_parameters,_PieChartReport_parameters_docs,_PieChartReport_parameters1,_PieChartReport_parameters_docs1,_PieChartReport_parameters2,_RadarChartReport_parameters,_RadarChartReport_parameters_docs,_RadarChartReport_parameters1,_RadarChartReport_parameters_docs1,_RadarChartReport_parameters2,_RadialChartReport_parameters,_RadialChartReport_parameters_docs,_RadialChartReport_parameters1,_RadialChartReport_parameters_docs1,_RadialChartReport_parameters2;__webpack_require__.r(__webpack_exports__),__webpack_require__.d(__webpack_exports__,{AreaChartReport:()=>AreaChartReport,BarChartReport:()=>BarChartReport,BarVerticalChartReport:()=>BarVerticalChartReport,BorderedVariant:()=>BorderedVariant,Default:()=>Default,LargeSize:()=>LargeSize,LineChartReport:()=>LineChartReport,PieChartReport:()=>PieChartReport,RadarChartReport:()=>RadarChartReport,RadialChartReport:()=>RadialChartReport,SmallSize:()=>SmallSize,TooltipChartReport:()=>TooltipChartReport,__namedExportsOrder:()=>__namedExportsOrder,default:()=>__WEBPACK_DEFAULT_EXPORT__});const __WEBPACK_DEFAULT_EXPORT__={title:"Charts & Reports/Reports/Cloc Report",component:__webpack_require__("../../packages/toolkit/atoms/dist/index.es.js").c02,parameters:{layout:"centered",docs:{description:{component:"\nA comprehensive chart-based reporting component that visualizes worked time data for employees and teams.\n\n**Features:**\n- Multiple chart types: bar, bar-vertical, area, pie, line, radar, radial, tooltip\n- Real-time data from ClocProvider context (report data)\n- Employee selector for admin users\n- Date range picker for custom time periods\n- Responsive design with multiple size variants\n- Loading states with overlay spinner\n- Automatic data transformation and chart configuration\n- Theme-aware styling and colors\n\n**Data Source:**\n- Gets data from `useClocContext().report` (transformed chart data)\n- Displays worked time by employee in selected date range\n- Loading state managed through `reportLoading`\n- Admin users can view team data, regular users see personal data\n\n**Chart Types:**\n- **Bar**: Horizontal bar chart (default)\n- **Bar-vertical**: Vertical bar chart\n- **Area**: Area chart with filled regions\n- **Line**: Line chart for trends\n- **Pie**: Circular pie chart\n- **Radar**: Radar/spider chart\n- **Radial**: Radial progress chart\n- **Tooltip**: Interactive tooltip-enhanced chart\n\n**Use Cases:**\n- Time tracking dashboards\n- Team productivity analytics\n- Employee performance reports\n- Time allocation visualization\n- Billing and invoicing reports\n                "}}},argTypes:{type:{control:{type:"select"},options:["bar","bar-vertical","area","pie","line","radar","radial","tooltip"],description:"Chart type for data visualization",defaultValue:"bar"},variant:{control:{type:"select"},options:["default","bordered"],description:"Visual style variant of the report component",defaultValue:"default"},size:{control:{type:"select"},options:["default","sm","lg"],description:"Size variant controlling width and spacing",defaultValue:"default"},className:{control:{type:"text"},description:"Additional CSS classes for custom styling"},draggable:{control:{type:"boolean"},description:"Whether the component can be dragged.",defaultValue:!1}}},Default={parameters:{docs:{description:{story:"\nThe default bar chart report displays worked time data in horizontal bars.\nPerfect for comparing time worked between different employees or time periods.\n\n**Default Configuration:**\n- Chart Type: Horizontal bar chart\n- Size: 700px width, 600px height\n- Data: Real-time from report context\n- Features: Employee selector, date range picker\n            "}}}},SmallSize={args:{size:"sm"},parameters:{docs:{description:{story:"\nCompact size variant perfect for dashboards with limited space.\nMaintains full functionality while reducing footprint.\n\n**Dimensions:**\n- Width: 600px (reduced from 700px)\n- Padding: 16px (reduced from 20px)\n- Text: Small size\n- Same height: 600px\n            "}}}},LargeSize={args:{size:"lg"},parameters:{docs:{description:{story:"\nLarge size variant for prominent dashboard placement.\nProvides maximum space for detailed data visualization.\n\n**Dimensions:**\n- Width: Full width with 1200px minimum\n- Padding: 40px horizontal (increased)\n- Same height: 600px\n- Enhanced visibility\n            "}}}},BorderedVariant={args:{variant:"bordered"},parameters:{docs:{description:{story:"\nBordered variant adding visual frame around the report component.\nPerfect for layouts requiring clear component separation.\n\n**Features:**\n- 2px border with secondary color\n- Enhanced visual separation\n- Professional appearance\n- Maintains all functionality\n            "}}}},AreaChartReport={args:{type:"area"},parameters:{docs:{description:{story:"\nArea chart visualization with filled regions showing time trends over periods.\nExcellent for visualizing cumulative time data and identifying patterns.\n\n**Features:**\n- Smooth area curves\n- Filled regions for visual impact\n- Trend analysis capabilities\n- Time period comparisons\n            "}}}},TooltipChartReport={args:{type:"tooltip"},parameters:{docs:{description:{story:"\nEnhanced chart with interactive tooltips providing detailed information.\nPerfect for detailed data exploration and analysis.\n\n**Features:**\n- Interactive hover tooltips\n- Detailed data on demand\n- Enhanced user experience\n- Rich information display\n            "}}}},LineChartReport={args:{type:"line"},parameters:{docs:{description:{story:"\nLine chart visualization perfect for trend analysis and time series data.\nShows clear progression and patterns in worked time over periods.\n\n**Features:**\n- Connected data points\n- Clear trend visualization\n- Time series analysis\n- Pattern identification\n            "}}}},BarVerticalChartReport={args:{type:"bar-vertical"},parameters:{docs:{description:{story:"\nVertical bar chart orientation providing alternative layout for time data.\nUseful when horizontal space is limited or for different visual preferences.\n\n**Features:**\n- Vertical bar orientation\n- Space-efficient layout\n- Alternative visualization\n- Same data functionality\n            "}}}},BarChartReport={args:{type:"bar"},parameters:{docs:{description:{story:"\nStandard horizontal bar chart for clear time data comparison.\nThe classic visualization for comparing worked time between employees.\n\n**Features:**\n- Horizontal bar layout\n- Easy comparison\n- Standard visualization\n- Clear data representation\n            "}}}},PieChartReport={args:{type:"pie"},parameters:{docs:{description:{story:"\nPie chart visualization showing time distribution as proportional segments.\nPerfect for understanding relative time allocation between employees.\n\n**Features:**\n- Circular segment layout\n- Proportional representation\n- Visual impact\n- Easy percentage comparison\n\n**Note:** Currently under development - shows placeholder message.\n            "}}}},RadarChartReport={args:{type:"radar"},parameters:{docs:{description:{story:"\nRadar chart visualization displaying time data across multiple dimensions.\nExcellent for comparing performance across different metrics simultaneously.\n\n**Features:**\n- Multi-dimensional analysis\n- Spider web layout\n- Performance comparison\n- Pattern recognition\n            "}}}},RadialChartReport={args:{type:"radial"},parameters:{docs:{description:{story:"\nRadial chart visualization showing time data in circular progress format.\nPerfect for displaying completion rates and progress indicators.\n\n**Features:**\n- Circular progress layout\n- Visual progress indication\n- Completion tracking\n- Modern design aesthetic\n            "}}}};Default.parameters={...Default.parameters,docs:{...null===(_Default_parameters=Default.parameters)||void 0===_Default_parameters?void 0:_Default_parameters.docs,source:{originalSource:"{\n  parameters: {\n    docs: {\n      description: {\n        story: `\nThe default bar chart report displays worked time data in horizontal bars.\nPerfect for comparing time worked between different employees or time periods.\n\n**Default Configuration:**\n- Chart Type: Horizontal bar chart\n- Size: 700px width, 600px height\n- Data: Real-time from report context\n- Features: Employee selector, date range picker\n            `\n      }\n    }\n  }\n}",...null===(_Default_parameters1=Default.parameters)||void 0===_Default_parameters1||null===(_Default_parameters_docs=_Default_parameters1.docs)||void 0===_Default_parameters_docs?void 0:_Default_parameters_docs.source},description:{story:"Default bar chart report showing worked time data.\r\nDisplays horizontal bars for easy comparison between employees.",...null===(_Default_parameters2=Default.parameters)||void 0===_Default_parameters2||null===(_Default_parameters_docs1=_Default_parameters2.docs)||void 0===_Default_parameters_docs1?void 0:_Default_parameters_docs1.description}}},SmallSize.parameters={...SmallSize.parameters,docs:{...null===(_SmallSize_parameters=SmallSize.parameters)||void 0===_SmallSize_parameters?void 0:_SmallSize_parameters.docs,source:{originalSource:"{\n  args: {\n    size: 'sm'\n  },\n  parameters: {\n    docs: {\n      description: {\n        story: `\nCompact size variant perfect for dashboards with limited space.\nMaintains full functionality while reducing footprint.\n\n**Dimensions:**\n- Width: 600px (reduced from 700px)\n- Padding: 16px (reduced from 20px)\n- Text: Small size\n- Same height: 600px\n            `\n      }\n    }\n  }\n}",...null===(_SmallSize_parameters1=SmallSize.parameters)||void 0===_SmallSize_parameters1||null===(_SmallSize_parameters_docs=_SmallSize_parameters1.docs)||void 0===_SmallSize_parameters_docs?void 0:_SmallSize_parameters_docs.source},description:{story:"Small size variant with compact dimensions.\r\nReduced width and padding for space-constrained layouts.",...null===(_SmallSize_parameters2=SmallSize.parameters)||void 0===_SmallSize_parameters2||null===(_SmallSize_parameters_docs1=_SmallSize_parameters2.docs)||void 0===_SmallSize_parameters_docs1?void 0:_SmallSize_parameters_docs1.description}}},LargeSize.parameters={...LargeSize.parameters,docs:{...null===(_LargeSize_parameters=LargeSize.parameters)||void 0===_LargeSize_parameters?void 0:_LargeSize_parameters.docs,source:{originalSource:"{\n  args: {\n    size: 'lg'\n  },\n  parameters: {\n    docs: {\n      description: {\n        story: `\nLarge size variant for prominent dashboard placement.\nProvides maximum space for detailed data visualization.\n\n**Dimensions:**\n- Width: Full width with 1200px minimum\n- Padding: 40px horizontal (increased)\n- Same height: 600px\n- Enhanced visibility\n            `\n      }\n    }\n  }\n}",...null===(_LargeSize_parameters1=LargeSize.parameters)||void 0===_LargeSize_parameters1||null===(_LargeSize_parameters_docs=_LargeSize_parameters1.docs)||void 0===_LargeSize_parameters_docs?void 0:_LargeSize_parameters_docs.source},description:{story:"Large size variant with expanded dimensions.\r\nIncreased width and padding for prominent display.",...null===(_LargeSize_parameters2=LargeSize.parameters)||void 0===_LargeSize_parameters2||null===(_LargeSize_parameters_docs1=_LargeSize_parameters2.docs)||void 0===_LargeSize_parameters_docs1?void 0:_LargeSize_parameters_docs1.description}}},BorderedVariant.parameters={...BorderedVariant.parameters,docs:{...null===(_BorderedVariant_parameters=BorderedVariant.parameters)||void 0===_BorderedVariant_parameters?void 0:_BorderedVariant_parameters.docs,source:{originalSource:"{\n  args: {\n    variant: 'bordered'\n  },\n  parameters: {\n    docs: {\n      description: {\n        story: `\nBordered variant adding visual frame around the report component.\nPerfect for layouts requiring clear component separation.\n\n**Features:**\n- 2px border with secondary color\n- Enhanced visual separation\n- Professional appearance\n- Maintains all functionality\n            `\n      }\n    }\n  }\n}",...null===(_BorderedVariant_parameters1=BorderedVariant.parameters)||void 0===_BorderedVariant_parameters1||null===(_BorderedVariant_parameters_docs=_BorderedVariant_parameters1.docs)||void 0===_BorderedVariant_parameters_docs?void 0:_BorderedVariant_parameters_docs.source},description:{story:"Bordered variant with visual frame.\r\nAdds border styling for enhanced visual separation.",...null===(_BorderedVariant_parameters2=BorderedVariant.parameters)||void 0===_BorderedVariant_parameters2||null===(_BorderedVariant_parameters_docs1=_BorderedVariant_parameters2.docs)||void 0===_BorderedVariant_parameters_docs1?void 0:_BorderedVariant_parameters_docs1.description}}},AreaChartReport.parameters={...AreaChartReport.parameters,docs:{...null===(_AreaChartReport_parameters=AreaChartReport.parameters)||void 0===_AreaChartReport_parameters?void 0:_AreaChartReport_parameters.docs,source:{originalSource:"{\n  args: {\n    type: 'area'\n  },\n  parameters: {\n    docs: {\n      description: {\n        story: `\nArea chart visualization with filled regions showing time trends over periods.\nExcellent for visualizing cumulative time data and identifying patterns.\n\n**Features:**\n- Smooth area curves\n- Filled regions for visual impact\n- Trend analysis capabilities\n- Time period comparisons\n            `\n      }\n    }\n  }\n}",...null===(_AreaChartReport_parameters1=AreaChartReport.parameters)||void 0===_AreaChartReport_parameters1||null===(_AreaChartReport_parameters_docs=_AreaChartReport_parameters1.docs)||void 0===_AreaChartReport_parameters_docs?void 0:_AreaChartReport_parameters_docs.source},description:{story:"Area chart report with filled regions.\r\nShows time trends with smooth curves and filled areas.",...null===(_AreaChartReport_parameters2=AreaChartReport.parameters)||void 0===_AreaChartReport_parameters2||null===(_AreaChartReport_parameters_docs1=_AreaChartReport_parameters2.docs)||void 0===_AreaChartReport_parameters_docs1?void 0:_AreaChartReport_parameters_docs1.description}}},TooltipChartReport.parameters={...TooltipChartReport.parameters,docs:{...null===(_TooltipChartReport_parameters=TooltipChartReport.parameters)||void 0===_TooltipChartReport_parameters?void 0:_TooltipChartReport_parameters.docs,source:{originalSource:"{\n  args: {\n    type: 'tooltip'\n  },\n  parameters: {\n    docs: {\n      description: {\n        story: `\nEnhanced chart with interactive tooltips providing detailed information.\nPerfect for detailed data exploration and analysis.\n\n**Features:**\n- Interactive hover tooltips\n- Detailed data on demand\n- Enhanced user experience\n- Rich information display\n            `\n      }\n    }\n  }\n}",...null===(_TooltipChartReport_parameters1=TooltipChartReport.parameters)||void 0===_TooltipChartReport_parameters1||null===(_TooltipChartReport_parameters_docs=_TooltipChartReport_parameters1.docs)||void 0===_TooltipChartReport_parameters_docs?void 0:_TooltipChartReport_parameters_docs.source},description:{story:"Interactive tooltip-enhanced chart.\r\nProvides detailed information on hover interactions.",...null===(_TooltipChartReport_parameters2=TooltipChartReport.parameters)||void 0===_TooltipChartReport_parameters2||null===(_TooltipChartReport_parameters_docs1=_TooltipChartReport_parameters2.docs)||void 0===_TooltipChartReport_parameters_docs1?void 0:_TooltipChartReport_parameters_docs1.description}}},LineChartReport.parameters={...LineChartReport.parameters,docs:{...null===(_LineChartReport_parameters=LineChartReport.parameters)||void 0===_LineChartReport_parameters?void 0:_LineChartReport_parameters.docs,source:{originalSource:"{\n  args: {\n    type: 'line'\n  },\n  parameters: {\n    docs: {\n      description: {\n        story: `\nLine chart visualization perfect for trend analysis and time series data.\nShows clear progression and patterns in worked time over periods.\n\n**Features:**\n- Connected data points\n- Clear trend visualization\n- Time series analysis\n- Pattern identification\n            `\n      }\n    }\n  }\n}",...null===(_LineChartReport_parameters1=LineChartReport.parameters)||void 0===_LineChartReport_parameters1||null===(_LineChartReport_parameters_docs=_LineChartReport_parameters1.docs)||void 0===_LineChartReport_parameters_docs?void 0:_LineChartReport_parameters_docs.source},description:{story:"Line chart report for trend analysis.\r\nShows time data as connected points for trend visualization.",...null===(_LineChartReport_parameters2=LineChartReport.parameters)||void 0===_LineChartReport_parameters2||null===(_LineChartReport_parameters_docs1=_LineChartReport_parameters2.docs)||void 0===_LineChartReport_parameters_docs1?void 0:_LineChartReport_parameters_docs1.description}}},BarVerticalChartReport.parameters={...BarVerticalChartReport.parameters,docs:{...null===(_BarVerticalChartReport_parameters=BarVerticalChartReport.parameters)||void 0===_BarVerticalChartReport_parameters?void 0:_BarVerticalChartReport_parameters.docs,source:{originalSource:"{\n  args: {\n    type: 'bar-vertical'\n  },\n  parameters: {\n    docs: {\n      description: {\n        story: `\nVertical bar chart orientation providing alternative layout for time data.\nUseful when horizontal space is limited or for different visual preferences.\n\n**Features:**\n- Vertical bar orientation\n- Space-efficient layout\n- Alternative visualization\n- Same data functionality\n            `\n      }\n    }\n  }\n}",...null===(_BarVerticalChartReport_parameters1=BarVerticalChartReport.parameters)||void 0===_BarVerticalChartReport_parameters1||null===(_BarVerticalChartReport_parameters_docs=_BarVerticalChartReport_parameters1.docs)||void 0===_BarVerticalChartReport_parameters_docs?void 0:_BarVerticalChartReport_parameters_docs.source},description:{story:"Vertical bar chart report.\r\nAlternative bar orientation for different layout needs.",...null===(_BarVerticalChartReport_parameters2=BarVerticalChartReport.parameters)||void 0===_BarVerticalChartReport_parameters2||null===(_BarVerticalChartReport_parameters_docs1=_BarVerticalChartReport_parameters2.docs)||void 0===_BarVerticalChartReport_parameters_docs1?void 0:_BarVerticalChartReport_parameters_docs1.description}}},BarChartReport.parameters={...BarChartReport.parameters,docs:{...null===(_BarChartReport_parameters=BarChartReport.parameters)||void 0===_BarChartReport_parameters?void 0:_BarChartReport_parameters.docs,source:{originalSource:"{\n  args: {\n    type: 'bar'\n  },\n  parameters: {\n    docs: {\n      description: {\n        story: `\nStandard horizontal bar chart for clear time data comparison.\nThe classic visualization for comparing worked time between employees.\n\n**Features:**\n- Horizontal bar layout\n- Easy comparison\n- Standard visualization\n- Clear data representation\n            `\n      }\n    }\n  }\n}",...null===(_BarChartReport_parameters1=BarChartReport.parameters)||void 0===_BarChartReport_parameters1||null===(_BarChartReport_parameters_docs=_BarChartReport_parameters1.docs)||void 0===_BarChartReport_parameters_docs?void 0:_BarChartReport_parameters_docs.source},description:{story:"Horizontal bar chart report (explicit).\r\nStandard horizontal bar chart for time comparison.",...null===(_BarChartReport_parameters2=BarChartReport.parameters)||void 0===_BarChartReport_parameters2||null===(_BarChartReport_parameters_docs1=_BarChartReport_parameters2.docs)||void 0===_BarChartReport_parameters_docs1?void 0:_BarChartReport_parameters_docs1.description}}},PieChartReport.parameters={...PieChartReport.parameters,docs:{...null===(_PieChartReport_parameters=PieChartReport.parameters)||void 0===_PieChartReport_parameters?void 0:_PieChartReport_parameters.docs,source:{originalSource:"{\n  args: {\n    type: 'pie'\n  },\n  parameters: {\n    docs: {\n      description: {\n        story: `\nPie chart visualization showing time distribution as proportional segments.\nPerfect for understanding relative time allocation between employees.\n\n**Features:**\n- Circular segment layout\n- Proportional representation\n- Visual impact\n- Easy percentage comparison\n\n**Note:** Currently under development - shows placeholder message.\n            `\n      }\n    }\n  }\n}",...null===(_PieChartReport_parameters1=PieChartReport.parameters)||void 0===_PieChartReport_parameters1||null===(_PieChartReport_parameters_docs=_PieChartReport_parameters1.docs)||void 0===_PieChartReport_parameters_docs?void 0:_PieChartReport_parameters_docs.source},description:{story:"Pie chart report for proportional data visualization.\r\nShows time distribution as circular segments.",...null===(_PieChartReport_parameters2=PieChartReport.parameters)||void 0===_PieChartReport_parameters2||null===(_PieChartReport_parameters_docs1=_PieChartReport_parameters2.docs)||void 0===_PieChartReport_parameters_docs1?void 0:_PieChartReport_parameters_docs1.description}}},RadarChartReport.parameters={...RadarChartReport.parameters,docs:{...null===(_RadarChartReport_parameters=RadarChartReport.parameters)||void 0===_RadarChartReport_parameters?void 0:_RadarChartReport_parameters.docs,source:{originalSource:"{\n  args: {\n    type: 'radar'\n  },\n  parameters: {\n    docs: {\n      description: {\n        story: `\nRadar chart visualization displaying time data across multiple dimensions.\nExcellent for comparing performance across different metrics simultaneously.\n\n**Features:**\n- Multi-dimensional analysis\n- Spider web layout\n- Performance comparison\n- Pattern recognition\n            `\n      }\n    }\n  }\n}",...null===(_RadarChartReport_parameters1=RadarChartReport.parameters)||void 0===_RadarChartReport_parameters1||null===(_RadarChartReport_parameters_docs=_RadarChartReport_parameters1.docs)||void 0===_RadarChartReport_parameters_docs?void 0:_RadarChartReport_parameters_docs.source},description:{story:"Radar chart report for multi-dimensional analysis.\r\nShows time data across multiple dimensions in a spider web format.",...null===(_RadarChartReport_parameters2=RadarChartReport.parameters)||void 0===_RadarChartReport_parameters2||null===(_RadarChartReport_parameters_docs1=_RadarChartReport_parameters2.docs)||void 0===_RadarChartReport_parameters_docs1?void 0:_RadarChartReport_parameters_docs1.description}}},RadialChartReport.parameters={...RadialChartReport.parameters,docs:{...null===(_RadialChartReport_parameters=RadialChartReport.parameters)||void 0===_RadialChartReport_parameters?void 0:_RadialChartReport_parameters.docs,source:{originalSource:"{\n  args: {\n    type: 'radial'\n  },\n  parameters: {\n    docs: {\n      description: {\n        story: `\nRadial chart visualization showing time data in circular progress format.\nPerfect for displaying completion rates and progress indicators.\n\n**Features:**\n- Circular progress layout\n- Visual progress indication\n- Completion tracking\n- Modern design aesthetic\n            `\n      }\n    }\n  }\n}",...null===(_RadialChartReport_parameters1=RadialChartReport.parameters)||void 0===_RadialChartReport_parameters1||null===(_RadialChartReport_parameters_docs=_RadialChartReport_parameters1.docs)||void 0===_RadialChartReport_parameters_docs?void 0:_RadialChartReport_parameters_docs.source},description:{story:"Radial chart report for circular progress visualization.\r\nShows time data in a radial progress format.",...null===(_RadialChartReport_parameters2=RadialChartReport.parameters)||void 0===_RadialChartReport_parameters2||null===(_RadialChartReport_parameters_docs1=_RadialChartReport_parameters2.docs)||void 0===_RadialChartReport_parameters_docs1?void 0:_RadialChartReport_parameters_docs1.description}}};const __namedExportsOrder=["Default","SmallSize","LargeSize","BorderedVariant","AreaChartReport","TooltipChartReport","LineChartReport","BarVerticalChartReport","BarChartReport","PieChartReport","RadarChartReport","RadialChartReport"]}}]);