"use strict";(self.webpackChunk_cloc_storybook=self.webpackChunk_cloc_storybook||[]).push([[7419],{"./src/stories/analytics-charts/reports/ClocTasksList.stories.tsx":(__unused_webpack_module,__webpack_exports__,__webpack_require__)=>{__webpack_require__.r(__webpack_exports__),__webpack_require__.d(__webpack_exports__,{BorderedVariant:()=>BorderedVariant,Default:()=>Default,LargeSize:()=>LargeSize,SmallSize:()=>SmallSize,__namedExportsOrder:()=>__namedExportsOrder,default:()=>__WEBPACK_DEFAULT_EXPORT__});var _Default_parameters,_Default_parameters_docs,_Default_parameters1,_Default_parameters_docs1,_Default_parameters2,_SmallSize_parameters,_SmallSize_parameters_docs,_SmallSize_parameters1,_SmallSize_parameters_docs1,_SmallSize_parameters2,_LargeSize_parameters,_LargeSize_parameters_docs,_LargeSize_parameters1,_LargeSize_parameters_docs1,_LargeSize_parameters2,_BorderedVariant_parameters,_BorderedVariant_parameters_docs,_BorderedVariant_parameters1,_BorderedVariant_parameters_docs1,_BorderedVariant_parameters2;__webpack_require__("../../node_modules/next/dist/compiled/react/index.js");const __WEBPACK_DEFAULT_EXPORT__={title:"Charts & Reports/Reports/Cloc Tasks List",component:__webpack_require__("../../packages/toolkit/atoms/dist/index.es.js").wWN,parameters:{layout:"centered",docs:{description:{component:"\nA specialized list component that displays task-based time tracking statistics and productivity metrics.\n\n**Features:**\n- Displays tasks with time duration and percentage breakdown\n- Real-time data from ClocProvider context (tasksStats)\n- Responsive design with multiple size variants\n- Loading states with overlay spinner\n- Scrollable list with custom styling\n- Automatic time formatting and percentage calculations\n- Empty state handling when no tasks are available\n\n**Data Source:**\n- Gets data from `useClocContext().tasksStats` (ITasksStats[])\n- Each item contains: duration, durationPercentage, and task details\n- Loading state managed through `tasksStatsLoading`\n\n**Use Cases:**\n- Task time tracking dashboards\n- Individual productivity analytics\n- Task performance reports\n- Work breakdown displays\n- Time allocation analysis\n                "}}},argTypes:{variant:{control:{type:"select"},options:["default","bordered"],description:"Visual style variant of the list component",defaultValue:"default"},size:{control:{type:"select"},options:["default","sm","lg"],description:"Size variant controlling width and spacing",defaultValue:"default"},className:{control:{type:"text"},description:"Additional CSS classes for custom styling"}}},Default={parameters:{docs:{description:{story:"\nThe default tasks list displays time tracking statistics for all tasks.\nData is automatically fetched from the ClocProvider context.\n\n**Default Configuration:**\n- Size: 600px width\n- Variant: Default styling\n- Data: Real-time from tasksStats\n- Loading: Automatic overlay when fetching data\n            "}}}},SmallSize={args:{size:"sm"},parameters:{docs:{description:{story:"\nSmall size variant with reduced width (400px) for compact layouts.\nPerfect for dashboard widgets or when space is limited.\n\n**Features:**\n- Compact 400px width\n- Same functionality as default\n- Optimized for smaller spaces\n- Maintains task data visibility\n            "}}}},LargeSize={args:{size:"lg"},parameters:{docs:{description:{story:"\nLarge size variant with expanded width (800px) for detailed displays.\nIdeal for main content areas or comprehensive task analysis.\n\n**Features:**\n- Expanded 800px width\n- Enhanced task visibility\n- Better for detailed analysis\n- Suitable for main dashboards\n            "}}}},BorderedVariant={args:{variant:"bordered"},parameters:{docs:{description:{story:"\nBordered variant adds a secondary color border for enhanced visual definition.\nUseful when the component needs to stand out from surrounding content.\n\n**Features:**\n- Secondary color border (2px)\n- Enhanced visual separation\n- Better component definition\n- Same task data functionality\n            "}}}};Default.parameters={...Default.parameters,docs:{...null===(_Default_parameters=Default.parameters)||void 0===_Default_parameters?void 0:_Default_parameters.docs,source:{originalSource:"{\n  parameters: {\n    docs: {\n      description: {\n        story: `\nThe default tasks list displays time tracking statistics for all tasks.\nData is automatically fetched from the ClocProvider context.\n\n**Default Configuration:**\n- Size: 600px width\n- Variant: Default styling\n- Data: Real-time from tasksStats\n- Loading: Automatic overlay when fetching data\n            `\n      }\n    }\n  }\n}",...null===(_Default_parameters1=Default.parameters)||void 0===_Default_parameters1||null===(_Default_parameters_docs=_Default_parameters1.docs)||void 0===_Default_parameters_docs?void 0:_Default_parameters_docs.source},description:{story:"Default tasks list with standard sizing and styling.\r\nShows task time tracking statistics and productivity metrics.",...null===(_Default_parameters2=Default.parameters)||void 0===_Default_parameters2||null===(_Default_parameters_docs1=_Default_parameters2.docs)||void 0===_Default_parameters_docs1?void 0:_Default_parameters_docs1.description}}},SmallSize.parameters={...SmallSize.parameters,docs:{...null===(_SmallSize_parameters=SmallSize.parameters)||void 0===_SmallSize_parameters?void 0:_SmallSize_parameters.docs,source:{originalSource:"{\n  args: {\n    size: 'sm'\n  },\n  parameters: {\n    docs: {\n      description: {\n        story: `\nSmall size variant with reduced width (400px) for compact layouts.\nPerfect for dashboard widgets or when space is limited.\n\n**Features:**\n- Compact 400px width\n- Same functionality as default\n- Optimized for smaller spaces\n- Maintains task data visibility\n            `\n      }\n    }\n  }\n}",...null===(_SmallSize_parameters1=SmallSize.parameters)||void 0===_SmallSize_parameters1||null===(_SmallSize_parameters_docs=_SmallSize_parameters1.docs)||void 0===_SmallSize_parameters_docs?void 0:_SmallSize_parameters_docs.source},description:{story:"Small size variant optimized for compact spaces.\r\nIdeal for dashboard widgets or sidebar placement.",...null===(_SmallSize_parameters2=SmallSize.parameters)||void 0===_SmallSize_parameters2||null===(_SmallSize_parameters_docs1=_SmallSize_parameters2.docs)||void 0===_SmallSize_parameters_docs1?void 0:_SmallSize_parameters_docs1.description}}},LargeSize.parameters={...LargeSize.parameters,docs:{...null===(_LargeSize_parameters=LargeSize.parameters)||void 0===_LargeSize_parameters?void 0:_LargeSize_parameters.docs,source:{originalSource:"{\n  args: {\n    size: 'lg'\n  },\n  parameters: {\n    docs: {\n      description: {\n        story: `\nLarge size variant with expanded width (800px) for detailed displays.\nIdeal for main content areas or comprehensive task analysis.\n\n**Features:**\n- Expanded 800px width\n- Enhanced task visibility\n- Better for detailed analysis\n- Suitable for main dashboards\n            `\n      }\n    }\n  }\n}",...null===(_LargeSize_parameters1=LargeSize.parameters)||void 0===_LargeSize_parameters1||null===(_LargeSize_parameters_docs=_LargeSize_parameters1.docs)||void 0===_LargeSize_parameters_docs?void 0:_LargeSize_parameters_docs.source},description:{story:"Large size variant for prominent display.\r\nSuitable for main content areas or detailed task analysis.",...null===(_LargeSize_parameters2=LargeSize.parameters)||void 0===_LargeSize_parameters2||null===(_LargeSize_parameters_docs1=_LargeSize_parameters2.docs)||void 0===_LargeSize_parameters_docs1?void 0:_LargeSize_parameters_docs1.description}}},BorderedVariant.parameters={...BorderedVariant.parameters,docs:{...null===(_BorderedVariant_parameters=BorderedVariant.parameters)||void 0===_BorderedVariant_parameters?void 0:_BorderedVariant_parameters.docs,source:{originalSource:"{\n  args: {\n    variant: 'bordered'\n  },\n  parameters: {\n    docs: {\n      description: {\n        story: `\nBordered variant adds a secondary color border for enhanced visual definition.\nUseful when the component needs to stand out from surrounding content.\n\n**Features:**\n- Secondary color border (2px)\n- Enhanced visual separation\n- Better component definition\n- Same task data functionality\n            `\n      }\n    }\n  }\n}",...null===(_BorderedVariant_parameters1=BorderedVariant.parameters)||void 0===_BorderedVariant_parameters1||null===(_BorderedVariant_parameters_docs=_BorderedVariant_parameters1.docs)||void 0===_BorderedVariant_parameters_docs?void 0:_BorderedVariant_parameters_docs.source},description:{story:"Bordered variant with enhanced visual definition.\r\nAdds a prominent border for better visual separation.",...null===(_BorderedVariant_parameters2=BorderedVariant.parameters)||void 0===_BorderedVariant_parameters2||null===(_BorderedVariant_parameters_docs1=_BorderedVariant_parameters2.docs)||void 0===_BorderedVariant_parameters_docs1?void 0:_BorderedVariant_parameters_docs1.description}}};const __namedExportsOrder=["Default","SmallSize","LargeSize","BorderedVariant"]}}]);