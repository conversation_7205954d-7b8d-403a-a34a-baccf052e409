/*! For license information please see stories-authentication-ClocLoginDialog-stories.090b9382.iframe.bundle.js.LICENSE.txt */
"use strict";(self.webpackChunk_cloc_storybook=self.webpackChunk_cloc_storybook||[]).push([[78],{"../../node_modules/lucide-react/dist/esm/createLucideIcon.js":(__unused_webpack_module,__webpack_exports__,__webpack_require__)=>{__webpack_require__.d(__webpack_exports__,{A:()=>createLucideIcon});var react=__webpack_require__("../../node_modules/next/dist/compiled/react/index.js");const mergeClasses=(...classes)=>classes.filter((className,index,array)=>Boolean(className)&&array.indexOf(className)===index).join(" ");var defaultAttributes={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};const Icon=(0,react.forwardRef)(({color="currentColor",size=24,strokeWidth=2,absoluteStrokeWidth,className="",children,iconNode,...rest},ref)=>(0,react.createElement)("svg",{ref,...defaultAttributes,width:size,height:size,stroke:color,strokeWidth:absoluteStrokeWidth?24*Number(strokeWidth)/Number(size):strokeWidth,className:mergeClasses("lucide",className),...rest},[...iconNode.map(([tag,attrs])=>(0,react.createElement)(tag,attrs)),...Array.isArray(children)?children:[children]])),createLucideIcon=(iconName,iconNode)=>{const Component=(0,react.forwardRef)(({className,...props},ref)=>{return(0,react.createElement)(Icon,{ref,iconNode,className:mergeClasses(`lucide-${string=iconName,string.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase()}`,className),...props});var string});return Component.displayName=`${iconName}`,Component}},"../../node_modules/lucide-react/dist/esm/icons/settings.js":(__unused_webpack_module,__webpack_exports__,__webpack_require__)=>{__webpack_require__.d(__webpack_exports__,{A:()=>Settings});const Settings=(0,__webpack_require__("../../node_modules/lucide-react/dist/esm/createLucideIcon.js").A)("Settings",[["path",{d:"M12.22 2h-.44a2 2 0 0 0-2 2v.18a2 2 0 0 1-1 1.73l-.43.25a2 2 0 0 1-2 0l-.15-.08a2 2 0 0 0-2.73.73l-.22.38a2 2 0 0 0 .73 2.73l.15.1a2 2 0 0 1 1 1.72v.51a2 2 0 0 1-1 1.74l-.15.09a2 2 0 0 0-.73 2.73l.22.38a2 2 0 0 0 2.73.73l.15-.08a2 2 0 0 1 2 0l.43.25a2 2 0 0 1 1 1.73V20a2 2 0 0 0 2 2h.44a2 2 0 0 0 2-2v-.18a2 2 0 0 1 1-1.73l.43-.25a2 2 0 0 1 2 0l.15.08a2 2 0 0 0 2.73-.73l.22-.39a2 2 0 0 0-.73-2.73l-.15-.08a2 2 0 0 1-1-1.74v-.5a2 2 0 0 1 1-1.74l.15-.09a2 2 0 0 0 .73-2.73l-.22-.38a2 2 0 0 0-2.73-.73l-.15.08a2 2 0 0 1-2 0l-.43-.25a2 2 0 0 1-1-1.73V4a2 2 0 0 0-2-2z",key:"1qme2f"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},"../../node_modules/lucide-react/dist/esm/icons/user.js":(__unused_webpack_module,__webpack_exports__,__webpack_require__)=>{__webpack_require__.d(__webpack_exports__,{A:()=>User});const User=(0,__webpack_require__("../../node_modules/lucide-react/dist/esm/createLucideIcon.js").A)("User",[["path",{d:"M19 21v-2a4 4 0 0 0-4-4H9a4 4 0 0 0-4 4v2",key:"975kel"}],["circle",{cx:"12",cy:"7",r:"4",key:"17ys0d"}]])},"./src/stories/authentication/ClocLoginDialog.stories.tsx":(__unused_webpack_module,__webpack_exports__,__webpack_require__)=>{__webpack_require__.r(__webpack_exports__),__webpack_require__.d(__webpack_exports__,{CallToAction:()=>CallToAction,CustomTrigger:()=>CustomTrigger,Default:()=>Default,HeaderAction:()=>HeaderAction,IconTrigger:()=>IconTrigger,LandingPageHero:()=>LandingPageHero,MobileMenu:()=>MobileMenu,NavigationItem:()=>NavigationItem,PrimaryButton:()=>PrimaryButton,SettingsMenu:()=>SettingsMenu,WithSignupLink:()=>WithSignupLink,__namedExportsOrder:()=>__namedExportsOrder,default:()=>ClocLoginDialog_stories});var jsx_runtime=__webpack_require__("../../node_modules/next/dist/compiled/react/jsx-runtime.js"),index_es=(__webpack_require__("../../node_modules/next/dist/compiled/react/index.js"),__webpack_require__("../../packages/toolkit/atoms/dist/index.es.js")),dist_index_es=__webpack_require__("../../packages/ui/dist/index.es.js");const LogIn=(0,__webpack_require__("../../node_modules/lucide-react/dist/esm/createLucideIcon.js").A)("LogIn",[["path",{d:"M15 3h4a2 2 0 0 1 2 2v14a2 2 0 0 1-2 2h-4",key:"u53s6r"}],["polyline",{points:"10 17 15 12 10 7",key:"1ail0h"}],["line",{x1:"15",x2:"3",y1:"12",y2:"12",key:"v6grx8"}]]);var _Default_parameters,_Default_parameters_docs,_Default_parameters1,_WithSignupLink_parameters,_WithSignupLink_parameters_docs,_WithSignupLink_parameters1,_CustomTrigger_parameters,_CustomTrigger_parameters_docs,_CustomTrigger_parameters1,_IconTrigger_parameters,_IconTrigger_parameters_docs,_IconTrigger_parameters1,_PrimaryButton_parameters,_PrimaryButton_parameters_docs,_PrimaryButton_parameters1,_NavigationItem_parameters,_NavigationItem_parameters_docs,_NavigationItem_parameters1,_HeaderAction_parameters,_HeaderAction_parameters_docs,_HeaderAction_parameters1,_CallToAction_parameters,_CallToAction_parameters_docs,_CallToAction_parameters1,_SettingsMenu_parameters,_SettingsMenu_parameters_docs,_SettingsMenu_parameters1,_MobileMenu_parameters,_MobileMenu_parameters_docs,_MobileMenu_parameters1,_LandingPageHero_parameters,_LandingPageHero_parameters_docs,_LandingPageHero_parameters1,user=__webpack_require__("../../node_modules/lucide-react/dist/esm/icons/user.js"),settings=__webpack_require__("../../node_modules/lucide-react/dist/esm/icons/settings.js");const ClocLoginDialog_stories={title:"Authentication/Login Dialog",component:index_es.ZUs,parameters:{layout:"centered",docs:{description:{component:"A login dialog component that displays a modal with login form when user is not authenticated, or shows user avatar when authenticated. Provides flexible trigger customization."}}},argTypes:{trigger:{control:!1,description:"Custom trigger element to open the dialog"},signupLink:{control:"text",description:"URL for the signup page link"},redirectHandler:{action:"redirected",description:"Function called after successful authentication"}}},Default={args:{}},WithSignupLink={args:{signupLink:"/signup"}},CustomTrigger={args:{trigger:(0,jsx_runtime.jsxs)(dist_index_es.$n,{variant:"outline",className:"flex items-center gap-2",children:[(0,jsx_runtime.jsx)(LogIn,{size:16}),"Login"]})}},IconTrigger={args:{trigger:(0,jsx_runtime.jsx)(dist_index_es.$n,{variant:"ghost",size:"icon",children:(0,jsx_runtime.jsx)(user.A,{size:20})})}},PrimaryButton={args:{trigger:(0,jsx_runtime.jsx)(dist_index_es.$n,{className:"bg-blue-600 hover:bg-blue-700 text-white",children:"Get Started"}),signupLink:"/signup"}},NavigationItem={args:{trigger:(0,jsx_runtime.jsx)("button",{className:"text-gray-700 dark:text-gray-300 hover:text-blue-600 dark:hover:text-blue-400 px-3 py-2 rounded-md text-sm font-medium transition-colors",children:"Sign In"})}},HeaderAction={render:()=>(0,jsx_runtime.jsx)("div",{className:"bg-white dark:bg-gray-900 border-b border-gray-200 dark:border-gray-700 p-4",children:(0,jsx_runtime.jsxs)("div",{className:"flex items-center justify-between max-w-6xl mx-auto",children:[(0,jsx_runtime.jsxs)("div",{className:"flex items-center gap-2",children:[(0,jsx_runtime.jsx)("div",{className:"w-8 h-8 bg-blue-600 rounded-full flex items-center justify-center",children:(0,jsx_runtime.jsx)("span",{className:"text-white font-bold text-sm",children:"C"})}),(0,jsx_runtime.jsx)("span",{className:"font-semibold text-gray-900 dark:text-white",children:"Cloc"})]}),(0,jsx_runtime.jsxs)("nav",{className:"flex items-center gap-6",children:[(0,jsx_runtime.jsx)("a",{href:"#",className:"text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white",children:"Features"}),(0,jsx_runtime.jsx)("a",{href:"#",className:"text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white",children:"Pricing"}),(0,jsx_runtime.jsx)(index_es.ZUs,{trigger:(0,jsx_runtime.jsx)(dist_index_es.$n,{variant:"outline",size:"sm",children:"Sign In"})})]})]})})},CallToAction={render:()=>(0,jsx_runtime.jsxs)("div",{className:"bg-gradient-to-r from-blue-600 to-purple-600 text-white p-8 rounded-lg text-center",children:[(0,jsx_runtime.jsx)("h2",{className:"text-2xl font-bold mb-2",children:"Start Tracking Your Time Today"}),(0,jsx_runtime.jsx)("p",{className:"text-blue-100 mb-6",children:"Join thousands of professionals who trust Cloc for their time tracking needs"}),(0,jsx_runtime.jsx)(index_es.ZUs,{trigger:(0,jsx_runtime.jsx)(dist_index_es.$n,{size:"lg",className:"bg-white text-blue-600 hover:bg-gray-100",children:"Get Started Free"}),signupLink:"/signup"})]})},SettingsMenu={args:{trigger:(0,jsx_runtime.jsxs)(dist_index_es.$n,{variant:"ghost",size:"sm",className:"flex items-center gap-2",children:[(0,jsx_runtime.jsx)(settings.A,{size:16}),"Account"]})}},MobileMenu={render:()=>(0,jsx_runtime.jsx)("div",{className:"bg-white dark:bg-gray-900 border border-gray-200 dark:border-gray-700 rounded-lg p-4 w-full max-w-sm",children:(0,jsx_runtime.jsxs)("div",{className:"space-y-3",children:[(0,jsx_runtime.jsx)("a",{href:"#",className:"block px-3 py-2 text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-800 rounded",children:"Dashboard"}),(0,jsx_runtime.jsx)("a",{href:"#",className:"block px-3 py-2 text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-800 rounded",children:"Projects"}),(0,jsx_runtime.jsx)("a",{href:"#",className:"block px-3 py-2 text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-800 rounded",children:"Reports"}),(0,jsx_runtime.jsx)("div",{className:"border-t border-gray-200 dark:border-gray-700 pt-3",children:(0,jsx_runtime.jsx)(index_es.ZUs,{trigger:(0,jsx_runtime.jsx)(dist_index_es.$n,{variant:"outline",className:"w-full",children:"Sign In"})})})]})})},LandingPageHero={render:()=>(0,jsx_runtime.jsx)("div",{className:"bg-gradient-to-br from-gray-50 to-gray-100 dark:from-gray-900 dark:to-gray-800 p-8 rounded-lg",children:(0,jsx_runtime.jsxs)("div",{className:"text-center max-w-2xl mx-auto",children:[(0,jsx_runtime.jsx)("h1",{className:"text-4xl font-bold text-gray-900 dark:text-white mb-4",children:"Time Tracking Made Simple"}),(0,jsx_runtime.jsx)("p",{className:"text-xl text-gray-600 dark:text-gray-400 mb-8",children:"Track your time, manage projects, and boost productivity with our intuitive time tracking solution."}),(0,jsx_runtime.jsxs)("div",{className:"flex flex-col sm:flex-row gap-4 justify-center",children:[(0,jsx_runtime.jsx)(index_es.ZUs,{trigger:(0,jsx_runtime.jsx)(dist_index_es.$n,{size:"lg",className:"bg-blue-600 hover:bg-blue-700 text-white px-8",children:"Start Free Trial"}),signupLink:"/signup"}),(0,jsx_runtime.jsx)(dist_index_es.$n,{variant:"outline",size:"lg",className:"px-8",children:"Watch Demo"})]})]})})};Default.parameters={...Default.parameters,docs:{...null===(_Default_parameters=Default.parameters)||void 0===_Default_parameters?void 0:_Default_parameters.docs,source:{originalSource:"{\n  args: {}\n}",...null===(_Default_parameters1=Default.parameters)||void 0===_Default_parameters1||null===(_Default_parameters_docs=_Default_parameters1.docs)||void 0===_Default_parameters_docs?void 0:_Default_parameters_docs.source}}},WithSignupLink.parameters={...WithSignupLink.parameters,docs:{...null===(_WithSignupLink_parameters=WithSignupLink.parameters)||void 0===_WithSignupLink_parameters?void 0:_WithSignupLink_parameters.docs,source:{originalSource:"{\n  args: {\n    signupLink: '/signup'\n  }\n}",...null===(_WithSignupLink_parameters1=WithSignupLink.parameters)||void 0===_WithSignupLink_parameters1||null===(_WithSignupLink_parameters_docs=_WithSignupLink_parameters1.docs)||void 0===_WithSignupLink_parameters_docs?void 0:_WithSignupLink_parameters_docs.source}}},CustomTrigger.parameters={...CustomTrigger.parameters,docs:{...null===(_CustomTrigger_parameters=CustomTrigger.parameters)||void 0===_CustomTrigger_parameters?void 0:_CustomTrigger_parameters.docs,source:{originalSource:'{\n  args: {\n    trigger: <Button variant="outline" className="flex items-center gap-2">\r\n                <LogIn size={16} />\r\n                Login\r\n            </Button>\n  }\n}',...null===(_CustomTrigger_parameters1=CustomTrigger.parameters)||void 0===_CustomTrigger_parameters1||null===(_CustomTrigger_parameters_docs=_CustomTrigger_parameters1.docs)||void 0===_CustomTrigger_parameters_docs?void 0:_CustomTrigger_parameters_docs.source}}},IconTrigger.parameters={...IconTrigger.parameters,docs:{...null===(_IconTrigger_parameters=IconTrigger.parameters)||void 0===_IconTrigger_parameters?void 0:_IconTrigger_parameters.docs,source:{originalSource:'{\n  args: {\n    trigger: <Button variant="ghost" size="icon">\r\n                <User size={20} />\r\n            </Button>\n  }\n}',...null===(_IconTrigger_parameters1=IconTrigger.parameters)||void 0===_IconTrigger_parameters1||null===(_IconTrigger_parameters_docs=_IconTrigger_parameters1.docs)||void 0===_IconTrigger_parameters_docs?void 0:_IconTrigger_parameters_docs.source}}},PrimaryButton.parameters={...PrimaryButton.parameters,docs:{...null===(_PrimaryButton_parameters=PrimaryButton.parameters)||void 0===_PrimaryButton_parameters?void 0:_PrimaryButton_parameters.docs,source:{originalSource:"{\n  args: {\n    trigger: <Button className=\"bg-blue-600 hover:bg-blue-700 text-white\">Get Started</Button>,\n    signupLink: '/signup'\n  }\n}",...null===(_PrimaryButton_parameters1=PrimaryButton.parameters)||void 0===_PrimaryButton_parameters1||null===(_PrimaryButton_parameters_docs=_PrimaryButton_parameters1.docs)||void 0===_PrimaryButton_parameters_docs?void 0:_PrimaryButton_parameters_docs.source}}},NavigationItem.parameters={...NavigationItem.parameters,docs:{...null===(_NavigationItem_parameters=NavigationItem.parameters)||void 0===_NavigationItem_parameters?void 0:_NavigationItem_parameters.docs,source:{originalSource:'{\n  args: {\n    trigger: <button className="text-gray-700 dark:text-gray-300 hover:text-blue-600 dark:hover:text-blue-400 px-3 py-2 rounded-md text-sm font-medium transition-colors">\r\n                Sign In\r\n            </button>\n  }\n}',...null===(_NavigationItem_parameters1=NavigationItem.parameters)||void 0===_NavigationItem_parameters1||null===(_NavigationItem_parameters_docs=_NavigationItem_parameters1.docs)||void 0===_NavigationItem_parameters_docs?void 0:_NavigationItem_parameters_docs.source}}},HeaderAction.parameters={...HeaderAction.parameters,docs:{...null===(_HeaderAction_parameters=HeaderAction.parameters)||void 0===_HeaderAction_parameters?void 0:_HeaderAction_parameters.docs,source:{originalSource:'{\n  render: () => <div className="bg-white dark:bg-gray-900 border-b border-gray-200 dark:border-gray-700 p-4">\r\n            <div className="flex items-center justify-between max-w-6xl mx-auto">\r\n                <div className="flex items-center gap-2">\r\n                    <div className="w-8 h-8 bg-blue-600 rounded-full flex items-center justify-center">\r\n                        <span className="text-white font-bold text-sm">C</span>\r\n                    </div>\r\n                    <span className="font-semibold text-gray-900 dark:text-white">Cloc</span>\r\n                </div>\r\n                <nav className="flex items-center gap-6">\r\n                    <a href="#" className="text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white">\r\n                        Features\r\n                    </a>\r\n                    <a href="#" className="text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white">\r\n                        Pricing\r\n                    </a>\r\n                    <ClocLoginDialog trigger={<Button variant="outline" size="sm">\r\n                                Sign In\r\n                            </Button>} />\r\n                </nav>\r\n            </div>\r\n        </div>\n}',...null===(_HeaderAction_parameters1=HeaderAction.parameters)||void 0===_HeaderAction_parameters1||null===(_HeaderAction_parameters_docs=_HeaderAction_parameters1.docs)||void 0===_HeaderAction_parameters_docs?void 0:_HeaderAction_parameters_docs.source}}},CallToAction.parameters={...CallToAction.parameters,docs:{...null===(_CallToAction_parameters=CallToAction.parameters)||void 0===_CallToAction_parameters?void 0:_CallToAction_parameters.docs,source:{originalSource:'{\n  render: () => <div className="bg-gradient-to-r from-blue-600 to-purple-600 text-white p-8 rounded-lg text-center">\r\n            <h2 className="text-2xl font-bold mb-2">Start Tracking Your Time Today</h2>\r\n            <p className="text-blue-100 mb-6">\r\n                Join thousands of professionals who trust Cloc for their time tracking needs\r\n            </p>\r\n            <ClocLoginDialog trigger={<Button size="lg" className="bg-white text-blue-600 hover:bg-gray-100">\r\n                        Get Started Free\r\n                    </Button>} signupLink="/signup" />\r\n        </div>\n}',...null===(_CallToAction_parameters1=CallToAction.parameters)||void 0===_CallToAction_parameters1||null===(_CallToAction_parameters_docs=_CallToAction_parameters1.docs)||void 0===_CallToAction_parameters_docs?void 0:_CallToAction_parameters_docs.source}}},SettingsMenu.parameters={...SettingsMenu.parameters,docs:{...null===(_SettingsMenu_parameters=SettingsMenu.parameters)||void 0===_SettingsMenu_parameters?void 0:_SettingsMenu_parameters.docs,source:{originalSource:'{\n  args: {\n    trigger: <Button variant="ghost" size="sm" className="flex items-center gap-2">\r\n                <Settings size={16} />\r\n                Account\r\n            </Button>\n  }\n}',...null===(_SettingsMenu_parameters1=SettingsMenu.parameters)||void 0===_SettingsMenu_parameters1||null===(_SettingsMenu_parameters_docs=_SettingsMenu_parameters1.docs)||void 0===_SettingsMenu_parameters_docs?void 0:_SettingsMenu_parameters_docs.source}}},MobileMenu.parameters={...MobileMenu.parameters,docs:{...null===(_MobileMenu_parameters=MobileMenu.parameters)||void 0===_MobileMenu_parameters?void 0:_MobileMenu_parameters.docs,source:{originalSource:'{\n  render: () => <div className="bg-white dark:bg-gray-900 border border-gray-200 dark:border-gray-700 rounded-lg p-4 w-full max-w-sm">\r\n            <div className="space-y-3">\r\n                <a href="#" className="block px-3 py-2 text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-800 rounded">\r\n                    Dashboard\r\n                </a>\r\n                <a href="#" className="block px-3 py-2 text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-800 rounded">\r\n                    Projects\r\n                </a>\r\n                <a href="#" className="block px-3 py-2 text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-800 rounded">\r\n                    Reports\r\n                </a>\r\n                <div className="border-t border-gray-200 dark:border-gray-700 pt-3">\r\n                    <ClocLoginDialog trigger={<Button variant="outline" className="w-full">\r\n                                Sign In\r\n                            </Button>} />\r\n                </div>\r\n            </div>\r\n        </div>\n}',...null===(_MobileMenu_parameters1=MobileMenu.parameters)||void 0===_MobileMenu_parameters1||null===(_MobileMenu_parameters_docs=_MobileMenu_parameters1.docs)||void 0===_MobileMenu_parameters_docs?void 0:_MobileMenu_parameters_docs.source}}},LandingPageHero.parameters={...LandingPageHero.parameters,docs:{...null===(_LandingPageHero_parameters=LandingPageHero.parameters)||void 0===_LandingPageHero_parameters?void 0:_LandingPageHero_parameters.docs,source:{originalSource:'{\n  render: () => <div className="bg-gradient-to-br from-gray-50 to-gray-100 dark:from-gray-900 dark:to-gray-800 p-8 rounded-lg">\r\n            <div className="text-center max-w-2xl mx-auto">\r\n                <h1 className="text-4xl font-bold text-gray-900 dark:text-white mb-4">Time Tracking Made Simple</h1>\r\n                <p className="text-xl text-gray-600 dark:text-gray-400 mb-8">\r\n                    Track your time, manage projects, and boost productivity with our intuitive time tracking solution.\r\n                </p>\r\n                <div className="flex flex-col sm:flex-row gap-4 justify-center">\r\n                    <ClocLoginDialog trigger={<Button size="lg" className="bg-blue-600 hover:bg-blue-700 text-white px-8">\r\n                                Start Free Trial\r\n                            </Button>} signupLink="/signup" />\r\n                    <Button variant="outline" size="lg" className="px-8">\r\n                        Watch Demo\r\n                    </Button>\r\n                </div>\r\n            </div>\r\n        </div>\n}',...null===(_LandingPageHero_parameters1=LandingPageHero.parameters)||void 0===_LandingPageHero_parameters1||null===(_LandingPageHero_parameters_docs=_LandingPageHero_parameters1.docs)||void 0===_LandingPageHero_parameters_docs?void 0:_LandingPageHero_parameters_docs.source}}};const __namedExportsOrder=["Default","WithSignupLink","CustomTrigger","IconTrigger","PrimaryButton","NavigationItem","HeaderAction","CallToAction","SettingsMenu","MobileMenu","LandingPageHero"]}}]);