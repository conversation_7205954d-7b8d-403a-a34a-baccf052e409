"use strict";(self.webpackChunk_cloc_storybook=self.webpackChunk_cloc_storybook||[]).push([[4364],{"./src/stories/authentication/ClocLoginForm.stories.tsx":(__unused_webpack_module,__webpack_exports__,__webpack_require__)=>{__webpack_require__.r(__webpack_exports__),__webpack_require__.d(__webpack_exports__,{CompactForm:()=>CompactForm,CustomStyling:()=>CustomStyling,Default:()=>Default,FullWidthForm:()=>FullWidthForm,InModal:()=>InModal,MobileOptimized:()=>MobileOptimized,OnboardingFlow:()=>OnboardingFlow,WithBranding:()=>WithBranding,WithRedirectHandler:()=>WithRedirectHandler,WithSignupLink:()=>WithSignupLink,__namedExportsOrder:()=>__namedExportsOrder,default:()=>__WEBPACK_DEFAULT_EXPORT__});var _Default_parameters,_Default_parameters_docs,_Default_parameters1,_WithSignupLink_parameters,_WithSignupLink_parameters_docs,_WithSignupLink_parameters1,_CustomStyling_parameters,_CustomStyling_parameters_docs,_CustomStyling_parameters1,_WithRedirectHandler_parameters,_WithRedirectHandler_parameters_docs,_WithRedirectHandler_parameters1,_CompactForm_parameters,_CompactForm_parameters_docs,_CompactForm_parameters1,_FullWidthForm_parameters,_FullWidthForm_parameters_docs,_FullWidthForm_parameters1,_InModal_parameters,_InModal_parameters_docs,_InModal_parameters1,_OnboardingFlow_parameters,_OnboardingFlow_parameters_docs,_OnboardingFlow_parameters1,_WithBranding_parameters,_WithBranding_parameters_docs,_WithBranding_parameters1,_MobileOptimized_parameters,_MobileOptimized_parameters_docs,_MobileOptimized_parameters1,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__=__webpack_require__("../../node_modules/next/dist/compiled/react/jsx-runtime.js"),_cloc_atoms__WEBPACK_IMPORTED_MODULE_2__=(__webpack_require__("../../node_modules/next/dist/compiled/react/index.js"),__webpack_require__("../../packages/toolkit/atoms/dist/index.es.js")),console=__webpack_require__("../../node_modules/console-browserify/index.js");const __WEBPACK_DEFAULT_EXPORT__={title:"Authentication/Login Form",component:_cloc_atoms__WEBPACK_IMPORTED_MODULE_2__.$oB,parameters:{layout:"centered",docs:{description:{component:"A comprehensive login form component with password and token authentication options. Includes tabs for different authentication methods and integrates with the Cloc authentication system."}}},argTypes:{className:{control:"text",description:"Additional CSS classes for styling"},signupLink:{control:"text",description:"URL for the signup page link"},redirectHandler:{action:"redirected",description:"Function called after successful authentication"}}},Default={args:{}},WithSignupLink={args:{signupLink:"/signup"}},CustomStyling={args:{className:"border-2 border-blue-300 rounded-lg p-6 bg-blue-50 dark:bg-blue-950"}},WithRedirectHandler={args:{signupLink:"/signup",redirectHandler:()=>{console.log("User authenticated successfully")}}},CompactForm={args:{className:"max-w-sm"}},FullWidthForm={args:{className:"w-full max-w-md"}},InModal={render:()=>(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div",{className:"p-6 bg-white dark:bg-gray-900 rounded-lg shadow-lg max-w-md",children:[(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("h2",{className:"text-xl font-semibold text-gray-900 dark:text-white mb-4",children:"Sign In to Your Account"}),(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(_cloc_atoms__WEBPACK_IMPORTED_MODULE_2__.$oB,{})]})},OnboardingFlow={render:()=>(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("div",{className:"min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 dark:from-gray-900 dark:to-gray-800 flex items-center justify-center p-4",children:(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div",{className:"bg-white dark:bg-gray-900 rounded-2xl shadow-xl p-8 w-full max-w-md",children:[(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div",{className:"text-center mb-6",children:[(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("h1",{className:"text-2xl font-bold text-gray-900 dark:text-white",children:"Welcome Back"}),(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("p",{className:"text-gray-600 dark:text-gray-400 mt-2",children:"Sign in to continue your time tracking journey"})]}),(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(_cloc_atoms__WEBPACK_IMPORTED_MODULE_2__.$oB,{signupLink:"/signup"}),(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("div",{className:"mt-6 text-center",children:(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("p",{className:"text-sm text-gray-500 dark:text-gray-400",children:["Don't have an account?"," ",(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("a",{href:"/signup",className:"text-blue-600 hover:text-blue-500 font-medium",children:"Sign up here"})]})})]})})},WithBranding={render:()=>(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div",{className:"bg-white dark:bg-gray-900 rounded-lg shadow-lg p-6 max-w-md",children:[(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div",{className:"text-center mb-6",children:[(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("div",{className:"w-16 h-16 bg-blue-600 rounded-full flex items-center justify-center mx-auto mb-4",children:(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("span",{className:"text-white font-bold text-xl",children:"C"})}),(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("h2",{className:"text-xl font-semibold text-gray-900 dark:text-white",children:"Cloc Time Tracker"}),(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("p",{className:"text-gray-600 dark:text-gray-400 text-sm mt-1",children:"Professional time tracking made simple"})]}),(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(_cloc_atoms__WEBPACK_IMPORTED_MODULE_2__.$oB,{})]})},MobileOptimized={render:()=>(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("div",{className:"w-full max-w-sm mx-auto p-4",children:(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div",{className:"bg-white dark:bg-gray-900 rounded-lg shadow-lg p-6",children:[(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("h2",{className:"text-lg font-semibold text-gray-900 dark:text-white mb-4 text-center",children:"Sign In"}),(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(_cloc_atoms__WEBPACK_IMPORTED_MODULE_2__.$oB,{className:"space-y-4"})]})})};Default.parameters={...Default.parameters,docs:{...null===(_Default_parameters=Default.parameters)||void 0===_Default_parameters?void 0:_Default_parameters.docs,source:{originalSource:"{\n  args: {}\n}",...null===(_Default_parameters1=Default.parameters)||void 0===_Default_parameters1||null===(_Default_parameters_docs=_Default_parameters1.docs)||void 0===_Default_parameters_docs?void 0:_Default_parameters_docs.source}}},WithSignupLink.parameters={...WithSignupLink.parameters,docs:{...null===(_WithSignupLink_parameters=WithSignupLink.parameters)||void 0===_WithSignupLink_parameters?void 0:_WithSignupLink_parameters.docs,source:{originalSource:"{\n  args: {\n    signupLink: '/signup'\n  }\n}",...null===(_WithSignupLink_parameters1=WithSignupLink.parameters)||void 0===_WithSignupLink_parameters1||null===(_WithSignupLink_parameters_docs=_WithSignupLink_parameters1.docs)||void 0===_WithSignupLink_parameters_docs?void 0:_WithSignupLink_parameters_docs.source}}},CustomStyling.parameters={...CustomStyling.parameters,docs:{...null===(_CustomStyling_parameters=CustomStyling.parameters)||void 0===_CustomStyling_parameters?void 0:_CustomStyling_parameters.docs,source:{originalSource:"{\n  args: {\n    className: 'border-2 border-blue-300 rounded-lg p-6 bg-blue-50 dark:bg-blue-950'\n  }\n}",...null===(_CustomStyling_parameters1=CustomStyling.parameters)||void 0===_CustomStyling_parameters1||null===(_CustomStyling_parameters_docs=_CustomStyling_parameters1.docs)||void 0===_CustomStyling_parameters_docs?void 0:_CustomStyling_parameters_docs.source}}},WithRedirectHandler.parameters={...WithRedirectHandler.parameters,docs:{...null===(_WithRedirectHandler_parameters=WithRedirectHandler.parameters)||void 0===_WithRedirectHandler_parameters?void 0:_WithRedirectHandler_parameters.docs,source:{originalSource:"{\n  args: {\n    signupLink: '/signup',\n    redirectHandler: () => {\n      console.log('User authenticated successfully');\n    }\n  }\n}",...null===(_WithRedirectHandler_parameters1=WithRedirectHandler.parameters)||void 0===_WithRedirectHandler_parameters1||null===(_WithRedirectHandler_parameters_docs=_WithRedirectHandler_parameters1.docs)||void 0===_WithRedirectHandler_parameters_docs?void 0:_WithRedirectHandler_parameters_docs.source}}},CompactForm.parameters={...CompactForm.parameters,docs:{...null===(_CompactForm_parameters=CompactForm.parameters)||void 0===_CompactForm_parameters?void 0:_CompactForm_parameters.docs,source:{originalSource:"{\n  args: {\n    className: 'max-w-sm'\n  }\n}",...null===(_CompactForm_parameters1=CompactForm.parameters)||void 0===_CompactForm_parameters1||null===(_CompactForm_parameters_docs=_CompactForm_parameters1.docs)||void 0===_CompactForm_parameters_docs?void 0:_CompactForm_parameters_docs.source}}},FullWidthForm.parameters={...FullWidthForm.parameters,docs:{...null===(_FullWidthForm_parameters=FullWidthForm.parameters)||void 0===_FullWidthForm_parameters?void 0:_FullWidthForm_parameters.docs,source:{originalSource:"{\n  args: {\n    className: 'w-full max-w-md'\n  }\n}",...null===(_FullWidthForm_parameters1=FullWidthForm.parameters)||void 0===_FullWidthForm_parameters1||null===(_FullWidthForm_parameters_docs=_FullWidthForm_parameters1.docs)||void 0===_FullWidthForm_parameters_docs?void 0:_FullWidthForm_parameters_docs.source}}},InModal.parameters={...InModal.parameters,docs:{...null===(_InModal_parameters=InModal.parameters)||void 0===_InModal_parameters?void 0:_InModal_parameters.docs,source:{originalSource:'{\n  render: () => <div className="p-6 bg-white dark:bg-gray-900 rounded-lg shadow-lg max-w-md">\r\n            <h2 className="text-xl font-semibold text-gray-900 dark:text-white mb-4">Sign In to Your Account</h2>\r\n            <ClocLoginForm />\r\n        </div>\n}',...null===(_InModal_parameters1=InModal.parameters)||void 0===_InModal_parameters1||null===(_InModal_parameters_docs=_InModal_parameters1.docs)||void 0===_InModal_parameters_docs?void 0:_InModal_parameters_docs.source}}},OnboardingFlow.parameters={...OnboardingFlow.parameters,docs:{...null===(_OnboardingFlow_parameters=OnboardingFlow.parameters)||void 0===_OnboardingFlow_parameters?void 0:_OnboardingFlow_parameters.docs,source:{originalSource:'{\n  render: () => <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 dark:from-gray-900 dark:to-gray-800 flex items-center justify-center p-4">\r\n            <div className="bg-white dark:bg-gray-900 rounded-2xl shadow-xl p-8 w-full max-w-md">\r\n                <div className="text-center mb-6">\r\n                    <h1 className="text-2xl font-bold text-gray-900 dark:text-white">Welcome Back</h1>\r\n                    <p className="text-gray-600 dark:text-gray-400 mt-2">\r\n                        Sign in to continue your time tracking journey\r\n                    </p>\r\n                </div>\r\n                <ClocLoginForm signupLink="/signup" />\r\n                <div className="mt-6 text-center">\r\n                    <p className="text-sm text-gray-500 dark:text-gray-400">\r\n                        Don\'t have an account?{\' \'}\r\n                        <a href="/signup" className="text-blue-600 hover:text-blue-500 font-medium">\r\n                            Sign up here\r\n                        </a>\r\n                    </p>\r\n                </div>\r\n            </div>\r\n        </div>\n}',...null===(_OnboardingFlow_parameters1=OnboardingFlow.parameters)||void 0===_OnboardingFlow_parameters1||null===(_OnboardingFlow_parameters_docs=_OnboardingFlow_parameters1.docs)||void 0===_OnboardingFlow_parameters_docs?void 0:_OnboardingFlow_parameters_docs.source}}},WithBranding.parameters={...WithBranding.parameters,docs:{...null===(_WithBranding_parameters=WithBranding.parameters)||void 0===_WithBranding_parameters?void 0:_WithBranding_parameters.docs,source:{originalSource:'{\n  render: () => <div className="bg-white dark:bg-gray-900 rounded-lg shadow-lg p-6 max-w-md">\r\n            <div className="text-center mb-6">\r\n                <div className="w-16 h-16 bg-blue-600 rounded-full flex items-center justify-center mx-auto mb-4">\r\n                    <span className="text-white font-bold text-xl">C</span>\r\n                </div>\r\n                <h2 className="text-xl font-semibold text-gray-900 dark:text-white">Cloc Time Tracker</h2>\r\n                <p className="text-gray-600 dark:text-gray-400 text-sm mt-1">Professional time tracking made simple</p>\r\n            </div>\r\n            <ClocLoginForm />\r\n        </div>\n}',...null===(_WithBranding_parameters1=WithBranding.parameters)||void 0===_WithBranding_parameters1||null===(_WithBranding_parameters_docs=_WithBranding_parameters1.docs)||void 0===_WithBranding_parameters_docs?void 0:_WithBranding_parameters_docs.source}}},MobileOptimized.parameters={...MobileOptimized.parameters,docs:{...null===(_MobileOptimized_parameters=MobileOptimized.parameters)||void 0===_MobileOptimized_parameters?void 0:_MobileOptimized_parameters.docs,source:{originalSource:'{\n  render: () => <div className="w-full max-w-sm mx-auto p-4">\r\n            <div className="bg-white dark:bg-gray-900 rounded-lg shadow-lg p-6">\r\n                <h2 className="text-lg font-semibold text-gray-900 dark:text-white mb-4 text-center">Sign In</h2>\r\n                <ClocLoginForm className="space-y-4" />\r\n            </div>\r\n        </div>\n}',...null===(_MobileOptimized_parameters1=MobileOptimized.parameters)||void 0===_MobileOptimized_parameters1||null===(_MobileOptimized_parameters_docs=_MobileOptimized_parameters1.docs)||void 0===_MobileOptimized_parameters_docs?void 0:_MobileOptimized_parameters_docs.source}}};const __namedExportsOrder=["Default","WithSignupLink","CustomStyling","WithRedirectHandler","CompactForm","FullWidthForm","InModal","OnboardingFlow","WithBranding","MobileOptimized"]}}]);