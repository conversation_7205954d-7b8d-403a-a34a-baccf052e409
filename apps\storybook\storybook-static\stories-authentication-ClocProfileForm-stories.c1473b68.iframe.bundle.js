"use strict";(self.webpackChunk_cloc_storybook=self.webpackChunk_cloc_storybook||[]).push([[1288],{"./src/stories/authentication/ClocProfileForm.stories.tsx":(__unused_webpack_module,__webpack_exports__,__webpack_require__)=>{__webpack_require__.r(__webpack_exports__),__webpack_require__.d(__webpack_exports__,{CompactLayout:()=>CompactLayout,CustomStyling:()=>CustomStyling,Default:()=>Default,InModal:()=>InModal,InSettingsPage:()=>InSettingsPage,InTabs:()=>InTabs,MobileView:()=>MobileView,OnboardingStep:()=>OnboardingStep,WithSidebar:()=>WithSidebar,__namedExportsOrder:()=>__namedExportsOrder,default:()=>__WEBPACK_DEFAULT_EXPORT__});var _Default_parameters,_Default_parameters_docs,_Default_parameters1,_CustomStyling_parameters,_CustomStyling_parameters_docs,_CustomStyling_parameters1,_CompactLayout_parameters,_CompactLayout_parameters_docs,_CompactLayout_parameters1,_InSettingsPage_parameters,_InSettingsPage_parameters_docs,_InSettingsPage_parameters1,_InModal_parameters,_InModal_parameters_docs,_InModal_parameters1,_InTabs_parameters,_InTabs_parameters_docs,_InTabs_parameters1,_MobileView_parameters,_MobileView_parameters_docs,_MobileView_parameters1,_WithSidebar_parameters,_WithSidebar_parameters_docs,_WithSidebar_parameters1,_OnboardingStep_parameters,_OnboardingStep_parameters_docs,_OnboardingStep_parameters1,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__=__webpack_require__("../../node_modules/next/dist/compiled/react/jsx-runtime.js"),_cloc_atoms__WEBPACK_IMPORTED_MODULE_2__=(__webpack_require__("../../node_modules/next/dist/compiled/react/index.js"),__webpack_require__("../../packages/toolkit/atoms/dist/index.es.js"));const __WEBPACK_DEFAULT_EXPORT__={title:"Authentication/Profile Form",component:_cloc_atoms__WEBPACK_IMPORTED_MODULE_2__.$QN,parameters:{layout:"centered",docs:{description:{component:"A comprehensive profile form component for updating user information including personal details, preferences, language, time format, and timezone settings. Includes profile photo upload functionality."}}},argTypes:{className:{control:"text",description:"Additional CSS classes for styling"}}},Default={args:{}},CustomStyling={args:{className:"max-w-2xl border border-gray-200 dark:border-gray-700 rounded-lg p-6 bg-gray-50 dark:bg-gray-900"}},CompactLayout={args:{className:"max-w-md"}},InSettingsPage={render:()=>(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("div",{className:"min-h-screen bg-gray-50 dark:bg-gray-900 p-6",children:(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("div",{className:"max-w-4xl mx-auto",children:(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div",{className:"bg-white dark:bg-gray-800 rounded-lg shadow-sm",children:[(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div",{className:"border-b border-gray-200 dark:border-gray-700 p-6",children:[(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("h1",{className:"text-2xl font-bold text-gray-900 dark:text-white",children:"Account Settings"}),(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("p",{className:"text-gray-600 dark:text-gray-400 mt-1",children:"Manage your account information and preferences"})]}),(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("div",{className:"p-6",children:(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(_cloc_atoms__WEBPACK_IMPORTED_MODULE_2__.$QN,{})})]})})})},InModal={render:()=>(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4",children:(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div",{className:"bg-white dark:bg-gray-900 rounded-lg shadow-xl max-w-2xl w-full max-h-[90vh] overflow-y-auto",children:[(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("div",{className:"p-6 border-b border-gray-200 dark:border-gray-700",children:(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("h2",{className:"text-xl font-semibold text-gray-900 dark:text-white",children:"Edit Profile"})}),(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("div",{className:"p-6",children:(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(_cloc_atoms__WEBPACK_IMPORTED_MODULE_2__.$QN,{className:"border-0 bg-transparent p-0"})})]})})},InTabs={render:()=>(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div",{className:"bg-white dark:bg-gray-900 rounded-lg shadow-lg max-w-4xl",children:[(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("div",{className:"border-b border-gray-200 dark:border-gray-700",children:(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("nav",{className:"flex space-x-8 px-6",children:[(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("button",{className:"py-4 px-1 border-b-2 border-blue-500 text-blue-600 dark:text-blue-400 font-medium text-sm",children:"Profile"}),(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("button",{className:"py-4 px-1 border-b-2 border-transparent text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300 font-medium text-sm",children:"Security"}),(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("button",{className:"py-4 px-1 border-b-2 border-transparent text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300 font-medium text-sm",children:"Notifications"}),(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("button",{className:"py-4 px-1 border-b-2 border-transparent text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300 font-medium text-sm",children:"Billing"})]})}),(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("div",{className:"p-6",children:(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(_cloc_atoms__WEBPACK_IMPORTED_MODULE_2__.$QN,{})})]})},MobileView={render:()=>(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div",{className:"w-full max-w-sm mx-auto bg-white dark:bg-gray-900 min-h-screen",children:[(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("div",{className:"p-4 border-b border-gray-200 dark:border-gray-700",children:(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("h1",{className:"text-lg font-semibold text-gray-900 dark:text-white",children:"Profile Settings"})}),(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("div",{className:"p-4",children:(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(_cloc_atoms__WEBPACK_IMPORTED_MODULE_2__.$QN,{className:"space-y-4"})})]})},WithSidebar={render:()=>(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div",{className:"flex bg-gray-50 dark:bg-gray-900 min-h-screen",children:[(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div",{className:"w-64 bg-white dark:bg-gray-800 border-r border-gray-200 dark:border-gray-700 p-6",children:[(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("h2",{className:"text-lg font-semibold text-gray-900 dark:text-white mb-4",children:"Settings"}),(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("nav",{className:"space-y-2",children:[(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("a",{href:"#",className:"block px-3 py-2 bg-blue-50 dark:bg-blue-900/20 text-blue-700 dark:text-blue-300 rounded-lg font-medium",children:"Profile"}),(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("a",{href:"#",className:"block px-3 py-2 text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-lg",children:"Account"}),(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("a",{href:"#",className:"block px-3 py-2 text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-lg",children:"Security"}),(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("a",{href:"#",className:"block px-3 py-2 text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-lg",children:"Preferences"})]})]}),(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("div",{className:"flex-1 p-6",children:(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("div",{className:"bg-white dark:bg-gray-800 rounded-lg shadow-sm p-6",children:(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(_cloc_atoms__WEBPACK_IMPORTED_MODULE_2__.$QN,{})})})]})},OnboardingStep={render:()=>(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("div",{className:"bg-gradient-to-br from-blue-50 to-indigo-100 dark:from-gray-900 dark:to-gray-800 min-h-screen flex items-center justify-center p-4",children:(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div",{className:"bg-white dark:bg-gray-900 rounded-2xl shadow-xl p-8 w-full max-w-2xl",children:[(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div",{className:"text-center mb-8",children:[(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("div",{className:"flex justify-center mb-4",children:(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div",{className:"flex space-x-2",children:[(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("div",{className:"w-3 h-3 bg-blue-600 rounded-full"}),(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("div",{className:"w-3 h-3 bg-blue-600 rounded-full"}),(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("div",{className:"w-3 h-3 bg-gray-300 dark:bg-gray-600 rounded-full"}),(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("div",{className:"w-3 h-3 bg-gray-300 dark:bg-gray-600 rounded-full"})]})}),(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("h1",{className:"text-2xl font-bold text-gray-900 dark:text-white",children:"Complete Your Profile"}),(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("p",{className:"text-gray-600 dark:text-gray-400 mt-2",children:"Help us personalize your experience by completing your profile information"})]}),(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(_cloc_atoms__WEBPACK_IMPORTED_MODULE_2__.$QN,{}),(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div",{className:"flex justify-between mt-8",children:[(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("button",{className:"px-6 py-2 text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white",children:"Skip for now"}),(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("button",{className:"px-6 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-lg font-medium",children:"Continue"})]})]})})};Default.parameters={...Default.parameters,docs:{...null===(_Default_parameters=Default.parameters)||void 0===_Default_parameters?void 0:_Default_parameters.docs,source:{originalSource:"{\n  args: {}\n}",...null===(_Default_parameters1=Default.parameters)||void 0===_Default_parameters1||null===(_Default_parameters_docs=_Default_parameters1.docs)||void 0===_Default_parameters_docs?void 0:_Default_parameters_docs.source}}},CustomStyling.parameters={...CustomStyling.parameters,docs:{...null===(_CustomStyling_parameters=CustomStyling.parameters)||void 0===_CustomStyling_parameters?void 0:_CustomStyling_parameters.docs,source:{originalSource:"{\n  args: {\n    className: 'max-w-2xl border border-gray-200 dark:border-gray-700 rounded-lg p-6 bg-gray-50 dark:bg-gray-900'\n  }\n}",...null===(_CustomStyling_parameters1=CustomStyling.parameters)||void 0===_CustomStyling_parameters1||null===(_CustomStyling_parameters_docs=_CustomStyling_parameters1.docs)||void 0===_CustomStyling_parameters_docs?void 0:_CustomStyling_parameters_docs.source}}},CompactLayout.parameters={...CompactLayout.parameters,docs:{...null===(_CompactLayout_parameters=CompactLayout.parameters)||void 0===_CompactLayout_parameters?void 0:_CompactLayout_parameters.docs,source:{originalSource:"{\n  args: {\n    className: 'max-w-md'\n  }\n}",...null===(_CompactLayout_parameters1=CompactLayout.parameters)||void 0===_CompactLayout_parameters1||null===(_CompactLayout_parameters_docs=_CompactLayout_parameters1.docs)||void 0===_CompactLayout_parameters_docs?void 0:_CompactLayout_parameters_docs.source}}},InSettingsPage.parameters={...InSettingsPage.parameters,docs:{...null===(_InSettingsPage_parameters=InSettingsPage.parameters)||void 0===_InSettingsPage_parameters?void 0:_InSettingsPage_parameters.docs,source:{originalSource:'{\n  render: () => <div className="min-h-screen bg-gray-50 dark:bg-gray-900 p-6">\r\n            <div className="max-w-4xl mx-auto">\r\n                <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm">\r\n                    <div className="border-b border-gray-200 dark:border-gray-700 p-6">\r\n                        <h1 className="text-2xl font-bold text-gray-900 dark:text-white">Account Settings</h1>\r\n                        <p className="text-gray-600 dark:text-gray-400 mt-1">\r\n                            Manage your account information and preferences\r\n                        </p>\r\n                    </div>\r\n                    <div className="p-6">\r\n                        <ClocProfileForm />\r\n                    </div>\r\n                </div>\r\n            </div>\r\n        </div>\n}',...null===(_InSettingsPage_parameters1=InSettingsPage.parameters)||void 0===_InSettingsPage_parameters1||null===(_InSettingsPage_parameters_docs=_InSettingsPage_parameters1.docs)||void 0===_InSettingsPage_parameters_docs?void 0:_InSettingsPage_parameters_docs.source}}},InModal.parameters={...InModal.parameters,docs:{...null===(_InModal_parameters=InModal.parameters)||void 0===_InModal_parameters?void 0:_InModal_parameters.docs,source:{originalSource:'{\n  render: () => <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4">\r\n            <div className="bg-white dark:bg-gray-900 rounded-lg shadow-xl max-w-2xl w-full max-h-[90vh] overflow-y-auto">\r\n                <div className="p-6 border-b border-gray-200 dark:border-gray-700">\r\n                    <h2 className="text-xl font-semibold text-gray-900 dark:text-white">Edit Profile</h2>\r\n                </div>\r\n                <div className="p-6">\r\n                    <ClocProfileForm className="border-0 bg-transparent p-0" />\r\n                </div>\r\n            </div>\r\n        </div>\n}',...null===(_InModal_parameters1=InModal.parameters)||void 0===_InModal_parameters1||null===(_InModal_parameters_docs=_InModal_parameters1.docs)||void 0===_InModal_parameters_docs?void 0:_InModal_parameters_docs.source}}},InTabs.parameters={...InTabs.parameters,docs:{...null===(_InTabs_parameters=InTabs.parameters)||void 0===_InTabs_parameters?void 0:_InTabs_parameters.docs,source:{originalSource:'{\n  render: () => <div className="bg-white dark:bg-gray-900 rounded-lg shadow-lg max-w-4xl">\r\n            <div className="border-b border-gray-200 dark:border-gray-700">\r\n                <nav className="flex space-x-8 px-6">\r\n                    <button className="py-4 px-1 border-b-2 border-blue-500 text-blue-600 dark:text-blue-400 font-medium text-sm">\r\n                        Profile\r\n                    </button>\r\n                    <button className="py-4 px-1 border-b-2 border-transparent text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300 font-medium text-sm">\r\n                        Security\r\n                    </button>\r\n                    <button className="py-4 px-1 border-b-2 border-transparent text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300 font-medium text-sm">\r\n                        Notifications\r\n                    </button>\r\n                    <button className="py-4 px-1 border-b-2 border-transparent text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300 font-medium text-sm">\r\n                        Billing\r\n                    </button>\r\n                </nav>\r\n            </div>\r\n            <div className="p-6">\r\n                <ClocProfileForm />\r\n            </div>\r\n        </div>\n}',...null===(_InTabs_parameters1=InTabs.parameters)||void 0===_InTabs_parameters1||null===(_InTabs_parameters_docs=_InTabs_parameters1.docs)||void 0===_InTabs_parameters_docs?void 0:_InTabs_parameters_docs.source}}},MobileView.parameters={...MobileView.parameters,docs:{...null===(_MobileView_parameters=MobileView.parameters)||void 0===_MobileView_parameters?void 0:_MobileView_parameters.docs,source:{originalSource:'{\n  render: () => <div className="w-full max-w-sm mx-auto bg-white dark:bg-gray-900 min-h-screen">\r\n            <div className="p-4 border-b border-gray-200 dark:border-gray-700">\r\n                <h1 className="text-lg font-semibold text-gray-900 dark:text-white">Profile Settings</h1>\r\n            </div>\r\n            <div className="p-4">\r\n                <ClocProfileForm className="space-y-4" />\r\n            </div>\r\n        </div>\n}',...null===(_MobileView_parameters1=MobileView.parameters)||void 0===_MobileView_parameters1||null===(_MobileView_parameters_docs=_MobileView_parameters1.docs)||void 0===_MobileView_parameters_docs?void 0:_MobileView_parameters_docs.source}}},WithSidebar.parameters={...WithSidebar.parameters,docs:{...null===(_WithSidebar_parameters=WithSidebar.parameters)||void 0===_WithSidebar_parameters?void 0:_WithSidebar_parameters.docs,source:{originalSource:'{\n  render: () => <div className="flex bg-gray-50 dark:bg-gray-900 min-h-screen">\r\n            <div className="w-64 bg-white dark:bg-gray-800 border-r border-gray-200 dark:border-gray-700 p-6">\r\n                <h2 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">Settings</h2>\r\n                <nav className="space-y-2">\r\n                    <a href="#" className="block px-3 py-2 bg-blue-50 dark:bg-blue-900/20 text-blue-700 dark:text-blue-300 rounded-lg font-medium">\r\n                        Profile\r\n                    </a>\r\n                    <a href="#" className="block px-3 py-2 text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-lg">\r\n                        Account\r\n                    </a>\r\n                    <a href="#" className="block px-3 py-2 text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-lg">\r\n                        Security\r\n                    </a>\r\n                    <a href="#" className="block px-3 py-2 text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-lg">\r\n                        Preferences\r\n                    </a>\r\n                </nav>\r\n            </div>\r\n            <div className="flex-1 p-6">\r\n                <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm p-6">\r\n                    <ClocProfileForm />\r\n                </div>\r\n            </div>\r\n        </div>\n}',...null===(_WithSidebar_parameters1=WithSidebar.parameters)||void 0===_WithSidebar_parameters1||null===(_WithSidebar_parameters_docs=_WithSidebar_parameters1.docs)||void 0===_WithSidebar_parameters_docs?void 0:_WithSidebar_parameters_docs.source}}},OnboardingStep.parameters={...OnboardingStep.parameters,docs:{...null===(_OnboardingStep_parameters=OnboardingStep.parameters)||void 0===_OnboardingStep_parameters?void 0:_OnboardingStep_parameters.docs,source:{originalSource:'{\n  render: () => <div className="bg-gradient-to-br from-blue-50 to-indigo-100 dark:from-gray-900 dark:to-gray-800 min-h-screen flex items-center justify-center p-4">\r\n            <div className="bg-white dark:bg-gray-900 rounded-2xl shadow-xl p-8 w-full max-w-2xl">\r\n                <div className="text-center mb-8">\r\n                    <div className="flex justify-center mb-4">\r\n                        <div className="flex space-x-2">\r\n                            <div className="w-3 h-3 bg-blue-600 rounded-full"></div>\r\n                            <div className="w-3 h-3 bg-blue-600 rounded-full"></div>\r\n                            <div className="w-3 h-3 bg-gray-300 dark:bg-gray-600 rounded-full"></div>\r\n                            <div className="w-3 h-3 bg-gray-300 dark:bg-gray-600 rounded-full"></div>\r\n                        </div>\r\n                    </div>\r\n                    <h1 className="text-2xl font-bold text-gray-900 dark:text-white">Complete Your Profile</h1>\r\n                    <p className="text-gray-600 dark:text-gray-400 mt-2">\r\n                        Help us personalize your experience by completing your profile information\r\n                    </p>\r\n                </div>\r\n                <ClocProfileForm />\r\n                <div className="flex justify-between mt-8">\r\n                    <button className="px-6 py-2 text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white">\r\n                        Skip for now\r\n                    </button>\r\n                    <button className="px-6 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-lg font-medium">\r\n                        Continue\r\n                    </button>\r\n                </div>\r\n            </div>\r\n        </div>\n}',...null===(_OnboardingStep_parameters1=OnboardingStep.parameters)||void 0===_OnboardingStep_parameters1||null===(_OnboardingStep_parameters_docs=_OnboardingStep_parameters1.docs)||void 0===_OnboardingStep_parameters_docs?void 0:_OnboardingStep_parameters_docs.source}}};const __namedExportsOrder=["Default","CustomStyling","CompactLayout","InSettingsPage","InModal","InTabs","MobileView","WithSidebar","OnboardingStep"]}}]);