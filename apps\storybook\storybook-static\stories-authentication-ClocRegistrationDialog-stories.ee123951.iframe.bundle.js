/*! For license information please see stories-authentication-ClocRegistrationDialog-stories.ee123951.iframe.bundle.js.LICENSE.txt */
"use strict";(self.webpackChunk_cloc_storybook=self.webpackChunk_cloc_storybook||[]).push([[1960],{"../../node_modules/lucide-react/dist/esm/createLucideIcon.js":(__unused_webpack_module,__webpack_exports__,__webpack_require__)=>{__webpack_require__.d(__webpack_exports__,{A:()=>createLucideIcon});var react=__webpack_require__("../../node_modules/next/dist/compiled/react/index.js");const mergeClasses=(...classes)=>classes.filter((className,index,array)=>Boolean(className)&&array.indexOf(className)===index).join(" ");var defaultAttributes={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};const Icon=(0,react.forwardRef)(({color="currentColor",size=24,strokeWidth=2,absoluteStrokeWidth,className="",children,iconNode,...rest},ref)=>(0,react.createElement)("svg",{ref,...defaultAttributes,width:size,height:size,stroke:color,strokeWidth:absoluteStrokeWidth?24*Number(strokeWidth)/Number(size):strokeWidth,className:mergeClasses("lucide",className),...rest},[...iconNode.map(([tag,attrs])=>(0,react.createElement)(tag,attrs)),...Array.isArray(children)?children:[children]])),createLucideIcon=(iconName,iconNode)=>{const Component=(0,react.forwardRef)(({className,...props},ref)=>{return(0,react.createElement)(Icon,{ref,iconNode,className:mergeClasses(`lucide-${string=iconName,string.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase()}`,className),...props});var string});return Component.displayName=`${iconName}`,Component}},"../../node_modules/lucide-react/dist/esm/icons/mail.js":(__unused_webpack_module,__webpack_exports__,__webpack_require__)=>{__webpack_require__.d(__webpack_exports__,{A:()=>Mail});const Mail=(0,__webpack_require__("../../node_modules/lucide-react/dist/esm/createLucideIcon.js").A)("Mail",[["rect",{width:"20",height:"16",x:"2",y:"4",rx:"2",key:"18n3k1"}],["path",{d:"m22 7-8.97 5.7a1.94 1.94 0 0 1-2.06 0L2 7",key:"1ocrg3"}]])},"../../node_modules/lucide-react/dist/esm/icons/user-plus.js":(__unused_webpack_module,__webpack_exports__,__webpack_require__)=>{__webpack_require__.d(__webpack_exports__,{A:()=>UserPlus});const UserPlus=(0,__webpack_require__("../../node_modules/lucide-react/dist/esm/createLucideIcon.js").A)("UserPlus",[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}],["line",{x1:"19",x2:"19",y1:"8",y2:"14",key:"1bvyxn"}],["line",{x1:"22",x2:"16",y1:"11",y2:"11",key:"1shjgl"}]])},"../../node_modules/lucide-react/dist/esm/icons/users.js":(__unused_webpack_module,__webpack_exports__,__webpack_require__)=>{__webpack_require__.d(__webpack_exports__,{A:()=>Users});const Users=(0,__webpack_require__("../../node_modules/lucide-react/dist/esm/createLucideIcon.js").A)("Users",[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}],["path",{d:"M22 21v-2a4 4 0 0 0-3-3.87",key:"kshegd"}],["path",{d:"M16 3.13a4 4 0 0 1 0 7.75",key:"1da9ce"}]])},"./src/stories/authentication/ClocRegistrationDialog.stories.tsx":(__unused_webpack_module,__webpack_exports__,__webpack_require__)=>{__webpack_require__.r(__webpack_exports__),__webpack_require__.d(__webpack_exports__,{CustomTrigger:()=>CustomTrigger,Default:()=>Default,EmailSignupTrigger:()=>EmailSignupTrigger,TeamInviteTrigger:()=>TeamInviteTrigger,WithSignInLink:()=>WithSignInLink,__namedExportsOrder:()=>__namedExportsOrder,default:()=>__WEBPACK_DEFAULT_EXPORT__});var _Default_parameters,_Default_parameters_docs,_Default_parameters1,_Default_parameters_docs1,_Default_parameters2,_WithSignInLink_parameters,_WithSignInLink_parameters_docs,_WithSignInLink_parameters1,_WithSignInLink_parameters_docs1,_WithSignInLink_parameters2,_CustomTrigger_parameters,_CustomTrigger_parameters_docs,_CustomTrigger_parameters1,_CustomTrigger_parameters_docs1,_CustomTrigger_parameters2,_EmailSignupTrigger_parameters,_EmailSignupTrigger_parameters_docs,_EmailSignupTrigger_parameters1,_EmailSignupTrigger_parameters_docs1,_EmailSignupTrigger_parameters2,_TeamInviteTrigger_parameters,_TeamInviteTrigger_parameters_docs,_TeamInviteTrigger_parameters1,_TeamInviteTrigger_parameters_docs1,_TeamInviteTrigger_parameters2,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__=__webpack_require__("../../node_modules/next/dist/compiled/react/jsx-runtime.js"),_cloc_atoms__WEBPACK_IMPORTED_MODULE_2__=(__webpack_require__("../../node_modules/next/dist/compiled/react/index.js"),__webpack_require__("../../packages/toolkit/atoms/dist/index.es.js")),_cloc_ui__WEBPACK_IMPORTED_MODULE_3__=__webpack_require__("../../packages/ui/dist/index.es.js"),_barrel_optimize_names_Mail_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_4__=__webpack_require__("../../node_modules/lucide-react/dist/esm/icons/user-plus.js"),_barrel_optimize_names_Mail_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_5__=__webpack_require__("../../node_modules/lucide-react/dist/esm/icons/mail.js"),_barrel_optimize_names_Mail_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_6__=__webpack_require__("../../node_modules/lucide-react/dist/esm/icons/users.js");const __WEBPACK_DEFAULT_EXPORT__={title:"Authentication/Registration Dialog",component:_cloc_atoms__WEBPACK_IMPORTED_MODULE_2__.BiX,parameters:{layout:"centered",docs:{description:{component:"A dialog wrapper component for the registration form. Provides a modal interface for user registration with customizable trigger elements. Automatically handles dialog state and form submission within the modal context."}}},argTypes:{trigger:{control:!1,description:'Custom trigger element to open the dialog. If not provided, uses default "REGISTER NOW" button.'},signInLink:{control:"text",description:"URL for the sign-in page link displayed within the registration form"}}},Default={args:{}},WithSignInLink={args:{signInLink:"/login"},parameters:{docs:{description:{story:"Registration dialog with a sign-in link for existing users to navigate to the login page."}}}},CustomTrigger={args:{trigger:(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)(_cloc_ui__WEBPACK_IMPORTED_MODULE_3__.cc,{className:"bg-blue-600 hover:bg-blue-700 text-white",children:[(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(_barrel_optimize_names_Mail_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_4__.A,{size:16,className:"mr-2"}),"Create Account"]}),signInLink:"/login"},parameters:{docs:{description:{story:"Dialog triggered by a custom blue-themed button with UserPlus icon."}}}},EmailSignupTrigger={args:{trigger:(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)(_cloc_ui__WEBPACK_IMPORTED_MODULE_3__.cc,{className:"bg-green-600 hover:bg-green-700 text-white",children:[(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(_barrel_optimize_names_Mail_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_5__.A,{size:16,className:"mr-2"}),"Sign Up with Email"]}),signInLink:"/login"},parameters:{docs:{description:{story:"Dialog triggered by an email-themed button, suitable for email signup flows."}}}},TeamInviteTrigger={args:{trigger:(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)(_cloc_ui__WEBPACK_IMPORTED_MODULE_3__.cc,{className:"bg-purple-600 hover:bg-purple-700 text-white",children:[(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(_barrel_optimize_names_Mail_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_6__.A,{size:16,className:"mr-2"}),"Join Team"]}),signInLink:"/login"},parameters:{docs:{description:{story:"Dialog triggered by a team-themed button, suitable for team invitation flows."}}}};Default.parameters={...Default.parameters,docs:{...null===(_Default_parameters=Default.parameters)||void 0===_Default_parameters?void 0:_Default_parameters.docs,source:{originalSource:"{\n  args: {}\n}",...null===(_Default_parameters1=Default.parameters)||void 0===_Default_parameters1||null===(_Default_parameters_docs=_Default_parameters1.docs)||void 0===_Default_parameters_docs?void 0:_Default_parameters_docs.source},description:{story:'Default registration dialog with standard trigger button.\r\nUses the built-in "REGISTER NOW" button to open the dialog.',...null===(_Default_parameters2=Default.parameters)||void 0===_Default_parameters2||null===(_Default_parameters_docs1=_Default_parameters2.docs)||void 0===_Default_parameters_docs1?void 0:_Default_parameters_docs1.description}}},WithSignInLink.parameters={...WithSignInLink.parameters,docs:{...null===(_WithSignInLink_parameters=WithSignInLink.parameters)||void 0===_WithSignInLink_parameters?void 0:_WithSignInLink_parameters.docs,source:{originalSource:"{\n  args: {\n    signInLink: '/login'\n  },\n  parameters: {\n    docs: {\n      description: {\n        story: 'Registration dialog with a sign-in link for existing users to navigate to the login page.'\n      }\n    }\n  }\n}",...null===(_WithSignInLink_parameters1=WithSignInLink.parameters)||void 0===_WithSignInLink_parameters1||null===(_WithSignInLink_parameters_docs=_WithSignInLink_parameters1.docs)||void 0===_WithSignInLink_parameters_docs?void 0:_WithSignInLink_parameters_docs.source},description:{story:"Registration dialog with sign-in link for existing users.\r\nDemonstrates the complete registration flow with navigation options.",...null===(_WithSignInLink_parameters2=WithSignInLink.parameters)||void 0===_WithSignInLink_parameters2||null===(_WithSignInLink_parameters_docs1=_WithSignInLink_parameters2.docs)||void 0===_WithSignInLink_parameters_docs1?void 0:_WithSignInLink_parameters_docs1.description}}},CustomTrigger.parameters={...CustomTrigger.parameters,docs:{...null===(_CustomTrigger_parameters=CustomTrigger.parameters)||void 0===_CustomTrigger_parameters?void 0:_CustomTrigger_parameters.docs,source:{originalSource:"{\n  args: {\n    trigger: <ThemedButton className=\"bg-blue-600 hover:bg-blue-700 text-white\">\r\n                <UserPlus size={16} className=\"mr-2\" />\r\n                Create Account\r\n            </ThemedButton>,\n    signInLink: '/login'\n  },\n  parameters: {\n    docs: {\n      description: {\n        story: 'Dialog triggered by a custom blue-themed button with UserPlus icon.'\n      }\n    }\n  }\n}",...null===(_CustomTrigger_parameters1=CustomTrigger.parameters)||void 0===_CustomTrigger_parameters1||null===(_CustomTrigger_parameters_docs=_CustomTrigger_parameters1.docs)||void 0===_CustomTrigger_parameters_docs?void 0:_CustomTrigger_parameters_docs.source},description:{story:"Registration dialog with custom trigger element.\r\nShows how to provide a custom button or element to trigger the dialog.",...null===(_CustomTrigger_parameters2=CustomTrigger.parameters)||void 0===_CustomTrigger_parameters2||null===(_CustomTrigger_parameters_docs1=_CustomTrigger_parameters2.docs)||void 0===_CustomTrigger_parameters_docs1?void 0:_CustomTrigger_parameters_docs1.description}}},EmailSignupTrigger.parameters={...EmailSignupTrigger.parameters,docs:{...null===(_EmailSignupTrigger_parameters=EmailSignupTrigger.parameters)||void 0===_EmailSignupTrigger_parameters?void 0:_EmailSignupTrigger_parameters.docs,source:{originalSource:"{\n  args: {\n    trigger: <ThemedButton className=\"bg-green-600 hover:bg-green-700 text-white\">\r\n                <Mail size={16} className=\"mr-2\" />\r\n                Sign Up with Email\r\n            </ThemedButton>,\n    signInLink: '/login'\n  },\n  parameters: {\n    docs: {\n      description: {\n        story: 'Dialog triggered by an email-themed button, suitable for email signup flows.'\n      }\n    }\n  }\n}",...null===(_EmailSignupTrigger_parameters1=EmailSignupTrigger.parameters)||void 0===_EmailSignupTrigger_parameters1||null===(_EmailSignupTrigger_parameters_docs=_EmailSignupTrigger_parameters1.docs)||void 0===_EmailSignupTrigger_parameters_docs?void 0:_EmailSignupTrigger_parameters_docs.source},description:{story:"Registration dialog with email-themed trigger.\r\nDemonstrates a trigger styled for email signup contexts.",...null===(_EmailSignupTrigger_parameters2=EmailSignupTrigger.parameters)||void 0===_EmailSignupTrigger_parameters2||null===(_EmailSignupTrigger_parameters_docs1=_EmailSignupTrigger_parameters2.docs)||void 0===_EmailSignupTrigger_parameters_docs1?void 0:_EmailSignupTrigger_parameters_docs1.description}}},TeamInviteTrigger.parameters={...TeamInviteTrigger.parameters,docs:{...null===(_TeamInviteTrigger_parameters=TeamInviteTrigger.parameters)||void 0===_TeamInviteTrigger_parameters?void 0:_TeamInviteTrigger_parameters.docs,source:{originalSource:"{\n  args: {\n    trigger: <ThemedButton className=\"bg-purple-600 hover:bg-purple-700 text-white\">\r\n                <Users size={16} className=\"mr-2\" />\r\n                Join Team\r\n            </ThemedButton>,\n    signInLink: '/login'\n  },\n  parameters: {\n    docs: {\n      description: {\n        story: 'Dialog triggered by a team-themed button, suitable for team invitation flows.'\n      }\n    }\n  }\n}",...null===(_TeamInviteTrigger_parameters1=TeamInviteTrigger.parameters)||void 0===_TeamInviteTrigger_parameters1||null===(_TeamInviteTrigger_parameters_docs=_TeamInviteTrigger_parameters1.docs)||void 0===_TeamInviteTrigger_parameters_docs?void 0:_TeamInviteTrigger_parameters_docs.source},description:{story:"Registration dialog with team invitation trigger.\r\nShows how the dialog can be used in team invitation contexts.",...null===(_TeamInviteTrigger_parameters2=TeamInviteTrigger.parameters)||void 0===_TeamInviteTrigger_parameters2||null===(_TeamInviteTrigger_parameters_docs1=_TeamInviteTrigger_parameters2.docs)||void 0===_TeamInviteTrigger_parameters_docs1?void 0:_TeamInviteTrigger_parameters_docs1.description}}};const __namedExportsOrder=["Default","WithSignInLink","CustomTrigger","EmailSignupTrigger","TeamInviteTrigger"]}}]);