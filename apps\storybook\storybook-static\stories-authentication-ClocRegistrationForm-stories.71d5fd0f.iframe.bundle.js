"use strict";(self.webpackChunk_cloc_storybook=self.webpackChunk_cloc_storybook||[]).push([[6418],{"./src/stories/authentication/ClocRegistrationForm.stories.tsx":(__unused_webpack_module,__webpack_exports__,__webpack_require__)=>{__webpack_require__.r(__webpack_exports__),__webpack_require__.d(__webpack_exports__,{CustomStyling:()=>CustomStyling,Default:()=>Default,WithRedirectHandler:()=>WithRedirectHandler,WithSignInLink:()=>WithSignInLink,__namedExportsOrder:()=>__namedExportsOrder,default:()=>__WEBPACK_DEFAULT_EXPORT__});__webpack_require__("../../node_modules/next/dist/compiled/react/index.js");var _Default_parameters,_Default_parameters_docs,_Default_parameters1,_Default_parameters_docs1,_Default_parameters2,_WithSignInLink_parameters,_WithSignInLink_parameters_docs,_WithSignInLink_parameters1,_WithSignInLink_parameters_docs1,_WithSignInLink_parameters2,_WithRedirectHandler_parameters,_WithRedirectHandler_parameters_docs,_WithRedirectHandler_parameters1,_WithRedirectHandler_parameters_docs1,_WithRedirectHandler_parameters2,_CustomStyling_parameters,_CustomStyling_parameters_docs,_CustomStyling_parameters1,_CustomStyling_parameters_docs1,_CustomStyling_parameters2,_cloc_atoms__WEBPACK_IMPORTED_MODULE_1__=__webpack_require__("../../packages/toolkit/atoms/dist/index.es.js"),console=__webpack_require__("../../node_modules/console-browserify/index.js");const __WEBPACK_DEFAULT_EXPORT__={title:"Authentication/Registration Form",component:_cloc_atoms__WEBPACK_IMPORTED_MODULE_1__.d7O,parameters:{layout:"centered",docs:{description:{component:"A comprehensive user registration form component with full name, email, password, and password confirmation fields. Includes terms and conditions acceptance checkbox, form validation, error handling, and optional sign-in link integration. Features loading states and redirect functionality upon successful registration."}}},argTypes:{className:{control:"text",description:"Additional CSS classes for styling the form container"},signInLink:{control:"text",description:"URL for the sign-in page link displayed at the bottom of the form"},redirectHandler:{action:"redirected",description:"Function called after successful registration"}}},Default={args:{}},WithSignInLink={args:{signInLink:"/login"},parameters:{docs:{description:{story:"Registration form with a sign-in link for existing users to navigate to the login page."}}}},WithRedirectHandler={args:{redirectHandler:()=>console.log("Registration successful, redirecting...")},parameters:{docs:{description:{story:"Registration form with a redirect handler that executes after successful registration."}}}},CustomStyling={args:{className:"p-4 rounded-lg border-2 border-green-200 dark:border-green-800 bg-green-50 dark:bg-green-950",signInLink:"/login"},parameters:{docs:{description:{story:"Form with custom green-themed styling applied through the className prop."}}}};Default.parameters={...Default.parameters,docs:{...null===(_Default_parameters=Default.parameters)||void 0===_Default_parameters?void 0:_Default_parameters.docs,source:{originalSource:"{\n  args: {}\n}",...null===(_Default_parameters1=Default.parameters)||void 0===_Default_parameters1||null===(_Default_parameters_docs=_Default_parameters1.docs)||void 0===_Default_parameters_docs?void 0:_Default_parameters_docs.source},description:{story:"Default registration form with standard styling and functionality.\r\nShows all registration fields including name, email, password, and terms acceptance.",...null===(_Default_parameters2=Default.parameters)||void 0===_Default_parameters2||null===(_Default_parameters_docs1=_Default_parameters2.docs)||void 0===_Default_parameters_docs1?void 0:_Default_parameters_docs1.description}}},WithSignInLink.parameters={...WithSignInLink.parameters,docs:{...null===(_WithSignInLink_parameters=WithSignInLink.parameters)||void 0===_WithSignInLink_parameters?void 0:_WithSignInLink_parameters.docs,source:{originalSource:"{\n  args: {\n    signInLink: '/login'\n  },\n  parameters: {\n    docs: {\n      description: {\n        story: 'Registration form with a sign-in link for existing users to navigate to the login page.'\n      }\n    }\n  }\n}",...null===(_WithSignInLink_parameters1=WithSignInLink.parameters)||void 0===_WithSignInLink_parameters1||null===(_WithSignInLink_parameters_docs=_WithSignInLink_parameters1.docs)||void 0===_WithSignInLink_parameters_docs?void 0:_WithSignInLink_parameters_docs.source},description:{story:"Registration form with sign-in link for users who already have accounts.\r\nDemonstrates the complete registration flow with navigation options.",...null===(_WithSignInLink_parameters2=WithSignInLink.parameters)||void 0===_WithSignInLink_parameters2||null===(_WithSignInLink_parameters_docs1=_WithSignInLink_parameters2.docs)||void 0===_WithSignInLink_parameters_docs1?void 0:_WithSignInLink_parameters_docs1.description}}},WithRedirectHandler.parameters={...WithRedirectHandler.parameters,docs:{...null===(_WithRedirectHandler_parameters=WithRedirectHandler.parameters)||void 0===_WithRedirectHandler_parameters?void 0:_WithRedirectHandler_parameters.docs,source:{originalSource:"{\n  args: {\n    redirectHandler: () => console.log('Registration successful, redirecting...')\n  },\n  parameters: {\n    docs: {\n      description: {\n        story: 'Registration form with a redirect handler that executes after successful registration.'\n      }\n    }\n  }\n}",...null===(_WithRedirectHandler_parameters1=WithRedirectHandler.parameters)||void 0===_WithRedirectHandler_parameters1||null===(_WithRedirectHandler_parameters_docs=_WithRedirectHandler_parameters1.docs)||void 0===_WithRedirectHandler_parameters_docs?void 0:_WithRedirectHandler_parameters_docs.source},description:{story:"Registration form with redirect handler for post-registration navigation.\r\nShows how to handle successful registration events.",...null===(_WithRedirectHandler_parameters2=WithRedirectHandler.parameters)||void 0===_WithRedirectHandler_parameters2||null===(_WithRedirectHandler_parameters_docs1=_WithRedirectHandler_parameters2.docs)||void 0===_WithRedirectHandler_parameters_docs1?void 0:_WithRedirectHandler_parameters_docs1.description}}},CustomStyling.parameters={...CustomStyling.parameters,docs:{...null===(_CustomStyling_parameters=CustomStyling.parameters)||void 0===_CustomStyling_parameters?void 0:_CustomStyling_parameters.docs,source:{originalSource:"{\n  args: {\n    className: 'p-4 rounded-lg border-2 border-green-200 dark:border-green-800 bg-green-50 dark:bg-green-950',\n    signInLink: '/login'\n  },\n  parameters: {\n    docs: {\n      description: {\n        story: 'Form with custom green-themed styling applied through the className prop.'\n      }\n    }\n  }\n}",...null===(_CustomStyling_parameters1=CustomStyling.parameters)||void 0===_CustomStyling_parameters1||null===(_CustomStyling_parameters_docs=_CustomStyling_parameters1.docs)||void 0===_CustomStyling_parameters_docs?void 0:_CustomStyling_parameters_docs.source},description:{story:"Registration form with custom styling applied via className prop.\r\nDemonstrates visual customization capabilities.",...null===(_CustomStyling_parameters2=CustomStyling.parameters)||void 0===_CustomStyling_parameters2||null===(_CustomStyling_parameters_docs1=_CustomStyling_parameters2.docs)||void 0===_CustomStyling_parameters_docs1?void 0:_CustomStyling_parameters_docs1.description}}};const __namedExportsOrder=["Default","WithSignInLink","WithRedirectHandler","CustomStyling"]}}]);