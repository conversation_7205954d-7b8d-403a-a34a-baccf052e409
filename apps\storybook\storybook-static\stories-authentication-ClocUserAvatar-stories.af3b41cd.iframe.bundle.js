/*! For license information please see stories-authentication-ClocUserAvatar-stories.af3b41cd.iframe.bundle.js.LICENSE.txt */
"use strict";(self.webpackChunk_cloc_storybook=self.webpackChunk_cloc_storybook||[]).push([[6789],{"../../node_modules/lucide-react/dist/esm/createLucideIcon.js":(__unused_webpack_module,__webpack_exports__,__webpack_require__)=>{__webpack_require__.d(__webpack_exports__,{A:()=>createLucideIcon});var react=__webpack_require__("../../node_modules/next/dist/compiled/react/index.js");const mergeClasses=(...classes)=>classes.filter((className,index,array)=>Boolean(className)&&array.indexOf(className)===index).join(" ");var defaultAttributes={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};const Icon=(0,react.forwardRef)(({color="currentColor",size=24,strokeWidth=2,absoluteStrokeWidth,className="",children,iconNode,...rest},ref)=>(0,react.createElement)("svg",{ref,...defaultAttributes,width:size,height:size,stroke:color,strokeWidth:absoluteStrokeWidth?24*Number(strokeWidth)/Number(size):strokeWidth,className:mergeClasses("lucide",className),...rest},[...iconNode.map(([tag,attrs])=>(0,react.createElement)(tag,attrs)),...Array.isArray(children)?children:[children]])),createLucideIcon=(iconName,iconNode)=>{const Component=(0,react.forwardRef)(({className,...props},ref)=>{return(0,react.createElement)(Icon,{ref,iconNode,className:mergeClasses(`lucide-${string=iconName,string.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase()}`,className),...props});var string});return Component.displayName=`${iconName}`,Component}},"../../node_modules/lucide-react/dist/esm/icons/settings.js":(__unused_webpack_module,__webpack_exports__,__webpack_require__)=>{__webpack_require__.d(__webpack_exports__,{A:()=>Settings});const Settings=(0,__webpack_require__("../../node_modules/lucide-react/dist/esm/createLucideIcon.js").A)("Settings",[["path",{d:"M12.22 2h-.44a2 2 0 0 0-2 2v.18a2 2 0 0 1-1 1.73l-.43.25a2 2 0 0 1-2 0l-.15-.08a2 2 0 0 0-2.73.73l-.22.38a2 2 0 0 0 .73 2.73l.15.1a2 2 0 0 1 1 1.72v.51a2 2 0 0 1-1 1.74l-.15.09a2 2 0 0 0-.73 2.73l.22.38a2 2 0 0 0 2.73.73l.15-.08a2 2 0 0 1 2 0l.43.25a2 2 0 0 1 1 1.73V20a2 2 0 0 0 2 2h.44a2 2 0 0 0 2-2v-.18a2 2 0 0 1 1-1.73l.43-.25a2 2 0 0 1 2 0l.15.08a2 2 0 0 0 2.73-.73l.22-.39a2 2 0 0 0-.73-2.73l-.15-.08a2 2 0 0 1-1-1.74v-.5a2 2 0 0 1 1-1.74l.15-.09a2 2 0 0 0 .73-2.73l-.22-.38a2 2 0 0 0-2.73-.73l-.15.08a2 2 0 0 1-2 0l-.43-.25a2 2 0 0 1-1-1.73V4a2 2 0 0 0-2-2z",key:"1qme2f"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},"../../node_modules/lucide-react/dist/esm/icons/user.js":(__unused_webpack_module,__webpack_exports__,__webpack_require__)=>{__webpack_require__.d(__webpack_exports__,{A:()=>User});const User=(0,__webpack_require__("../../node_modules/lucide-react/dist/esm/createLucideIcon.js").A)("User",[["path",{d:"M19 21v-2a4 4 0 0 0-4-4H9a4 4 0 0 0-4 4v2",key:"975kel"}],["circle",{cx:"12",cy:"7",r:"4",key:"17ys0d"}]])},"./src/stories/authentication/ClocUserAvatar.stories.tsx":(__unused_webpack_module,__webpack_exports__,__webpack_require__)=>{__webpack_require__.r(__webpack_exports__),__webpack_require__.d(__webpack_exports__,{CenterPosition:()=>CenterPosition,CompactView:()=>CompactView,Default:()=>Default,InNavigation:()=>InNavigation,InSidebar:()=>InSidebar,InToolbar:()=>InToolbar,MobileHeader:()=>MobileHeader,StartPosition:()=>StartPosition,WithCustomMenu:()=>WithCustomMenu,WithoutMenu:()=>WithoutMenu,__namedExportsOrder:()=>__namedExportsOrder,default:()=>ClocUserAvatar_stories});var jsx_runtime=__webpack_require__("../../node_modules/next/dist/compiled/react/jsx-runtime.js"),index_es=(__webpack_require__("../../node_modules/next/dist/compiled/react/index.js"),__webpack_require__("../../packages/toolkit/atoms/dist/index.es.js"));const Bell=(0,__webpack_require__("../../node_modules/lucide-react/dist/esm/createLucideIcon.js").A)("Bell",[["path",{d:"M6 8a6 6 0 0 1 12 0c0 7 3 9 3 9H3s3-2 3-9",key:"1qo2s2"}],["path",{d:"M10.3 21a1.94 1.94 0 0 0 3.4 0",key:"qgo35s"}]]);var _Default_parameters,_Default_parameters_docs,_Default_parameters1,_WithoutMenu_parameters,_WithoutMenu_parameters_docs,_WithoutMenu_parameters1,_CenterPosition_parameters,_CenterPosition_parameters_docs,_CenterPosition_parameters1,_StartPosition_parameters,_StartPosition_parameters_docs,_StartPosition_parameters1,_InNavigation_parameters,_InNavigation_parameters_docs,_InNavigation_parameters1,_InSidebar_parameters,_InSidebar_parameters_docs,_InSidebar_parameters1,_WithCustomMenu_parameters,_WithCustomMenu_parameters_docs,_WithCustomMenu_parameters1,_CompactView_parameters,_CompactView_parameters_docs,_CompactView_parameters1,_InToolbar_parameters,_InToolbar_parameters_docs,_InToolbar_parameters1,_MobileHeader_parameters,_MobileHeader_parameters_docs,_MobileHeader_parameters1,user=__webpack_require__("../../node_modules/lucide-react/dist/esm/icons/user.js"),settings=__webpack_require__("../../node_modules/lucide-react/dist/esm/icons/settings.js");const ClocUserAvatar_stories={title:"Authentication/User Avatar",component:index_es.axA,parameters:{layout:"centered",docs:{description:{component:"A user avatar component that displays the authenticated user's profile picture with an optional popover menu. Shows loading state when user data is being fetched and provides navigation options when clicked."}}},argTypes:{showMenu:{control:"boolean",description:"Whether to show the popover menu on click"},position:{control:"select",options:["center","end","start"],description:"Position alignment for the popover menu"},children:{control:!1,description:"Custom content to display in the popover"}}},Default={args:{showMenu:!0,position:"end"}},WithoutMenu={args:{showMenu:!1}},CenterPosition={args:{showMenu:!0,position:"center"}},StartPosition={args:{showMenu:!0,position:"start"}},InNavigation={render:()=>(0,jsx_runtime.jsx)("div",{className:"bg-white dark:bg-gray-900 border-b border-gray-200 dark:border-gray-700 p-4",children:(0,jsx_runtime.jsxs)("div",{className:"flex items-center justify-between max-w-6xl mx-auto",children:[(0,jsx_runtime.jsxs)("div",{className:"flex items-center gap-2",children:[(0,jsx_runtime.jsx)("div",{className:"w-8 h-8 bg-blue-600 rounded-full flex items-center justify-center",children:(0,jsx_runtime.jsx)("span",{className:"text-white font-bold text-sm",children:"C"})}),(0,jsx_runtime.jsx)("span",{className:"font-semibold text-gray-900 dark:text-white",children:"Cloc Dashboard"})]}),(0,jsx_runtime.jsxs)("div",{className:"flex items-center gap-4",children:[(0,jsx_runtime.jsx)("button",{className:"p-2 text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white",children:(0,jsx_runtime.jsx)(Bell,{size:20})}),(0,jsx_runtime.jsx)(index_es.axA,{showMenu:!0,position:"end"})]})]})})},InSidebar={render:()=>(0,jsx_runtime.jsx)("div",{className:"w-64 bg-gray-50 dark:bg-gray-900 border-r border-gray-200 dark:border-gray-700 p-4",children:(0,jsx_runtime.jsxs)("div",{className:"space-y-4",children:[(0,jsx_runtime.jsxs)("div",{className:"flex items-center gap-3 p-3 bg-white dark:bg-gray-800 rounded-lg",children:[(0,jsx_runtime.jsx)(index_es.axA,{showMenu:!1}),(0,jsx_runtime.jsxs)("div",{className:"flex-1 min-w-0",children:[(0,jsx_runtime.jsx)("p",{className:"text-sm font-medium text-gray-900 dark:text-white truncate",children:"John Doe"}),(0,jsx_runtime.jsx)("p",{className:"text-xs text-gray-500 dark:text-gray-400 truncate",children:"<EMAIL>"})]})]}),(0,jsx_runtime.jsxs)("nav",{className:"space-y-1",children:[(0,jsx_runtime.jsxs)("a",{href:"#",className:"flex items-center gap-3 px-3 py-2 text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-800 rounded",children:[(0,jsx_runtime.jsx)(user.A,{size:16}),"Profile"]}),(0,jsx_runtime.jsxs)("a",{href:"#",className:"flex items-center gap-3 px-3 py-2 text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-800 rounded",children:[(0,jsx_runtime.jsx)(settings.A,{size:16}),"Settings"]})]})]})})},WithCustomMenu={args:{showMenu:!0,position:"end",children:(0,jsx_runtime.jsxs)("div",{className:"p-2 space-y-1",children:[(0,jsx_runtime.jsxs)("button",{className:"w-full flex items-center gap-2 px-3 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-800 rounded",children:[(0,jsx_runtime.jsx)(user.A,{size:16}),"View Profile"]}),(0,jsx_runtime.jsxs)("button",{className:"w-full flex items-center gap-2 px-3 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-800 rounded",children:[(0,jsx_runtime.jsx)(settings.A,{size:16}),"Account Settings"]}),(0,jsx_runtime.jsx)("hr",{className:"border-gray-200 dark:border-gray-700"})]})}},CompactView={render:()=>(0,jsx_runtime.jsxs)("div",{className:"flex items-center gap-2 p-2 bg-gray-100 dark:bg-gray-800 rounded-lg",children:[(0,jsx_runtime.jsx)(index_es.axA,{showMenu:!0,position:"end"}),(0,jsx_runtime.jsx)("span",{className:"text-sm text-gray-700 dark:text-gray-300",children:"John D."})]})},InToolbar={render:()=>(0,jsx_runtime.jsx)("div",{className:"bg-white dark:bg-gray-900 border border-gray-200 dark:border-gray-700 rounded-lg p-3",children:(0,jsx_runtime.jsxs)("div",{className:"flex items-center justify-between",children:[(0,jsx_runtime.jsx)("div",{className:"flex items-center gap-3",children:(0,jsx_runtime.jsx)("h2",{className:"text-lg font-semibold text-gray-900 dark:text-white",children:"Project Dashboard"})}),(0,jsx_runtime.jsxs)("div",{className:"flex items-center gap-3",children:[(0,jsx_runtime.jsx)("button",{className:"px-3 py-1 text-sm bg-blue-600 text-white rounded hover:bg-blue-700",children:"New Project"}),(0,jsx_runtime.jsx)(index_es.axA,{showMenu:!0,position:"end"})]})]})})},MobileHeader={render:()=>(0,jsx_runtime.jsx)("div",{className:"bg-white dark:bg-gray-900 border-b border-gray-200 dark:border-gray-700 p-4",children:(0,jsx_runtime.jsxs)("div",{className:"flex items-center justify-between",children:[(0,jsx_runtime.jsx)("h1",{className:"text-xl font-semibold text-gray-900 dark:text-white",children:"Cloc"}),(0,jsx_runtime.jsx)(index_es.axA,{showMenu:!0,position:"end"})]})})};Default.parameters={...Default.parameters,docs:{...null===(_Default_parameters=Default.parameters)||void 0===_Default_parameters?void 0:_Default_parameters.docs,source:{originalSource:"{\n  args: {\n    showMenu: true,\n    position: 'end'\n  }\n}",...null===(_Default_parameters1=Default.parameters)||void 0===_Default_parameters1||null===(_Default_parameters_docs=_Default_parameters1.docs)||void 0===_Default_parameters_docs?void 0:_Default_parameters_docs.source}}},WithoutMenu.parameters={...WithoutMenu.parameters,docs:{...null===(_WithoutMenu_parameters=WithoutMenu.parameters)||void 0===_WithoutMenu_parameters?void 0:_WithoutMenu_parameters.docs,source:{originalSource:"{\n  args: {\n    showMenu: false\n  }\n}",...null===(_WithoutMenu_parameters1=WithoutMenu.parameters)||void 0===_WithoutMenu_parameters1||null===(_WithoutMenu_parameters_docs=_WithoutMenu_parameters1.docs)||void 0===_WithoutMenu_parameters_docs?void 0:_WithoutMenu_parameters_docs.source}}},CenterPosition.parameters={...CenterPosition.parameters,docs:{...null===(_CenterPosition_parameters=CenterPosition.parameters)||void 0===_CenterPosition_parameters?void 0:_CenterPosition_parameters.docs,source:{originalSource:"{\n  args: {\n    showMenu: true,\n    position: 'center'\n  }\n}",...null===(_CenterPosition_parameters1=CenterPosition.parameters)||void 0===_CenterPosition_parameters1||null===(_CenterPosition_parameters_docs=_CenterPosition_parameters1.docs)||void 0===_CenterPosition_parameters_docs?void 0:_CenterPosition_parameters_docs.source}}},StartPosition.parameters={...StartPosition.parameters,docs:{...null===(_StartPosition_parameters=StartPosition.parameters)||void 0===_StartPosition_parameters?void 0:_StartPosition_parameters.docs,source:{originalSource:"{\n  args: {\n    showMenu: true,\n    position: 'start'\n  }\n}",...null===(_StartPosition_parameters1=StartPosition.parameters)||void 0===_StartPosition_parameters1||null===(_StartPosition_parameters_docs=_StartPosition_parameters1.docs)||void 0===_StartPosition_parameters_docs?void 0:_StartPosition_parameters_docs.source}}},InNavigation.parameters={...InNavigation.parameters,docs:{...null===(_InNavigation_parameters=InNavigation.parameters)||void 0===_InNavigation_parameters?void 0:_InNavigation_parameters.docs,source:{originalSource:'{\n  render: () => <div className="bg-white dark:bg-gray-900 border-b border-gray-200 dark:border-gray-700 p-4">\r\n            <div className="flex items-center justify-between max-w-6xl mx-auto">\r\n                <div className="flex items-center gap-2">\r\n                    <div className="w-8 h-8 bg-blue-600 rounded-full flex items-center justify-center">\r\n                        <span className="text-white font-bold text-sm">C</span>\r\n                    </div>\r\n                    <span className="font-semibold text-gray-900 dark:text-white">Cloc Dashboard</span>\r\n                </div>\r\n                <div className="flex items-center gap-4">\r\n                    <button className="p-2 text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white">\r\n                        <Bell size={20} />\r\n                    </button>\r\n                    <ClocUserAvatar showMenu={true} position="end" />\r\n                </div>\r\n            </div>\r\n        </div>\n}',...null===(_InNavigation_parameters1=InNavigation.parameters)||void 0===_InNavigation_parameters1||null===(_InNavigation_parameters_docs=_InNavigation_parameters1.docs)||void 0===_InNavigation_parameters_docs?void 0:_InNavigation_parameters_docs.source}}},InSidebar.parameters={...InSidebar.parameters,docs:{...null===(_InSidebar_parameters=InSidebar.parameters)||void 0===_InSidebar_parameters?void 0:_InSidebar_parameters.docs,source:{originalSource:'{\n  render: () => <div className="w-64 bg-gray-50 dark:bg-gray-900 border-r border-gray-200 dark:border-gray-700 p-4">\r\n            <div className="space-y-4">\r\n                <div className="flex items-center gap-3 p-3 bg-white dark:bg-gray-800 rounded-lg">\r\n                    <ClocUserAvatar showMenu={false} />\r\n                    <div className="flex-1 min-w-0">\r\n                        <p className="text-sm font-medium text-gray-900 dark:text-white truncate">John Doe</p>\r\n                        <p className="text-xs text-gray-500 dark:text-gray-400 truncate"><EMAIL></p>\r\n                    </div>\r\n                </div>\r\n                <nav className="space-y-1">\r\n                    <a href="#" className="flex items-center gap-3 px-3 py-2 text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-800 rounded">\r\n                        <User size={16} />\r\n                        Profile\r\n                    </a>\r\n                    <a href="#" className="flex items-center gap-3 px-3 py-2 text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-800 rounded">\r\n                        <Settings size={16} />\r\n                        Settings\r\n                    </a>\r\n                </nav>\r\n            </div>\r\n        </div>\n}',...null===(_InSidebar_parameters1=InSidebar.parameters)||void 0===_InSidebar_parameters1||null===(_InSidebar_parameters_docs=_InSidebar_parameters1.docs)||void 0===_InSidebar_parameters_docs?void 0:_InSidebar_parameters_docs.source}}},WithCustomMenu.parameters={...WithCustomMenu.parameters,docs:{...null===(_WithCustomMenu_parameters=WithCustomMenu.parameters)||void 0===_WithCustomMenu_parameters?void 0:_WithCustomMenu_parameters.docs,source:{originalSource:'{\n  args: {\n    showMenu: true,\n    position: \'end\',\n    children: <div className="p-2 space-y-1">\r\n                <button className="w-full flex items-center gap-2 px-3 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-800 rounded">\r\n                    <User size={16} />\r\n                    View Profile\r\n                </button>\r\n                <button className="w-full flex items-center gap-2 px-3 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-800 rounded">\r\n                    <Settings size={16} />\r\n                    Account Settings\r\n                </button>\r\n                <hr className="border-gray-200 dark:border-gray-700" />\r\n            </div>\n  }\n}',...null===(_WithCustomMenu_parameters1=WithCustomMenu.parameters)||void 0===_WithCustomMenu_parameters1||null===(_WithCustomMenu_parameters_docs=_WithCustomMenu_parameters1.docs)||void 0===_WithCustomMenu_parameters_docs?void 0:_WithCustomMenu_parameters_docs.source}}},CompactView.parameters={...CompactView.parameters,docs:{...null===(_CompactView_parameters=CompactView.parameters)||void 0===_CompactView_parameters?void 0:_CompactView_parameters.docs,source:{originalSource:'{\n  render: () => <div className="flex items-center gap-2 p-2 bg-gray-100 dark:bg-gray-800 rounded-lg">\r\n            <ClocUserAvatar showMenu={true} position="end" />\r\n            <span className="text-sm text-gray-700 dark:text-gray-300">John D.</span>\r\n        </div>\n}',...null===(_CompactView_parameters1=CompactView.parameters)||void 0===_CompactView_parameters1||null===(_CompactView_parameters_docs=_CompactView_parameters1.docs)||void 0===_CompactView_parameters_docs?void 0:_CompactView_parameters_docs.source}}},InToolbar.parameters={...InToolbar.parameters,docs:{...null===(_InToolbar_parameters=InToolbar.parameters)||void 0===_InToolbar_parameters?void 0:_InToolbar_parameters.docs,source:{originalSource:'{\n  render: () => <div className="bg-white dark:bg-gray-900 border border-gray-200 dark:border-gray-700 rounded-lg p-3">\r\n            <div className="flex items-center justify-between">\r\n                <div className="flex items-center gap-3">\r\n                    <h2 className="text-lg font-semibold text-gray-900 dark:text-white">Project Dashboard</h2>\r\n                </div>\r\n                <div className="flex items-center gap-3">\r\n                    <button className="px-3 py-1 text-sm bg-blue-600 text-white rounded hover:bg-blue-700">\r\n                        New Project\r\n                    </button>\r\n                    <ClocUserAvatar showMenu={true} position="end" />\r\n                </div>\r\n            </div>\r\n        </div>\n}',...null===(_InToolbar_parameters1=InToolbar.parameters)||void 0===_InToolbar_parameters1||null===(_InToolbar_parameters_docs=_InToolbar_parameters1.docs)||void 0===_InToolbar_parameters_docs?void 0:_InToolbar_parameters_docs.source}}},MobileHeader.parameters={...MobileHeader.parameters,docs:{...null===(_MobileHeader_parameters=MobileHeader.parameters)||void 0===_MobileHeader_parameters?void 0:_MobileHeader_parameters.docs,source:{originalSource:'{\n  render: () => <div className="bg-white dark:bg-gray-900 border-b border-gray-200 dark:border-gray-700 p-4">\r\n            <div className="flex items-center justify-between">\r\n                <h1 className="text-xl font-semibold text-gray-900 dark:text-white">Cloc</h1>\r\n                <ClocUserAvatar showMenu={true} position="end" />\r\n            </div>\r\n        </div>\n}',...null===(_MobileHeader_parameters1=MobileHeader.parameters)||void 0===_MobileHeader_parameters1||null===(_MobileHeader_parameters_docs=_MobileHeader_parameters1.docs)||void 0===_MobileHeader_parameters_docs?void 0:_MobileHeader_parameters_docs.source}}};const __namedExportsOrder=["Default","WithoutMenu","CenterPosition","StartPosition","InNavigation","InSidebar","WithCustomMenu","CompactView","InToolbar","MobileHeader"]}}]);