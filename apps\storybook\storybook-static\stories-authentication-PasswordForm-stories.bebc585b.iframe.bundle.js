"use strict";(self.webpackChunk_cloc_storybook=self.webpackChunk_cloc_storybook||[]).push([[3977],{"./src/stories/authentication/PasswordForm.stories.tsx":(__unused_webpack_module,__webpack_exports__,__webpack_require__)=>{__webpack_require__.r(__webpack_exports__),__webpack_require__.d(__webpack_exports__,{CustomStyling:()=>CustomStyling,Default:()=>Default,InLoginPage:()=>InLoginPage,WithRedirectHandler:()=>WithRedirectHandler,__namedExportsOrder:()=>__namedExportsOrder,default:()=>__WEBPACK_DEFAULT_EXPORT__});var _Default_parameters,_Default_parameters_docs,_Default_parameters1,_Default_parameters_docs1,_Default_parameters2,_WithRedirectHandler_parameters,_WithRedirectHandler_parameters_docs,_WithRedirectHandler_parameters1,_WithRedirectHandler_parameters_docs1,_WithRedirectHandler_parameters2,_CustomStyling_parameters,_CustomStyling_parameters_docs,_CustomStyling_parameters1,_CustomStyling_parameters_docs1,_CustomStyling_parameters2,_InLoginPage_parameters,_InLoginPage_parameters_docs,_InLoginPage_parameters1,_InLoginPage_parameters_docs1,_InLoginPage_parameters2,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__=__webpack_require__("../../node_modules/next/dist/compiled/react/jsx-runtime.js"),_cloc_atoms__WEBPACK_IMPORTED_MODULE_2__=(__webpack_require__("../../node_modules/next/dist/compiled/react/index.js"),__webpack_require__("../../packages/toolkit/atoms/dist/index.es.js")),console=__webpack_require__("../../node_modules/console-browserify/index.js");const __WEBPACK_DEFAULT_EXPORT__={title:"Authentication/Password Form",component:_cloc_atoms__WEBPACK_IMPORTED_MODULE_2__.EIW,parameters:{layout:"centered",docs:{description:{component:"A simple password-based authentication form component with email and password fields. Features basic form validation, loading states, error handling, and optional redirect functionality. Uses Theme-UI integration for consistent styling across the application."}}},argTypes:{className:{control:"text",description:"Additional CSS classes for styling the form container"},redirectHandler:{action:"redirected",description:"Function called after successful authentication"}}},Default={args:{}},WithRedirectHandler={args:{redirectHandler:()=>console.log("Authentication successful, redirecting...")},parameters:{docs:{description:{story:"Password form with a redirect handler that executes after successful authentication."}}}},CustomStyling={args:{className:"p-4 rounded-lg border-2 border-blue-200 dark:border-blue-800 bg-blue-50 dark:bg-blue-950"},parameters:{docs:{description:{story:"Form with custom blue-themed styling applied through the className prop."}}}},InLoginPage={render:()=>(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("div",{className:" bg-gray-50 dark:bg-gray-900 flex items-center justify-center p-4",children:(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div",{className:"bg-white dark:bg-gray-800 rounded-lg shadow-lg p-8 w-full max-w-md",children:[(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div",{className:"text-center mb-8",children:[(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("h1",{className:"text-2xl font-bold text-gray-900 dark:text-white",children:"Welcome Back"}),(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("p",{className:"text-gray-600 dark:text-gray-400 mt-2",children:"Sign in to your account"})]}),(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(_cloc_atoms__WEBPACK_IMPORTED_MODULE_2__.EIW,{}),(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("div",{className:"mt-6 text-center",children:(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("a",{href:"#",className:"text-blue-600 hover:text-blue-700 text-sm",children:"Forgot your password?"})})]})}),parameters:{docs:{description:{story:"Password form within a complete login page layout with header and forgot password link."}}}};Default.parameters={...Default.parameters,docs:{...null===(_Default_parameters=Default.parameters)||void 0===_Default_parameters?void 0:_Default_parameters.docs,source:{originalSource:"{\n  args: {}\n}",...null===(_Default_parameters1=Default.parameters)||void 0===_Default_parameters1||null===(_Default_parameters_docs=_Default_parameters1.docs)||void 0===_Default_parameters_docs?void 0:_Default_parameters_docs.source},description:{story:"Default password form with standard styling and functionality.\r\nShows email and password input fields with sign-in button.",...null===(_Default_parameters2=Default.parameters)||void 0===_Default_parameters2||null===(_Default_parameters_docs1=_Default_parameters2.docs)||void 0===_Default_parameters_docs1?void 0:_Default_parameters_docs1.description}}},WithRedirectHandler.parameters={...WithRedirectHandler.parameters,docs:{...null===(_WithRedirectHandler_parameters=WithRedirectHandler.parameters)||void 0===_WithRedirectHandler_parameters?void 0:_WithRedirectHandler_parameters.docs,source:{originalSource:"{\n  args: {\n    redirectHandler: () => console.log('Authentication successful, redirecting...')\n  },\n  parameters: {\n    docs: {\n      description: {\n        story: 'Password form with a redirect handler that executes after successful authentication.'\n      }\n    }\n  }\n}",...null===(_WithRedirectHandler_parameters1=WithRedirectHandler.parameters)||void 0===_WithRedirectHandler_parameters1||null===(_WithRedirectHandler_parameters_docs=_WithRedirectHandler_parameters1.docs)||void 0===_WithRedirectHandler_parameters_docs?void 0:_WithRedirectHandler_parameters_docs.source},description:{story:"Password form with redirect handler for post-authentication navigation.\r\nShows how to handle successful authentication events.",...null===(_WithRedirectHandler_parameters2=WithRedirectHandler.parameters)||void 0===_WithRedirectHandler_parameters2||null===(_WithRedirectHandler_parameters_docs1=_WithRedirectHandler_parameters2.docs)||void 0===_WithRedirectHandler_parameters_docs1?void 0:_WithRedirectHandler_parameters_docs1.description}}},CustomStyling.parameters={...CustomStyling.parameters,docs:{...null===(_CustomStyling_parameters=CustomStyling.parameters)||void 0===_CustomStyling_parameters?void 0:_CustomStyling_parameters.docs,source:{originalSource:"{\n  args: {\n    className: 'p-4 rounded-lg border-2 border-blue-200 dark:border-blue-800 bg-blue-50 dark:bg-blue-950'\n  },\n  parameters: {\n    docs: {\n      description: {\n        story: 'Form with custom blue-themed styling applied through the className prop.'\n      }\n    }\n  }\n}",...null===(_CustomStyling_parameters1=CustomStyling.parameters)||void 0===_CustomStyling_parameters1||null===(_CustomStyling_parameters_docs=_CustomStyling_parameters1.docs)||void 0===_CustomStyling_parameters_docs?void 0:_CustomStyling_parameters_docs.source},description:{story:"Password form with custom styling applied via className prop.\r\nDemonstrates visual customization capabilities.",...null===(_CustomStyling_parameters2=CustomStyling.parameters)||void 0===_CustomStyling_parameters2||null===(_CustomStyling_parameters_docs1=_CustomStyling_parameters2.docs)||void 0===_CustomStyling_parameters_docs1?void 0:_CustomStyling_parameters_docs1.description}}},InLoginPage.parameters={...InLoginPage.parameters,docs:{...null===(_InLoginPage_parameters=InLoginPage.parameters)||void 0===_InLoginPage_parameters?void 0:_InLoginPage_parameters.docs,source:{originalSource:'{\n  render: () => <div className=" bg-gray-50 dark:bg-gray-900 flex items-center justify-center p-4">\r\n            <div className="bg-white dark:bg-gray-800 rounded-lg shadow-lg p-8 w-full max-w-md">\r\n                <div className="text-center mb-8">\r\n                    <h1 className="text-2xl font-bold text-gray-900 dark:text-white">Welcome Back</h1>\r\n                    <p className="text-gray-600 dark:text-gray-400 mt-2">Sign in to your account</p>\r\n                </div>\r\n                <PasswordForm />\r\n                <div className="mt-6 text-center">\r\n                    <a href="#" className="text-blue-600 hover:text-blue-700 text-sm">\r\n                        Forgot your password?\r\n                    </a>\r\n                </div>\r\n            </div>\r\n        </div>,\n  parameters: {\n    docs: {\n      description: {\n        story: \'Password form within a complete login page layout with header and forgot password link.\'\n      }\n    }\n  }\n}',...null===(_InLoginPage_parameters1=InLoginPage.parameters)||void 0===_InLoginPage_parameters1||null===(_InLoginPage_parameters_docs=_InLoginPage_parameters1.docs)||void 0===_InLoginPage_parameters_docs?void 0:_InLoginPage_parameters_docs.source},description:{story:"Password form in a login page layout.\r\nShows how the form appears within a complete authentication interface.",...null===(_InLoginPage_parameters2=InLoginPage.parameters)||void 0===_InLoginPage_parameters2||null===(_InLoginPage_parameters_docs1=_InLoginPage_parameters2.docs)||void 0===_InLoginPage_parameters_docs1?void 0:_InLoginPage_parameters_docs1.description}}};const __namedExportsOrder=["Default","WithRedirectHandler","CustomStyling","InLoginPage"]}}]);