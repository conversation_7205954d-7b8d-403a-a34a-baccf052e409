"use strict";(self.webpackChunk_cloc_storybook=self.webpackChunk_cloc_storybook||[]).push([[6997],{"./src/stories/authentication/TokenForm.stories.tsx":(__unused_webpack_module,__webpack_exports__,__webpack_require__)=>{__webpack_require__.r(__webpack_exports__),__webpack_require__.d(__webpack_exports__,{CustomStyling:()=>CustomStyling,Default:()=>Default,WithRedirectHandler:()=>WithRedirectHandler,__namedExportsOrder:()=>__namedExportsOrder,default:()=>__WEBPACK_DEFAULT_EXPORT__});__webpack_require__("../../node_modules/next/dist/compiled/react/index.js");var _Default_parameters,_Default_parameters_docs,_Default_parameters1,_Default_parameters_docs1,_Default_parameters2,_WithRedirectHandler_parameters,_WithRedirectHandler_parameters_docs,_WithRedirectHandler_parameters1,_WithRedirectHandler_parameters_docs1,_WithRedirectHandler_parameters2,_CustomStyling_parameters,_CustomStyling_parameters_docs,_CustomStyling_parameters1,_CustomStyling_parameters_docs1,_CustomStyling_parameters2,_cloc_atoms__WEBPACK_IMPORTED_MODULE_1__=__webpack_require__("../../packages/toolkit/atoms/dist/index.es.js"),console=__webpack_require__("../../node_modules/console-browserify/index.js");const __WEBPACK_DEFAULT_EXPORT__={title:"Authentication/Token Form",component:_cloc_atoms__WEBPACK_IMPORTED_MODULE_1__.eef,parameters:{layout:"centered",docs:{description:{component:"A token-based authentication form component with a single token input field. Features form validation, loading states, error handling, and optional redirect functionality. Uses Theme-UI integration for consistent styling and supports various token authentication methods."}}},argTypes:{className:{control:"text",description:"Additional CSS classes for styling the form container"},redirectHandler:{action:"redirected",description:"Function called after successful token authentication"}}},Default={args:{}},WithRedirectHandler={args:{redirectHandler:()=>console.log("Token authentication successful, redirecting...")},parameters:{docs:{description:{story:"Token form with a redirect handler that executes after successful authentication."}}}},CustomStyling={args:{className:"p-4 rounded-lg border-2 border-purple-200 dark:border-purple-800 bg-purple-50 dark:bg-purple-950"},parameters:{docs:{description:{story:"Form with custom purple-themed styling applied through the className prop."}}}};Default.parameters={...Default.parameters,docs:{...null===(_Default_parameters=Default.parameters)||void 0===_Default_parameters?void 0:_Default_parameters.docs,source:{originalSource:"{\n  args: {}\n}",...null===(_Default_parameters1=Default.parameters)||void 0===_Default_parameters1||null===(_Default_parameters_docs=_Default_parameters1.docs)||void 0===_Default_parameters_docs?void 0:_Default_parameters_docs.source},description:{story:"Default token form with standard styling and functionality.\r\nShows token input field with sign-in button.",...null===(_Default_parameters2=Default.parameters)||void 0===_Default_parameters2||null===(_Default_parameters_docs1=_Default_parameters2.docs)||void 0===_Default_parameters_docs1?void 0:_Default_parameters_docs1.description}}},WithRedirectHandler.parameters={...WithRedirectHandler.parameters,docs:{...null===(_WithRedirectHandler_parameters=WithRedirectHandler.parameters)||void 0===_WithRedirectHandler_parameters?void 0:_WithRedirectHandler_parameters.docs,source:{originalSource:"{\n  args: {\n    redirectHandler: () => console.log('Token authentication successful, redirecting...')\n  },\n  parameters: {\n    docs: {\n      description: {\n        story: 'Token form with a redirect handler that executes after successful authentication.'\n      }\n    }\n  }\n}",...null===(_WithRedirectHandler_parameters1=WithRedirectHandler.parameters)||void 0===_WithRedirectHandler_parameters1||null===(_WithRedirectHandler_parameters_docs=_WithRedirectHandler_parameters1.docs)||void 0===_WithRedirectHandler_parameters_docs?void 0:_WithRedirectHandler_parameters_docs.source},description:{story:"Token form with redirect handler for post-authentication navigation.\r\nShows how to handle successful token authentication events.",...null===(_WithRedirectHandler_parameters2=WithRedirectHandler.parameters)||void 0===_WithRedirectHandler_parameters2||null===(_WithRedirectHandler_parameters_docs1=_WithRedirectHandler_parameters2.docs)||void 0===_WithRedirectHandler_parameters_docs1?void 0:_WithRedirectHandler_parameters_docs1.description}}},CustomStyling.parameters={...CustomStyling.parameters,docs:{...null===(_CustomStyling_parameters=CustomStyling.parameters)||void 0===_CustomStyling_parameters?void 0:_CustomStyling_parameters.docs,source:{originalSource:"{\n  args: {\n    className: 'p-4 rounded-lg border-2 border-purple-200 dark:border-purple-800 bg-purple-50 dark:bg-purple-950'\n  },\n  parameters: {\n    docs: {\n      description: {\n        story: 'Form with custom purple-themed styling applied through the className prop.'\n      }\n    }\n  }\n}",...null===(_CustomStyling_parameters1=CustomStyling.parameters)||void 0===_CustomStyling_parameters1||null===(_CustomStyling_parameters_docs=_CustomStyling_parameters1.docs)||void 0===_CustomStyling_parameters_docs?void 0:_CustomStyling_parameters_docs.source},description:{story:"Token form with custom styling applied via className prop.\r\nDemonstrates visual customization capabilities.",...null===(_CustomStyling_parameters2=CustomStyling.parameters)||void 0===_CustomStyling_parameters2||null===(_CustomStyling_parameters_docs1=_CustomStyling_parameters2.docs)||void 0===_CustomStyling_parameters_docs1?void 0:_CustomStyling_parameters_docs1.description}}};const __namedExportsOrder=["Default","WithRedirectHandler","CustomStyling"]}}]);