/*! For license information please see stories-foundation-Button-stories.a0971a29.iframe.bundle.js.LICENSE.txt */
(self.webpackChunk_cloc_storybook=self.webpackChunk_cloc_storybook||[]).push([[8157],{"../../node_modules/lucide-react/dist/esm/createLucideIcon.js":(__unused_webpack_module,__webpack_exports__,__webpack_require__)=>{"use strict";__webpack_require__.d(__webpack_exports__,{A:()=>createLucideIcon});var react=__webpack_require__("../../node_modules/next/dist/compiled/react/index.js");const mergeClasses=(...classes)=>classes.filter((className,index,array)=>Boolean(className)&&array.indexOf(className)===index).join(" ");var defaultAttributes={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};const Icon=(0,react.forwardRef)(({color="currentColor",size=24,strokeWidth=2,absoluteStrokeWidth,className="",children,iconNode,...rest},ref)=>(0,react.createElement)("svg",{ref,...defaultAttributes,width:size,height:size,stroke:color,strokeWidth:absoluteStrokeWidth?24*Number(strokeWidth)/Number(size):strokeWidth,className:mergeClasses("lucide",className),...rest},[...iconNode.map(([tag,attrs])=>(0,react.createElement)(tag,attrs)),...Array.isArray(children)?children:[children]])),createLucideIcon=(iconName,iconNode)=>{const Component=(0,react.forwardRef)(({className,...props},ref)=>{return(0,react.createElement)(Icon,{ref,iconNode,className:mergeClasses(`lucide-${string=iconName,string.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase()}`,className),...props});var string});return Component.displayName=`${iconName}`,Component}},"./node_modules/@storybook/test/dist sync recursive":module=>{function webpackEmptyContext(req){var e=new Error("Cannot find module '"+req+"'");throw e.code="MODULE_NOT_FOUND",e}webpackEmptyContext.keys=()=>[],webpackEmptyContext.resolve=webpackEmptyContext,webpackEmptyContext.id="./node_modules/@storybook/test/dist sync recursive",module.exports=webpackEmptyContext},"./src/stories/foundation/Button.stories.tsx":(__unused_webpack_module,__webpack_exports__,__webpack_require__)=>{"use strict";__webpack_require__.r(__webpack_exports__),__webpack_require__.d(__webpack_exports__,{Default:()=>Default,Destructive:()=>Destructive,Disabled:()=>Disabled,Ghost:()=>Ghost,Icon:()=>Icon,Large:()=>Large,Link:()=>Link,Outline:()=>Outline,Secondary:()=>Secondary,Small:()=>Small,__namedExportsOrder:()=>__namedExportsOrder,default:()=>Button_stories});var jsx_runtime=__webpack_require__("../../node_modules/next/dist/compiled/react/jsx-runtime.js"),dist=__webpack_require__("./node_modules/@storybook/test/dist/index.mjs"),index_es=__webpack_require__("../../packages/ui/dist/index.es.js");const ChevronLeft=(0,__webpack_require__("../../node_modules/lucide-react/dist/esm/createLucideIcon.js").A)("ChevronLeft",[["path",{d:"m15 18-6-6 6-6",key:"1wnfg3"}]]);var _Default_parameters,_Default_parameters_docs,_Default_parameters1,_Default_parameters_docs1,_Default_parameters2,_Destructive_parameters,_Destructive_parameters_docs,_Destructive_parameters1,_Destructive_parameters_docs1,_Destructive_parameters2,_Outline_parameters,_Outline_parameters_docs,_Outline_parameters1,_Outline_parameters_docs1,_Outline_parameters2,_Secondary_parameters,_Secondary_parameters_docs,_Secondary_parameters1,_Secondary_parameters_docs1,_Secondary_parameters2,_Ghost_parameters,_Ghost_parameters_docs,_Ghost_parameters1,_Ghost_parameters_docs1,_Ghost_parameters2,_Link_parameters,_Link_parameters_docs,_Link_parameters1,_Link_parameters_docs1,_Link_parameters2,_Small_parameters,_Small_parameters_docs,_Small_parameters1,_Small_parameters_docs1,_Small_parameters2,_Large_parameters,_Large_parameters_docs,_Large_parameters1,_Large_parameters_docs1,_Large_parameters2,_Icon_parameters,_Icon_parameters_docs,_Icon_parameters1,_Icon_parameters_docs1,_Icon_parameters2,_Disabled_parameters,_Disabled_parameters_docs,_Disabled_parameters1,_Disabled_parameters_docs1,_Disabled_parameters2;const Button_stories={title:"Foundation/Button",component:index_es.$n,parameters:{layout:"centered",docs:{description:{component:"\nButton is a fundamental UI component that provides consistent styling and behavior for user interactions. It supports multiple variants, sizes, and states to cover all common use cases in modern web applications.\n\n### Key Capabilities\n\n- **Comprehensive Variant System**: Six distinct visual styles (default, destructive, outline, secondary, ghost, link)\n- **Flexible Sizing**: Four size options including specialized icon button sizing\n- **Accessibility First**: Built-in ARIA attributes, keyboard navigation, and screen reader support\n- **Theme Compatibility**: Seamless integration with dark and light themes\n- **Icon Integration**: Proper spacing and alignment for icons with text or standalone\n- **State Management**: Loading, disabled, and active states with appropriate visual feedback\n\n### Styling System\n\nThe component uses a class-variance-authority (CVA) based styling system that provides:\n- Consistent spacing and typography across all variants\n- Proper color contrast for accessibility compliance\n- Smooth transitions and hover effects\n- Responsive design considerations\n\n### Best Practices\n\n- Use `default` variant for primary actions\n- Use `destructive` for dangerous or irreversible actions\n- Use `outline` or `secondary` for secondary actions\n- Use `ghost` for subtle actions in dense interfaces\n- Use `link` variant for navigation that should look like text links\n- Use `icon` size for toolbar buttons and compact interfaces\n                "}}},argTypes:{variant:{control:"select",options:["default","destructive","outline","secondary","ghost","link"],description:"Visual style variant of the button",table:{type:{summary:"string"},defaultValue:{summary:"default"}}},size:{control:"select",options:["default","sm","lg","icon"],description:"Size variant of the button",table:{type:{summary:"string"},defaultValue:{summary:"default"}}},disabled:{control:"boolean",description:"Whether the button is disabled",table:{type:{summary:"boolean"},defaultValue:{summary:"false"}}},children:{control:"text",description:"Button content (text, icons, or other elements)",table:{type:{summary:"ReactNode"}}},onClick:{action:"clicked",description:"Function called when button is clicked",table:{type:{summary:"() => void"}}},className:{control:"text",description:"Additional CSS classes for custom styling",table:{type:{summary:"string"}}}},args:{onClick:(0,dist.fn)()}},Default={args:{variant:"default",size:"default",children:"Default Button"},parameters:{docs:{description:{story:"The default button variant with solid background styling. Ideal for primary actions, form submissions, and main call-to-action buttons."}}}},Destructive={args:{variant:"destructive",size:"default",children:"Delete Item"},parameters:{docs:{description:{story:"Destructive button variant with red styling for dangerous actions like delete, remove, or other irreversible operations."}}}},Outline={args:{variant:"outline",size:"default",children:"Cancel"},parameters:{docs:{description:{story:"Outline button variant with border styling for secondary actions, cancel operations, or alternative choices."}}}},Secondary={args:{variant:"secondary",size:"default",children:"Secondary Action"},parameters:{docs:{description:{story:"Secondary button variant with muted background for less prominent actions and supporting functionality."}}}},Ghost={args:{variant:"ghost",size:"default",children:"Ghost Button"},parameters:{docs:{description:{story:"Ghost button variant with minimal styling for tertiary actions and subtle interactions in dense interfaces."}}}},Link={args:{variant:"link",size:"default",children:"Link Button"},parameters:{docs:{description:{story:"Link button variant that appears as text with underline styling for navigation or actions that should look like links."}}}},Small={args:{variant:"default",size:"sm",children:"Small Button"},parameters:{docs:{description:{story:"Small size button variant for compact interfaces, toolbars, and space-constrained layouts."}}}},Large={args:{variant:"default",size:"lg",children:"Large Button"},parameters:{docs:{description:{story:"Large size button variant for prominent actions, hero sections, and important call-to-action buttons."}}}},Icon={args:{variant:"default",size:"icon",children:(0,jsx_runtime.jsx)(ChevronLeft,{className:"h-4 w-4"})},parameters:{docs:{description:{story:"Icon-only button variant for toolbar actions and compact interfaces where space is limited."}}}},Disabled={args:{variant:"default",size:"default",children:"Disabled Button",disabled:!0},parameters:{docs:{description:{story:"Disabled button state with reduced opacity and no interaction. Use for unavailable actions or form validation states."}}}};Default.parameters={...Default.parameters,docs:{...null===(_Default_parameters=Default.parameters)||void 0===_Default_parameters?void 0:_Default_parameters.docs,source:{originalSource:"{\n  args: {\n    variant: 'default',\n    size: 'default',\n    children: 'Default Button'\n  },\n  parameters: {\n    docs: {\n      description: {\n        story: 'The default button variant with solid background styling. Ideal for primary actions, form submissions, and main call-to-action buttons.'\n      }\n    }\n  }\n}",...null===(_Default_parameters1=Default.parameters)||void 0===_Default_parameters1||null===(_Default_parameters_docs=_Default_parameters1.docs)||void 0===_Default_parameters_docs?void 0:_Default_parameters_docs.source},description:{story:"Default button variant with solid background for primary actions.\r\nUse for main call-to-action buttons and primary user interactions.",...null===(_Default_parameters2=Default.parameters)||void 0===_Default_parameters2||null===(_Default_parameters_docs1=_Default_parameters2.docs)||void 0===_Default_parameters_docs1?void 0:_Default_parameters_docs1.description}}},Destructive.parameters={...Destructive.parameters,docs:{...null===(_Destructive_parameters=Destructive.parameters)||void 0===_Destructive_parameters?void 0:_Destructive_parameters.docs,source:{originalSource:"{\n  args: {\n    variant: 'destructive',\n    size: 'default',\n    children: 'Delete Item'\n  },\n  parameters: {\n    docs: {\n      description: {\n        story: 'Destructive button variant with red styling for dangerous actions like delete, remove, or other irreversible operations.'\n      }\n    }\n  }\n}",...null===(_Destructive_parameters1=Destructive.parameters)||void 0===_Destructive_parameters1||null===(_Destructive_parameters_docs=_Destructive_parameters1.docs)||void 0===_Destructive_parameters_docs?void 0:_Destructive_parameters_docs.source},description:{story:"Destructive button for dangerous or irreversible actions.\r\nUse for delete operations, permanent changes, or warning actions.",...null===(_Destructive_parameters2=Destructive.parameters)||void 0===_Destructive_parameters2||null===(_Destructive_parameters_docs1=_Destructive_parameters2.docs)||void 0===_Destructive_parameters_docs1?void 0:_Destructive_parameters_docs1.description}}},Outline.parameters={...Outline.parameters,docs:{...null===(_Outline_parameters=Outline.parameters)||void 0===_Outline_parameters?void 0:_Outline_parameters.docs,source:{originalSource:"{\n  args: {\n    variant: 'outline',\n    size: 'default',\n    children: 'Cancel'\n  },\n  parameters: {\n    docs: {\n      description: {\n        story: 'Outline button variant with border styling for secondary actions, cancel operations, or alternative choices.'\n      }\n    }\n  }\n}",...null===(_Outline_parameters1=Outline.parameters)||void 0===_Outline_parameters1||null===(_Outline_parameters_docs=_Outline_parameters1.docs)||void 0===_Outline_parameters_docs?void 0:_Outline_parameters_docs.source},description:{story:"Outline button for secondary actions with border styling.\r\nUse for alternative actions, cancel buttons, or secondary CTAs.",...null===(_Outline_parameters2=Outline.parameters)||void 0===_Outline_parameters2||null===(_Outline_parameters_docs1=_Outline_parameters2.docs)||void 0===_Outline_parameters_docs1?void 0:_Outline_parameters_docs1.description}}},Secondary.parameters={...Secondary.parameters,docs:{...null===(_Secondary_parameters=Secondary.parameters)||void 0===_Secondary_parameters?void 0:_Secondary_parameters.docs,source:{originalSource:"{\n  args: {\n    variant: 'secondary',\n    size: 'default',\n    children: 'Secondary Action'\n  },\n  parameters: {\n    docs: {\n      description: {\n        story: 'Secondary button variant with muted background for less prominent actions and supporting functionality.'\n      }\n    }\n  }\n}",...null===(_Secondary_parameters1=Secondary.parameters)||void 0===_Secondary_parameters1||null===(_Secondary_parameters_docs=_Secondary_parameters1.docs)||void 0===_Secondary_parameters_docs?void 0:_Secondary_parameters_docs.source},description:{story:"Secondary button with muted background for subtle actions.\r\nUse for less prominent actions or supporting functionality.",...null===(_Secondary_parameters2=Secondary.parameters)||void 0===_Secondary_parameters2||null===(_Secondary_parameters_docs1=_Secondary_parameters2.docs)||void 0===_Secondary_parameters_docs1?void 0:_Secondary_parameters_docs1.description}}},Ghost.parameters={...Ghost.parameters,docs:{...null===(_Ghost_parameters=Ghost.parameters)||void 0===_Ghost_parameters?void 0:_Ghost_parameters.docs,source:{originalSource:"{\n  args: {\n    variant: 'ghost',\n    size: 'default',\n    children: 'Ghost Button'\n  },\n  parameters: {\n    docs: {\n      description: {\n        story: 'Ghost button variant with minimal styling for tertiary actions and subtle interactions in dense interfaces.'\n      }\n    }\n  }\n}",...null===(_Ghost_parameters1=Ghost.parameters)||void 0===_Ghost_parameters1||null===(_Ghost_parameters_docs=_Ghost_parameters1.docs)||void 0===_Ghost_parameters_docs?void 0:_Ghost_parameters_docs.source},description:{story:"Ghost button with minimal styling for tertiary actions.\r\nUse in dense interfaces or for subtle interactive elements.",...null===(_Ghost_parameters2=Ghost.parameters)||void 0===_Ghost_parameters2||null===(_Ghost_parameters_docs1=_Ghost_parameters2.docs)||void 0===_Ghost_parameters_docs1?void 0:_Ghost_parameters_docs1.description}}},Link.parameters={...Link.parameters,docs:{...null===(_Link_parameters=Link.parameters)||void 0===_Link_parameters?void 0:_Link_parameters.docs,source:{originalSource:"{\n  args: {\n    variant: 'link',\n    size: 'default',\n    children: 'Link Button'\n  },\n  parameters: {\n    docs: {\n      description: {\n        story: 'Link button variant that appears as text with underline styling for navigation or actions that should look like links.'\n      }\n    }\n  }\n}",...null===(_Link_parameters1=Link.parameters)||void 0===_Link_parameters1||null===(_Link_parameters_docs=_Link_parameters1.docs)||void 0===_Link_parameters_docs?void 0:_Link_parameters_docs.source},description:{story:"Link-styled button that appears as text with underline.\r\nUse for navigation or actions that should look like links.",...null===(_Link_parameters2=Link.parameters)||void 0===_Link_parameters2||null===(_Link_parameters_docs1=_Link_parameters2.docs)||void 0===_Link_parameters_docs1?void 0:_Link_parameters_docs1.description}}},Small.parameters={...Small.parameters,docs:{...null===(_Small_parameters=Small.parameters)||void 0===_Small_parameters?void 0:_Small_parameters.docs,source:{originalSource:"{\n  args: {\n    variant: 'default',\n    size: 'sm',\n    children: 'Small Button'\n  },\n  parameters: {\n    docs: {\n      description: {\n        story: 'Small size button variant for compact interfaces, toolbars, and space-constrained layouts.'\n      }\n    }\n  }\n}",...null===(_Small_parameters1=Small.parameters)||void 0===_Small_parameters1||null===(_Small_parameters_docs=_Small_parameters1.docs)||void 0===_Small_parameters_docs?void 0:_Small_parameters_docs.source},description:{story:"Small size button for compact interfaces and secondary actions.\r\nUse in toolbars, forms, or space-constrained layouts.",...null===(_Small_parameters2=Small.parameters)||void 0===_Small_parameters2||null===(_Small_parameters_docs1=_Small_parameters2.docs)||void 0===_Small_parameters_docs1?void 0:_Small_parameters_docs1.description}}},Large.parameters={...Large.parameters,docs:{...null===(_Large_parameters=Large.parameters)||void 0===_Large_parameters?void 0:_Large_parameters.docs,source:{originalSource:"{\n  args: {\n    variant: 'default',\n    size: 'lg',\n    children: 'Large Button'\n  },\n  parameters: {\n    docs: {\n      description: {\n        story: 'Large size button variant for prominent actions, hero sections, and important call-to-action buttons.'\n      }\n    }\n  }\n}",...null===(_Large_parameters1=Large.parameters)||void 0===_Large_parameters1||null===(_Large_parameters_docs=_Large_parameters1.docs)||void 0===_Large_parameters_docs?void 0:_Large_parameters_docs.source},description:{story:"Large size button for prominent actions and hero sections.\r\nUse for main CTAs, landing pages, or important actions.",...null===(_Large_parameters2=Large.parameters)||void 0===_Large_parameters2||null===(_Large_parameters_docs1=_Large_parameters2.docs)||void 0===_Large_parameters_docs1?void 0:_Large_parameters_docs1.description}}},Icon.parameters={...Icon.parameters,docs:{...null===(_Icon_parameters=Icon.parameters)||void 0===_Icon_parameters?void 0:_Icon_parameters.docs,source:{originalSource:"{\n  args: {\n    variant: 'default',\n    size: 'icon',\n    children: <ChevronLeft className=\"h-4 w-4\" />\n  },\n  parameters: {\n    docs: {\n      description: {\n        story: 'Icon-only button variant for toolbar actions and compact interfaces where space is limited.'\n      }\n    }\n  }\n}",...null===(_Icon_parameters1=Icon.parameters)||void 0===_Icon_parameters1||null===(_Icon_parameters_docs=_Icon_parameters1.docs)||void 0===_Icon_parameters_docs?void 0:_Icon_parameters_docs.source},description:{story:"Icon-only button for toolbar actions and compact interfaces.\r\nUse for actions that can be represented clearly with just an icon.",...null===(_Icon_parameters2=Icon.parameters)||void 0===_Icon_parameters2||null===(_Icon_parameters_docs1=_Icon_parameters2.docs)||void 0===_Icon_parameters_docs1?void 0:_Icon_parameters_docs1.description}}},Disabled.parameters={...Disabled.parameters,docs:{...null===(_Disabled_parameters=Disabled.parameters)||void 0===_Disabled_parameters?void 0:_Disabled_parameters.docs,source:{originalSource:"{\n  args: {\n    variant: 'default',\n    size: 'default',\n    children: 'Disabled Button',\n    disabled: true\n  },\n  parameters: {\n    docs: {\n      description: {\n        story: 'Disabled button state with reduced opacity and no interaction. Use for unavailable actions or form validation states.'\n      }\n    }\n  }\n}",...null===(_Disabled_parameters1=Disabled.parameters)||void 0===_Disabled_parameters1||null===(_Disabled_parameters_docs=_Disabled_parameters1.docs)||void 0===_Disabled_parameters_docs?void 0:_Disabled_parameters_docs.source},description:{story:"Disabled button state showing non-interactive appearance.\r\nUse to indicate unavailable actions or form validation states.",...null===(_Disabled_parameters2=Disabled.parameters)||void 0===_Disabled_parameters2||null===(_Disabled_parameters_docs1=_Disabled_parameters2.docs)||void 0===_Disabled_parameters_docs1?void 0:_Disabled_parameters_docs1.description}}};const __namedExportsOrder=["Default","Destructive","Outline","Secondary","Ghost","Link","Small","Large","Icon","Disabled"]}}]);