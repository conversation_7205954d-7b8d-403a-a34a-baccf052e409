"use strict";(self.webpackChunk_cloc_storybook=self.webpackChunk_cloc_storybook||[]).push([[2663],{"./src/stories/foundation/Calendar.stories.tsx":(__unused_webpack_module,__webpack_exports__,__webpack_require__)=>{var _Default_parameters,_Default_parameters_docs,_Default_parameters1,_Default_parameters_docs1,_Default_parameters2,_WithoutOutsideDays_parameters,_WithoutOutsideDays_parameters_docs,_WithoutOutsideDays_parameters1,_WithoutOutsideDays_parameters_docs1,_WithoutOutsideDays_parameters2,_WithSelectedDate_parameters,_WithSelectedDate_parameters_docs,_WithSelectedDate_parameters1,_WithSelectedDate_parameters_docs1,_WithSelectedDate_parameters2,_Disabled_parameters,_Disabled_parameters_docs,_Disabled_parameters1,_Disabled_parameters_docs1,_Disabled_parameters2;__webpack_require__.r(__webpack_exports__),__webpack_require__.d(__webpack_exports__,{Default:()=>Default,Disabled:()=>Disabled,WithSelectedDate:()=>WithSelectedDate,WithoutOutsideDays:()=>WithoutOutsideDays,__namedExportsOrder:()=>__namedExportsOrder,default:()=>__WEBPACK_DEFAULT_EXPORT__});const __WEBPACK_DEFAULT_EXPORT__={title:"Foundation/Calendar",component:__webpack_require__("../../packages/ui/dist/index.es.js").Vv,parameters:{layout:"centered",docs:{description:{component:"\nCalendar is a comprehensive date selection component that provides intuitive navigation and flexible display options. It's designed for various date-related interactions from simple date picking to complex scheduling interfaces.\n\n### Key Capabilities\n\n- **Flexible Date Selection**: Support for single dates, multiple dates, and date ranges\n- **Navigation Controls**: Intuitive month/year navigation with keyboard support\n- **Display Customization**: Configurable outside days, week numbers, and multiple month views\n- **Accessibility First**: Full keyboard navigation, ARIA attributes, and screen reader support\n- **Theme Compatibility**: Seamless integration with dark and light themes\n- **Localization Ready**: Support for different locales, languages, and date formats\n\n### Display Modes\n\nThe calendar supports various display configurations:\n- **Outside Days**: Show/hide days from adjacent months for better context\n- **Week Numbers**: Optional display of ISO week numbers\n- **Multiple Months**: Display several months simultaneously for range selection\n- **Custom Styling**: Flexible theming and custom CSS class support\n\n### Interaction Patterns\n\n- **Mouse Navigation**: Click to select dates, navigate months\n- **Keyboard Navigation**: Arrow keys for date navigation, Enter/Space for selection\n- **Touch Support**: Mobile-friendly touch interactions\n- **Range Selection**: Click and drag or click start/end dates for ranges\n\n### Integration\n\nWorks seamlessly with:\n- Form libraries for date input validation\n- Date picker components as the core calendar interface\n- Event systems for appointment and scheduling applications\n- Filter systems for date-based data filtering\n\n### Best Practices\n\n- Consider showing outside days for better month context\n- Implement proper date validation and constraints\n- Provide clear visual feedback for selected dates\n- Test keyboard navigation thoroughly\n- Ensure proper contrast for accessibility\n- Use appropriate date formats for target locale\n                "}}},argTypes:{showOutsideDays:{control:"boolean",description:"Whether to show days from adjacent months",table:{type:{summary:"boolean"},defaultValue:{summary:"true"}}},selected:{control:"date",description:"Currently selected date",table:{type:{summary:"Date"}}},onSelect:{action:"date-selected",description:"Function called when a date is selected",table:{type:{summary:"(date: Date) => void"}}},disabled:{control:"boolean",description:"Whether the calendar is disabled",table:{type:{summary:"boolean"},defaultValue:{summary:"false"}}},className:{control:"text",description:"Additional CSS classes for custom styling",table:{type:{summary:"string"}}}}},Default={args:{showOutsideDays:!0},parameters:{docs:{description:{story:"The default calendar component with outside days visible. Shows days from adjacent months for better context and navigation clarity."}}}},WithoutOutsideDays={args:{showOutsideDays:!1},parameters:{docs:{description:{story:"Calendar with outside days hidden for a clean, focused view. Shows only the current month days without adjacent month distractions."}}}},WithSelectedDate={args:{showOutsideDays:!0,selected:new Date},parameters:{docs:{description:{story:"Calendar with a pre-selected date (today). Demonstrates how the calendar appears with an initial selection for form integration."}}}},Disabled={args:{showOutsideDays:!0,disabled:!0},parameters:{docs:{description:{story:"Disabled calendar state with reduced opacity and no interaction. Use when date selection should be temporarily unavailable."}}}};Default.parameters={...Default.parameters,docs:{...null===(_Default_parameters=Default.parameters)||void 0===_Default_parameters?void 0:_Default_parameters.docs,source:{originalSource:"{\n  args: {\n    showOutsideDays: true\n  },\n  parameters: {\n    docs: {\n      description: {\n        story: 'The default calendar component with outside days visible. Shows days from adjacent months for better context and navigation clarity.'\n      }\n    }\n  }\n}",...null===(_Default_parameters1=Default.parameters)||void 0===_Default_parameters1||null===(_Default_parameters_docs=_Default_parameters1.docs)||void 0===_Default_parameters_docs?void 0:_Default_parameters_docs.source},description:{story:"Default calendar with outside days shown for better month context.\r\nProvides full month view with adjacent month days for navigation clarity.",...null===(_Default_parameters2=Default.parameters)||void 0===_Default_parameters2||null===(_Default_parameters_docs1=_Default_parameters2.docs)||void 0===_Default_parameters_docs1?void 0:_Default_parameters_docs1.description}}},WithoutOutsideDays.parameters={...WithoutOutsideDays.parameters,docs:{...null===(_WithoutOutsideDays_parameters=WithoutOutsideDays.parameters)||void 0===_WithoutOutsideDays_parameters?void 0:_WithoutOutsideDays_parameters.docs,source:{originalSource:"{\n  args: {\n    showOutsideDays: false\n  },\n  parameters: {\n    docs: {\n      description: {\n        story: 'Calendar with outside days hidden for a clean, focused view. Shows only the current month days without adjacent month distractions.'\n      }\n    }\n  }\n}",...null===(_WithoutOutsideDays_parameters1=WithoutOutsideDays.parameters)||void 0===_WithoutOutsideDays_parameters1||null===(_WithoutOutsideDays_parameters_docs=_WithoutOutsideDays_parameters1.docs)||void 0===_WithoutOutsideDays_parameters_docs?void 0:_WithoutOutsideDays_parameters_docs.source},description:{story:"Clean calendar view with only current month days displayed.\r\nProvides focused view without adjacent month distractions.",...null===(_WithoutOutsideDays_parameters2=WithoutOutsideDays.parameters)||void 0===_WithoutOutsideDays_parameters2||null===(_WithoutOutsideDays_parameters_docs1=_WithoutOutsideDays_parameters2.docs)||void 0===_WithoutOutsideDays_parameters_docs1?void 0:_WithoutOutsideDays_parameters_docs1.description}}},WithSelectedDate.parameters={...WithSelectedDate.parameters,docs:{...null===(_WithSelectedDate_parameters=WithSelectedDate.parameters)||void 0===_WithSelectedDate_parameters?void 0:_WithSelectedDate_parameters.docs,source:{originalSource:"{\n  args: {\n    showOutsideDays: true,\n    selected: new Date()\n  },\n  parameters: {\n    docs: {\n      description: {\n        story: 'Calendar with a pre-selected date (today). Demonstrates how the calendar appears with an initial selection for form integration.'\n      }\n    }\n  }\n}",...null===(_WithSelectedDate_parameters1=WithSelectedDate.parameters)||void 0===_WithSelectedDate_parameters1||null===(_WithSelectedDate_parameters_docs=_WithSelectedDate_parameters1.docs)||void 0===_WithSelectedDate_parameters_docs?void 0:_WithSelectedDate_parameters_docs.source},description:{story:"Calendar with pre-selected date for form integration.\r\nShows how the calendar appears with an initial date selection.",...null===(_WithSelectedDate_parameters2=WithSelectedDate.parameters)||void 0===_WithSelectedDate_parameters2||null===(_WithSelectedDate_parameters_docs1=_WithSelectedDate_parameters2.docs)||void 0===_WithSelectedDate_parameters_docs1?void 0:_WithSelectedDate_parameters_docs1.description}}},Disabled.parameters={...Disabled.parameters,docs:{...null===(_Disabled_parameters=Disabled.parameters)||void 0===_Disabled_parameters?void 0:_Disabled_parameters.docs,source:{originalSource:"{\n  args: {\n    showOutsideDays: true,\n    disabled: true\n  },\n  parameters: {\n    docs: {\n      description: {\n        story: 'Disabled calendar state with reduced opacity and no interaction. Use when date selection should be temporarily unavailable.'\n      }\n    }\n  }\n}",...null===(_Disabled_parameters1=Disabled.parameters)||void 0===_Disabled_parameters1||null===(_Disabled_parameters_docs=_Disabled_parameters1.docs)||void 0===_Disabled_parameters_docs?void 0:_Disabled_parameters_docs.source},description:{story:"Disabled calendar state for read-only or unavailable contexts.\r\nUse when date selection should be temporarily unavailable.",...null===(_Disabled_parameters2=Disabled.parameters)||void 0===_Disabled_parameters2||null===(_Disabled_parameters_docs1=_Disabled_parameters2.docs)||void 0===_Disabled_parameters_docs1?void 0:_Disabled_parameters_docs1.description}}};const __namedExportsOrder=["Default","WithoutOutsideDays","WithSelectedDate","Disabled"]}}]);