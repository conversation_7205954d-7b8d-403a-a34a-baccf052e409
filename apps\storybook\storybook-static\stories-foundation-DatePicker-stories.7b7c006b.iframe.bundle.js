"use strict";(self.webpackChunk_cloc_storybook=self.webpackChunk_cloc_storybook||[]).push([[2411],{"./src/stories/foundation/DatePicker.stories.tsx":(__unused_webpack_module,__webpack_exports__,__webpack_require__)=>{var _Default_parameters,_Default_parameters_docs,_Default_parameters1,_Default_parameters_docs1,_Default_parameters2,_WithoutIcon_parameters,_WithoutIcon_parameters_docs,_WithoutIcon_parameters1,_WithoutIcon_parameters_docs1,_WithoutIcon_parameters2,_CustomPlaceholder_parameters,_CustomPlaceholder_parameters_docs,_CustomPlaceholder_parameters1,_CustomPlaceholder_parameters_docs1,_CustomPlaceholder_parameters2,_WithSelectedDate_parameters,_WithSelectedDate_parameters_docs,_WithSelectedDate_parameters1,_WithSelectedDate_parameters_docs1,_WithSelectedDate_parameters2,_Disabled_parameters,_Disabled_parameters_docs,_Disabled_parameters1,_Disabled_parameters_docs1,_Disabled_parameters2;__webpack_require__.r(__webpack_exports__),__webpack_require__.d(__webpack_exports__,{CustomPlaceholder:()=>CustomPlaceholder,Default:()=>Default,Disabled:()=>Disabled,WithSelectedDate:()=>WithSelectedDate,WithoutIcon:()=>WithoutIcon,__namedExportsOrder:()=>__namedExportsOrder,default:()=>__WEBPACK_DEFAULT_EXPORT__});const __WEBPACK_DEFAULT_EXPORT__={title:"Foundation/Date Picker",component:__webpack_require__("../../packages/ui/dist/index.es.js").lr,parameters:{layout:"centered",docs:{description:{component:"\nDatePicker combines a text input field with a calendar popup to provide flexible date selection. Users can either type dates directly or use the visual calendar interface for intuitive date picking.\n\n### Key Capabilities\n\n- **Dual Input Methods**: Support for both direct text input and visual calendar selection\n- **Smart Formatting**: Automatic date formatting and parsing with validation\n- **Calendar Integration**: Built-in calendar popup with month/year navigation\n- **Icon Customization**: Optional calendar icon for enhanced visual clarity\n- **Keyboard Friendly**: Full keyboard support for accessibility and power users\n- **Validation Ready**: Built-in date validation with error state handling\n\n### Interaction Patterns\n\n- **Click to Open**: Click input field or icon to open calendar popup\n- **Direct Typing**: Type dates directly with automatic formatting\n- **Calendar Selection**: Click dates in calendar for visual selection\n- **Keyboard Navigation**: Use arrow keys and Enter for date selection\n- **Outside Click**: Click outside to close calendar popup\n\n### Input Validation\n\nThe component provides:\n- Automatic date format validation\n- Invalid date detection and error states\n- Range validation (when min/max dates are set)\n- Required field validation support\n\n### Accessibility Features\n\n- Proper ARIA labels and descriptions\n- Keyboard navigation support\n- Screen reader compatibility\n- Focus management for popup interactions\n- High contrast support for visual accessibility\n\n### Best Practices\n\n- Provide clear placeholder text indicating expected format\n- Use calendar icon when space allows for better UX\n- Implement proper validation and error messaging\n- Consider date format based on user locale\n- Test keyboard navigation thoroughly\n- Ensure proper focus management in popup interactions\n                "}}},argTypes:{placeholder:{control:"text",description:"Placeholder text displayed when no date is selected",table:{type:{summary:"string"},defaultValue:{summary:"Pick a date"}}},icon:{control:"boolean",description:"Whether to show the calendar icon",table:{type:{summary:"boolean"},defaultValue:{summary:"true"}}},disabled:{control:"boolean",description:"Whether the date picker is disabled",table:{type:{summary:"boolean"},defaultValue:{summary:"false"}}},value:{control:"date",description:"Currently selected date",table:{type:{summary:"Date"}}},onSelect:{action:"date-selected",description:"Function called when a date is selected",table:{type:{summary:"(date: Date) => void"}}},className:{control:"text",description:"Additional CSS classes for custom styling",table:{type:{summary:"string"}}}}},Default={args:{placeholder:"Pick a date"},parameters:{docs:{description:{story:"The default date picker with calendar icon and standard placeholder. Use for general date selection in forms and applications."}}}},WithoutIcon={args:{placeholder:"Choose a date",icon:!1},parameters:{docs:{description:{story:"Date picker without calendar icon for a minimal, clean appearance. Use when space is limited or for subtle interface design."}}}},CustomPlaceholder={args:{placeholder:"Select your birthday"},parameters:{docs:{description:{story:"Date picker with custom placeholder text for specific contexts. Provides clear guidance about the expected date type."}}}},WithSelectedDate={args:{placeholder:"Pick a date",value:new Date},parameters:{docs:{description:{story:"Date picker with a pre-selected date (today). Demonstrates how the component appears with an existing value for form editing."}}}},Disabled={args:{placeholder:"Date unavailable",disabled:!0},parameters:{docs:{description:{story:"Disabled date picker state with reduced opacity and no interaction. Use when date selection should be temporarily unavailable."}}}};Default.parameters={...Default.parameters,docs:{...null===(_Default_parameters=Default.parameters)||void 0===_Default_parameters?void 0:_Default_parameters.docs,source:{originalSource:"{\n  args: {\n    placeholder: 'Pick a date'\n  },\n  parameters: {\n    docs: {\n      description: {\n        story: 'The default date picker with calendar icon and standard placeholder. Use for general date selection in forms and applications.'\n      }\n    }\n  }\n}",...null===(_Default_parameters1=Default.parameters)||void 0===_Default_parameters1||null===(_Default_parameters_docs=_Default_parameters1.docs)||void 0===_Default_parameters_docs?void 0:_Default_parameters_docs.source},description:{story:"Default date picker with calendar icon and standard placeholder.\r\nThe most common configuration for general date selection needs.",...null===(_Default_parameters2=Default.parameters)||void 0===_Default_parameters2||null===(_Default_parameters_docs1=_Default_parameters2.docs)||void 0===_Default_parameters_docs1?void 0:_Default_parameters_docs1.description}}},WithoutIcon.parameters={...WithoutIcon.parameters,docs:{...null===(_WithoutIcon_parameters=WithoutIcon.parameters)||void 0===_WithoutIcon_parameters?void 0:_WithoutIcon_parameters.docs,source:{originalSource:"{\n  args: {\n    placeholder: 'Choose a date',\n    icon: false\n  },\n  parameters: {\n    docs: {\n      description: {\n        story: 'Date picker without calendar icon for a minimal, clean appearance. Use when space is limited or for subtle interface design.'\n      }\n    }\n  }\n}",...null===(_WithoutIcon_parameters1=WithoutIcon.parameters)||void 0===_WithoutIcon_parameters1||null===(_WithoutIcon_parameters_docs=_WithoutIcon_parameters1.docs)||void 0===_WithoutIcon_parameters_docs?void 0:_WithoutIcon_parameters_docs.source},description:{story:"Date picker without calendar icon for minimal, clean appearance.\r\nUse when space is limited or for a more subtle interface.",...null===(_WithoutIcon_parameters2=WithoutIcon.parameters)||void 0===_WithoutIcon_parameters2||null===(_WithoutIcon_parameters_docs1=_WithoutIcon_parameters2.docs)||void 0===_WithoutIcon_parameters_docs1?void 0:_WithoutIcon_parameters_docs1.description}}},CustomPlaceholder.parameters={...CustomPlaceholder.parameters,docs:{...null===(_CustomPlaceholder_parameters=CustomPlaceholder.parameters)||void 0===_CustomPlaceholder_parameters?void 0:_CustomPlaceholder_parameters.docs,source:{originalSource:"{\n  args: {\n    placeholder: 'Select your birthday'\n  },\n  parameters: {\n    docs: {\n      description: {\n        story: 'Date picker with custom placeholder text for specific contexts. Provides clear guidance about the expected date type.'\n      }\n    }\n  }\n}",...null===(_CustomPlaceholder_parameters1=CustomPlaceholder.parameters)||void 0===_CustomPlaceholder_parameters1||null===(_CustomPlaceholder_parameters_docs=_CustomPlaceholder_parameters1.docs)||void 0===_CustomPlaceholder_parameters_docs?void 0:_CustomPlaceholder_parameters_docs.source},description:{story:"Date picker with custom placeholder text for specific contexts.\r\nUse to provide context-specific guidance to users.",...null===(_CustomPlaceholder_parameters2=CustomPlaceholder.parameters)||void 0===_CustomPlaceholder_parameters2||null===(_CustomPlaceholder_parameters_docs1=_CustomPlaceholder_parameters2.docs)||void 0===_CustomPlaceholder_parameters_docs1?void 0:_CustomPlaceholder_parameters_docs1.description}}},WithSelectedDate.parameters={...WithSelectedDate.parameters,docs:{...null===(_WithSelectedDate_parameters=WithSelectedDate.parameters)||void 0===_WithSelectedDate_parameters?void 0:_WithSelectedDate_parameters.docs,source:{originalSource:"{\n  args: {\n    placeholder: 'Pick a date',\n    value: new Date()\n  },\n  parameters: {\n    docs: {\n      description: {\n        story: 'Date picker with a pre-selected date (today). Demonstrates how the component appears with an existing value for form editing.'\n      }\n    }\n  }\n}",...null===(_WithSelectedDate_parameters1=WithSelectedDate.parameters)||void 0===_WithSelectedDate_parameters1||null===(_WithSelectedDate_parameters_docs=_WithSelectedDate_parameters1.docs)||void 0===_WithSelectedDate_parameters_docs?void 0:_WithSelectedDate_parameters_docs.source},description:{story:"Date picker with pre-selected date for form editing scenarios.\r\nShows how the component appears with an existing date value.",...null===(_WithSelectedDate_parameters2=WithSelectedDate.parameters)||void 0===_WithSelectedDate_parameters2||null===(_WithSelectedDate_parameters_docs1=_WithSelectedDate_parameters2.docs)||void 0===_WithSelectedDate_parameters_docs1?void 0:_WithSelectedDate_parameters_docs1.description}}},Disabled.parameters={...Disabled.parameters,docs:{...null===(_Disabled_parameters=Disabled.parameters)||void 0===_Disabled_parameters?void 0:_Disabled_parameters.docs,source:{originalSource:"{\n  args: {\n    placeholder: 'Date unavailable',\n    disabled: true\n  },\n  parameters: {\n    docs: {\n      description: {\n        story: 'Disabled date picker state with reduced opacity and no interaction. Use when date selection should be temporarily unavailable.'\n      }\n    }\n  }\n}",...null===(_Disabled_parameters1=Disabled.parameters)||void 0===_Disabled_parameters1||null===(_Disabled_parameters_docs=_Disabled_parameters1.docs)||void 0===_Disabled_parameters_docs?void 0:_Disabled_parameters_docs.source},description:{story:"Disabled date picker state for read-only or unavailable contexts.\r\nUse when date selection should be temporarily unavailable.",...null===(_Disabled_parameters2=Disabled.parameters)||void 0===_Disabled_parameters2||null===(_Disabled_parameters_docs1=_Disabled_parameters2.docs)||void 0===_Disabled_parameters_docs1?void 0:_Disabled_parameters_docs1.description}}};const __namedExportsOrder=["Default","WithoutIcon","CustomPlaceholder","WithSelectedDate","Disabled"]}}]);