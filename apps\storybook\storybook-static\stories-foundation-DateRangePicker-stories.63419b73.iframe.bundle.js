"use strict";(self.webpackChunk_cloc_storybook=self.webpackChunk_cloc_storybook||[]).push([[362],{"./src/stories/foundation/DateRangePicker.stories.tsx":(__unused_webpack_module,__webpack_exports__,__webpack_require__)=>{var _Default_parameters,_Default_parameters_docs,_Default_parameters1,_Default_parameters_docs1,_Default_parameters2,_CustomPlaceholder_parameters,_CustomPlaceholder_parameters_docs,_CustomPlaceholder_parameters1,_CustomPlaceholder_parameters_docs1,_CustomPlaceholder_parameters2,_WithSelectedRange_parameters,_WithSelectedRange_parameters_docs,_WithSelectedRange_parameters1,_WithSelectedRange_parameters_docs1,_WithSelectedRange_parameters2,_Disabled_parameters,_Disabled_parameters_docs,_Disabled_parameters1,_Disabled_parameters_docs1,_Disabled_parameters2;__webpack_require__.r(__webpack_exports__),__webpack_require__.d(__webpack_exports__,{CustomPlaceholder:()=>CustomPlaceholder,Default:()=>Default,Disabled:()=>Disabled,WithSelectedRange:()=>WithSelectedRange,__namedExportsOrder:()=>__namedExportsOrder,default:()=>__WEBPACK_DEFAULT_EXPORT__});const __WEBPACK_DEFAULT_EXPORT__={title:"Foundation/Date Range Picker",component:__webpack_require__("../../packages/ui/dist/index.es.js").Ur,parameters:{layout:"centered",docs:{description:{component:"\nDateRangePicker provides a comprehensive solution for selecting date ranges with both visual calendar interface and direct text input capabilities. It's designed for scenarios where users need to specify a period or duration.\n\n### Key Capabilities\n\n- **Dual Date Selection**: Intuitive start and end date selection with visual feedback\n- **Multiple Input Methods**: Support for calendar selection, direct typing, and preset ranges\n- **Range Validation**: Automatic validation ensuring end date is after start date\n- **Visual Range Display**: Clear highlighting of selected date range in calendar\n- **Flexible Formatting**: Customizable date format display and parsing\n- **Preset Options**: Quick selection for common ranges (today, last week, last month, etc.)\n\n### Interaction Patterns\n\n- **Calendar Selection**: Click start date, then end date to define range\n- **Drag Selection**: Click and drag across calendar dates for quick range selection\n- **Input Fields**: Type dates directly with automatic formatting and validation\n- **Preset Buttons**: One-click selection for common date ranges\n- **Keyboard Navigation**: Arrow keys and shortcuts for accessibility\n\n### Range Validation\n\nThe component provides:\n- Automatic start/end date validation\n- Minimum and maximum date constraints\n- Invalid range detection and error states\n- Required field validation support\n- Custom validation rule integration\n\n### Accessibility Features\n\n- Proper ARIA labels and descriptions for range selection\n- Keyboard navigation support for all interactive elements\n- Screen reader announcements for range changes\n- Focus management for complex popup interactions\n- High contrast support for visual accessibility\n\n### Best Practices\n\n- Provide clear labels for start and end date inputs\n- Use preset ranges for common selections to improve UX\n- Implement proper validation with clear error messaging\n- Consider date format based on user locale and preferences\n- Test keyboard navigation and screen reader compatibility\n- Ensure proper focus management in popup interactions\n                "}}},argTypes:{placeholder:{control:"text",description:"Placeholder text for the date range input",table:{type:{summary:"string"},defaultValue:{summary:"Select date range"}}},disabled:{control:"boolean",description:"Whether the date range picker is disabled",table:{type:{summary:"boolean"},defaultValue:{summary:"false"}}},value:{control:"object",description:"Currently selected date range with from and to dates",table:{type:{summary:"{ from: Date, to: Date }"}}},onSelect:{action:"range-selected",description:"Function called when a date range is selected",table:{type:{summary:"(range: { from: Date, to: Date }) => void"}}},className:{control:"text",description:"Additional CSS classes for custom styling",table:{type:{summary:"string"}}}}},Default={args:{placeholder:"Select date range"},parameters:{docs:{description:{story:"The default date range picker component for general range selection. Use for analytics, reporting, and filtering scenarios."}}}},CustomPlaceholder={args:{placeholder:"Choose booking dates"},parameters:{docs:{description:{story:"Date range picker with custom placeholder text for specific contexts like booking or reservation systems."}}}},WithSelectedRange={args:{placeholder:"Select date range",value:{from:new Date(Date.now()-6048e5),to:new Date}},parameters:{docs:{description:{story:"Date range picker with a pre-selected range (last 7 days). Demonstrates how the component appears with existing values for editing scenarios."}}}},Disabled={args:{placeholder:"Date range unavailable",disabled:!0},parameters:{docs:{description:{story:"Disabled date range picker state with reduced opacity and no interaction. Use when range selection should be temporarily unavailable."}}}};Default.parameters={...Default.parameters,docs:{...null===(_Default_parameters=Default.parameters)||void 0===_Default_parameters?void 0:_Default_parameters.docs,source:{originalSource:"{\n  args: {\n    placeholder: 'Select date range'\n  },\n  parameters: {\n    docs: {\n      description: {\n        story: 'The default date range picker component for general range selection. Use for analytics, reporting, and filtering scenarios.'\n      }\n    }\n  }\n}",...null===(_Default_parameters1=Default.parameters)||void 0===_Default_parameters1||null===(_Default_parameters_docs=_Default_parameters1.docs)||void 0===_Default_parameters_docs?void 0:_Default_parameters_docs.source},description:{story:"Default date range picker for general range selection needs.\r\nStandard configuration for most date range selection scenarios.",...null===(_Default_parameters2=Default.parameters)||void 0===_Default_parameters2||null===(_Default_parameters_docs1=_Default_parameters2.docs)||void 0===_Default_parameters_docs1?void 0:_Default_parameters_docs1.description}}},CustomPlaceholder.parameters={...CustomPlaceholder.parameters,docs:{...null===(_CustomPlaceholder_parameters=CustomPlaceholder.parameters)||void 0===_CustomPlaceholder_parameters?void 0:_CustomPlaceholder_parameters.docs,source:{originalSource:"{\n  args: {\n    placeholder: 'Choose booking dates'\n  },\n  parameters: {\n    docs: {\n      description: {\n        story: 'Date range picker with custom placeholder text for specific contexts like booking or reservation systems.'\n      }\n    }\n  }\n}",...null===(_CustomPlaceholder_parameters1=CustomPlaceholder.parameters)||void 0===_CustomPlaceholder_parameters1||null===(_CustomPlaceholder_parameters_docs=_CustomPlaceholder_parameters1.docs)||void 0===_CustomPlaceholder_parameters_docs?void 0:_CustomPlaceholder_parameters_docs.source},description:{story:"Date range picker with custom placeholder for specific contexts.\r\nProvides context-specific guidance for users.",...null===(_CustomPlaceholder_parameters2=CustomPlaceholder.parameters)||void 0===_CustomPlaceholder_parameters2||null===(_CustomPlaceholder_parameters_docs1=_CustomPlaceholder_parameters2.docs)||void 0===_CustomPlaceholder_parameters_docs1?void 0:_CustomPlaceholder_parameters_docs1.description}}},WithSelectedRange.parameters={...WithSelectedRange.parameters,docs:{...null===(_WithSelectedRange_parameters=WithSelectedRange.parameters)||void 0===_WithSelectedRange_parameters?void 0:_WithSelectedRange_parameters.docs,source:{originalSource:"{\n  args: {\n    placeholder: 'Select date range',\n    value: {\n      from: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000),\n      // 7 days ago\n      to: new Date() // today\n    }\n  },\n  parameters: {\n    docs: {\n      description: {\n        story: 'Date range picker with a pre-selected range (last 7 days). Demonstrates how the component appears with existing values for editing scenarios.'\n      }\n    }\n  }\n}",...null===(_WithSelectedRange_parameters1=WithSelectedRange.parameters)||void 0===_WithSelectedRange_parameters1||null===(_WithSelectedRange_parameters_docs=_WithSelectedRange_parameters1.docs)||void 0===_WithSelectedRange_parameters_docs?void 0:_WithSelectedRange_parameters_docs.source},description:{story:"Date range picker with pre-selected range for editing scenarios.\r\nShows how the component appears with existing range values.",...null===(_WithSelectedRange_parameters2=WithSelectedRange.parameters)||void 0===_WithSelectedRange_parameters2||null===(_WithSelectedRange_parameters_docs1=_WithSelectedRange_parameters2.docs)||void 0===_WithSelectedRange_parameters_docs1?void 0:_WithSelectedRange_parameters_docs1.description}}},Disabled.parameters={...Disabled.parameters,docs:{...null===(_Disabled_parameters=Disabled.parameters)||void 0===_Disabled_parameters?void 0:_Disabled_parameters.docs,source:{originalSource:"{\n  args: {\n    placeholder: 'Date range unavailable',\n    disabled: true\n  },\n  parameters: {\n    docs: {\n      description: {\n        story: 'Disabled date range picker state with reduced opacity and no interaction. Use when range selection should be temporarily unavailable.'\n      }\n    }\n  }\n}",...null===(_Disabled_parameters1=Disabled.parameters)||void 0===_Disabled_parameters1||null===(_Disabled_parameters_docs=_Disabled_parameters1.docs)||void 0===_Disabled_parameters_docs?void 0:_Disabled_parameters_docs.source},description:{story:"Disabled date range picker for read-only or unavailable contexts.\r\nUse when range selection should be temporarily unavailable.",...null===(_Disabled_parameters2=Disabled.parameters)||void 0===_Disabled_parameters2||null===(_Disabled_parameters_docs1=_Disabled_parameters2.docs)||void 0===_Disabled_parameters_docs1?void 0:_Disabled_parameters_docs1.description}}};const __namedExportsOrder=["Default","CustomPlaceholder","WithSelectedRange","Disabled"]}}]);