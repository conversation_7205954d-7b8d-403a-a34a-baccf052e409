"use strict";(self.webpackChunk_cloc_storybook=self.webpackChunk_cloc_storybook||[]).push([[5591],{"./src/stories/foundation/Input.stories.tsx":(__unused_webpack_module,__webpack_exports__,__webpack_require__)=>{var _Default_parameters,_Default_parameters_docs,_Default_parameters1,_Default_parameters_docs1,_Default_parameters2,_Email_parameters,_Email_parameters_docs,_Email_parameters1,_Email_parameters_docs1,_Email_parameters2,_Password_parameters,_Password_parameters_docs,_Password_parameters1,_Password_parameters_docs1,_Password_parameters2,_Number_parameters,_Number_parameters_docs,_Number_parameters1,_Number_parameters_docs1,_Number_parameters2,_Search_parameters,_Search_parameters_docs,_Search_parameters1,_Search_parameters_docs1,_Search_parameters2,_Disabled_parameters,_Disabled_parameters_docs,_Disabled_parameters1,_Disabled_parameters_docs1,_Disabled_parameters2;__webpack_require__.r(__webpack_exports__),__webpack_require__.d(__webpack_exports__,{Default:()=>Default,Disabled:()=>Disabled,Email:()=>Email,Number:()=>Number,Password:()=>Password,Search:()=>Search,__namedExportsOrder:()=>__namedExportsOrder,default:()=>__WEBPACK_DEFAULT_EXPORT__});const __WEBPACK_DEFAULT_EXPORT__={title:"Foundation/Input",component:__webpack_require__("../../packages/ui/dist/index.es.js").pd,parameters:{layout:"centered",docs:{description:{component:"\nInput is a fundamental form component that provides consistent styling and behavior for text input fields. It supports various input types, states, and accessibility features for comprehensive form building.\n\n### Key Capabilities\n\n- **Multiple Input Types**: Support for text, email, password, number, search, URL, and telephone inputs\n- **State Management**: Visual feedback for focus, disabled, error, and validation states\n- **Accessibility First**: Built-in ARIA attributes, proper labeling, and keyboard navigation\n- **Theme Compatibility**: Seamless integration with dark and light themes\n- **Form Integration**: Works with popular form libraries like React Hook Form and Formik\n- **Validation Ready**: Easy integration with validation libraries and custom validation logic\n\n### Styling System\n\nThe component uses consistent styling that provides:\n- Clean, modern appearance with proper spacing\n- Clear focus indicators for accessibility\n- Smooth transitions between states\n- Responsive design for different screen sizes\n- Proper color contrast for readability\n\n### Best Practices\n\n- Always provide meaningful placeholder text\n- Use appropriate input types for better mobile experience\n- Implement proper validation and error messaging\n- Ensure labels are properly associated with inputs\n- Consider using autocomplete attributes for better UX\n- Test with keyboard navigation and screen readers\n                "}}},argTypes:{type:{control:"select",options:["text","email","password","number","search","url","tel"],description:"HTML input type attribute",table:{type:{summary:"string"},defaultValue:{summary:"text"}}},placeholder:{control:"text",description:"Placeholder text displayed when input is empty",table:{type:{summary:"string"}}},disabled:{control:"boolean",description:"Whether the input is disabled",table:{type:{summary:"boolean"},defaultValue:{summary:"false"}}},value:{control:"text",description:"Current value of the input",table:{type:{summary:"string"}}},className:{control:"text",description:"Additional CSS classes for custom styling",table:{type:{summary:"string"}}}}},Default={args:{placeholder:"Enter text..."},parameters:{docs:{description:{story:"The default text input component for general text entry. Use for names, titles, descriptions, and other text-based form fields."}}}},Email={args:{type:"email",placeholder:"Enter your email..."},parameters:{docs:{description:{story:"Email input type with built-in validation and optimized mobile keyboard. Use for email collection, login forms, and contact information."}}}},Password={args:{type:"password",placeholder:"Enter password..."},parameters:{docs:{description:{story:"Password input type with hidden text display for secure data entry. Use for passwords, PINs, and sensitive information."}}}},Number={args:{type:"number",placeholder:"Enter number..."},parameters:{docs:{description:{story:"Number input type with numeric validation and spinner controls. Use for quantities, ages, prices, and other numeric data entry."}}}},Search={args:{type:"search",placeholder:"Search..."},parameters:{docs:{description:{story:"Search input type optimized for search functionality with appropriate styling and behavior. Use for search bars and filter inputs."}}}},Disabled={args:{placeholder:"Disabled input...",disabled:!0},parameters:{docs:{description:{story:"Disabled input state with reduced opacity and no interaction. Use for unavailable fields or read-only data display."}}}};Default.parameters={...Default.parameters,docs:{...null===(_Default_parameters=Default.parameters)||void 0===_Default_parameters?void 0:_Default_parameters.docs,source:{originalSource:"{\n  args: {\n    placeholder: 'Enter text...'\n  },\n  parameters: {\n    docs: {\n      description: {\n        story: 'The default text input component for general text entry. Use for names, titles, descriptions, and other text-based form fields.'\n      }\n    }\n  }\n}",...null===(_Default_parameters1=Default.parameters)||void 0===_Default_parameters1||null===(_Default_parameters_docs=_Default_parameters1.docs)||void 0===_Default_parameters_docs?void 0:_Default_parameters_docs.source},description:{story:"Default text input for general text entry and form fields.\r\nThe most common input variant for user text input.",...null===(_Default_parameters2=Default.parameters)||void 0===_Default_parameters2||null===(_Default_parameters_docs1=_Default_parameters2.docs)||void 0===_Default_parameters_docs1?void 0:_Default_parameters_docs1.description}}},Email.parameters={...Email.parameters,docs:{...null===(_Email_parameters=Email.parameters)||void 0===_Email_parameters?void 0:_Email_parameters.docs,source:{originalSource:"{\n  args: {\n    type: 'email',\n    placeholder: 'Enter your email...'\n  },\n  parameters: {\n    docs: {\n      description: {\n        story: 'Email input type with built-in validation and optimized mobile keyboard. Use for email collection, login forms, and contact information.'\n      }\n    }\n  }\n}",...null===(_Email_parameters1=Email.parameters)||void 0===_Email_parameters1||null===(_Email_parameters_docs=_Email_parameters1.docs)||void 0===_Email_parameters_docs?void 0:_Email_parameters_docs.source},description:{story:"Email input with built-in validation and appropriate mobile keyboard.\r\nUse for email address collection and user authentication.",...null===(_Email_parameters2=Email.parameters)||void 0===_Email_parameters2||null===(_Email_parameters_docs1=_Email_parameters2.docs)||void 0===_Email_parameters_docs1?void 0:_Email_parameters_docs1.description}}},Password.parameters={...Password.parameters,docs:{...null===(_Password_parameters=Password.parameters)||void 0===_Password_parameters?void 0:_Password_parameters.docs,source:{originalSource:"{\n  args: {\n    type: 'password',\n    placeholder: 'Enter password...'\n  },\n  parameters: {\n    docs: {\n      description: {\n        story: 'Password input type with hidden text display for secure data entry. Use for passwords, PINs, and sensitive information.'\n      }\n    }\n  }\n}",...null===(_Password_parameters1=Password.parameters)||void 0===_Password_parameters1||null===(_Password_parameters_docs=_Password_parameters1.docs)||void 0===_Password_parameters_docs?void 0:_Password_parameters_docs.source},description:{story:"Password input with hidden text for secure data entry.\r\nUse for passwords, PINs, and other sensitive information.",...null===(_Password_parameters2=Password.parameters)||void 0===_Password_parameters2||null===(_Password_parameters_docs1=_Password_parameters2.docs)||void 0===_Password_parameters_docs1?void 0:_Password_parameters_docs1.description}}},Number.parameters={...Number.parameters,docs:{...null===(_Number_parameters=Number.parameters)||void 0===_Number_parameters?void 0:_Number_parameters.docs,source:{originalSource:"{\n  args: {\n    type: 'number',\n    placeholder: 'Enter number...'\n  },\n  parameters: {\n    docs: {\n      description: {\n        story: 'Number input type with numeric validation and spinner controls. Use for quantities, ages, prices, and other numeric data entry.'\n      }\n    }\n  }\n}",...null===(_Number_parameters1=Number.parameters)||void 0===_Number_parameters1||null===(_Number_parameters_docs=_Number_parameters1.docs)||void 0===_Number_parameters_docs?void 0:_Number_parameters_docs.source},description:{story:"Number input with numeric validation and spinner controls.\r\nUse for quantities, ages, prices, and other numeric data.",...null===(_Number_parameters2=Number.parameters)||void 0===_Number_parameters2||null===(_Number_parameters_docs1=_Number_parameters2.docs)||void 0===_Number_parameters_docs1?void 0:_Number_parameters_docs1.description}}},Search.parameters={...Search.parameters,docs:{...null===(_Search_parameters=Search.parameters)||void 0===_Search_parameters?void 0:_Search_parameters.docs,source:{originalSource:"{\n  args: {\n    type: 'search',\n    placeholder: 'Search...'\n  },\n  parameters: {\n    docs: {\n      description: {\n        story: 'Search input type optimized for search functionality with appropriate styling and behavior. Use for search bars and filter inputs.'\n      }\n    }\n  }\n}",...null===(_Search_parameters1=Search.parameters)||void 0===_Search_parameters1||null===(_Search_parameters_docs=_Search_parameters1.docs)||void 0===_Search_parameters_docs?void 0:_Search_parameters_docs.source},description:{story:"Search input optimized for search functionality.\r\nUse for search bars, filters, and query inputs.",...null===(_Search_parameters2=Search.parameters)||void 0===_Search_parameters2||null===(_Search_parameters_docs1=_Search_parameters2.docs)||void 0===_Search_parameters_docs1?void 0:_Search_parameters_docs1.description}}},Disabled.parameters={...Disabled.parameters,docs:{...null===(_Disabled_parameters=Disabled.parameters)||void 0===_Disabled_parameters?void 0:_Disabled_parameters.docs,source:{originalSource:"{\n  args: {\n    placeholder: 'Disabled input...',\n    disabled: true\n  },\n  parameters: {\n    docs: {\n      description: {\n        story: 'Disabled input state with reduced opacity and no interaction. Use for unavailable fields or read-only data display.'\n      }\n    }\n  }\n}",...null===(_Disabled_parameters1=Disabled.parameters)||void 0===_Disabled_parameters1||null===(_Disabled_parameters_docs=_Disabled_parameters1.docs)||void 0===_Disabled_parameters_docs?void 0:_Disabled_parameters_docs.source},description:{story:"Disabled input state showing non-interactive appearance.\r\nUse to indicate unavailable fields or read-only data.",...null===(_Disabled_parameters2=Disabled.parameters)||void 0===_Disabled_parameters2||null===(_Disabled_parameters_docs1=_Disabled_parameters2.docs)||void 0===_Disabled_parameters_docs1?void 0:_Disabled_parameters_docs1.description}}};const __namedExportsOrder=["Default","Email","Password","Number","Search","Disabled"]}}]);