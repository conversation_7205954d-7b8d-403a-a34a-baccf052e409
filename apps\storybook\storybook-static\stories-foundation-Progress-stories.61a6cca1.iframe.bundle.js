"use strict";(self.webpackChunk_cloc_storybook=self.webpackChunk_cloc_storybook||[]).push([[9476],{"./src/stories/foundation/Progress.stories.tsx":(__unused_webpack_module,__webpack_exports__,__webpack_require__)=>{var _Default_parameters,_Default_parameters_docs,_Default_parameters1,_Default_parameters_docs1,_Default_parameters2,_LowProgress_parameters,_LowProgress_parameters_docs,_LowProgress_parameters1,_LowProgress_parameters_docs1,_LowProgress_parameters2,_HighProgress_parameters,_HighProgress_parameters_docs,_HighProgress_parameters1,_HighProgress_parameters_docs1,_HighProgress_parameters2,_Complete_parameters,_Complete_parameters_docs,_Complete_parameters1,_Complete_parameters_docs1,_Complete_parameters2,_CustomStyling_parameters,_CustomStyling_parameters_docs,_CustomStyling_parameters1,_CustomStyling_parameters_docs1,_CustomStyling_parameters2,_Empty_parameters,_Empty_parameters_docs,_Empty_parameters1,_Empty_parameters_docs1,_Empty_parameters2;__webpack_require__.r(__webpack_exports__),__webpack_require__.d(__webpack_exports__,{Complete:()=>Complete,CustomStyling:()=>CustomStyling,Default:()=>Default,Empty:()=>Empty,HighProgress:()=>HighProgress,LowProgress:()=>LowProgress,__namedExportsOrder:()=>__namedExportsOrder,default:()=>__WEBPACK_DEFAULT_EXPORT__});const __WEBPACK_DEFAULT_EXPORT__={title:"Foundation/Progress",component:__webpack_require__("../../packages/ui/dist/index.es.js").ke,parameters:{layout:"centered",docs:{description:{component:"\nProgress is a visual indicator component that shows the completion status of tasks, processes, or goals. It provides clear feedback to users about ongoing operations and achievement levels.\n\n### Key Capabilities\n\n- **Percentage Display**: Visual representation of progress from 0% to 100%\n- **Smooth Animations**: Fluid transitions between progress values for better UX\n- **Accessibility Support**: Built-in ARIA attributes and screen reader compatibility\n- **Theme Integration**: Consistent styling with dark and light theme support\n- **Custom Styling**: Flexible color and size customization through className prop\n- **Responsive Design**: Adapts to different container sizes and screen dimensions\n\n### Visual Design\n\nThe component features:\n- Clean, modern progress bar design\n- Smooth fill animation with appropriate timing\n- Clear visual distinction between completed and remaining progress\n- Consistent spacing and proportions\n- High contrast for accessibility compliance\n\n### Progress Values\n\n- **0-100**: Standard percentage values for determinate progress\n- **Smooth Transitions**: Animated changes between values\n- **Completion States**: Special styling for 100% completion\n- **Custom Colors**: Override default colors through className prop\n\n### Accessibility Features\n\n- Proper ARIA role and attributes\n- Screen reader announcements for progress changes\n- High contrast support for visual accessibility\n- Keyboard navigation support where applicable\n\n### Best Practices\n\n- Use for operations that take more than a few seconds\n- Provide clear context about what is progressing\n- Update progress values smoothly, not in large jumps\n- Consider adding percentage text for precise feedback\n- Use appropriate colors for different contexts (success, warning, error)\n- Test with screen readers for accessibility compliance\n                "}}},argTypes:{value:{control:{type:"range",min:0,max:100,step:1},description:"Progress value as a percentage (0-100)",table:{type:{summary:"number"},defaultValue:{summary:"0"}}},className:{control:"text",description:"Additional CSS classes for custom styling",table:{type:{summary:"string"}}},max:{control:"number",description:"Maximum value for progress calculation",table:{type:{summary:"number"},defaultValue:{summary:"100"}}}}},Default={args:{value:50},parameters:{docs:{description:{story:"The default progress component showing 50% completion. Use for general progress indication in forms, uploads, and task completion."}}}},LowProgress={args:{value:10},parameters:{docs:{description:{story:"Progress bar showing 10% completion. Demonstrates how the component appears in the early stages of a task or process."}}}},HighProgress={args:{value:75},parameters:{docs:{description:{story:"Progress bar showing 75% completion. Indicates a task that is mostly finished and nearing completion."}}}},Complete={args:{value:100},parameters:{docs:{description:{story:"Completed progress bar at 100%. Shows the final state indicating successful task completion."}}}},CustomStyling={args:{value:60,className:"h-3 bg-gradient-to-r from-blue-500 to-purple-500"},parameters:{docs:{description:{story:"Progress bar with custom styling including height and gradient colors. Demonstrates customization capabilities for specific design requirements."}}}},Empty={args:{value:0},parameters:{docs:{description:{story:"Empty progress bar at 0%. Shows the initial state before any progress has been made on a task or process."}}}};Default.parameters={...Default.parameters,docs:{...null===(_Default_parameters=Default.parameters)||void 0===_Default_parameters?void 0:_Default_parameters.docs,source:{originalSource:"{\n  args: {\n    value: 50\n  },\n  parameters: {\n    docs: {\n      description: {\n        story: 'The default progress component showing 50% completion. Use for general progress indication in forms, uploads, and task completion.'\n      }\n    }\n  }\n}",...null===(_Default_parameters1=Default.parameters)||void 0===_Default_parameters1||null===(_Default_parameters_docs=_Default_parameters1.docs)||void 0===_Default_parameters_docs?void 0:_Default_parameters_docs.source},description:{story:"Default progress bar showing 50% completion.\r\nStandard progress indicator for general use cases.",...null===(_Default_parameters2=Default.parameters)||void 0===_Default_parameters2||null===(_Default_parameters_docs1=_Default_parameters2.docs)||void 0===_Default_parameters_docs1?void 0:_Default_parameters_docs1.description}}},LowProgress.parameters={...LowProgress.parameters,docs:{...null===(_LowProgress_parameters=LowProgress.parameters)||void 0===_LowProgress_parameters?void 0:_LowProgress_parameters.docs,source:{originalSource:"{\n  args: {\n    value: 10\n  },\n  parameters: {\n    docs: {\n      description: {\n        story: 'Progress bar showing 10% completion. Demonstrates how the component appears in the early stages of a task or process.'\n      }\n    }\n  }\n}",...null===(_LowProgress_parameters1=LowProgress.parameters)||void 0===_LowProgress_parameters1||null===(_LowProgress_parameters_docs=_LowProgress_parameters1.docs)||void 0===_LowProgress_parameters_docs?void 0:_LowProgress_parameters_docs.source},description:{story:"Progress bar at the beginning of a task (10% completion).\r\nShows how the component appears in early stages.",...null===(_LowProgress_parameters2=LowProgress.parameters)||void 0===_LowProgress_parameters2||null===(_LowProgress_parameters_docs1=_LowProgress_parameters2.docs)||void 0===_LowProgress_parameters_docs1?void 0:_LowProgress_parameters_docs1.description}}},HighProgress.parameters={...HighProgress.parameters,docs:{...null===(_HighProgress_parameters=HighProgress.parameters)||void 0===_HighProgress_parameters?void 0:_HighProgress_parameters.docs,source:{originalSource:"{\n  args: {\n    value: 75\n  },\n  parameters: {\n    docs: {\n      description: {\n        story: 'Progress bar showing 75% completion. Indicates a task that is mostly finished and nearing completion.'\n      }\n    }\n  }\n}",...null===(_HighProgress_parameters1=HighProgress.parameters)||void 0===_HighProgress_parameters1||null===(_HighProgress_parameters_docs=_HighProgress_parameters1.docs)||void 0===_HighProgress_parameters_docs?void 0:_HighProgress_parameters_docs.source},description:{story:"Progress bar showing significant completion (75%).\r\nIndicates a task that is mostly finished.",...null===(_HighProgress_parameters2=HighProgress.parameters)||void 0===_HighProgress_parameters2||null===(_HighProgress_parameters_docs1=_HighProgress_parameters2.docs)||void 0===_HighProgress_parameters_docs1?void 0:_HighProgress_parameters_docs1.description}}},Complete.parameters={...Complete.parameters,docs:{...null===(_Complete_parameters=Complete.parameters)||void 0===_Complete_parameters?void 0:_Complete_parameters.docs,source:{originalSource:"{\n  args: {\n    value: 100\n  },\n  parameters: {\n    docs: {\n      description: {\n        story: 'Completed progress bar at 100%. Shows the final state indicating successful task completion.'\n      }\n    }\n  }\n}",...null===(_Complete_parameters1=Complete.parameters)||void 0===_Complete_parameters1||null===(_Complete_parameters_docs=_Complete_parameters1.docs)||void 0===_Complete_parameters_docs?void 0:_Complete_parameters_docs.source},description:{story:"Completed progress bar (100%) showing task completion.\r\nFinal state indicating successful completion.",...null===(_Complete_parameters2=Complete.parameters)||void 0===_Complete_parameters2||null===(_Complete_parameters_docs1=_Complete_parameters2.docs)||void 0===_Complete_parameters_docs1?void 0:_Complete_parameters_docs1.description}}},CustomStyling.parameters={...CustomStyling.parameters,docs:{...null===(_CustomStyling_parameters=CustomStyling.parameters)||void 0===_CustomStyling_parameters?void 0:_CustomStyling_parameters.docs,source:{originalSource:"{\n  args: {\n    value: 60,\n    className: 'h-3 bg-gradient-to-r from-blue-500 to-purple-500'\n  },\n  parameters: {\n    docs: {\n      description: {\n        story: 'Progress bar with custom styling including height and gradient colors. Demonstrates customization capabilities for specific design requirements.'\n      }\n    }\n  }\n}",...null===(_CustomStyling_parameters1=CustomStyling.parameters)||void 0===_CustomStyling_parameters1||null===(_CustomStyling_parameters_docs=_CustomStyling_parameters1.docs)||void 0===_CustomStyling_parameters_docs?void 0:_CustomStyling_parameters_docs.source},description:{story:"Progress bar with custom styling for different contexts.\r\nExample of color customization for specific use cases.",...null===(_CustomStyling_parameters2=CustomStyling.parameters)||void 0===_CustomStyling_parameters2||null===(_CustomStyling_parameters_docs1=_CustomStyling_parameters2.docs)||void 0===_CustomStyling_parameters_docs1?void 0:_CustomStyling_parameters_docs1.description}}},Empty.parameters={...Empty.parameters,docs:{...null===(_Empty_parameters=Empty.parameters)||void 0===_Empty_parameters?void 0:_Empty_parameters.docs,source:{originalSource:"{\n  args: {\n    value: 0\n  },\n  parameters: {\n    docs: {\n      description: {\n        story: 'Empty progress bar at 0%. Shows the initial state before any progress has been made on a task or process.'\n      }\n    }\n  }\n}",...null===(_Empty_parameters1=Empty.parameters)||void 0===_Empty_parameters1||null===(_Empty_parameters_docs=_Empty_parameters1.docs)||void 0===_Empty_parameters_docs?void 0:_Empty_parameters_docs.source},description:{story:"Empty progress bar (0%) showing initial state.\r\nStarting point before any progress has been made.",...null===(_Empty_parameters2=Empty.parameters)||void 0===_Empty_parameters2||null===(_Empty_parameters_docs1=_Empty_parameters2.docs)||void 0===_Empty_parameters_docs1?void 0:_Empty_parameters_docs1.description}}};const __namedExportsOrder=["Default","LowProgress","HighProgress","Complete","CustomStyling","Empty"]}}]);