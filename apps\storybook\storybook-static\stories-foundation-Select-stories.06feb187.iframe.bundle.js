"use strict";(self.webpackChunk_cloc_storybook=self.webpackChunk_cloc_storybook||[]).push([[9077],{"./src/stories/foundation/Select.stories.tsx":(__unused_webpack_module,__webpack_exports__,__webpack_require__)=>{var _Default_parameters,_Default_parameters_docs,_Default_parameters1,_Default_parameters_docs1,_Default_parameters2,_WithIcons_parameters,_WithIcons_parameters_docs,_WithIcons_parameters1,_WithIcons_parameters_docs1,_WithIcons_parameters2,_Small_parameters,_Small_parameters_docs,_Small_parameters1,_Small_parameters_docs1,_Small_parameters2,_Large_parameters,_Large_parameters_docs,_Large_parameters1,_Large_parameters_docs1,_Large_parameters2,_Loading_parameters,_Loading_parameters_docs,_Loading_parameters1,_Loading_parameters_docs1,_Loading_parameters2,_Disabled_parameters,_Disabled_parameters_docs,_Disabled_parameters1,_Disabled_parameters_docs1,_Disabled_parameters2;__webpack_require__.r(__webpack_exports__),__webpack_require__.d(__webpack_exports__,{Default:()=>Default,Disabled:()=>Disabled,Large:()=>Large,Loading:()=>Loading,Small:()=>Small,WithIcons:()=>WithIcons,__namedExportsOrder:()=>__namedExportsOrder,default:()=>__WEBPACK_DEFAULT_EXPORT__});const __WEBPACK_DEFAULT_EXPORT__={title:"Foundation/Select",component:__webpack_require__("../../packages/ui/dist/index.es.js").l6,parameters:{layout:"centered",docs:{description:{component:"\nSelect is a comprehensive dropdown component that provides intuitive option selection with support for icons, loading states, and various sizing options. It's designed for consistent user experience across different contexts.\n\n### Key Capabilities\n\n- **Rich Option Support**: Options with labels, values, and optional icons for enhanced visual identification\n- **State Management**: Loading, disabled, and selection states with appropriate visual feedback\n- **Size Flexibility**: Three size variants (sm, default, lg) for different interface contexts\n- **Accessibility First**: Full keyboard navigation, ARIA attributes, and screen reader support\n- **Theme Compatibility**: Seamless integration with dark and light themes\n- **Icon Integration**: Built-in support for option icons with proper spacing and alignment\n\n### Option Structure\n\nOptions are provided as an array of objects with the following structure:\n- `label`: Display text for the option\n- `value`: Actual value used for selection\n- `icon`: Optional icon identifier for visual enhancement\n\n### Styling System\n\nThe component uses consistent styling that provides:\n- Clean dropdown appearance with proper spacing\n- Clear selection indicators and hover states\n- Smooth animations for opening/closing\n- Responsive design for different screen sizes\n- Proper color contrast for accessibility\n\n### Best Practices\n\n- Provide clear, descriptive labels for all options\n- Use icons consistently when they add value\n- Implement loading states for async data\n- Group related options when dealing with large lists\n- Ensure proper keyboard navigation support\n- Test with screen readers for accessibility\n                "}}},argTypes:{values:{control:"object",description:"Array of option objects with label, value, and optional icon",table:{type:{summary:"Array<{label: string, value: string, icon?: string}>"}}},placeholder:{control:"text",description:"Placeholder text displayed when no option is selected",table:{type:{summary:"string"}}},size:{control:"select",options:["sm","default","lg"],description:"Size variant of the select component",table:{type:{summary:"string"},defaultValue:{summary:"default"}}},disabled:{control:"boolean",description:"Whether the select is disabled",table:{type:{summary:"boolean"},defaultValue:{summary:"false"}}},loading:{control:"boolean",description:"Whether the select is in loading state",table:{type:{summary:"boolean"},defaultValue:{summary:"false"}}},value:{control:"text",description:"Currently selected value",table:{type:{summary:"string"}}},className:{control:"text",description:"Additional CSS classes for custom styling",table:{type:{summary:"string"}}}}},sampleOptions=[{label:"Option 1",value:"option1"},{label:"Option 2",value:"option2"},{label:"Option 3",value:"option3"},{label:"Option 4",value:"option4"}],Default={args:{placeholder:"Select an option...",values:sampleOptions},parameters:{docs:{description:{story:"The default select component with basic options. Use for standard form fields, filters, and general option selection."}}}},WithIcons={args:{placeholder:"Select a country...",values:[{label:"United States",value:"us",icon:"us"},{label:"France",value:"fr",icon:"fr"},{label:"Germany",value:"de",icon:"de"},{label:"Japan",value:"jp",icon:"jp"}]},parameters:{docs:{description:{story:"Select component with icons for enhanced visual identification. Use for countries, categories, or any options that benefit from visual representation."}}}},Small={args:{placeholder:"Select...",values:sampleOptions,size:"sm"},parameters:{docs:{description:{story:"Small size select variant for compact interfaces, toolbars, and space-constrained layouts."}}}},Large={args:{placeholder:"Select an important option...",values:sampleOptions,size:"lg"},parameters:{docs:{description:{story:"Large size select variant for prominent selections and important choices in forms or configuration panels."}}}},Loading={args:{placeholder:"Loading options...",values:[],loading:!0},parameters:{docs:{description:{story:"Loading state select with spinner indicator. Use when options are being fetched asynchronously from an API or database."}}}},Disabled={args:{placeholder:"Disabled select...",values:sampleOptions,disabled:!0},parameters:{docs:{description:{story:"Disabled select state with reduced opacity and no interaction. Use for unavailable selections or read-only data display."}}}};Default.parameters={...Default.parameters,docs:{...null===(_Default_parameters=Default.parameters)||void 0===_Default_parameters?void 0:_Default_parameters.docs,source:{originalSource:"{\n  args: {\n    placeholder: 'Select an option...',\n    values: sampleOptions\n  },\n  parameters: {\n    docs: {\n      description: {\n        story: 'The default select component with basic options. Use for standard form fields, filters, and general option selection.'\n      }\n    }\n  }\n}",...null===(_Default_parameters1=Default.parameters)||void 0===_Default_parameters1||null===(_Default_parameters_docs=_Default_parameters1.docs)||void 0===_Default_parameters_docs?void 0:_Default_parameters_docs.source},description:{story:"Default select component with basic options for general selection needs.\r\nThe most common select variant for standard form fields.",...null===(_Default_parameters2=Default.parameters)||void 0===_Default_parameters2||null===(_Default_parameters_docs1=_Default_parameters2.docs)||void 0===_Default_parameters_docs1?void 0:_Default_parameters_docs1.description}}},WithIcons.parameters={...WithIcons.parameters,docs:{...null===(_WithIcons_parameters=WithIcons.parameters)||void 0===_WithIcons_parameters?void 0:_WithIcons_parameters.docs,source:{originalSource:"{\n  args: {\n    placeholder: 'Select a country...',\n    values: optionsWithIcons\n  },\n  parameters: {\n    docs: {\n      description: {\n        story: 'Select component with icons for enhanced visual identification. Use for countries, categories, or any options that benefit from visual representation.'\n      }\n    }\n  }\n}",...null===(_WithIcons_parameters1=WithIcons.parameters)||void 0===_WithIcons_parameters1||null===(_WithIcons_parameters_docs=_WithIcons_parameters1.docs)||void 0===_WithIcons_parameters_docs?void 0:_WithIcons_parameters_docs.source},description:{story:"Select with icons for enhanced visual identification of options.\r\nUse when options benefit from visual representation like countries or categories.",...null===(_WithIcons_parameters2=WithIcons.parameters)||void 0===_WithIcons_parameters2||null===(_WithIcons_parameters_docs1=_WithIcons_parameters2.docs)||void 0===_WithIcons_parameters_docs1?void 0:_WithIcons_parameters_docs1.description}}},Small.parameters={...Small.parameters,docs:{...null===(_Small_parameters=Small.parameters)||void 0===_Small_parameters?void 0:_Small_parameters.docs,source:{originalSource:"{\n  args: {\n    placeholder: 'Select...',\n    values: sampleOptions,\n    size: 'sm'\n  },\n  parameters: {\n    docs: {\n      description: {\n        story: 'Small size select variant for compact interfaces, toolbars, and space-constrained layouts.'\n      }\n    }\n  }\n}",...null===(_Small_parameters1=Small.parameters)||void 0===_Small_parameters1||null===(_Small_parameters_docs=_Small_parameters1.docs)||void 0===_Small_parameters_docs?void 0:_Small_parameters_docs.source},description:{story:"Small size select for compact interfaces and dense layouts.\r\nUse in toolbars, table filters, or space-constrained areas.",...null===(_Small_parameters2=Small.parameters)||void 0===_Small_parameters2||null===(_Small_parameters_docs1=_Small_parameters2.docs)||void 0===_Small_parameters_docs1?void 0:_Small_parameters_docs1.description}}},Large.parameters={...Large.parameters,docs:{...null===(_Large_parameters=Large.parameters)||void 0===_Large_parameters?void 0:_Large_parameters.docs,source:{originalSource:"{\n  args: {\n    placeholder: 'Select an important option...',\n    values: sampleOptions,\n    size: 'lg'\n  },\n  parameters: {\n    docs: {\n      description: {\n        story: 'Large size select variant for prominent selections and important choices in forms or configuration panels.'\n      }\n    }\n  }\n}",...null===(_Large_parameters1=Large.parameters)||void 0===_Large_parameters1||null===(_Large_parameters_docs=_Large_parameters1.docs)||void 0===_Large_parameters_docs?void 0:_Large_parameters_docs.source},description:{story:"Large size select for prominent selections and important choices.\r\nUse for main configuration options or primary form fields.",...null===(_Large_parameters2=Large.parameters)||void 0===_Large_parameters2||null===(_Large_parameters_docs1=_Large_parameters2.docs)||void 0===_Large_parameters_docs1?void 0:_Large_parameters_docs1.description}}},Loading.parameters={...Loading.parameters,docs:{...null===(_Loading_parameters=Loading.parameters)||void 0===_Loading_parameters?void 0:_Loading_parameters.docs,source:{originalSource:"{\n  args: {\n    placeholder: 'Loading options...',\n    values: [],\n    loading: true\n  },\n  parameters: {\n    docs: {\n      description: {\n        story: 'Loading state select with spinner indicator. Use when options are being fetched asynchronously from an API or database.'\n      }\n    }\n  }\n}",...null===(_Loading_parameters1=Loading.parameters)||void 0===_Loading_parameters1||null===(_Loading_parameters_docs=_Loading_parameters1.docs)||void 0===_Loading_parameters_docs?void 0:_Loading_parameters_docs.source},description:{story:"Loading state select showing spinner while fetching options.\r\nUse when options are loaded asynchronously from an API.",...null===(_Loading_parameters2=Loading.parameters)||void 0===_Loading_parameters2||null===(_Loading_parameters_docs1=_Loading_parameters2.docs)||void 0===_Loading_parameters_docs1?void 0:_Loading_parameters_docs1.description}}},Disabled.parameters={...Disabled.parameters,docs:{...null===(_Disabled_parameters=Disabled.parameters)||void 0===_Disabled_parameters?void 0:_Disabled_parameters.docs,source:{originalSource:"{\n  args: {\n    placeholder: 'Disabled select...',\n    values: sampleOptions,\n    disabled: true\n  },\n  parameters: {\n    docs: {\n      description: {\n        story: 'Disabled select state with reduced opacity and no interaction. Use for unavailable selections or read-only data display.'\n      }\n    }\n  }\n}",...null===(_Disabled_parameters1=Disabled.parameters)||void 0===_Disabled_parameters1||null===(_Disabled_parameters_docs=_Disabled_parameters1.docs)||void 0===_Disabled_parameters_docs?void 0:_Disabled_parameters_docs.source},description:{story:"Disabled select state showing non-interactive appearance.\r\nUse to indicate unavailable selections or read-only data.",...null===(_Disabled_parameters2=Disabled.parameters)||void 0===_Disabled_parameters2||null===(_Disabled_parameters_docs1=_Disabled_parameters2.docs)||void 0===_Disabled_parameters_docs1?void 0:_Disabled_parameters_docs1.description}}};const __namedExportsOrder=["Default","WithIcons","Small","Large","Loading","Disabled"]}}]);