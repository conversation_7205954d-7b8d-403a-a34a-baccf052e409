"use strict";(self.webpackChunk_cloc_storybook=self.webpackChunk_cloc_storybook||[]).push([[9332],{"./src/stories/inputs/global-selectors/ClocActiveEmployeeSelector.stories.tsx":(__unused_webpack_module,__webpack_exports__,__webpack_require__)=>{__webpack_require__.r(__webpack_exports__),__webpack_require__.d(__webpack_exports__,{CustomStyling:()=>CustomStyling,Default:()=>Default,LargeSize:()=>LargeSize,SmallSize:()=>SmallSize,WithLabel:()=>WithLabel,__namedExportsOrder:()=>__namedExportsOrder,default:()=>__WEBPACK_DEFAULT_EXPORT__});var _Default_parameters,_Default_parameters_docs,_Default_parameters1,_Default_parameters_docs1,_Default_parameters2,_WithLabel_parameters,_WithLabel_parameters_docs,_WithLabel_parameters1,_WithLabel_parameters_docs1,_WithLabel_parameters2,_SmallSize_parameters,_SmallSize_parameters_docs,_SmallSize_parameters1,_SmallSize_parameters_docs1,_SmallSize_parameters2,_LargeSize_parameters,_LargeSize_parameters_docs,_LargeSize_parameters1,_LargeSize_parameters_docs1,_LargeSize_parameters2,_CustomStyling_parameters,_CustomStyling_parameters_docs,_CustomStyling_parameters1,_CustomStyling_parameters_docs1,_CustomStyling_parameters2,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__=__webpack_require__("../../node_modules/next/dist/compiled/react/jsx-runtime.js");const __WEBPACK_DEFAULT_EXPORT__={title:"Inputs/Global Selectors/Cloc Active Employee Selector",component:__webpack_require__("../../packages/toolkit/atoms/dist/index.es.js").TCr,parameters:{layout:"centered",docs:{description:{component:'\nClocActiveEmployeeSelector is a permission-controlled global selector component that enables employee selection for users with appropriate administrative privileges. It provides comprehensive employee management capabilities with intelligent permission handling.\n\n### Key Capabilities\n\n- **Permission Control**: Automatically hides for users without CHANGE_SELECTED_EMPLOYEE permission\n- **Employee Management**: Displays all organization members with "All employees" option for comprehensive filtering\n- **Icon Integration**: Optional labeled mode with Users icon for clear visual identification\n- **Loading Handling**: Provides visual feedback during member data fetching\n- **Size Flexibility**: Supports multiple size variants for different UI contexts\n- **Global State Management**: Seamlessly updates selectedEmployee across the entire application\n- **Custom Styling**: Flexible className support for visual customization\n\n### Permission System\n\nThe component implements a sophisticated permission system that checks for CHANGE_SELECTED_EMPLOYEE permission. Users without this permission will not see the component at all, ensuring secure access control for employee selection functionality.\n\n### Technical Implementation\n\nThe component integrates with the ClocProvider context to access organization members, permission data, and global employee selection state. It provides intelligent permission checking and graceful handling of loading states.\n                '}}},argTypes:{size:{control:"select",options:["default","sm","lg"],description:"Size variant of the select component",table:{type:{summary:"'default' | 'sm' | 'lg' | null"},defaultValue:{summary:"'default'"}}},labeled:{control:"boolean",description:"Whether to show the label with Users icon above the select",table:{type:{summary:"boolean"},defaultValue:{summary:"false"}}},className:{control:"text",description:"Additional CSS classes to apply to the select component",table:{type:{summary:"string"},defaultValue:{summary:"undefined"}}}},decorators:[Story=>(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("div",{style:{width:"300px",height:"200px"},children:(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(Story,{})})]},Default={args:{size:"default",labeled:!1},parameters:{docs:{description:{story:'The default ClocActiveEmployeeSelector component without label. Shows the employee selection dropdown with "All employees" option, loading states, and permission-based visibility.'}}}},WithLabel={args:{size:"default",labeled:!0},parameters:{docs:{description:{story:'ClocActiveEmployeeSelector with label enabled (labeled=true). Displays the "Employee" label with Users icon above the selection dropdown for clear visual identification.'}}}},SmallSize={args:{size:"sm",labeled:!0},parameters:{docs:{description:{story:'ClocActiveEmployeeSelector with small size variant (size="sm"). Ideal for compact layouts, toolbars, or dense interfaces while maintaining full employee selection functionality.'}}}},LargeSize={args:{size:"lg",labeled:!0},parameters:{docs:{description:{story:'ClocActiveEmployeeSelector with large size variant (size="lg"). Perfect for prominent placement in main workflows or when employee selection is a primary action.'}}}},CustomStyling={args:{size:"default",labeled:!0,className:"border-blue-300 bg-blue-50 dark:border-blue-700 dark:bg-blue-950"},parameters:{docs:{description:{story:"ClocActiveEmployeeSelector with custom styling applied through the className prop. Features custom border and background colors while preserving all employee selection functionality."}}}};Default.parameters={...Default.parameters,docs:{...null===(_Default_parameters=Default.parameters)||void 0===_Default_parameters?void 0:_Default_parameters.docs,source:{originalSource:"{\n  args: {\n    size: 'default',\n    labeled: false\n  },\n  parameters: {\n    docs: {\n      description: {\n        story: 'The default ClocActiveEmployeeSelector component without label. Shows the employee selection dropdown with \"All employees\" option, loading states, and permission-based visibility.'\n      }\n    }\n  }\n}",...null===(_Default_parameters1=Default.parameters)||void 0===_Default_parameters1||null===(_Default_parameters_docs=_Default_parameters1.docs)||void 0===_Default_parameters_docs?void 0:_Default_parameters_docs.source},description:{story:'Default employee selector without label, showing clean selection interface.\r\nDisplays employee dropdown with "All employees" option and standard styling.',...null===(_Default_parameters2=Default.parameters)||void 0===_Default_parameters2||null===(_Default_parameters_docs1=_Default_parameters2.docs)||void 0===_Default_parameters_docs1?void 0:_Default_parameters_docs1.description}}},WithLabel.parameters={...WithLabel.parameters,docs:{...null===(_WithLabel_parameters=WithLabel.parameters)||void 0===_WithLabel_parameters?void 0:_WithLabel_parameters.docs,source:{originalSource:"{\n  args: {\n    size: 'default',\n    labeled: true\n  },\n  parameters: {\n    docs: {\n      description: {\n        story: 'ClocActiveEmployeeSelector with label enabled (labeled=true). Displays the \"Employee\" label with Users icon above the selection dropdown for clear visual identification.'\n      }\n    }\n  }\n}",...null===(_WithLabel_parameters1=WithLabel.parameters)||void 0===_WithLabel_parameters1||null===(_WithLabel_parameters_docs=_WithLabel_parameters1.docs)||void 0===_WithLabel_parameters_docs?void 0:_WithLabel_parameters_docs.source},description:{story:"Employee selector with label and Users icon for clear identification.\r\nProvides enhanced visual context for the selection purpose.",...null===(_WithLabel_parameters2=WithLabel.parameters)||void 0===_WithLabel_parameters2||null===(_WithLabel_parameters_docs1=_WithLabel_parameters2.docs)||void 0===_WithLabel_parameters_docs1?void 0:_WithLabel_parameters_docs1.description}}},SmallSize.parameters={...SmallSize.parameters,docs:{...null===(_SmallSize_parameters=SmallSize.parameters)||void 0===_SmallSize_parameters?void 0:_SmallSize_parameters.docs,source:{originalSource:"{\n  args: {\n    size: 'sm',\n    labeled: true\n  },\n  parameters: {\n    docs: {\n      description: {\n        story: 'ClocActiveEmployeeSelector with small size variant (size=\"sm\"). Ideal for compact layouts, toolbars, or dense interfaces while maintaining full employee selection functionality.'\n      }\n    }\n  }\n}",...null===(_SmallSize_parameters1=SmallSize.parameters)||void 0===_SmallSize_parameters1||null===(_SmallSize_parameters_docs=_SmallSize_parameters1.docs)||void 0===_SmallSize_parameters_docs?void 0:_SmallSize_parameters_docs.source},description:{story:"Small-sized employee selector optimized for compact layouts.\r\nMaintains full functionality while taking up less space.",...null===(_SmallSize_parameters2=SmallSize.parameters)||void 0===_SmallSize_parameters2||null===(_SmallSize_parameters_docs1=_SmallSize_parameters2.docs)||void 0===_SmallSize_parameters_docs1?void 0:_SmallSize_parameters_docs1.description}}},LargeSize.parameters={...LargeSize.parameters,docs:{...null===(_LargeSize_parameters=LargeSize.parameters)||void 0===_LargeSize_parameters?void 0:_LargeSize_parameters.docs,source:{originalSource:"{\n  args: {\n    size: 'lg',\n    labeled: true\n  },\n  parameters: {\n    docs: {\n      description: {\n        story: 'ClocActiveEmployeeSelector with large size variant (size=\"lg\"). Perfect for prominent placement in main workflows or when employee selection is a primary action.'\n      }\n    }\n  }\n}",...null===(_LargeSize_parameters1=LargeSize.parameters)||void 0===_LargeSize_parameters1||null===(_LargeSize_parameters_docs=_LargeSize_parameters1.docs)||void 0===_LargeSize_parameters_docs?void 0:_LargeSize_parameters_docs.source},description:{story:"Large-sized employee selector for prominent placement.\r\nProvides enhanced visibility and easier interaction.",...null===(_LargeSize_parameters2=LargeSize.parameters)||void 0===_LargeSize_parameters2||null===(_LargeSize_parameters_docs1=_LargeSize_parameters2.docs)||void 0===_LargeSize_parameters_docs1?void 0:_LargeSize_parameters_docs1.description}}},CustomStyling.parameters={...CustomStyling.parameters,docs:{...null===(_CustomStyling_parameters=CustomStyling.parameters)||void 0===_CustomStyling_parameters?void 0:_CustomStyling_parameters.docs,source:{originalSource:"{\n  args: {\n    size: 'default',\n    labeled: true,\n    className: 'border-blue-300 bg-blue-50 dark:border-blue-700 dark:bg-blue-950'\n  },\n  parameters: {\n    docs: {\n      description: {\n        story: 'ClocActiveEmployeeSelector with custom styling applied through the className prop. Features custom border and background colors while preserving all employee selection functionality.'\n      }\n    }\n  }\n}",...null===(_CustomStyling_parameters1=CustomStyling.parameters)||void 0===_CustomStyling_parameters1||null===(_CustomStyling_parameters_docs=_CustomStyling_parameters1.docs)||void 0===_CustomStyling_parameters_docs?void 0:_CustomStyling_parameters_docs.source},description:{story:"Employee selector with custom styling applied through className prop.\r\nDemonstrates visual customization while maintaining full functionality.",...null===(_CustomStyling_parameters2=CustomStyling.parameters)||void 0===_CustomStyling_parameters2||null===(_CustomStyling_parameters_docs1=_CustomStyling_parameters2.docs)||void 0===_CustomStyling_parameters_docs1?void 0:_CustomStyling_parameters_docs1.description}}};const __namedExportsOrder=["Default","WithLabel","SmallSize","LargeSize","CustomStyling"]}}]);