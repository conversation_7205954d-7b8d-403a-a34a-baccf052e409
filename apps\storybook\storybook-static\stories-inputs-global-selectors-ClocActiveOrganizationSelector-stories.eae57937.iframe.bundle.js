"use strict";(self.webpackChunk_cloc_storybook=self.webpackChunk_cloc_storybook||[]).push([[5565],{"./src/stories/inputs/global-selectors/ClocActiveOrganizationSelector.stories.tsx":(__unused_webpack_module,__webpack_exports__,__webpack_require__)=>{__webpack_require__.r(__webpack_exports__),__webpack_require__.d(__webpack_exports__,{CustomStyling:()=>CustomStyling,Default:()=>Default,LargeSize:()=>LargeSize,SmallSize:()=>SmallSize,WithLabel:()=>WithLabel,__namedExportsOrder:()=>__namedExportsOrder,default:()=>__WEBPACK_DEFAULT_EXPORT__});var _Default_parameters,_Default_parameters_docs,_Default_parameters1,_Default_parameters_docs1,_Default_parameters2,_WithLabel_parameters,_WithLabel_parameters_docs,_WithLabel_parameters1,_WithLabel_parameters_docs1,_WithLabel_parameters2,_SmallSize_parameters,_SmallSize_parameters_docs,_SmallSize_parameters1,_SmallSize_parameters_docs1,_SmallSize_parameters2,_LargeSize_parameters,_LargeSize_parameters_docs,_LargeSize_parameters1,_LargeSize_parameters_docs1,_LargeSize_parameters2,_CustomStyling_parameters,_CustomStyling_parameters_docs,_CustomStyling_parameters1,_CustomStyling_parameters_docs1,_CustomStyling_parameters2,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__=__webpack_require__("../../node_modules/next/dist/compiled/react/jsx-runtime.js");const __WEBPACK_DEFAULT_EXPORT__={title:"Inputs/Global Selectors/Cloc Active Organization Selector",component:__webpack_require__("../../packages/toolkit/atoms/dist/index.es.js").sPD,parameters:{layout:"centered",docs:{description:{component:"\nClocActiveOrganizationSelector is a global selector component that enables organization selection for users with access to multiple organizations. It provides seamless organization switching capabilities with comprehensive state management.\n\n### Key Capabilities\n\n- **Organization Management**: Displays all user-accessible organizations with clear selection interface\n- **Custom Labeling**: Flexible label system allowing custom text for different use cases\n- **Loading Handling**: Provides visual feedback during organization data fetching\n- **Size Flexibility**: Supports multiple size variants for different UI contexts\n- **Global State Management**: Seamlessly updates selectedOrganization across the entire application\n- **Custom Styling**: Flexible className support for visual customization\n\n### Technical Implementation\n\nThe component integrates with the ClocProvider context to access user organizations, loading states, and global organization selection state. It provides efficient organization switching with immediate state updates across the application.\n                "}}},argTypes:{size:{control:"select",options:["default","sm","lg"],description:"Size variant of the select component",table:{type:{summary:"'default' | 'sm' | 'lg' | null"},defaultValue:{summary:"'default'"}}},label:{control:"text",description:"Custom label text to display above the select",table:{type:{summary:"string"},defaultValue:{summary:"undefined"}}},className:{control:"text",description:"Additional CSS classes to apply to the select component",table:{type:{summary:"string"},defaultValue:{summary:"undefined"}}}},decorators:[Story=>(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("div",{style:{width:"300px",height:"200px"},children:(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(Story,{})})]},Default={args:{size:"default"},parameters:{docs:{description:{story:"The default ClocActiveOrganizationSelector component without label. Shows the organization selection dropdown with loading states and global state management."}}}},WithLabel={args:{size:"default",label:"Select Organization"},parameters:{docs:{description:{story:"ClocActiveOrganizationSelector with custom label text. Displays the specified label above the selection dropdown for clear visual identification and context."}}}},SmallSize={args:{size:"sm",label:"Organization"},parameters:{docs:{description:{story:'ClocActiveOrganizationSelector with small size variant (size="sm"). Ideal for compact layouts, toolbars, or dense interfaces while maintaining full organization selection functionality.'}}}},LargeSize={args:{size:"lg",label:"Choose Your Organization"},parameters:{docs:{description:{story:'ClocActiveOrganizationSelector with large size variant (size="lg"). Perfect for prominent placement in main workflows, onboarding flows, or when organization selection is a primary action.'}}}},CustomStyling={args:{size:"default",label:"Organization",className:"border-green-300 bg-green-50 dark:border-green-700 dark:bg-green-950"},parameters:{docs:{description:{story:"ClocActiveOrganizationSelector with custom styling applied through the className prop. Features custom border and background colors while preserving all organization selection functionality."}}}};Default.parameters={...Default.parameters,docs:{...null===(_Default_parameters=Default.parameters)||void 0===_Default_parameters?void 0:_Default_parameters.docs,source:{originalSource:"{\n  args: {\n    size: 'default'\n  },\n  parameters: {\n    docs: {\n      description: {\n        story: 'The default ClocActiveOrganizationSelector component without label. Shows the organization selection dropdown with loading states and global state management.'\n      }\n    }\n  }\n}",...null===(_Default_parameters1=Default.parameters)||void 0===_Default_parameters1||null===(_Default_parameters_docs=_Default_parameters1.docs)||void 0===_Default_parameters_docs?void 0:_Default_parameters_docs.source},description:{story:"Default organization selector without label, showing clean selection interface.\r\nDisplays organization dropdown with standard styling and functionality.",...null===(_Default_parameters2=Default.parameters)||void 0===_Default_parameters2||null===(_Default_parameters_docs1=_Default_parameters2.docs)||void 0===_Default_parameters_docs1?void 0:_Default_parameters_docs1.description}}},WithLabel.parameters={...WithLabel.parameters,docs:{...null===(_WithLabel_parameters=WithLabel.parameters)||void 0===_WithLabel_parameters?void 0:_WithLabel_parameters.docs,source:{originalSource:"{\n  args: {\n    size: 'default',\n    label: 'Select Organization'\n  },\n  parameters: {\n    docs: {\n      description: {\n        story: 'ClocActiveOrganizationSelector with custom label text. Displays the specified label above the selection dropdown for clear visual identification and context.'\n      }\n    }\n  }\n}",...null===(_WithLabel_parameters1=WithLabel.parameters)||void 0===_WithLabel_parameters1||null===(_WithLabel_parameters_docs=_WithLabel_parameters1.docs)||void 0===_WithLabel_parameters_docs?void 0:_WithLabel_parameters_docs.source},description:{story:"Organization selector with custom label for enhanced context.\r\nProvides clear identification of the selection purpose.",...null===(_WithLabel_parameters2=WithLabel.parameters)||void 0===_WithLabel_parameters2||null===(_WithLabel_parameters_docs1=_WithLabel_parameters2.docs)||void 0===_WithLabel_parameters_docs1?void 0:_WithLabel_parameters_docs1.description}}},SmallSize.parameters={...SmallSize.parameters,docs:{...null===(_SmallSize_parameters=SmallSize.parameters)||void 0===_SmallSize_parameters?void 0:_SmallSize_parameters.docs,source:{originalSource:"{\n  args: {\n    size: 'sm',\n    label: 'Organization'\n  },\n  parameters: {\n    docs: {\n      description: {\n        story: 'ClocActiveOrganizationSelector with small size variant (size=\"sm\"). Ideal for compact layouts, toolbars, or dense interfaces while maintaining full organization selection functionality.'\n      }\n    }\n  }\n}",...null===(_SmallSize_parameters1=SmallSize.parameters)||void 0===_SmallSize_parameters1||null===(_SmallSize_parameters_docs=_SmallSize_parameters1.docs)||void 0===_SmallSize_parameters_docs?void 0:_SmallSize_parameters_docs.source},description:{story:"Small-sized organization selector optimized for compact layouts.\r\nMaintains full functionality while taking up less space.",...null===(_SmallSize_parameters2=SmallSize.parameters)||void 0===_SmallSize_parameters2||null===(_SmallSize_parameters_docs1=_SmallSize_parameters2.docs)||void 0===_SmallSize_parameters_docs1?void 0:_SmallSize_parameters_docs1.description}}},LargeSize.parameters={...LargeSize.parameters,docs:{...null===(_LargeSize_parameters=LargeSize.parameters)||void 0===_LargeSize_parameters?void 0:_LargeSize_parameters.docs,source:{originalSource:"{\n  args: {\n    size: 'lg',\n    label: 'Choose Your Organization'\n  },\n  parameters: {\n    docs: {\n      description: {\n        story: 'ClocActiveOrganizationSelector with large size variant (size=\"lg\"). Perfect for prominent placement in main workflows, onboarding flows, or when organization selection is a primary action.'\n      }\n    }\n  }\n}",...null===(_LargeSize_parameters1=LargeSize.parameters)||void 0===_LargeSize_parameters1||null===(_LargeSize_parameters_docs=_LargeSize_parameters1.docs)||void 0===_LargeSize_parameters_docs?void 0:_LargeSize_parameters_docs.source},description:{story:"Large-sized organization selector for prominent placement.\r\nProvides enhanced visibility and easier interaction.",...null===(_LargeSize_parameters2=LargeSize.parameters)||void 0===_LargeSize_parameters2||null===(_LargeSize_parameters_docs1=_LargeSize_parameters2.docs)||void 0===_LargeSize_parameters_docs1?void 0:_LargeSize_parameters_docs1.description}}},CustomStyling.parameters={...CustomStyling.parameters,docs:{...null===(_CustomStyling_parameters=CustomStyling.parameters)||void 0===_CustomStyling_parameters?void 0:_CustomStyling_parameters.docs,source:{originalSource:"{\n  args: {\n    size: 'default',\n    label: 'Organization',\n    className: 'border-green-300 bg-green-50 dark:border-green-700 dark:bg-green-950'\n  },\n  parameters: {\n    docs: {\n      description: {\n        story: 'ClocActiveOrganizationSelector with custom styling applied through the className prop. Features custom border and background colors while preserving all organization selection functionality.'\n      }\n    }\n  }\n}",...null===(_CustomStyling_parameters1=CustomStyling.parameters)||void 0===_CustomStyling_parameters1||null===(_CustomStyling_parameters_docs=_CustomStyling_parameters1.docs)||void 0===_CustomStyling_parameters_docs?void 0:_CustomStyling_parameters_docs.source},description:{story:"Organization selector with custom styling applied through className prop.\r\nDemonstrates visual customization while maintaining full functionality.",...null===(_CustomStyling_parameters2=CustomStyling.parameters)||void 0===_CustomStyling_parameters2||null===(_CustomStyling_parameters_docs1=_CustomStyling_parameters2.docs)||void 0===_CustomStyling_parameters_docs1?void 0:_CustomStyling_parameters_docs1.description}}};const __namedExportsOrder=["Default","WithLabel","SmallSize","LargeSize","CustomStyling"]}}]);