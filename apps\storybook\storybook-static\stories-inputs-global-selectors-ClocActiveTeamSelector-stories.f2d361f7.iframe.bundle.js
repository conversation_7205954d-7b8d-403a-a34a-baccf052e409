"use strict";(self.webpackChunk_cloc_storybook=self.webpackChunk_cloc_storybook||[]).push([[6875],{"./src/stories/inputs/global-selectors/ClocActiveTeamSelector.stories.tsx":(__unused_webpack_module,__webpack_exports__,__webpack_require__)=>{__webpack_require__.r(__webpack_exports__),__webpack_require__.d(__webpack_exports__,{CustomStyling:()=>CustomStyling,Default:()=>Default,LargeSize:()=>LargeSize,SmallSize:()=>SmallSize,WithLabel:()=>WithLabel,__namedExportsOrder:()=>__namedExportsOrder,default:()=>__WEBPACK_DEFAULT_EXPORT__});var _Default_parameters,_Default_parameters_docs,_Default_parameters1,_Default_parameters_docs1,_Default_parameters2,_WithLabel_parameters,_WithLabel_parameters_docs,_WithLabel_parameters1,_WithLabel_parameters_docs1,_WithLabel_parameters2,_SmallSize_parameters,_SmallSize_parameters_docs,_SmallSize_parameters1,_SmallSize_parameters_docs1,_SmallSize_parameters2,_LargeSize_parameters,_LargeSize_parameters_docs,_LargeSize_parameters1,_LargeSize_parameters_docs1,_LargeSize_parameters2,_CustomStyling_parameters,_CustomStyling_parameters_docs,_CustomStyling_parameters1,_CustomStyling_parameters_docs1,_CustomStyling_parameters2,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__=__webpack_require__("../../node_modules/next/dist/compiled/react/jsx-runtime.js");const __WEBPACK_DEFAULT_EXPORT__={title:"Inputs/Global Selectors/Cloc Active Team Selector",component:__webpack_require__("../../packages/toolkit/atoms/dist/index.es.js").y$X,parameters:{layout:"centered",docs:{description:{component:'\nClocActiveTeamSelector is a global selector component that enables team selection with intelligent member count display and comprehensive team management capabilities. It provides seamless team switching with "All teams" option for broader scope operations.\n\n### Key Capabilities\n\n- **Team Management**: Displays all organization teams with clear selection interface and member counts\n- **All Teams Option**: Includes "All teams" option for comprehensive team-wide operations\n- **Member Count Display**: Shows team member count in format "Team Name (X)" for better context\n- **Custom Labeling**: Flexible label system allowing custom text for different use cases\n- **Loading Handling**: Provides visual feedback during team data fetching\n- **Size Flexibility**: Supports multiple size variants for different UI contexts\n- **Global State Management**: Seamlessly updates selectedTeam across the entire application\n- **Internationalization**: Full i18n support for placeholder text and labels\n\n### Technical Implementation\n\nThe component integrates with the ClocProvider context to access organization teams, loading states, and global team selection state. It provides intelligent team member counting and graceful fallback to "All teams" when no teams are available.\n                '}}},argTypes:{size:{control:"select",options:["default","sm","lg"],description:"Size variant of the select component",table:{type:{summary:"'default' | 'sm' | 'lg' | null"},defaultValue:{summary:"'default'"}}},label:{control:"text",description:"Custom label text to display above the select",table:{type:{summary:"string"},defaultValue:{summary:"undefined"}}},className:{control:"text",description:"Additional CSS classes to apply to the select component",table:{type:{summary:"string"},defaultValue:{summary:"undefined"}}}},decorators:[Story=>(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("div",{style:{width:"300px",height:"200px"},children:(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(Story,{})})]},Default={args:{size:"default"},parameters:{docs:{description:{story:'The default ClocActiveTeamSelector component without label. Shows the team selection dropdown with "All teams" option, member counts in team labels, loading states, and internationalized placeholder text.'}}}},WithLabel={args:{size:"default",label:"Select Team"},parameters:{docs:{description:{story:"ClocActiveTeamSelector with custom label text. Displays the specified label above the selection dropdown for clear visual identification and context, along with team member counts."}}}},SmallSize={args:{size:"sm",label:"Team"},parameters:{docs:{description:{story:'ClocActiveTeamSelector with small size variant (size="sm"). Ideal for compact layouts, toolbars, or dense interfaces while maintaining full team selection functionality and member count display.'}}}},LargeSize={args:{size:"lg",label:"Choose Your Team"},parameters:{docs:{description:{story:'ClocActiveTeamSelector with large size variant (size="lg"). Perfect for prominent placement in main workflows, onboarding flows, or when team selection is a primary action.'}}}},CustomStyling={args:{size:"default",label:"Team",className:"border-purple-300 bg-purple-50 dark:border-purple-700 dark:bg-purple-950"},parameters:{docs:{description:{story:"ClocActiveTeamSelector with custom styling applied through the className prop. Features custom border and background colors while preserving all team selection functionality and member count display."}}}};Default.parameters={...Default.parameters,docs:{...null===(_Default_parameters=Default.parameters)||void 0===_Default_parameters?void 0:_Default_parameters.docs,source:{originalSource:"{\n  args: {\n    size: 'default'\n  },\n  parameters: {\n    docs: {\n      description: {\n        story: 'The default ClocActiveTeamSelector component without label. Shows the team selection dropdown with \"All teams\" option, member counts in team labels, loading states, and internationalized placeholder text.'\n      }\n    }\n  }\n}",...null===(_Default_parameters1=Default.parameters)||void 0===_Default_parameters1||null===(_Default_parameters_docs=_Default_parameters1.docs)||void 0===_Default_parameters_docs?void 0:_Default_parameters_docs.source},description:{story:'Default team selector without label, showing clean selection interface.\r\nDisplays team dropdown with "All teams" option and member counts.',...null===(_Default_parameters2=Default.parameters)||void 0===_Default_parameters2||null===(_Default_parameters_docs1=_Default_parameters2.docs)||void 0===_Default_parameters_docs1?void 0:_Default_parameters_docs1.description}}},WithLabel.parameters={...WithLabel.parameters,docs:{...null===(_WithLabel_parameters=WithLabel.parameters)||void 0===_WithLabel_parameters?void 0:_WithLabel_parameters.docs,source:{originalSource:"{\n  args: {\n    size: 'default',\n    label: 'Select Team'\n  },\n  parameters: {\n    docs: {\n      description: {\n        story: 'ClocActiveTeamSelector with custom label text. Displays the specified label above the selection dropdown for clear visual identification and context, along with team member counts.'\n      }\n    }\n  }\n}",...null===(_WithLabel_parameters1=WithLabel.parameters)||void 0===_WithLabel_parameters1||null===(_WithLabel_parameters_docs=_WithLabel_parameters1.docs)||void 0===_WithLabel_parameters_docs?void 0:_WithLabel_parameters_docs.source},description:{story:"Team selector with custom label for enhanced context.\r\nProvides clear identification of the selection purpose.",...null===(_WithLabel_parameters2=WithLabel.parameters)||void 0===_WithLabel_parameters2||null===(_WithLabel_parameters_docs1=_WithLabel_parameters2.docs)||void 0===_WithLabel_parameters_docs1?void 0:_WithLabel_parameters_docs1.description}}},SmallSize.parameters={...SmallSize.parameters,docs:{...null===(_SmallSize_parameters=SmallSize.parameters)||void 0===_SmallSize_parameters?void 0:_SmallSize_parameters.docs,source:{originalSource:"{\n  args: {\n    size: 'sm',\n    label: 'Team'\n  },\n  parameters: {\n    docs: {\n      description: {\n        story: 'ClocActiveTeamSelector with small size variant (size=\"sm\"). Ideal for compact layouts, toolbars, or dense interfaces while maintaining full team selection functionality and member count display.'\n      }\n    }\n  }\n}",...null===(_SmallSize_parameters1=SmallSize.parameters)||void 0===_SmallSize_parameters1||null===(_SmallSize_parameters_docs=_SmallSize_parameters1.docs)||void 0===_SmallSize_parameters_docs?void 0:_SmallSize_parameters_docs.source},description:{story:"Small-sized team selector optimized for compact layouts.\r\nMaintains full functionality while taking up less space.",...null===(_SmallSize_parameters2=SmallSize.parameters)||void 0===_SmallSize_parameters2||null===(_SmallSize_parameters_docs1=_SmallSize_parameters2.docs)||void 0===_SmallSize_parameters_docs1?void 0:_SmallSize_parameters_docs1.description}}},LargeSize.parameters={...LargeSize.parameters,docs:{...null===(_LargeSize_parameters=LargeSize.parameters)||void 0===_LargeSize_parameters?void 0:_LargeSize_parameters.docs,source:{originalSource:"{\n  args: {\n    size: 'lg',\n    label: 'Choose Your Team'\n  },\n  parameters: {\n    docs: {\n      description: {\n        story: 'ClocActiveTeamSelector with large size variant (size=\"lg\"). Perfect for prominent placement in main workflows, onboarding flows, or when team selection is a primary action.'\n      }\n    }\n  }\n}",...null===(_LargeSize_parameters1=LargeSize.parameters)||void 0===_LargeSize_parameters1||null===(_LargeSize_parameters_docs=_LargeSize_parameters1.docs)||void 0===_LargeSize_parameters_docs?void 0:_LargeSize_parameters_docs.source},description:{story:"Large-sized team selector for prominent placement.\r\nProvides enhanced visibility and easier interaction.",...null===(_LargeSize_parameters2=LargeSize.parameters)||void 0===_LargeSize_parameters2||null===(_LargeSize_parameters_docs1=_LargeSize_parameters2.docs)||void 0===_LargeSize_parameters_docs1?void 0:_LargeSize_parameters_docs1.description}}},CustomStyling.parameters={...CustomStyling.parameters,docs:{...null===(_CustomStyling_parameters=CustomStyling.parameters)||void 0===_CustomStyling_parameters?void 0:_CustomStyling_parameters.docs,source:{originalSource:"{\n  args: {\n    size: 'default',\n    label: 'Team',\n    className: 'border-purple-300 bg-purple-50 dark:border-purple-700 dark:bg-purple-950'\n  },\n  parameters: {\n    docs: {\n      description: {\n        story: 'ClocActiveTeamSelector with custom styling applied through the className prop. Features custom border and background colors while preserving all team selection functionality and member count display.'\n      }\n    }\n  }\n}",...null===(_CustomStyling_parameters1=CustomStyling.parameters)||void 0===_CustomStyling_parameters1||null===(_CustomStyling_parameters_docs=_CustomStyling_parameters1.docs)||void 0===_CustomStyling_parameters_docs?void 0:_CustomStyling_parameters_docs.source},description:{story:"Team selector with custom styling applied through className prop.\r\nDemonstrates visual customization while maintaining full functionality.",...null===(_CustomStyling_parameters2=CustomStyling.parameters)||void 0===_CustomStyling_parameters2||null===(_CustomStyling_parameters_docs1=_CustomStyling_parameters2.docs)||void 0===_CustomStyling_parameters_docs1?void 0:_CustomStyling_parameters_docs1.description}}};const __namedExportsOrder=["Default","WithLabel","SmallSize","LargeSize","CustomStyling"]}}]);