"use strict";(self.webpackChunk_cloc_storybook=self.webpackChunk_cloc_storybook||[]).push([[3459],{"./src/stories/inputs/global-selectors/ClocReportDatesRangePicker.stories.tsx":(__unused_webpack_module,__webpack_exports__,__webpack_require__)=>{__webpack_require__.r(__webpack_exports__),__webpack_require__.d(__webpack_exports__,{CustomStyling:()=>CustomStyling,Default:()=>Default,__namedExportsOrder:()=>__namedExportsOrder,default:()=>__WEBPACK_DEFAULT_EXPORT__});var _Default_parameters,_Default_parameters_docs,_Default_parameters1,_Default_parameters_docs1,_Default_parameters2,_CustomStyling_parameters,_CustomStyling_parameters_docs,_CustomStyling_parameters1,_CustomStyling_parameters_docs1,_CustomStyling_parameters2,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__=__webpack_require__("../../node_modules/next/dist/compiled/react/jsx-runtime.js");const __WEBPACK_DEFAULT_EXPORT__={title:"Inputs/Global Selectors/Cloc Report Dates Range Picker",component:__webpack_require__("../../packages/toolkit/atoms/dist/index.es.js").uFf,parameters:{layout:"centered",docs:{description:{component:'\nClocReportDatesRangePicker is a specialized global date range selector that provides comprehensive date range selection capabilities for report generation and data filtering. It wraps the ClocDateRangePicker component with global state management for report dates.\n\n### Key Capabilities\n\n- **Global Date Management**: Seamlessly integrates with global reportDates state for consistent date ranges across all reports\n- **Interactive Calendar**: Dual-month calendar interface for intuitive date range selection\n- **Theme Integration**: Automatically adapts visual styling to match the current application theme\n- **Visual Indicators**: Clear start and end date highlighting with custom colors and styling\n- **Date Formatting**: Human-readable date format display (e.g., "Jan 01, 2024 - Jan 07, 2024")\n- **Responsive Layout**: Adaptive design that works across different screen sizes and contexts\n\n### Technical Implementation\n\nThe component serves as a wrapper around ClocDateRangePicker, connecting it to the global reportDates state from ClocProvider context. It inherits all the functionality of the underlying date range picker while providing seamless integration with the global reporting system.\n\n### Integration with Reports\n\nThis component is designed to work seamlessly with all report components in the system, providing a consistent date range selection experience that automatically updates all connected reports and analytics when the date range changes.\n                '}}},argTypes:{},decorators:[Story=>(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("div",{style:{width:"400px",height:"300px"},children:(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(Story,{})})]},Default={args:{},parameters:{docs:{description:{story:"The default ClocReportDatesRangePicker component with full date range selection functionality. Displays an interactive calendar interface for selecting date ranges that automatically updates the global reportDates state for all connected reports and analytics."}}}},CustomStyling={args:{},parameters:{docs:{description:{story:"ClocReportDatesRangePicker demonstrating the date range selection interface. The component automatically inherits theme colors and styling from the ClocProvider context, providing consistent visual integration with the application theme."}}}};Default.parameters={...Default.parameters,docs:{...null===(_Default_parameters=Default.parameters)||void 0===_Default_parameters?void 0:_Default_parameters.docs,source:{originalSource:"{\n  args: {},\n  parameters: {\n    docs: {\n      description: {\n        story: 'The default ClocReportDatesRangePicker component with full date range selection functionality. Displays an interactive calendar interface for selecting date ranges that automatically updates the global reportDates state for all connected reports and analytics.'\n      }\n    }\n  }\n}",...null===(_Default_parameters1=Default.parameters)||void 0===_Default_parameters1||null===(_Default_parameters_docs=_Default_parameters1.docs)||void 0===_Default_parameters_docs?void 0:_Default_parameters_docs.source},description:{story:"Default report dates range picker with full functionality.\r\nShows the complete date range selection interface for reports.",...null===(_Default_parameters2=Default.parameters)||void 0===_Default_parameters2||null===(_Default_parameters_docs1=_Default_parameters2.docs)||void 0===_Default_parameters_docs1?void 0:_Default_parameters_docs1.description}}},CustomStyling.parameters={...CustomStyling.parameters,docs:{...null===(_CustomStyling_parameters=CustomStyling.parameters)||void 0===_CustomStyling_parameters?void 0:_CustomStyling_parameters.docs,source:{originalSource:"{\n  args: {},\n  parameters: {\n    docs: {\n      description: {\n        story: 'ClocReportDatesRangePicker demonstrating the date range selection interface. The component automatically inherits theme colors and styling from the ClocProvider context, providing consistent visual integration with the application theme.'\n      }\n    }\n  }\n}",...null===(_CustomStyling_parameters1=CustomStyling.parameters)||void 0===_CustomStyling_parameters1||null===(_CustomStyling_parameters_docs=_CustomStyling_parameters1.docs)||void 0===_CustomStyling_parameters_docs?void 0:_CustomStyling_parameters_docs.source},description:{story:"Report dates range picker demonstrating the selection interface.\r\nShows how the component appears during date range selection.",...null===(_CustomStyling_parameters2=CustomStyling.parameters)||void 0===_CustomStyling_parameters2||null===(_CustomStyling_parameters_docs1=_CustomStyling_parameters2.docs)||void 0===_CustomStyling_parameters_docs1?void 0:_CustomStyling_parameters_docs1.description}}};const __namedExportsOrder=["Default","CustomStyling"]}}]);