"use strict";(self.webpackChunk_cloc_storybook=self.webpackChunk_cloc_storybook||[]).push([[336],{"./src/stories/inputs/timer-selects/ClocTimerProjectSelect.stories.tsx":(__unused_webpack_module,__webpack_exports__,__webpack_require__)=>{__webpack_require__.r(__webpack_exports__),__webpack_require__.d(__webpack_exports__,{Default:()=>Default,LargeSize:()=>LargeSize,SmallSize:()=>SmallSize,__namedExportsOrder:()=>__namedExportsOrder,default:()=>__WEBPACK_DEFAULT_EXPORT__});var _Default_parameters,_Default_parameters_docs,_Default_parameters1,_Default_parameters_docs1,_Default_parameters2,_SmallSize_parameters,_SmallSize_parameters_docs,_SmallSize_parameters1,_SmallSize_parameters_docs1,_SmallSize_parameters2,_LargeSize_parameters,_LargeSize_parameters_docs,_LargeSize_parameters1,_LargeSize_parameters_docs1,_LargeSize_parameters2,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__=__webpack_require__("../../node_modules/next/dist/compiled/react/jsx-runtime.js");const __WEBPACK_DEFAULT_EXPORT__={title:"Inputs/Timer Selects/Cloc Timer Project Select",component:__webpack_require__("../../packages/toolkit/atoms/dist/index.es.js").mPM,parameters:{layout:"centered",docs:{description:{component:"\nClocTimerProjectSelect is a specialized input component designed for project selection within timer workflows. It provides seamless integration with the timer system, automatically handling disabled states when the timer is active and offering comprehensive project management capabilities.\n\n### Key Capabilities\n\n- **Project Management**: Displays all available organization projects with clear selection interface\n- **Timer State Awareness**: Automatically disables selection when timer is running to prevent mid-session changes\n- **Tooltip Integration**: Shows informative warning messages when selection is disabled\n- **Loading Handling**: Provides visual feedback during project data fetching\n- **Size Flexibility**: Supports multiple size variants for different UI contexts\n- **State Synchronization**: Seamlessly updates global timer state with selected project\n\n### Technical Implementation\n\nThe component integrates deeply with the ClocProvider context to access organization projects, timer state, and loading indicators. It uses the currentClocState management system to ensure timer configuration remains consistent across the application.\n                "}}},argTypes:{size:{control:"select",options:["default","sm","lg"],description:"Size variant of the select component",table:{type:{summary:"'default' | 'sm' | 'lg' | null"},defaultValue:{summary:"'default'"}}}},decorators:[Story=>(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("div",{style:{width:"300px",height:"200px"},children:(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(Story,{})})]},Default={args:{size:"default"},parameters:{docs:{description:{story:"The default ClocTimerProjectSelect component with standard sizing. Displays the project selection dropdown with internationalized label and placeholder text, loading states, and timer integration."}}}},SmallSize={args:{size:"sm"},parameters:{docs:{description:{story:'ClocTimerProjectSelect with small size variant (size="sm"). Ideal for compact layouts, toolbars, or dense interfaces while maintaining full project selection functionality.'}}}},LargeSize={args:{size:"lg"},parameters:{docs:{description:{story:'ClocTimerProjectSelect with large size variant (size="lg"). Perfect for prominent placement in main workflows, onboarding flows, or when project selection is a primary action.'}}}};Default.parameters={...Default.parameters,docs:{...null===(_Default_parameters=Default.parameters)||void 0===_Default_parameters?void 0:_Default_parameters.docs,source:{originalSource:"{\n  args: {\n    size: 'default'\n  },\n  parameters: {\n    docs: {\n      description: {\n        story: 'The default ClocTimerProjectSelect component with standard sizing. Displays the project selection dropdown with internationalized label and placeholder text, loading states, and timer integration.'\n      }\n    }\n  }\n}",...null===(_Default_parameters1=Default.parameters)||void 0===_Default_parameters1||null===(_Default_parameters_docs=_Default_parameters1.docs)||void 0===_Default_parameters_docs?void 0:_Default_parameters_docs.source},description:{story:"Default project select component with standard sizing and functionality.\r\nShows the complete project selection interface with label and dropdown.",...null===(_Default_parameters2=Default.parameters)||void 0===_Default_parameters2||null===(_Default_parameters_docs1=_Default_parameters2.docs)||void 0===_Default_parameters_docs1?void 0:_Default_parameters_docs1.description}}},SmallSize.parameters={...SmallSize.parameters,docs:{...null===(_SmallSize_parameters=SmallSize.parameters)||void 0===_SmallSize_parameters?void 0:_SmallSize_parameters.docs,source:{originalSource:"{\n  args: {\n    size: 'sm'\n  },\n  parameters: {\n    docs: {\n      description: {\n        story: 'ClocTimerProjectSelect with small size variant (size=\"sm\"). Ideal for compact layouts, toolbars, or dense interfaces while maintaining full project selection functionality.'\n      }\n    }\n  }\n}",...null===(_SmallSize_parameters1=SmallSize.parameters)||void 0===_SmallSize_parameters1||null===(_SmallSize_parameters_docs=_SmallSize_parameters1.docs)||void 0===_SmallSize_parameters_docs?void 0:_SmallSize_parameters_docs.source},description:{story:"Small-sized project select component optimized for compact layouts.\r\nMaintains full functionality while taking up less vertical space.",...null===(_SmallSize_parameters2=SmallSize.parameters)||void 0===_SmallSize_parameters2||null===(_SmallSize_parameters_docs1=_SmallSize_parameters2.docs)||void 0===_SmallSize_parameters_docs1?void 0:_SmallSize_parameters_docs1.description}}},LargeSize.parameters={...LargeSize.parameters,docs:{...null===(_LargeSize_parameters=LargeSize.parameters)||void 0===_LargeSize_parameters?void 0:_LargeSize_parameters.docs,source:{originalSource:"{\n  args: {\n    size: 'lg'\n  },\n  parameters: {\n    docs: {\n      description: {\n        story: 'ClocTimerProjectSelect with large size variant (size=\"lg\"). Perfect for prominent placement in main workflows, onboarding flows, or when project selection is a primary action.'\n      }\n    }\n  }\n}",...null===(_LargeSize_parameters1=LargeSize.parameters)||void 0===_LargeSize_parameters1||null===(_LargeSize_parameters_docs=_LargeSize_parameters1.docs)||void 0===_LargeSize_parameters_docs?void 0:_LargeSize_parameters_docs.source},description:{story:"Large-sized project select component for prominent placement.\r\nProvides enhanced visibility and easier interaction for primary workflows.",...null===(_LargeSize_parameters2=LargeSize.parameters)||void 0===_LargeSize_parameters2||null===(_LargeSize_parameters_docs1=_LargeSize_parameters2.docs)||void 0===_LargeSize_parameters_docs1?void 0:_LargeSize_parameters_docs1.description}}};const __namedExportsOrder=["Default","SmallSize","LargeSize"]}}]);