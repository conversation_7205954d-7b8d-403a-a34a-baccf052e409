"use strict";(self.webpackChunk_cloc_storybook=self.webpackChunk_cloc_storybook||[]).push([[6386],{"./src/stories/report-displayers/activity-displayers/ClocDailyActivityDisplayer.stories.tsx":(__unused_webpack_module,__webpack_exports__,__webpack_require__)=>{__webpack_require__.r(__webpack_exports__),__webpack_require__.d(__webpack_exports__,{CustomStyling:()=>CustomStyling,Default:()=>Default,WithoutProgress:()=>WithoutProgress,__namedExportsOrder:()=>__namedExportsOrder,default:()=>__WEBPACK_DEFAULT_EXPORT__});var _Default_parameters,_Default_parameters_docs,_Default_parameters1,_Default_parameters_docs1,_Default_parameters2,_WithoutProgress_parameters,_WithoutProgress_parameters_docs,_WithoutProgress_parameters1,_WithoutProgress_parameters_docs1,_WithoutProgress_parameters2,_CustomStyling_parameters,_CustomStyling_parameters_docs,_CustomStyling_parameters1,_CustomStyling_parameters_docs1,_CustomStyling_parameters2,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__=__webpack_require__("../../node_modules/next/dist/compiled/react/jsx-runtime.js");const __WEBPACK_DEFAULT_EXPORT__={title:"Report Displayers/Activity Displayers/Cloc Daily Activity Displayer",component:__webpack_require__("../../packages/toolkit/atoms/dist/index.es.js").hOs,parameters:{layout:"centered",docs:{description:{component:"\nClocDailyActivityDisplayer is a specialized display component that shows today's activity percentage with optional progress visualization. It provides real-time insights into daily productivity metrics with comprehensive loading states and theme integration.\n\n### Key Capabilities\n\n- **Activity Percentage Display**: Shows today's activity as a percentage value with clear visual presentation\n- **Progress Bar Integration**: Optional progress bar that visually represents activity completion level\n- **Loading State Management**: Displays overlay spinner during data fetching for better user experience\n- **Theme Compatibility**: Seamless integration with dark and light themes using proper color schemes\n- **Internationalization**: Full i18n support with localized labels and text content\n- **Responsive Design**: Card-based layout that adapts to different screen sizes and contexts\n\n### Technical Implementation\n\nThe component uses the ClocActivityDisplayer base component with today's activity data from the ClocProvider context. It automatically handles loading states and provides internationalized labels through react-i18next integration.\n\n### Data Flow\n\nThe component retrieves today's activity percentage from `statisticsCounts.todayActivities` and displays it with the label from `t('REPORT.today_activity')`. Loading states are managed through `statisticsCountsLoading` to provide visual feedback during data updates.\n                "}}},argTypes:{showProgress:{control:"boolean",description:"Whether to show the progress bar below the activity percentage",table:{type:{summary:"boolean"},defaultValue:{summary:"true"}}},className:{control:"text",description:"Additional CSS classes to apply to the card component",table:{type:{summary:"string"},defaultValue:{summary:"undefined"}}}},decorators:[Story=>(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("div",{style:{width:"200px",height:"150px"},children:(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(Story,{})})]},Default={args:{showProgress:!0},parameters:{docs:{description:{story:"The default ClocDailyActivityDisplayer component with progress bar enabled. Displays today's activity percentage with internationalized label, loading states, and visual progress indicator."}}}},WithoutProgress={args:{showProgress:!1},parameters:{docs:{description:{story:"ClocDailyActivityDisplayer with progress bar disabled (showProgress=false). Ideal for minimal layouts where only the activity percentage value is needed without visual progress indication."}}}},CustomStyling={args:{showProgress:!0,className:"border-blue-300 bg-blue-50 dark:border-blue-700 dark:bg-blue-950"},parameters:{docs:{description:{story:"ClocDailyActivityDisplayer with custom styling applied through the className prop. Features custom border and background colors while preserving all activity display functionality and progress visualization."}}}};Default.parameters={...Default.parameters,docs:{...null===(_Default_parameters=Default.parameters)||void 0===_Default_parameters?void 0:_Default_parameters.docs,source:{originalSource:"{\n  args: {\n    showProgress: true\n  },\n  parameters: {\n    docs: {\n      description: {\n        story: 'The default ClocDailyActivityDisplayer component with progress bar enabled. Displays today\\'s activity percentage with internationalized label, loading states, and visual progress indicator.'\n      }\n    }\n  }\n}",...null===(_Default_parameters1=Default.parameters)||void 0===_Default_parameters1||null===(_Default_parameters_docs=_Default_parameters1.docs)||void 0===_Default_parameters_docs?void 0:_Default_parameters_docs.source},description:{story:"Default daily activity displayer with progress bar and standard styling.\r\nShows today's activity percentage with visual progress indicator.",...null===(_Default_parameters2=Default.parameters)||void 0===_Default_parameters2||null===(_Default_parameters_docs1=_Default_parameters2.docs)||void 0===_Default_parameters_docs1?void 0:_Default_parameters_docs1.description}}},WithoutProgress.parameters={...WithoutProgress.parameters,docs:{...null===(_WithoutProgress_parameters=WithoutProgress.parameters)||void 0===_WithoutProgress_parameters?void 0:_WithoutProgress_parameters.docs,source:{originalSource:"{\n  args: {\n    showProgress: false\n  },\n  parameters: {\n    docs: {\n      description: {\n        story: 'ClocDailyActivityDisplayer with progress bar disabled (showProgress=false). Ideal for minimal layouts where only the activity percentage value is needed without visual progress indication.'\n      }\n    }\n  }\n}",...null===(_WithoutProgress_parameters1=WithoutProgress.parameters)||void 0===_WithoutProgress_parameters1||null===(_WithoutProgress_parameters_docs=_WithoutProgress_parameters1.docs)||void 0===_WithoutProgress_parameters_docs?void 0:_WithoutProgress_parameters_docs.source},description:{story:"Daily activity displayer without progress bar for minimal display.\r\nShows only the activity percentage without visual progress indicator.",...null===(_WithoutProgress_parameters2=WithoutProgress.parameters)||void 0===_WithoutProgress_parameters2||null===(_WithoutProgress_parameters_docs1=_WithoutProgress_parameters2.docs)||void 0===_WithoutProgress_parameters_docs1?void 0:_WithoutProgress_parameters_docs1.description}}},CustomStyling.parameters={...CustomStyling.parameters,docs:{...null===(_CustomStyling_parameters=CustomStyling.parameters)||void 0===_CustomStyling_parameters?void 0:_CustomStyling_parameters.docs,source:{originalSource:"{\n  args: {\n    showProgress: true,\n    className: 'border-blue-300 bg-blue-50 dark:border-blue-700 dark:bg-blue-950'\n  },\n  parameters: {\n    docs: {\n      description: {\n        story: 'ClocDailyActivityDisplayer with custom styling applied through the className prop. Features custom border and background colors while preserving all activity display functionality and progress visualization.'\n      }\n    }\n  }\n}",...null===(_CustomStyling_parameters1=CustomStyling.parameters)||void 0===_CustomStyling_parameters1||null===(_CustomStyling_parameters_docs=_CustomStyling_parameters1.docs)||void 0===_CustomStyling_parameters_docs?void 0:_CustomStyling_parameters_docs.source},description:{story:"Daily activity displayer with custom styling applied through className prop.\r\nDemonstrates visual customization while maintaining full functionality.",...null===(_CustomStyling_parameters2=CustomStyling.parameters)||void 0===_CustomStyling_parameters2||null===(_CustomStyling_parameters_docs1=_CustomStyling_parameters2.docs)||void 0===_CustomStyling_parameters_docs1?void 0:_CustomStyling_parameters_docs1.description}}};const __namedExportsOrder=["Default","WithoutProgress","CustomStyling"]}}]);