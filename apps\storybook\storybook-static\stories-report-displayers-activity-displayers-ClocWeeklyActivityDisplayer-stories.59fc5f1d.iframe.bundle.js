"use strict";(self.webpackChunk_cloc_storybook=self.webpackChunk_cloc_storybook||[]).push([[6366],{"./src/stories/report-displayers/activity-displayers/ClocWeeklyActivityDisplayer.stories.tsx":(__unused_webpack_module,__webpack_exports__,__webpack_require__)=>{__webpack_require__.r(__webpack_exports__),__webpack_require__.d(__webpack_exports__,{CustomStyling:()=>CustomStyling,Default:()=>Default,WithoutProgress:()=>WithoutProgress,__namedExportsOrder:()=>__namedExportsOrder,default:()=>__WEBPACK_DEFAULT_EXPORT__});var _Default_parameters,_Default_parameters_docs,_Default_parameters1,_Default_parameters_docs1,_Default_parameters2,_WithoutProgress_parameters,_WithoutProgress_parameters_docs,_WithoutProgress_parameters1,_WithoutProgress_parameters_docs1,_WithoutProgress_parameters2,_CustomStyling_parameters,_CustomStyling_parameters_docs,_CustomStyling_parameters1,_CustomStyling_parameters_docs1,_CustomStyling_parameters2,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__=__webpack_require__("../../node_modules/next/dist/compiled/react/jsx-runtime.js");const __WEBPACK_DEFAULT_EXPORT__={title:"Report Displayers/Activity Displayers/Cloc Weekly Activity Displayer",component:__webpack_require__("../../packages/toolkit/atoms/dist/index.es.js").ry8,parameters:{layout:"centered",docs:{description:{component:'\nClocWeeklyActivityDisplayer is a sophisticated display component that shows weekly activity percentage with intelligent date range labeling. It automatically adapts its label based on whether the current report dates match the current week or represent a custom period.\n\n### Key Capabilities\n\n- **Weekly Activity Display**: Shows week\'s activity as a percentage value with clear visual presentation\n- **Intelligent Labeling**: Automatically switches between "Week Activity" and "Activity Over Period" based on selected date range\n- **Date Range Logic**: Uses `getWeekStartAndEnd()` and `areDatesEqual()` to determine appropriate labeling\n- **Progress Bar Integration**: Optional progress bar that visually represents activity completion level\n- **Loading State Management**: Displays overlay spinner during data fetching for better user experience\n- **Theme Compatibility**: Seamless integration with dark and light themes using proper color schemes\n- **Internationalization**: Full i18n support with localized labels for different contexts\n\n### Smart Labeling System\n\nThe component intelligently determines the appropriate label:\n- **Current Week**: Shows "Week Activity" when report dates match current week start/end\n- **Custom Period**: Shows "Activity Over Period" when report dates represent a different time range\n\n### Technical Implementation\n\nThe component uses the ClocActivityDisplayer base component with weekly activity data from the ClocProvider context. It includes sophisticated date comparison logic to provide contextually appropriate labels.\n                '}}},argTypes:{showProgress:{control:"boolean",description:"Whether to show the progress bar below the activity percentage",table:{type:{summary:"boolean"},defaultValue:{summary:"true"}}},className:{control:"text",description:"Additional CSS classes to apply to the card component",table:{type:{summary:"string"},defaultValue:{summary:"undefined"}}}},decorators:[Story=>(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("div",{style:{width:"200px",height:"150px"},children:(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(Story,{})})]},Default={args:{showProgress:!0},parameters:{docs:{description:{story:'The default ClocWeeklyActivityDisplayer component with progress bar enabled. Displays week\'s activity percentage with intelligent labeling that switches between "Week Activity" and "Activity Over Period" based on selected report dates.'}}}},WithoutProgress={args:{showProgress:!1},parameters:{docs:{description:{story:"ClocWeeklyActivityDisplayer with progress bar disabled (showProgress=false). Ideal for minimal layouts where only the activity percentage value is needed without visual progress indication, while maintaining smart date labeling."}}}},CustomStyling={args:{showProgress:!0,className:"border-green-300 bg-green-50 dark:border-green-700 dark:bg-green-950"},parameters:{docs:{description:{story:"ClocWeeklyActivityDisplayer with custom styling applied through the className prop. Features custom border and background colors while preserving all activity display functionality, progress visualization, and intelligent date range labeling."}}}};Default.parameters={...Default.parameters,docs:{...null===(_Default_parameters=Default.parameters)||void 0===_Default_parameters?void 0:_Default_parameters.docs,source:{originalSource:'{\n  args: {\n    showProgress: true\n  },\n  parameters: {\n    docs: {\n      description: {\n        story: \'The default ClocWeeklyActivityDisplayer component with progress bar enabled. Displays week\\\'s activity percentage with intelligent labeling that switches between "Week Activity" and "Activity Over Period" based on selected report dates.\'\n      }\n    }\n  }\n}',...null===(_Default_parameters1=Default.parameters)||void 0===_Default_parameters1||null===(_Default_parameters_docs=_Default_parameters1.docs)||void 0===_Default_parameters_docs?void 0:_Default_parameters_docs.source},description:{story:"Default weekly activity displayer with progress bar and intelligent labeling.\r\nShows week's activity percentage with smart date range detection.",...null===(_Default_parameters2=Default.parameters)||void 0===_Default_parameters2||null===(_Default_parameters_docs1=_Default_parameters2.docs)||void 0===_Default_parameters_docs1?void 0:_Default_parameters_docs1.description}}},WithoutProgress.parameters={...WithoutProgress.parameters,docs:{...null===(_WithoutProgress_parameters=WithoutProgress.parameters)||void 0===_WithoutProgress_parameters?void 0:_WithoutProgress_parameters.docs,source:{originalSource:"{\n  args: {\n    showProgress: false\n  },\n  parameters: {\n    docs: {\n      description: {\n        story: 'ClocWeeklyActivityDisplayer with progress bar disabled (showProgress=false). Ideal for minimal layouts where only the activity percentage value is needed without visual progress indication, while maintaining smart date labeling.'\n      }\n    }\n  }\n}",...null===(_WithoutProgress_parameters1=WithoutProgress.parameters)||void 0===_WithoutProgress_parameters1||null===(_WithoutProgress_parameters_docs=_WithoutProgress_parameters1.docs)||void 0===_WithoutProgress_parameters_docs?void 0:_WithoutProgress_parameters_docs.source},description:{story:"Weekly activity displayer without progress bar for minimal display.\r\nShows only the activity percentage without visual progress indicator.",...null===(_WithoutProgress_parameters2=WithoutProgress.parameters)||void 0===_WithoutProgress_parameters2||null===(_WithoutProgress_parameters_docs1=_WithoutProgress_parameters2.docs)||void 0===_WithoutProgress_parameters_docs1?void 0:_WithoutProgress_parameters_docs1.description}}},CustomStyling.parameters={...CustomStyling.parameters,docs:{...null===(_CustomStyling_parameters=CustomStyling.parameters)||void 0===_CustomStyling_parameters?void 0:_CustomStyling_parameters.docs,source:{originalSource:"{\n  args: {\n    showProgress: true,\n    className: 'border-green-300 bg-green-50 dark:border-green-700 dark:bg-green-950'\n  },\n  parameters: {\n    docs: {\n      description: {\n        story: 'ClocWeeklyActivityDisplayer with custom styling applied through the className prop. Features custom border and background colors while preserving all activity display functionality, progress visualization, and intelligent date range labeling.'\n      }\n    }\n  }\n}",...null===(_CustomStyling_parameters1=CustomStyling.parameters)||void 0===_CustomStyling_parameters1||null===(_CustomStyling_parameters_docs=_CustomStyling_parameters1.docs)||void 0===_CustomStyling_parameters_docs?void 0:_CustomStyling_parameters_docs.source},description:{story:"Weekly activity displayer with custom styling applied through className prop.\r\nDemonstrates visual customization while maintaining intelligent labeling.",...null===(_CustomStyling_parameters2=CustomStyling.parameters)||void 0===_CustomStyling_parameters2||null===(_CustomStyling_parameters_docs1=_CustomStyling_parameters2.docs)||void 0===_CustomStyling_parameters_docs1?void 0:_CustomStyling_parameters_docs1.description}}};const __namedExportsOrder=["Default","WithoutProgress","CustomStyling"]}}]);