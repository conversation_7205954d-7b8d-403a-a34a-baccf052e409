"use strict";(self.webpackChunk_cloc_storybook=self.webpackChunk_cloc_storybook||[]).push([[5525],{"./src/stories/report-displayers/project-displayers/ClocWorkedProjectDisplayer.stories.tsx":(__unused_webpack_module,__webpack_exports__,__webpack_require__)=>{__webpack_require__.r(__webpack_exports__),__webpack_require__.d(__webpack_exports__,{CustomStyling:()=>CustomStyling,Default:()=>Default,WithoutProgress:()=>WithoutProgress,__namedExportsOrder:()=>__namedExportsOrder,default:()=>__WEBPACK_DEFAULT_EXPORT__});var _Default_parameters,_Default_parameters_docs,_Default_parameters1,_Default_parameters_docs1,_Default_parameters2,_WithoutProgress_parameters,_WithoutProgress_parameters_docs,_WithoutProgress_parameters1,_WithoutProgress_parameters_docs1,_WithoutProgress_parameters2,_CustomStyling_parameters,_CustomStyling_parameters_docs,_CustomStyling_parameters1,_CustomStyling_parameters_docs1,_CustomStyling_parameters2,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__=__webpack_require__("../../node_modules/next/dist/compiled/react/jsx-runtime.js");const __WEBPACK_DEFAULT_EXPORT__={title:"Report Displayers/Project Displayers/Cloc Worked Project Displayer",component:__webpack_require__("../../packages/toolkit/atoms/dist/index.es.js").GFK,parameters:{layout:"centered",docs:{description:{component:"\nClocWorkedProjectDisplayer is a specialized display component that shows the count of projects worked on with progress visualization based on total available projects. It provides insights into project engagement and participation levels.\n\n### Key Capabilities\n\n- **Project Count Display**: Shows the number of projects that have been worked on with clear numerical presentation\n- **Progress Calculation**: Automatically calculates progress percentage based on worked projects vs total organization projects\n- **Progress Bar Integration**: Optional progress bar that visually represents project engagement level\n- **Loading State Management**: Displays overlay spinner during data fetching for better user experience\n- **Theme Compatibility**: Seamless integration with dark and light themes using proper color schemes\n- **Internationalization**: Full i18n support with localized labels from the common translation namespace\n- **Responsive Design**: Card-based layout that adapts to different screen sizes and contexts\n\n### Progress Logic\n\nThe component calculates progress as: (worked projects / total organization projects) × 100\nThis provides a meaningful percentage that represents project engagement across the organization's available projects.\n\n### Technical Implementation\n\nThe component uses the ClocProjectDisplayer base component with project count data from the ClocProvider context. It automatically handles the progress calculation and provides internationalized labels through react-i18next integration.\n\n### Data Flow\n\nThe component retrieves worked project count from `statisticsCounts.projectsCount` and total projects from `organizationProjects.length`, then displays the count with the label from `t('COMMON.project')`.\n                "}}},argTypes:{showProgress:{control:"boolean",description:"Whether to show the progress bar below the project count",table:{type:{summary:"boolean"},defaultValue:{summary:"true"}}},className:{control:"text",description:"Additional CSS classes to apply to the card component",table:{type:{summary:"string"},defaultValue:{summary:"undefined"}}}},decorators:[Story=>(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("div",{style:{width:"200px",height:"150px"},children:(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(Story,{})})]},Default={args:{showProgress:!0},parameters:{docs:{description:{story:"The default ClocWorkedProjectDisplayer component with progress bar enabled. Displays the count of worked projects with internationalized label, loading states, and visual progress indicator based on total organization projects."}}}},WithoutProgress={args:{showProgress:!1},parameters:{docs:{description:{story:"ClocWorkedProjectDisplayer with progress bar disabled (showProgress=false). Ideal for minimal layouts where only the project count value is needed without visual progress indication."}}}},CustomStyling={args:{showProgress:!0,className:"border-purple-300 bg-purple-50 dark:border-purple-700 dark:bg-purple-950"},parameters:{docs:{description:{story:"ClocWorkedProjectDisplayer with custom styling applied through the className prop. Features custom border and background colors while preserving all project display functionality and progress visualization."}}}};Default.parameters={...Default.parameters,docs:{...null===(_Default_parameters=Default.parameters)||void 0===_Default_parameters?void 0:_Default_parameters.docs,source:{originalSource:"{\n  args: {\n    showProgress: true\n  },\n  parameters: {\n    docs: {\n      description: {\n        story: 'The default ClocWorkedProjectDisplayer component with progress bar enabled. Displays the count of worked projects with internationalized label, loading states, and visual progress indicator based on total organization projects.'\n      }\n    }\n  }\n}",...null===(_Default_parameters1=Default.parameters)||void 0===_Default_parameters1||null===(_Default_parameters_docs=_Default_parameters1.docs)||void 0===_Default_parameters_docs?void 0:_Default_parameters_docs.source},description:{story:"Default worked project displayer with progress bar and standard styling.\r\nShows worked project count with visual progress indicator.",...null===(_Default_parameters2=Default.parameters)||void 0===_Default_parameters2||null===(_Default_parameters_docs1=_Default_parameters2.docs)||void 0===_Default_parameters_docs1?void 0:_Default_parameters_docs1.description}}},WithoutProgress.parameters={...WithoutProgress.parameters,docs:{...null===(_WithoutProgress_parameters=WithoutProgress.parameters)||void 0===_WithoutProgress_parameters?void 0:_WithoutProgress_parameters.docs,source:{originalSource:"{\n  args: {\n    showProgress: false\n  },\n  parameters: {\n    docs: {\n      description: {\n        story: 'ClocWorkedProjectDisplayer with progress bar disabled (showProgress=false). Ideal for minimal layouts where only the project count value is needed without visual progress indication.'\n      }\n    }\n  }\n}",...null===(_WithoutProgress_parameters1=WithoutProgress.parameters)||void 0===_WithoutProgress_parameters1||null===(_WithoutProgress_parameters_docs=_WithoutProgress_parameters1.docs)||void 0===_WithoutProgress_parameters_docs?void 0:_WithoutProgress_parameters_docs.source},description:{story:"Worked project displayer without progress bar for minimal display.\r\nShows only the project count without visual progress indicator.",...null===(_WithoutProgress_parameters2=WithoutProgress.parameters)||void 0===_WithoutProgress_parameters2||null===(_WithoutProgress_parameters_docs1=_WithoutProgress_parameters2.docs)||void 0===_WithoutProgress_parameters_docs1?void 0:_WithoutProgress_parameters_docs1.description}}},CustomStyling.parameters={...CustomStyling.parameters,docs:{...null===(_CustomStyling_parameters=CustomStyling.parameters)||void 0===_CustomStyling_parameters?void 0:_CustomStyling_parameters.docs,source:{originalSource:"{\n  args: {\n    showProgress: true,\n    className: 'border-purple-300 bg-purple-50 dark:border-purple-700 dark:bg-purple-950'\n  },\n  parameters: {\n    docs: {\n      description: {\n        story: 'ClocWorkedProjectDisplayer with custom styling applied through the className prop. Features custom border and background colors while preserving all project display functionality and progress visualization.'\n      }\n    }\n  }\n}",...null===(_CustomStyling_parameters1=CustomStyling.parameters)||void 0===_CustomStyling_parameters1||null===(_CustomStyling_parameters_docs=_CustomStyling_parameters1.docs)||void 0===_CustomStyling_parameters_docs?void 0:_CustomStyling_parameters_docs.source},description:{story:"Worked project displayer with custom styling applied through className prop.\r\nDemonstrates visual customization while maintaining full functionality.",...null===(_CustomStyling_parameters2=CustomStyling.parameters)||void 0===_CustomStyling_parameters2||null===(_CustomStyling_parameters_docs1=_CustomStyling_parameters2.docs)||void 0===_CustomStyling_parameters_docs1?void 0:_CustomStyling_parameters_docs1.description}}};const __namedExportsOrder=["Default","WithoutProgress","CustomStyling"]}}]);