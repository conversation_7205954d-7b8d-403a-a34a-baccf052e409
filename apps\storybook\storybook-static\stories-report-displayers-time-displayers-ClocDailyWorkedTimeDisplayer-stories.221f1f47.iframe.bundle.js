"use strict";(self.webpackChunk_cloc_storybook=self.webpackChunk_cloc_storybook||[]).push([[4154],{"./src/stories/report-displayers/time-displayers/ClocDailyWorkedTimeDisplayer.stories.tsx":(__unused_webpack_module,__webpack_exports__,__webpack_require__)=>{__webpack_require__.r(__webpack_exports__),__webpack_require__.d(__webpack_exports__,{CustomStyling:()=>CustomStyling,Default:()=>Default,WithoutProgress:()=>WithoutProgress,__namedExportsOrder:()=>__namedExportsOrder,default:()=>__WEBPACK_DEFAULT_EXPORT__});var _Default_parameters,_Default_parameters_docs,_Default_parameters1,_Default_parameters_docs1,_Default_parameters2,_WithoutProgress_parameters,_WithoutProgress_parameters_docs,_WithoutProgress_parameters1,_WithoutProgress_parameters_docs1,_WithoutProgress_parameters2,_CustomStyling_parameters,_CustomStyling_parameters_docs,_CustomStyling_parameters1,_CustomStyling_parameters_docs1,_CustomStyling_parameters2,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__=__webpack_require__("../../node_modules/next/dist/compiled/react/jsx-runtime.js");const __WEBPACK_DEFAULT_EXPORT__={title:"Report Displayers/Time Displayers/Cloc Daily Worked Time Displayer",component:__webpack_require__("../../packages/toolkit/atoms/dist/index.es.js").pQE,parameters:{layout:"centered",docs:{description:{component:"\nClocDailyWorkedTimeDisplayer is a specialized display component that shows today's worked time with progress visualization against a standard 8-hour workday target. It provides real-time insights into daily time tracking progress with comprehensive formatting and theme integration.\n\n### Key Capabilities\n\n- **Formatted Time Display**: Shows today's worked time using the `formatTime()` utility for human-readable format (e.g., \"2h 30m\")\n- **8-Hour Target Progress**: Automatically calculates progress percentage against standard 8-hour workday (28,800 seconds)\n- **Progress Bar Integration**: Optional progress bar that visually represents daily time completion level\n- **Loading State Management**: Displays overlay spinner during data fetching for better user experience\n- **Theme Compatibility**: Seamless integration with dark and light themes using proper color schemes\n- **Internationalization**: Full i18n support with localized labels from the report translation namespace\n- **Responsive Design**: Card-based layout that adapts to different screen sizes and contexts\n\n### Progress Calculation\n\nThe component calculates progress as: (today's worked time in seconds / 28,800 seconds) × 100\nThis provides a meaningful percentage that represents daily progress toward a standard 8-hour workday target.\n\n### Technical Implementation\n\nThe component uses the ClocReportDisplayer base component with today's duration data from the ClocProvider context. It automatically handles time formatting and progress calculation with a fixed 8-hour maximum work target.\n\n### Data Flow\n\nThe component retrieves today's worked time from `statisticsCounts.todayDuration` and displays it with the label from `t('REPORT.worked_today')`. The time is formatted using the `formatTime()` utility for consistent presentation.\n                "}}},argTypes:{showProgress:{control:"boolean",description:"Whether to show the progress bar below the worked time",table:{type:{summary:"boolean"},defaultValue:{summary:"true"}}},className:{control:"text",description:"Additional CSS classes to apply to the card component",table:{type:{summary:"string"},defaultValue:{summary:"undefined"}}}},decorators:[Story=>(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("div",{style:{width:"200px",height:"150px"},children:(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(Story,{})})]},Default={args:{showProgress:!0},parameters:{docs:{description:{story:"The default ClocDailyWorkedTimeDisplayer component with progress bar enabled. Displays today's worked time in formatted format with internationalized label, loading states, and visual progress indicator against 8-hour workday target."}}}},WithoutProgress={args:{showProgress:!1},parameters:{docs:{description:{story:"ClocDailyWorkedTimeDisplayer with progress bar disabled (showProgress=false). Ideal for minimal layouts where only the worked time value is needed without visual progress indication against daily targets."}}}},CustomStyling={args:{showProgress:!0,className:"border-orange-300 bg-orange-50 dark:border-orange-700 dark:bg-orange-950"},parameters:{docs:{description:{story:"ClocDailyWorkedTimeDisplayer with custom styling applied through the className prop. Features custom border and background colors while preserving all time display functionality, formatting, and progress visualization against 8-hour target."}}}};Default.parameters={...Default.parameters,docs:{...null===(_Default_parameters=Default.parameters)||void 0===_Default_parameters?void 0:_Default_parameters.docs,source:{originalSource:"{\n  args: {\n    showProgress: true\n  },\n  parameters: {\n    docs: {\n      description: {\n        story: 'The default ClocDailyWorkedTimeDisplayer component with progress bar enabled. Displays today\\'s worked time in formatted format with internationalized label, loading states, and visual progress indicator against 8-hour workday target.'\n      }\n    }\n  }\n}",...null===(_Default_parameters1=Default.parameters)||void 0===_Default_parameters1||null===(_Default_parameters_docs=_Default_parameters1.docs)||void 0===_Default_parameters_docs?void 0:_Default_parameters_docs.source},description:{story:"Default daily worked time displayer with progress bar and 8-hour target.\r\nShows today's worked time with visual progress indicator.",...null===(_Default_parameters2=Default.parameters)||void 0===_Default_parameters2||null===(_Default_parameters_docs1=_Default_parameters2.docs)||void 0===_Default_parameters_docs1?void 0:_Default_parameters_docs1.description}}},WithoutProgress.parameters={...WithoutProgress.parameters,docs:{...null===(_WithoutProgress_parameters=WithoutProgress.parameters)||void 0===_WithoutProgress_parameters?void 0:_WithoutProgress_parameters.docs,source:{originalSource:"{\n  args: {\n    showProgress: false\n  },\n  parameters: {\n    docs: {\n      description: {\n        story: 'ClocDailyWorkedTimeDisplayer with progress bar disabled (showProgress=false). Ideal for minimal layouts where only the worked time value is needed without visual progress indication against daily targets.'\n      }\n    }\n  }\n}",...null===(_WithoutProgress_parameters1=WithoutProgress.parameters)||void 0===_WithoutProgress_parameters1||null===(_WithoutProgress_parameters_docs=_WithoutProgress_parameters1.docs)||void 0===_WithoutProgress_parameters_docs?void 0:_WithoutProgress_parameters_docs.source},description:{story:"Daily worked time displayer without progress bar for minimal display.\r\nShows only the worked time without visual progress indicator.",...null===(_WithoutProgress_parameters2=WithoutProgress.parameters)||void 0===_WithoutProgress_parameters2||null===(_WithoutProgress_parameters_docs1=_WithoutProgress_parameters2.docs)||void 0===_WithoutProgress_parameters_docs1?void 0:_WithoutProgress_parameters_docs1.description}}},CustomStyling.parameters={...CustomStyling.parameters,docs:{...null===(_CustomStyling_parameters=CustomStyling.parameters)||void 0===_CustomStyling_parameters?void 0:_CustomStyling_parameters.docs,source:{originalSource:"{\n  args: {\n    showProgress: true,\n    className: 'border-orange-300 bg-orange-50 dark:border-orange-700 dark:bg-orange-950'\n  },\n  parameters: {\n    docs: {\n      description: {\n        story: 'ClocDailyWorkedTimeDisplayer with custom styling applied through the className prop. Features custom border and background colors while preserving all time display functionality, formatting, and progress visualization against 8-hour target.'\n      }\n    }\n  }\n}",...null===(_CustomStyling_parameters1=CustomStyling.parameters)||void 0===_CustomStyling_parameters1||null===(_CustomStyling_parameters_docs=_CustomStyling_parameters1.docs)||void 0===_CustomStyling_parameters_docs?void 0:_CustomStyling_parameters_docs.source},description:{story:"Daily worked time displayer with custom styling applied through className prop.\r\nDemonstrates visual customization while maintaining time formatting and progress calculation.",...null===(_CustomStyling_parameters2=CustomStyling.parameters)||void 0===_CustomStyling_parameters2||null===(_CustomStyling_parameters_docs1=_CustomStyling_parameters2.docs)||void 0===_CustomStyling_parameters_docs1?void 0:_CustomStyling_parameters_docs1.description}}};const __namedExportsOrder=["Default","WithoutProgress","CustomStyling"]}}]);