"use strict";(self.webpackChunk_cloc_storybook=self.webpackChunk_cloc_storybook||[]).push([[4710],{"./src/stories/report-displayers/time-displayers/ClocWeeklyWorkedTimeDisplayer.stories.tsx":(__unused_webpack_module,__webpack_exports__,__webpack_require__)=>{__webpack_require__.r(__webpack_exports__),__webpack_require__.d(__webpack_exports__,{CustomStyling:()=>CustomStyling,Default:()=>Default,WithoutProgress:()=>WithoutProgress,__namedExportsOrder:()=>__namedExportsOrder,default:()=>__WEBPACK_DEFAULT_EXPORT__});var _Default_parameters,_Default_parameters_docs,_Default_parameters1,_Default_parameters_docs1,_Default_parameters2,_WithoutProgress_parameters,_WithoutProgress_parameters_docs,_WithoutProgress_parameters1,_WithoutProgress_parameters_docs1,_WithoutProgress_parameters2,_CustomStyling_parameters,_CustomStyling_parameters_docs,_CustomStyling_parameters1,_CustomStyling_parameters_docs1,_CustomStyling_parameters2,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__=__webpack_require__("../../node_modules/next/dist/compiled/react/jsx-runtime.js");const __WEBPACK_DEFAULT_EXPORT__={title:"Report Displayers/Time Displayers/Cloc Weekly Worked Time Displayer",component:__webpack_require__("../../packages/toolkit/atoms/dist/index.es.js").vSU,parameters:{layout:"centered",docs:{description:{component:'\nClocWeeklyWorkedTimeDisplayer is a sophisticated display component that shows weekly worked time with intelligent date range labeling and progress visualization against a standard 40-hour work week target. It automatically adapts its label based on whether the current report dates match the current week or represent a custom period.\n\n### Key Capabilities\n\n- **Formatted Time Display**: Shows week\'s worked time using the `formatTime()` utility for human-readable format (e.g., "25h 30m")\n- **40-Hour Target Progress**: Automatically calculates progress percentage against standard 40-hour work week (144,000 seconds)\n- **Intelligent Labeling**: Automatically switches between "Worked This Week" and "Worked Over Period" based on selected date range\n- **Date Range Logic**: Uses `getWeekStartAndEnd()` and `areDatesEqual()` to determine appropriate labeling\n- **Progress Bar Integration**: Optional progress bar that visually represents weekly time completion level\n- **Loading State Management**: Displays overlay spinner during data fetching for better user experience\n- **Theme Compatibility**: Seamless integration with dark and light themes using proper color schemes\n- **Internationalization**: Full i18n support with localized labels for different contexts\n\n### Smart Labeling System\n\nThe component intelligently determines the appropriate label:\n- **Current Week**: Shows "Worked This Week" when report dates match current week start/end\n- **Custom Period**: Shows "Worked Over Period" when report dates represent a different time range\n\n### Progress Calculation\n\nThe component calculates progress as: (week\'s worked time in seconds / 144,000 seconds) × 100\nThis provides a meaningful percentage that represents weekly progress toward a standard 40-hour work week target.\n\n### Technical Implementation\n\nThe component uses the ClocReportDisplayer base component with weekly duration data from the ClocProvider context. It includes sophisticated date comparison logic to provide contextually appropriate labels and uses a fixed 40-hour maximum work target.\n                '}}},argTypes:{showProgress:{control:"boolean",description:"Whether to show the progress bar below the worked time",table:{type:{summary:"boolean"},defaultValue:{summary:"true"}}},className:{control:"text",description:"Additional CSS classes to apply to the card component",table:{type:{summary:"string"},defaultValue:{summary:"undefined"}}}},decorators:[Story=>(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("div",{style:{width:"200px",height:"150px"},children:(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(Story,{})})]},Default={args:{showProgress:!0},parameters:{docs:{description:{story:'The default ClocWeeklyWorkedTimeDisplayer component with progress bar enabled. Displays week\'s worked time in formatted format with intelligent labeling that switches between "Worked This Week" and "Worked Over Period" based on selected report dates, with progress against 40-hour target.'}}}},WithoutProgress={args:{showProgress:!1},parameters:{docs:{description:{story:"ClocWeeklyWorkedTimeDisplayer with progress bar disabled (showProgress=false). Ideal for minimal layouts where only the worked time value is needed without visual progress indication against weekly targets, while maintaining smart date labeling."}}}},CustomStyling={args:{showProgress:!0,className:"border-teal-300 bg-teal-50 dark:border-teal-700 dark:bg-teal-950"},parameters:{docs:{description:{story:"ClocWeeklyWorkedTimeDisplayer with custom styling applied through the className prop. Features custom border and background colors while preserving all time display functionality, formatting, progress visualization against 40-hour target, and intelligent date range labeling."}}}};Default.parameters={...Default.parameters,docs:{...null===(_Default_parameters=Default.parameters)||void 0===_Default_parameters?void 0:_Default_parameters.docs,source:{originalSource:'{\n  args: {\n    showProgress: true\n  },\n  parameters: {\n    docs: {\n      description: {\n        story: \'The default ClocWeeklyWorkedTimeDisplayer component with progress bar enabled. Displays week\\\'s worked time in formatted format with intelligent labeling that switches between "Worked This Week" and "Worked Over Period" based on selected report dates, with progress against 40-hour target.\'\n      }\n    }\n  }\n}',...null===(_Default_parameters1=Default.parameters)||void 0===_Default_parameters1||null===(_Default_parameters_docs=_Default_parameters1.docs)||void 0===_Default_parameters_docs?void 0:_Default_parameters_docs.source},description:{story:"Default weekly worked time displayer with progress bar and intelligent labeling.\r\nShows week's worked time with smart date range detection and 40-hour target.",...null===(_Default_parameters2=Default.parameters)||void 0===_Default_parameters2||null===(_Default_parameters_docs1=_Default_parameters2.docs)||void 0===_Default_parameters_docs1?void 0:_Default_parameters_docs1.description}}},WithoutProgress.parameters={...WithoutProgress.parameters,docs:{...null===(_WithoutProgress_parameters=WithoutProgress.parameters)||void 0===_WithoutProgress_parameters?void 0:_WithoutProgress_parameters.docs,source:{originalSource:"{\n  args: {\n    showProgress: false\n  },\n  parameters: {\n    docs: {\n      description: {\n        story: 'ClocWeeklyWorkedTimeDisplayer with progress bar disabled (showProgress=false). Ideal for minimal layouts where only the worked time value is needed without visual progress indication against weekly targets, while maintaining smart date labeling.'\n      }\n    }\n  }\n}",...null===(_WithoutProgress_parameters1=WithoutProgress.parameters)||void 0===_WithoutProgress_parameters1||null===(_WithoutProgress_parameters_docs=_WithoutProgress_parameters1.docs)||void 0===_WithoutProgress_parameters_docs?void 0:_WithoutProgress_parameters_docs.source},description:{story:"Weekly worked time displayer without progress bar for minimal display.\r\nShows only the worked time without visual progress indicator.",...null===(_WithoutProgress_parameters2=WithoutProgress.parameters)||void 0===_WithoutProgress_parameters2||null===(_WithoutProgress_parameters_docs1=_WithoutProgress_parameters2.docs)||void 0===_WithoutProgress_parameters_docs1?void 0:_WithoutProgress_parameters_docs1.description}}},CustomStyling.parameters={...CustomStyling.parameters,docs:{...null===(_CustomStyling_parameters=CustomStyling.parameters)||void 0===_CustomStyling_parameters?void 0:_CustomStyling_parameters.docs,source:{originalSource:"{\n  args: {\n    showProgress: true,\n    className: 'border-teal-300 bg-teal-50 dark:border-teal-700 dark:bg-teal-950'\n  },\n  parameters: {\n    docs: {\n      description: {\n        story: 'ClocWeeklyWorkedTimeDisplayer with custom styling applied through the className prop. Features custom border and background colors while preserving all time display functionality, formatting, progress visualization against 40-hour target, and intelligent date range labeling.'\n      }\n    }\n  }\n}",...null===(_CustomStyling_parameters1=CustomStyling.parameters)||void 0===_CustomStyling_parameters1||null===(_CustomStyling_parameters_docs=_CustomStyling_parameters1.docs)||void 0===_CustomStyling_parameters_docs?void 0:_CustomStyling_parameters_docs.source},description:{story:"Weekly worked time displayer with custom styling applied through className prop.\r\nDemonstrates visual customization while maintaining intelligent labeling and time formatting.",...null===(_CustomStyling_parameters2=CustomStyling.parameters)||void 0===_CustomStyling_parameters2||null===(_CustomStyling_parameters_docs1=_CustomStyling_parameters2.docs)||void 0===_CustomStyling_parameters_docs1?void 0:_CustomStyling_parameters_docs1.description}}};const __namedExportsOrder=["Default","WithoutProgress","CustomStyling"]}}]);