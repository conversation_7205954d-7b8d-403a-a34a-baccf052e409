/*! For license information please see stories-team-management-ClocMemberInvitationFormDialog-stories.a6bbcbe1.iframe.bundle.js.LICENSE.txt */
"use strict";(self.webpackChunk_cloc_storybook=self.webpackChunk_cloc_storybook||[]).push([[4795],{"../../node_modules/lucide-react/dist/esm/createLucideIcon.js":(__unused_webpack_module,__webpack_exports__,__webpack_require__)=>{__webpack_require__.d(__webpack_exports__,{A:()=>createLucideIcon});var react=__webpack_require__("../../node_modules/next/dist/compiled/react/index.js");const mergeClasses=(...classes)=>classes.filter((className,index,array)=>Boolean(className)&&array.indexOf(className)===index).join(" ");var defaultAttributes={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};const Icon=(0,react.forwardRef)(({color="currentColor",size=24,strokeWidth=2,absoluteStrokeWidth,className="",children,iconNode,...rest},ref)=>(0,react.createElement)("svg",{ref,...defaultAttributes,width:size,height:size,stroke:color,strokeWidth:absoluteStrokeWidth?24*Number(strokeWidth)/Number(size):strokeWidth,className:mergeClasses("lucide",className),...rest},[...iconNode.map(([tag,attrs])=>(0,react.createElement)(tag,attrs)),...Array.isArray(children)?children:[children]])),createLucideIcon=(iconName,iconNode)=>{const Component=(0,react.forwardRef)(({className,...props},ref)=>{return(0,react.createElement)(Icon,{ref,iconNode,className:mergeClasses(`lucide-${string=iconName,string.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase()}`,className),...props});var string});return Component.displayName=`${iconName}`,Component}},"../../node_modules/lucide-react/dist/esm/icons/mail.js":(__unused_webpack_module,__webpack_exports__,__webpack_require__)=>{__webpack_require__.d(__webpack_exports__,{A:()=>Mail});const Mail=(0,__webpack_require__("../../node_modules/lucide-react/dist/esm/createLucideIcon.js").A)("Mail",[["rect",{width:"20",height:"16",x:"2",y:"4",rx:"2",key:"18n3k1"}],["path",{d:"m22 7-8.97 5.7a1.94 1.94 0 0 1-2.06 0L2 7",key:"1ocrg3"}]])},"../../node_modules/lucide-react/dist/esm/icons/user-plus.js":(__unused_webpack_module,__webpack_exports__,__webpack_require__)=>{__webpack_require__.d(__webpack_exports__,{A:()=>UserPlus});const UserPlus=(0,__webpack_require__("../../node_modules/lucide-react/dist/esm/createLucideIcon.js").A)("UserPlus",[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}],["line",{x1:"19",x2:"19",y1:"8",y2:"14",key:"1bvyxn"}],["line",{x1:"22",x2:"16",y1:"11",y2:"11",key:"1shjgl"}]])},"./src/stories/team-management/ClocMemberInvitationFormDialog.stories.tsx":(__unused_webpack_module,__webpack_exports__,__webpack_require__)=>{__webpack_require__.r(__webpack_exports__),__webpack_require__.d(__webpack_exports__,{CustomStyling:()=>CustomStyling,CustomTrigger:()=>CustomTrigger,Default:()=>Default,IconTrigger:()=>IconTrigger,__namedExportsOrder:()=>__namedExportsOrder,default:()=>__WEBPACK_DEFAULT_EXPORT__});var _Default_parameters,_Default_parameters_docs,_Default_parameters1,_Default_parameters_docs1,_Default_parameters2,_CustomStyling_parameters,_CustomStyling_parameters_docs,_CustomStyling_parameters1,_CustomStyling_parameters_docs1,_CustomStyling_parameters2,_CustomTrigger_parameters,_CustomTrigger_parameters_docs,_CustomTrigger_parameters1,_CustomTrigger_parameters_docs1,_CustomTrigger_parameters2,_IconTrigger_parameters,_IconTrigger_parameters_docs,_IconTrigger_parameters1,_IconTrigger_parameters_docs1,_IconTrigger_parameters2,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__=__webpack_require__("../../node_modules/next/dist/compiled/react/jsx-runtime.js"),_cloc_atoms__WEBPACK_IMPORTED_MODULE_2__=(__webpack_require__("../../node_modules/next/dist/compiled/react/index.js"),__webpack_require__("../../packages/toolkit/atoms/dist/index.es.js")),_cloc_ui__WEBPACK_IMPORTED_MODULE_3__=__webpack_require__("../../packages/ui/dist/index.es.js"),_barrel_optimize_names_Mail_UserPlus_lucide_react__WEBPACK_IMPORTED_MODULE_4__=__webpack_require__("../../node_modules/lucide-react/dist/esm/icons/user-plus.js"),_barrel_optimize_names_Mail_UserPlus_lucide_react__WEBPACK_IMPORTED_MODULE_5__=__webpack_require__("../../node_modules/lucide-react/dist/esm/icons/mail.js");const __WEBPACK_DEFAULT_EXPORT__={title:"Team Management/Member Invitation Form Dialog",component:_cloc_atoms__WEBPACK_IMPORTED_MODULE_2__.lDk,parameters:{layout:"centered",docs:{description:{component:"A dialog wrapper component for the member invitation form. Provides a modal interface for inviting team members with customizable trigger elements. Automatically handles dialog state and form submission within the modal context."}}},argTypes:{className:{control:"text",description:"Additional CSS classes for styling the dialog content"},trigger:{control:!1,description:"Custom trigger element to open the dialog. If not provided, uses default button."}}},Default={args:{}},CustomStyling={args:{className:"border-2 border-green-200 dark:border-green-800"},parameters:{docs:{description:{story:"Dialog with custom green-themed border styling applied to the form content."}}}},CustomTrigger={args:{trigger:(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)(_cloc_ui__WEBPACK_IMPORTED_MODULE_3__.cc,{className:"bg-purple-600 hover:bg-purple-700 text-white",children:[(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(_barrel_optimize_names_Mail_UserPlus_lucide_react__WEBPACK_IMPORTED_MODULE_4__.A,{size:16,className:"mr-2"}),"Add Team Member"]})},parameters:{docs:{description:{story:"Dialog triggered by a custom purple-themed button with UserPlus icon."}}}},IconTrigger={args:{trigger:(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(_cloc_ui__WEBPACK_IMPORTED_MODULE_3__.cc,{size:"sm",className:"p-2",children:(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(_barrel_optimize_names_Mail_UserPlus_lucide_react__WEBPACK_IMPORTED_MODULE_5__.A,{size:16})})},parameters:{docs:{description:{story:"Dialog triggered by a compact icon-only button, suitable for toolbars or headers."}}}};Default.parameters={...Default.parameters,docs:{...null===(_Default_parameters=Default.parameters)||void 0===_Default_parameters?void 0:_Default_parameters.docs,source:{originalSource:"{\n  args: {}\n}",...null===(_Default_parameters1=Default.parameters)||void 0===_Default_parameters1||null===(_Default_parameters_docs=_Default_parameters1.docs)||void 0===_Default_parameters_docs?void 0:_Default_parameters_docs.source},description:{story:'Default member invitation dialog with standard trigger button.\r\nUses the built-in "Invite Member" button to open the dialog.',...null===(_Default_parameters2=Default.parameters)||void 0===_Default_parameters2||null===(_Default_parameters_docs1=_Default_parameters2.docs)||void 0===_Default_parameters_docs1?void 0:_Default_parameters_docs1.description}}},CustomStyling.parameters={...CustomStyling.parameters,docs:{...null===(_CustomStyling_parameters=CustomStyling.parameters)||void 0===_CustomStyling_parameters?void 0:_CustomStyling_parameters.docs,source:{originalSource:"{\n  args: {\n    className: 'border-2 border-green-200 dark:border-green-800'\n  },\n  parameters: {\n    docs: {\n      description: {\n        story: 'Dialog with custom green-themed border styling applied to the form content.'\n      }\n    }\n  }\n}",...null===(_CustomStyling_parameters1=CustomStyling.parameters)||void 0===_CustomStyling_parameters1||null===(_CustomStyling_parameters_docs=_CustomStyling_parameters1.docs)||void 0===_CustomStyling_parameters_docs?void 0:_CustomStyling_parameters_docs.source},description:{story:"Member invitation dialog with custom trigger button styling.\r\nDemonstrates how to customize the dialog appearance.",...null===(_CustomStyling_parameters2=CustomStyling.parameters)||void 0===_CustomStyling_parameters2||null===(_CustomStyling_parameters_docs1=_CustomStyling_parameters2.docs)||void 0===_CustomStyling_parameters_docs1?void 0:_CustomStyling_parameters_docs1.description}}},CustomTrigger.parameters={...CustomTrigger.parameters,docs:{...null===(_CustomTrigger_parameters=CustomTrigger.parameters)||void 0===_CustomTrigger_parameters?void 0:_CustomTrigger_parameters.docs,source:{originalSource:'{\n  args: {\n    trigger: <ThemedButton className="bg-purple-600 hover:bg-purple-700 text-white">\r\n                <UserPlus size={16} className="mr-2" />\r\n                Add Team Member\r\n            </ThemedButton>\n  },\n  parameters: {\n    docs: {\n      description: {\n        story: \'Dialog triggered by a custom purple-themed button with UserPlus icon.\'\n      }\n    }\n  }\n}',...null===(_CustomTrigger_parameters1=CustomTrigger.parameters)||void 0===_CustomTrigger_parameters1||null===(_CustomTrigger_parameters_docs=_CustomTrigger_parameters1.docs)||void 0===_CustomTrigger_parameters_docs?void 0:_CustomTrigger_parameters_docs.source},description:{story:"Member invitation dialog with custom trigger element.\r\nShows how to provide a custom button or element to trigger the dialog.",...null===(_CustomTrigger_parameters2=CustomTrigger.parameters)||void 0===_CustomTrigger_parameters2||null===(_CustomTrigger_parameters_docs1=_CustomTrigger_parameters2.docs)||void 0===_CustomTrigger_parameters_docs1?void 0:_CustomTrigger_parameters_docs1.description}}},IconTrigger.parameters={...IconTrigger.parameters,docs:{...null===(_IconTrigger_parameters=IconTrigger.parameters)||void 0===_IconTrigger_parameters?void 0:_IconTrigger_parameters.docs,source:{originalSource:'{\n  args: {\n    trigger: <ThemedButton size="sm" className="p-2">\r\n                <Mail size={16} />\r\n            </ThemedButton>\n  },\n  parameters: {\n    docs: {\n      description: {\n        story: \'Dialog triggered by a compact icon-only button, suitable for toolbars or headers.\'\n      }\n    }\n  }\n}',...null===(_IconTrigger_parameters1=IconTrigger.parameters)||void 0===_IconTrigger_parameters1||null===(_IconTrigger_parameters_docs=_IconTrigger_parameters1.docs)||void 0===_IconTrigger_parameters_docs?void 0:_IconTrigger_parameters_docs.source},description:{story:"Member invitation dialog with icon-only trigger.\r\nDemonstrates a compact trigger for toolbar or header usage.",...null===(_IconTrigger_parameters2=IconTrigger.parameters)||void 0===_IconTrigger_parameters2||null===(_IconTrigger_parameters_docs1=_IconTrigger_parameters2.docs)||void 0===_IconTrigger_parameters_docs1?void 0:_IconTrigger_parameters_docs1.description}}};const __namedExportsOrder=["Default","CustomStyling","CustomTrigger","IconTrigger"]}}]);