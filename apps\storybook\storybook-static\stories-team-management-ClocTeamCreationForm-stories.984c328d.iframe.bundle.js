"use strict";(self.webpackChunk_cloc_storybook=self.webpackChunk_cloc_storybook||[]).push([[3540],{"./src/stories/team-management/ClocTeamCreationForm.stories.tsx":(__unused_webpack_module,__webpack_exports__,__webpack_require__)=>{__webpack_require__.r(__webpack_exports__),__webpack_require__.d(__webpack_exports__,{CustomStyling:()=>CustomStyling,Default:()=>Default,InModal:()=>InModal,OnboardingFlow:()=>OnboardingFlow,__namedExportsOrder:()=>__namedExportsOrder,default:()=>__WEBPACK_DEFAULT_EXPORT__});var _Default_parameters,_Default_parameters_docs,_Default_parameters1,_Default_parameters_docs1,_Default_parameters2,_InModal_parameters,_InModal_parameters_docs,_InModal_parameters1,_InModal_parameters_docs1,_InModal_parameters2,_CustomStyling_parameters,_CustomStyling_parameters_docs,_CustomStyling_parameters1,_CustomStyling_parameters_docs1,_CustomStyling_parameters2,_OnboardingFlow_parameters,_OnboardingFlow_parameters_docs,_OnboardingFlow_parameters1,_OnboardingFlow_parameters_docs1,_OnboardingFlow_parameters2,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__=__webpack_require__("../../node_modules/next/dist/compiled/react/jsx-runtime.js"),_cloc_atoms__WEBPACK_IMPORTED_MODULE_2__=(__webpack_require__("../../node_modules/next/dist/compiled/react/index.js"),__webpack_require__("../../packages/toolkit/atoms/dist/index.es.js"));const __WEBPACK_DEFAULT_EXPORT__={title:"Team Management/Team Creation Form",component:_cloc_atoms__WEBPACK_IMPORTED_MODULE_2__.oCJ,parameters:{layout:"centered",docs:{description:{component:"A comprehensive form component for creating new teams within an organization. Features team name and description input fields with validation, organization context integration, and loading states. Includes tooltip guidance for authentication requirements and comprehensive error handling."}}},argTypes:{className:{control:"text",description:"Additional CSS classes for styling the form container"}}},Default={args:{}},InModal={render:()=>(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4",children:(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div",{className:"bg-white dark:bg-gray-900 rounded-lg shadow-xl max-w-lg w-full",children:[(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("div",{className:"p-6 border-b border-gray-200 dark:border-gray-700",children:(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("h2",{className:"text-xl font-semibold text-gray-900 dark:text-white",children:"Create New Team"})}),(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("div",{className:"p-6",children:(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(_cloc_atoms__WEBPACK_IMPORTED_MODULE_2__.oCJ,{className:"border-0 bg-transparent p-0 shadow-none"})})]})}),parameters:{docs:{description:{story:"Team creation form optimized for modal display with transparent background and removed borders."}}}},CustomStyling={args:{className:"border-2 border-indigo-200 dark:border-indigo-800 bg-indigo-50 dark:bg-indigo-950"},parameters:{docs:{description:{story:"Form with custom indigo-themed styling applied through the className prop."}}}},OnboardingFlow={render:()=>(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("div",{className:"bg-gradient-to-br from-blue-50 to-indigo-100 dark:from-gray-900 dark:to-gray-800 min-h-screen flex items-center justify-center p-4",children:(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div",{className:"bg-white dark:bg-gray-900 rounded-2xl shadow-xl p-8 w-full max-w-2xl",children:[(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div",{className:"text-center mb-8",children:[(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("div",{className:"flex justify-center mb-4",children:(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div",{className:"flex space-x-2",children:[(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("div",{className:"w-3 h-3 bg-blue-600 rounded-full"}),(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("div",{className:"w-3 h-3 bg-blue-600 rounded-full"}),(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("div",{className:"w-3 h-3 bg-blue-600 rounded-full"}),(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("div",{className:"w-3 h-3 bg-gray-300 dark:bg-gray-600 rounded-full"})]})}),(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("h1",{className:"text-2xl font-bold text-gray-900 dark:text-white",children:"Create Your First Team"}),(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("p",{className:"text-gray-600 dark:text-gray-400 mt-2",children:"Set up your team to start collaborating and tracking time together"})]}),(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(_cloc_atoms__WEBPACK_IMPORTED_MODULE_2__.oCJ,{}),(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div",{className:"flex justify-between mt-8",children:[(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("button",{className:"px-6 py-2 text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white",children:"Skip for now"}),(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("button",{className:"px-6 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-lg font-medium",children:"Continue"})]})]})}),parameters:{docs:{description:{story:"Team creation form within an onboarding flow, showing step progress and contextual guidance."}}}};Default.parameters={...Default.parameters,docs:{...null===(_Default_parameters=Default.parameters)||void 0===_Default_parameters?void 0:_Default_parameters.docs,source:{originalSource:"{\n  args: {}\n}",...null===(_Default_parameters1=Default.parameters)||void 0===_Default_parameters1||null===(_Default_parameters_docs=_Default_parameters1.docs)||void 0===_Default_parameters_docs?void 0:_Default_parameters_docs.source},description:{story:"Default team creation form with standard styling and functionality.\r\nShows team name input, description textarea, and create button.",...null===(_Default_parameters2=Default.parameters)||void 0===_Default_parameters2||null===(_Default_parameters_docs1=_Default_parameters2.docs)||void 0===_Default_parameters_docs1?void 0:_Default_parameters_docs1.description}}},InModal.parameters={...InModal.parameters,docs:{...null===(_InModal_parameters=InModal.parameters)||void 0===_InModal_parameters?void 0:_InModal_parameters.docs,source:{originalSource:'{\n  render: () => <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4">\r\n            <div className="bg-white dark:bg-gray-900 rounded-lg shadow-xl max-w-lg w-full">\r\n                <div className="p-6 border-b border-gray-200 dark:border-gray-700">\r\n                    <h2 className="text-xl font-semibold text-gray-900 dark:text-white">Create New Team</h2>\r\n                </div>\r\n                <div className="p-6">\r\n                    <ClocTeamCreationForm className="border-0 bg-transparent p-0 shadow-none" />\r\n                </div>\r\n            </div>\r\n        </div>,\n  parameters: {\n    docs: {\n      description: {\n        story: \'Team creation form optimized for modal display with transparent background and removed borders.\'\n      }\n    }\n  }\n}',...null===(_InModal_parameters1=InModal.parameters)||void 0===_InModal_parameters1||null===(_InModal_parameters_docs=_InModal_parameters1.docs)||void 0===_InModal_parameters_docs?void 0:_InModal_parameters_docs.source},description:{story:"Team creation form displayed within a modal context.\r\nDemonstrates how the form appears in dialog overlays.",...null===(_InModal_parameters2=InModal.parameters)||void 0===_InModal_parameters2||null===(_InModal_parameters_docs1=_InModal_parameters2.docs)||void 0===_InModal_parameters_docs1?void 0:_InModal_parameters_docs1.description}}},CustomStyling.parameters={...CustomStyling.parameters,docs:{...null===(_CustomStyling_parameters=CustomStyling.parameters)||void 0===_CustomStyling_parameters?void 0:_CustomStyling_parameters.docs,source:{originalSource:"{\n  args: {\n    className: 'border-2 border-indigo-200 dark:border-indigo-800 bg-indigo-50 dark:bg-indigo-950'\n  },\n  parameters: {\n    docs: {\n      description: {\n        story: 'Form with custom indigo-themed styling applied through the className prop.'\n      }\n    }\n  }\n}",...null===(_CustomStyling_parameters1=CustomStyling.parameters)||void 0===_CustomStyling_parameters1||null===(_CustomStyling_parameters_docs=_CustomStyling_parameters1.docs)||void 0===_CustomStyling_parameters_docs?void 0:_CustomStyling_parameters_docs.source},description:{story:"Team creation form with custom styling applied via className prop.\r\nDemonstrates visual customization capabilities.",...null===(_CustomStyling_parameters2=CustomStyling.parameters)||void 0===_CustomStyling_parameters2||null===(_CustomStyling_parameters_docs1=_CustomStyling_parameters2.docs)||void 0===_CustomStyling_parameters_docs1?void 0:_CustomStyling_parameters_docs1.description}}},OnboardingFlow.parameters={...OnboardingFlow.parameters,docs:{...null===(_OnboardingFlow_parameters=OnboardingFlow.parameters)||void 0===_OnboardingFlow_parameters?void 0:_OnboardingFlow_parameters.docs,source:{originalSource:'{\n  render: () => <div className="bg-gradient-to-br from-blue-50 to-indigo-100 dark:from-gray-900 dark:to-gray-800 min-h-screen flex items-center justify-center p-4">\r\n            <div className="bg-white dark:bg-gray-900 rounded-2xl shadow-xl p-8 w-full max-w-2xl">\r\n                <div className="text-center mb-8">\r\n                    <div className="flex justify-center mb-4">\r\n                        <div className="flex space-x-2">\r\n                            <div className="w-3 h-3 bg-blue-600 rounded-full"></div>\r\n                            <div className="w-3 h-3 bg-blue-600 rounded-full"></div>\r\n                            <div className="w-3 h-3 bg-blue-600 rounded-full"></div>\r\n                            <div className="w-3 h-3 bg-gray-300 dark:bg-gray-600 rounded-full"></div>\r\n                        </div>\r\n                    </div>\r\n                    <h1 className="text-2xl font-bold text-gray-900 dark:text-white">Create Your First Team</h1>\r\n                    <p className="text-gray-600 dark:text-gray-400 mt-2">\r\n                        Set up your team to start collaborating and tracking time together\r\n                    </p>\r\n                </div>\r\n                <ClocTeamCreationForm />\r\n                <div className="flex justify-between mt-8">\r\n                    <button className="px-6 py-2 text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white">\r\n                        Skip for now\r\n                    </button>\r\n                    <button className="px-6 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-lg font-medium">\r\n                        Continue\r\n                    </button>\r\n                </div>\r\n            </div>\r\n        </div>,\n  parameters: {\n    docs: {\n      description: {\n        story: \'Team creation form within an onboarding flow, showing step progress and contextual guidance.\'\n      }\n    }\n  }\n}',...null===(_OnboardingFlow_parameters1=OnboardingFlow.parameters)||void 0===_OnboardingFlow_parameters1||null===(_OnboardingFlow_parameters_docs=_OnboardingFlow_parameters1.docs)||void 0===_OnboardingFlow_parameters_docs?void 0:_OnboardingFlow_parameters_docs.source},description:{story:"Team creation form in an onboarding flow context.\r\nShows how the form appears during initial team setup.",...null===(_OnboardingFlow_parameters2=OnboardingFlow.parameters)||void 0===_OnboardingFlow_parameters2||null===(_OnboardingFlow_parameters_docs1=_OnboardingFlow_parameters2.docs)||void 0===_OnboardingFlow_parameters_docs1?void 0:_OnboardingFlow_parameters_docs1.description}}};const __namedExportsOrder=["Default","InModal","CustomStyling","OnboardingFlow"]}}]);