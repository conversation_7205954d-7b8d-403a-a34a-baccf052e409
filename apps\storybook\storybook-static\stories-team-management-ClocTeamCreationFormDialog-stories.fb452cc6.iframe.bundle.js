/*! For license information please see stories-team-management-ClocTeamCreationFormDialog-stories.fb452cc6.iframe.bundle.js.LICENSE.txt */
"use strict";(self.webpackChunk_cloc_storybook=self.webpackChunk_cloc_storybook||[]).push([[7268],{"../../node_modules/lucide-react/dist/esm/createLucideIcon.js":(__unused_webpack_module,__webpack_exports__,__webpack_require__)=>{__webpack_require__.d(__webpack_exports__,{A:()=>createLucideIcon});var react=__webpack_require__("../../node_modules/next/dist/compiled/react/index.js");const mergeClasses=(...classes)=>classes.filter((className,index,array)=>Boolean(className)&&array.indexOf(className)===index).join(" ");var defaultAttributes={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};const Icon=(0,react.forwardRef)(({color="currentColor",size=24,strokeWidth=2,absoluteStrokeWidth,className="",children,iconNode,...rest},ref)=>(0,react.createElement)("svg",{ref,...defaultAttributes,width:size,height:size,stroke:color,strokeWidth:absoluteStrokeWidth?24*Number(strokeWidth)/Number(size):strokeWidth,className:mergeClasses("lucide",className),...rest},[...iconNode.map(([tag,attrs])=>(0,react.createElement)(tag,attrs)),...Array.isArray(children)?children:[children]])),createLucideIcon=(iconName,iconNode)=>{const Component=(0,react.forwardRef)(({className,...props},ref)=>{return(0,react.createElement)(Icon,{ref,iconNode,className:mergeClasses(`lucide-${string=iconName,string.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase()}`,className),...props});var string});return Component.displayName=`${iconName}`,Component}},"../../node_modules/lucide-react/dist/esm/icons/users.js":(__unused_webpack_module,__webpack_exports__,__webpack_require__)=>{__webpack_require__.d(__webpack_exports__,{A:()=>Users});const Users=(0,__webpack_require__("../../node_modules/lucide-react/dist/esm/createLucideIcon.js").A)("Users",[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}],["path",{d:"M22 21v-2a4 4 0 0 0-3-3.87",key:"kshegd"}],["path",{d:"M16 3.13a4 4 0 0 1 0 7.75",key:"1da9ce"}]])},"./src/stories/team-management/ClocTeamCreationFormDialog.stories.tsx":(__unused_webpack_module,__webpack_exports__,__webpack_require__)=>{__webpack_require__.r(__webpack_exports__),__webpack_require__.d(__webpack_exports__,{CustomStyling:()=>CustomStyling,CustomTrigger:()=>CustomTrigger,Default:()=>Default,IconTrigger:()=>IconTrigger,OrganizationTrigger:()=>OrganizationTrigger,__namedExportsOrder:()=>__namedExportsOrder,default:()=>ClocTeamCreationFormDialog_stories});var jsx_runtime=__webpack_require__("../../node_modules/next/dist/compiled/react/jsx-runtime.js"),index_es=(__webpack_require__("../../node_modules/next/dist/compiled/react/index.js"),__webpack_require__("../../packages/toolkit/atoms/dist/index.es.js")),dist_index_es=__webpack_require__("../../packages/ui/dist/index.es.js"),users=__webpack_require__("../../node_modules/lucide-react/dist/esm/icons/users.js"),createLucideIcon=__webpack_require__("../../node_modules/lucide-react/dist/esm/createLucideIcon.js");const Building=(0,createLucideIcon.A)("Building",[["rect",{width:"16",height:"20",x:"4",y:"2",rx:"2",ry:"2",key:"76otgf"}],["path",{d:"M9 22v-4h6v4",key:"r93iot"}],["path",{d:"M8 6h.01",key:"1dz90k"}],["path",{d:"M16 6h.01",key:"1x0f13"}],["path",{d:"M12 6h.01",key:"1vi96p"}],["path",{d:"M12 10h.01",key:"1nrarc"}],["path",{d:"M12 14h.01",key:"1etili"}],["path",{d:"M16 10h.01",key:"1m94wz"}],["path",{d:"M16 14h.01",key:"1gbofw"}],["path",{d:"M8 10h.01",key:"19clt8"}],["path",{d:"M8 14h.01",key:"6423bh"}]]),Plus=(0,createLucideIcon.A)("Plus",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"M12 5v14",key:"s699le"}]]);var _Default_parameters,_Default_parameters_docs,_Default_parameters1,_Default_parameters_docs1,_Default_parameters2,_CustomStyling_parameters,_CustomStyling_parameters_docs,_CustomStyling_parameters1,_CustomStyling_parameters_docs1,_CustomStyling_parameters2,_CustomTrigger_parameters,_CustomTrigger_parameters_docs,_CustomTrigger_parameters1,_CustomTrigger_parameters_docs1,_CustomTrigger_parameters2,_OrganizationTrigger_parameters,_OrganizationTrigger_parameters_docs,_OrganizationTrigger_parameters1,_OrganizationTrigger_parameters_docs1,_OrganizationTrigger_parameters2,_IconTrigger_parameters,_IconTrigger_parameters_docs,_IconTrigger_parameters1,_IconTrigger_parameters_docs1,_IconTrigger_parameters2;const ClocTeamCreationFormDialog_stories={title:"Team Management/Team Creation Form Dialog",component:index_es.uJL,parameters:{layout:"centered",docs:{description:{component:"A dialog wrapper component for the team creation form. Provides a modal interface for creating new teams with customizable trigger elements. Automatically handles dialog state and form submission within the modal context."}}},argTypes:{className:{control:"text",description:"Additional CSS classes for styling the dialog content"},trigger:{control:!1,description:"Custom trigger element to open the dialog. If not provided, uses default button."}}},Default={args:{}},CustomStyling={args:{className:"border-2 border-purple-200 dark:border-purple-800"},parameters:{docs:{description:{story:"Dialog with custom purple-themed border styling applied to the form content."}}}},CustomTrigger={args:{trigger:(0,jsx_runtime.jsxs)(dist_index_es.cc,{className:"bg-green-600 hover:bg-green-700 text-white",children:[(0,jsx_runtime.jsx)(users.A,{size:16,className:"mr-2"}),"New Team"]})},parameters:{docs:{description:{story:"Dialog triggered by a custom green-themed button with Users icon."}}}},OrganizationTrigger={args:{trigger:(0,jsx_runtime.jsxs)(dist_index_es.cc,{className:"bg-blue-600 hover:bg-blue-700 text-white",children:[(0,jsx_runtime.jsx)(Building,{size:16,className:"mr-2"}),"Add Team to Organization"]})},parameters:{docs:{description:{story:"Dialog triggered by an organization-themed button, suitable for admin interfaces."}}}},IconTrigger={args:{trigger:(0,jsx_runtime.jsx)(dist_index_es.cc,{size:"sm",className:"p-2",children:(0,jsx_runtime.jsx)(Plus,{size:16})})},parameters:{docs:{description:{story:"Dialog triggered by a compact icon-only button, suitable for toolbars or headers."}}}};Default.parameters={...Default.parameters,docs:{...null===(_Default_parameters=Default.parameters)||void 0===_Default_parameters?void 0:_Default_parameters.docs,source:{originalSource:"{\n  args: {}\n}",...null===(_Default_parameters1=Default.parameters)||void 0===_Default_parameters1||null===(_Default_parameters_docs=_Default_parameters1.docs)||void 0===_Default_parameters_docs?void 0:_Default_parameters_docs.source},description:{story:'Default team creation dialog with standard trigger button.\r\nUses the built-in "Create New Team" button to open the dialog.',...null===(_Default_parameters2=Default.parameters)||void 0===_Default_parameters2||null===(_Default_parameters_docs1=_Default_parameters2.docs)||void 0===_Default_parameters_docs1?void 0:_Default_parameters_docs1.description}}},CustomStyling.parameters={...CustomStyling.parameters,docs:{...null===(_CustomStyling_parameters=CustomStyling.parameters)||void 0===_CustomStyling_parameters?void 0:_CustomStyling_parameters.docs,source:{originalSource:"{\n  args: {\n    className: 'border-2 border-purple-200 dark:border-purple-800'\n  },\n  parameters: {\n    docs: {\n      description: {\n        story: 'Dialog with custom purple-themed border styling applied to the form content.'\n      }\n    }\n  }\n}",...null===(_CustomStyling_parameters1=CustomStyling.parameters)||void 0===_CustomStyling_parameters1||null===(_CustomStyling_parameters_docs=_CustomStyling_parameters1.docs)||void 0===_CustomStyling_parameters_docs?void 0:_CustomStyling_parameters_docs.source},description:{story:"Team creation dialog with custom styling applied to the form content.\r\nDemonstrates how to customize the dialog appearance.",...null===(_CustomStyling_parameters2=CustomStyling.parameters)||void 0===_CustomStyling_parameters2||null===(_CustomStyling_parameters_docs1=_CustomStyling_parameters2.docs)||void 0===_CustomStyling_parameters_docs1?void 0:_CustomStyling_parameters_docs1.description}}},CustomTrigger.parameters={...CustomTrigger.parameters,docs:{...null===(_CustomTrigger_parameters=CustomTrigger.parameters)||void 0===_CustomTrigger_parameters?void 0:_CustomTrigger_parameters.docs,source:{originalSource:'{\n  args: {\n    trigger: <ThemedButton className="bg-green-600 hover:bg-green-700 text-white">\r\n                <Users size={16} className="mr-2" />\r\n                New Team\r\n            </ThemedButton>\n  },\n  parameters: {\n    docs: {\n      description: {\n        story: \'Dialog triggered by a custom green-themed button with Users icon.\'\n      }\n    }\n  }\n}',...null===(_CustomTrigger_parameters1=CustomTrigger.parameters)||void 0===_CustomTrigger_parameters1||null===(_CustomTrigger_parameters_docs=_CustomTrigger_parameters1.docs)||void 0===_CustomTrigger_parameters_docs?void 0:_CustomTrigger_parameters_docs.source},description:{story:"Team creation dialog with custom trigger element.\r\nShows how to provide a custom button or element to trigger the dialog.",...null===(_CustomTrigger_parameters2=CustomTrigger.parameters)||void 0===_CustomTrigger_parameters2||null===(_CustomTrigger_parameters_docs1=_CustomTrigger_parameters2.docs)||void 0===_CustomTrigger_parameters_docs1?void 0:_CustomTrigger_parameters_docs1.description}}},OrganizationTrigger.parameters={...OrganizationTrigger.parameters,docs:{...null===(_OrganizationTrigger_parameters=OrganizationTrigger.parameters)||void 0===_OrganizationTrigger_parameters?void 0:_OrganizationTrigger_parameters.docs,source:{originalSource:'{\n  args: {\n    trigger: <ThemedButton className="bg-blue-600 hover:bg-blue-700 text-white">\r\n                <Building size={16} className="mr-2" />\r\n                Add Team to Organization\r\n            </ThemedButton>\n  },\n  parameters: {\n    docs: {\n      description: {\n        story: \'Dialog triggered by an organization-themed button, suitable for admin interfaces.\'\n      }\n    }\n  }\n}',...null===(_OrganizationTrigger_parameters1=OrganizationTrigger.parameters)||void 0===_OrganizationTrigger_parameters1||null===(_OrganizationTrigger_parameters_docs=_OrganizationTrigger_parameters1.docs)||void 0===_OrganizationTrigger_parameters_docs?void 0:_OrganizationTrigger_parameters_docs.source},description:{story:"Team creation dialog with organization-themed trigger.\r\nDemonstrates a trigger styled for organization management contexts.",...null===(_OrganizationTrigger_parameters2=OrganizationTrigger.parameters)||void 0===_OrganizationTrigger_parameters2||null===(_OrganizationTrigger_parameters_docs1=_OrganizationTrigger_parameters2.docs)||void 0===_OrganizationTrigger_parameters_docs1?void 0:_OrganizationTrigger_parameters_docs1.description}}},IconTrigger.parameters={...IconTrigger.parameters,docs:{...null===(_IconTrigger_parameters=IconTrigger.parameters)||void 0===_IconTrigger_parameters?void 0:_IconTrigger_parameters.docs,source:{originalSource:'{\n  args: {\n    trigger: <ThemedButton size="sm" className="p-2">\r\n                <Plus size={16} />\r\n            </ThemedButton>\n  },\n  parameters: {\n    docs: {\n      description: {\n        story: \'Dialog triggered by a compact icon-only button, suitable for toolbars or headers.\'\n      }\n    }\n  }\n}',...null===(_IconTrigger_parameters1=IconTrigger.parameters)||void 0===_IconTrigger_parameters1||null===(_IconTrigger_parameters_docs=_IconTrigger_parameters1.docs)||void 0===_IconTrigger_parameters_docs?void 0:_IconTrigger_parameters_docs.source},description:{story:"Team creation dialog with icon-only trigger.\r\nDemonstrates a compact trigger for toolbar or header usage.",...null===(_IconTrigger_parameters2=IconTrigger.parameters)||void 0===_IconTrigger_parameters2||null===(_IconTrigger_parameters_docs1=_IconTrigger_parameters2.docs)||void 0===_IconTrigger_parameters_docs1?void 0:_IconTrigger_parameters_docs1.description}}};const __namedExportsOrder=["Default","CustomStyling","CustomTrigger","OrganizationTrigger","IconTrigger"]}}]);