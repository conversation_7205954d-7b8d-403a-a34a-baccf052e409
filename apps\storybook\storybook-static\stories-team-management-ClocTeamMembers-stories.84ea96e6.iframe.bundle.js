"use strict";(self.webpackChunk_cloc_storybook=self.webpackChunk_cloc_storybook||[]).push([[9986],{"./src/stories/team-management/ClocTeamMembers.stories.tsx":(__unused_webpack_module,__webpack_exports__,__webpack_require__)=>{__webpack_require__.r(__webpack_exports__),__webpack_require__.d(__webpack_exports__,{CustomStyling:()=>CustomStyling,Default:()=>Default,__namedExportsOrder:()=>__namedExportsOrder,default:()=>__WEBPACK_DEFAULT_EXPORT__});var _Default_parameters,_Default_parameters_docs,_Default_parameters1,_Default_parameters_docs1,_Default_parameters2,_CustomStyling_parameters,_CustomStyling_parameters_docs,_CustomStyling_parameters1,_CustomStyling_parameters_docs1,_CustomStyling_parameters2;__webpack_require__("../../node_modules/next/dist/compiled/react/index.js");const __WEBPACK_DEFAULT_EXPORT__={title:"Team Management/Cloc Team Members",component:__webpack_require__("../../packages/toolkit/atoms/dist/index.es.js").Yfb,parameters:{layout:"centered",docs:{description:{component:"\nA comprehensive team members management component that provides detailed member information and role-based management capabilities.\n\n**Features:**\n- Displays team members with avatars, names, emails, positions, and roles\n- Real-time data from ClocProvider context\n- Built-in search functionality for member filtering\n- Pagination with configurable items per page (5, 10, 20, 50)\n- Role-based actions (managers can remove members)\n- Member invitation dialog for adding new members\n- Confirmation dialogs for member removal/leaving\n- Join/leave date tracking with status badges\n- Duplicate member handling by employeeId\n\n**Data Source:**\n- Gets data from `useClocContext().selectedTeam`, `authenticatedUser`, `selectedOrganization`\n- Uses `organizationTeamsAtom` from Jotai for team data\n- Automatic member deduplication and filtering\n\n**Role-Based Features:**\n- Manager permissions for member management\n- Member invitation (managers only)\n- Member removal with confirmation\n- Leave team functionality\n- Role differentiation (MANAGER vs regular members)\n\n**Use Cases:**\n- Team member management\n- Member directory and contact information\n- Role-based team administration\n- Member onboarding and offboarding\n- Team composition analysis\n                "}}},argTypes:{className:{control:{type:"text"},description:"Additional CSS classes for custom styling and layout modifications"}}},Default={parameters:{docs:{description:{story:"\nThe default team members view displays all members of the selected team in a detailed table format.\nData is automatically fetched from ClocProvider context and Jotai atoms.\n\n**Default Features:**\n- Comprehensive member information table\n- Search functionality\n- Pagination controls\n- Role-based action menus\n- Member invitation (for managers)\n- Loading states and error handling\n            "}}}},CustomStyling={args:{className:"max-w-5xl border-2 border-green-200 dark:border-green-800"},parameters:{docs:{description:{story:"\nCustom styled variant demonstrating className prop usage for visual customization.\nShows how to apply custom borders, width constraints, and theme-aware styling.\n\n**Customizations:**\n- Maximum width constraint (5xl)\n- Custom green border styling\n- Theme-aware border colors\n- Enhanced visual definition for member management\n            "}}}};Default.parameters={...Default.parameters,docs:{...null===(_Default_parameters=Default.parameters)||void 0===_Default_parameters?void 0:_Default_parameters.docs,source:{originalSource:"{\n  parameters: {\n    docs: {\n      description: {\n        story: `\nThe default team members view displays all members of the selected team in a detailed table format.\nData is automatically fetched from ClocProvider context and Jotai atoms.\n\n**Default Features:**\n- Comprehensive member information table\n- Search functionality\n- Pagination controls\n- Role-based action menus\n- Member invitation (for managers)\n- Loading states and error handling\n            `\n      }\n    }\n  }\n}",...null===(_Default_parameters1=Default.parameters)||void 0===_Default_parameters1||null===(_Default_parameters_docs=_Default_parameters1.docs)||void 0===_Default_parameters_docs?void 0:_Default_parameters_docs.source},description:{story:"Default team members view with standard styling and full functionality.\r\nShows all team members with management capabilities based on user role.",...null===(_Default_parameters2=Default.parameters)||void 0===_Default_parameters2||null===(_Default_parameters_docs1=_Default_parameters2.docs)||void 0===_Default_parameters_docs1?void 0:_Default_parameters_docs1.description}}},CustomStyling.parameters={...CustomStyling.parameters,docs:{...null===(_CustomStyling_parameters=CustomStyling.parameters)||void 0===_CustomStyling_parameters?void 0:_CustomStyling_parameters.docs,source:{originalSource:"{\n  args: {\n    className: 'max-w-5xl border-2 border-green-200 dark:border-green-800'\n  },\n  parameters: {\n    docs: {\n      description: {\n        story: `\nCustom styled variant demonstrating className prop usage for visual customization.\nShows how to apply custom borders, width constraints, and theme-aware styling.\n\n**Customizations:**\n- Maximum width constraint (5xl)\n- Custom green border styling\n- Theme-aware border colors\n- Enhanced visual definition for member management\n            `\n      }\n    }\n  }\n}",...null===(_CustomStyling_parameters1=CustomStyling.parameters)||void 0===_CustomStyling_parameters1||null===(_CustomStyling_parameters_docs=_CustomStyling_parameters1.docs)||void 0===_CustomStyling_parameters_docs?void 0:_CustomStyling_parameters_docs.source},description:{story:"Custom styled team members view with enhanced visual definition.\r\nDemonstrates className customization for different layouts.",...null===(_CustomStyling_parameters2=CustomStyling.parameters)||void 0===_CustomStyling_parameters2||null===(_CustomStyling_parameters_docs1=_CustomStyling_parameters2.docs)||void 0===_CustomStyling_parameters_docs1?void 0:_CustomStyling_parameters_docs1.description}}};const __namedExportsOrder=["Default","CustomStyling"]}}]);