"use strict";(self.webpackChunk_cloc_storybook=self.webpackChunk_cloc_storybook||[]).push([[6759],{"./src/stories/team-management/ClocTeamSetting.stories.tsx":(__unused_webpack_module,__webpack_exports__,__webpack_require__)=>{__webpack_require__.r(__webpack_exports__),__webpack_require__.d(__webpack_exports__,{CustomStyling:()=>CustomStyling,Default:()=>Default,InModal:()=>InModal,InSettingsPage:()=>InSettingsPage,__namedExportsOrder:()=>__namedExportsOrder,default:()=>__WEBPACK_DEFAULT_EXPORT__});var _Default_parameters,_Default_parameters_docs,_Default_parameters1,_Default_parameters_docs1,_Default_parameters2,_InModal_parameters,_InModal_parameters_docs,_InModal_parameters1,_InModal_parameters_docs1,_InModal_parameters2,_CustomStyling_parameters,_CustomStyling_parameters_docs,_CustomStyling_parameters1,_CustomStyling_parameters_docs1,_CustomStyling_parameters2,_InSettingsPage_parameters,_InSettingsPage_parameters_docs,_InSettingsPage_parameters1,_InSettingsPage_parameters_docs1,_InSettingsPage_parameters2,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__=__webpack_require__("../../node_modules/next/dist/compiled/react/jsx-runtime.js"),_cloc_atoms__WEBPACK_IMPORTED_MODULE_2__=(__webpack_require__("../../node_modules/next/dist/compiled/react/index.js"),__webpack_require__("../../packages/toolkit/atoms/dist/index.es.js"));const __WEBPACK_DEFAULT_EXPORT__={title:"Team Management/Team Setting",component:_cloc_atoms__WEBPACK_IMPORTED_MODULE_2__.TUS,parameters:{layout:"centered",docs:{description:{component:"A comprehensive team settings form component for configuring team properties. Features team avatar upload with preview, color picker for branding, team size selection, and public/private visibility settings. Includes loading overlays during submission and comprehensive form validation."}}},argTypes:{className:{control:"text",description:"Additional CSS classes for styling the form container"}}},Default={args:{}},InModal={render:()=>(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4",children:(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div",{className:"bg-white dark:bg-gray-900 rounded-lg shadow-xl max-w-2xl w-full max-h-[90vh] overflow-y-auto",children:[(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("div",{className:"p-6 border-b border-gray-200 dark:border-gray-700",children:(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("h2",{className:"text-xl font-semibold text-gray-900 dark:text-white",children:"Team Settings"})}),(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("div",{className:"p-6",children:(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(_cloc_atoms__WEBPACK_IMPORTED_MODULE_2__.TUS,{className:"border-0 bg-transparent p-0 shadow-none"})})]})}),parameters:{docs:{description:{story:"Team settings form optimized for modal display with transparent background and removed borders."}}}},CustomStyling={args:{className:"border-2 border-orange-200 dark:border-orange-800 bg-orange-50 dark:bg-orange-950"},parameters:{docs:{description:{story:"Form with custom orange-themed styling applied through the className prop."}}}},InSettingsPage={render:()=>(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("div",{className:"min-h-screen bg-gray-50 dark:bg-gray-900 p-6",children:(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("div",{className:"max-w-4xl mx-auto",children:(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div",{className:"bg-white dark:bg-gray-800 rounded-lg shadow-sm",children:[(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div",{className:"border-b border-gray-200 dark:border-gray-700 p-6",children:[(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("h1",{className:"text-2xl font-bold text-gray-900 dark:text-white",children:"Team Management"}),(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("p",{className:"text-gray-600 dark:text-gray-400 mt-1",children:"Configure your team settings and preferences"})]}),(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("div",{className:"p-6",children:(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(_cloc_atoms__WEBPACK_IMPORTED_MODULE_2__.TUS,{})})]})})}),parameters:{docs:{description:{story:"Team settings form within a comprehensive settings page layout with header and description."}}}};Default.parameters={...Default.parameters,docs:{...null===(_Default_parameters=Default.parameters)||void 0===_Default_parameters?void 0:_Default_parameters.docs,source:{originalSource:"{\n  args: {}\n}",...null===(_Default_parameters1=Default.parameters)||void 0===_Default_parameters1||null===(_Default_parameters_docs=_Default_parameters1.docs)||void 0===_Default_parameters_docs?void 0:_Default_parameters_docs.source},description:{story:"Default team settings form with standard styling and functionality.\r\nShows all team configuration options including avatar, name, color, size, and visibility.",...null===(_Default_parameters2=Default.parameters)||void 0===_Default_parameters2||null===(_Default_parameters_docs1=_Default_parameters2.docs)||void 0===_Default_parameters_docs1?void 0:_Default_parameters_docs1.description}}},InModal.parameters={...InModal.parameters,docs:{...null===(_InModal_parameters=InModal.parameters)||void 0===_InModal_parameters?void 0:_InModal_parameters.docs,source:{originalSource:'{\n  render: () => <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4">\r\n            <div className="bg-white dark:bg-gray-900 rounded-lg shadow-xl max-w-2xl w-full max-h-[90vh] overflow-y-auto">\r\n                <div className="p-6 border-b border-gray-200 dark:border-gray-700">\r\n                    <h2 className="text-xl font-semibold text-gray-900 dark:text-white">Team Settings</h2>\r\n                </div>\r\n                <div className="p-6">\r\n                    <ClocTeamSetting className="border-0 bg-transparent p-0 shadow-none" />\r\n                </div>\r\n            </div>\r\n        </div>,\n  parameters: {\n    docs: {\n      description: {\n        story: \'Team settings form optimized for modal display with transparent background and removed borders.\'\n      }\n    }\n  }\n}',...null===(_InModal_parameters1=InModal.parameters)||void 0===_InModal_parameters1||null===(_InModal_parameters_docs=_InModal_parameters1.docs)||void 0===_InModal_parameters_docs?void 0:_InModal_parameters_docs.source},description:{story:"Team settings form displayed within a modal context.\r\nDemonstrates how the form appears in dialog overlays.",...null===(_InModal_parameters2=InModal.parameters)||void 0===_InModal_parameters2||null===(_InModal_parameters_docs1=_InModal_parameters2.docs)||void 0===_InModal_parameters_docs1?void 0:_InModal_parameters_docs1.description}}},CustomStyling.parameters={...CustomStyling.parameters,docs:{...null===(_CustomStyling_parameters=CustomStyling.parameters)||void 0===_CustomStyling_parameters?void 0:_CustomStyling_parameters.docs,source:{originalSource:"{\n  args: {\n    className: 'border-2 border-orange-200 dark:border-orange-800 bg-orange-50 dark:bg-orange-950'\n  },\n  parameters: {\n    docs: {\n      description: {\n        story: 'Form with custom orange-themed styling applied through the className prop.'\n      }\n    }\n  }\n}",...null===(_CustomStyling_parameters1=CustomStyling.parameters)||void 0===_CustomStyling_parameters1||null===(_CustomStyling_parameters_docs=_CustomStyling_parameters1.docs)||void 0===_CustomStyling_parameters_docs?void 0:_CustomStyling_parameters_docs.source},description:{story:"Team settings form with custom styling applied via className prop.\r\nDemonstrates visual customization capabilities.",...null===(_CustomStyling_parameters2=CustomStyling.parameters)||void 0===_CustomStyling_parameters2||null===(_CustomStyling_parameters_docs1=_CustomStyling_parameters2.docs)||void 0===_CustomStyling_parameters_docs1?void 0:_CustomStyling_parameters_docs1.description}}},InSettingsPage.parameters={...InSettingsPage.parameters,docs:{...null===(_InSettingsPage_parameters=InSettingsPage.parameters)||void 0===_InSettingsPage_parameters?void 0:_InSettingsPage_parameters.docs,source:{originalSource:'{\n  render: () => <div className="min-h-screen bg-gray-50 dark:bg-gray-900 p-6">\r\n            <div className="max-w-4xl mx-auto">\r\n                <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm">\r\n                    <div className="border-b border-gray-200 dark:border-gray-700 p-6">\r\n                        <h1 className="text-2xl font-bold text-gray-900 dark:text-white">Team Management</h1>\r\n                        <p className="text-gray-600 dark:text-gray-400 mt-1">\r\n                            Configure your team settings and preferences\r\n                        </p>\r\n                    </div>\r\n                    <div className="p-6">\r\n                        <ClocTeamSetting />\r\n                    </div>\r\n                </div>\r\n            </div>\r\n        </div>,\n  parameters: {\n    docs: {\n      description: {\n        story: \'Team settings form within a comprehensive settings page layout with header and description.\'\n      }\n    }\n  }\n}',...null===(_InSettingsPage_parameters1=InSettingsPage.parameters)||void 0===_InSettingsPage_parameters1||null===(_InSettingsPage_parameters_docs=_InSettingsPage_parameters1.docs)||void 0===_InSettingsPage_parameters_docs?void 0:_InSettingsPage_parameters_docs.source},description:{story:"Team settings form in a settings page layout.\r\nShows how the form appears within a comprehensive settings interface.",...null===(_InSettingsPage_parameters2=InSettingsPage.parameters)||void 0===_InSettingsPage_parameters2||null===(_InSettingsPage_parameters_docs1=_InSettingsPage_parameters2.docs)||void 0===_InSettingsPage_parameters_docs1?void 0:_InSettingsPage_parameters_docs1.description}}};const __namedExportsOrder=["Default","InModal","CustomStyling","InSettingsPage"]}}]);