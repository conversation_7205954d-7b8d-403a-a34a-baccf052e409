"use strict";(self.webpackChunk_cloc_storybook=self.webpackChunk_cloc_storybook||[]).push([[6702],{"./src/stories/team-management/ClocTeamsViewer.stories.tsx":(__unused_webpack_module,__webpack_exports__,__webpack_require__)=>{__webpack_require__.r(__webpack_exports__),__webpack_require__.d(__webpack_exports__,{CustomStyling:()=>CustomStyling,Default:()=>Default,__namedExportsOrder:()=>__namedExportsOrder,default:()=>__WEBPACK_DEFAULT_EXPORT__});var _Default_parameters,_Default_parameters_docs,_Default_parameters1,_Default_parameters_docs1,_Default_parameters2,_CustomStyling_parameters,_CustomStyling_parameters_docs,_CustomStyling_parameters1,_CustomStyling_parameters_docs1,_CustomStyling_parameters2;__webpack_require__("../../node_modules/next/dist/compiled/react/index.js");const __WEBPACK_DEFAULT_EXPORT__={title:"Team Management/Cloc Teams Viewer",component:__webpack_require__("../../packages/toolkit/atoms/dist/index.es.js").ews,parameters:{layout:"centered",docs:{description:{component:"\nA comprehensive teams viewer component that displays organization teams in a table format with advanced management capabilities.\n\n**Features:**\n- Displays organization teams with avatars, names, managers, and members\n- Real-time data from ClocProvider context (organizationTeams)\n- Built-in search functionality for team filtering\n- Pagination with configurable items per page (5, 10, 20, 50)\n- Team creation dialog for adding new teams\n- Responsive table design with loading states\n- Manager and member role differentiation\n- Team selection integration with context\n\n**Data Source:**\n- Gets data from `useClocContext().organizationTeams` (IOrganizationTeamList[])\n- Uses `selectedTeam`, `userOrganizations`, `selectedOrganization`\n- Loading state managed through `organisationsLoading`\n\n**Team Management:**\n- View team details and composition\n- Create new teams (dialog-based)\n- Search and filter teams\n- Navigate between teams\n- Role-based access control\n\n**Use Cases:**\n- Organization team management\n- Team overview dashboards\n- Administrative interfaces\n- Team selection and navigation\n- Organizational structure visualization\n                "}}},argTypes:{className:{control:{type:"text"},description:"Additional CSS classes for custom styling and layout modifications"}}},Default={parameters:{docs:{description:{story:"\nThe default teams viewer displays all organization teams in a comprehensive table format.\nData is automatically fetched from the ClocProvider context.\n\n**Default Features:**\n- Full-width responsive table\n- Search functionality\n- Pagination controls\n- Team creation button\n- Loading states\n- Role-based team information\n            "}}}},CustomStyling={args:{className:"max-w-4xl border-2 border-blue-200 dark:border-blue-800"},parameters:{docs:{description:{story:"\nCustom styled variant demonstrating className prop usage for visual customization.\nShows how to apply custom borders, width constraints, and theme-aware styling.\n\n**Customizations:**\n- Maximum width constraint (4xl)\n- Custom border styling\n- Theme-aware border colors\n- Enhanced visual definition\n            "}}}};Default.parameters={...Default.parameters,docs:{...null===(_Default_parameters=Default.parameters)||void 0===_Default_parameters?void 0:_Default_parameters.docs,source:{originalSource:"{\n  parameters: {\n    docs: {\n      description: {\n        story: `\nThe default teams viewer displays all organization teams in a comprehensive table format.\nData is automatically fetched from the ClocProvider context.\n\n**Default Features:**\n- Full-width responsive table\n- Search functionality\n- Pagination controls\n- Team creation button\n- Loading states\n- Role-based team information\n            `\n      }\n    }\n  }\n}",...null===(_Default_parameters1=Default.parameters)||void 0===_Default_parameters1||null===(_Default_parameters_docs=_Default_parameters1.docs)||void 0===_Default_parameters_docs?void 0:_Default_parameters_docs.source},description:{story:"Default teams viewer with standard styling and full functionality.\r\nShows all organization teams with management capabilities.",...null===(_Default_parameters2=Default.parameters)||void 0===_Default_parameters2||null===(_Default_parameters_docs1=_Default_parameters2.docs)||void 0===_Default_parameters_docs1?void 0:_Default_parameters_docs1.description}}},CustomStyling.parameters={...CustomStyling.parameters,docs:{...null===(_CustomStyling_parameters=CustomStyling.parameters)||void 0===_CustomStyling_parameters?void 0:_CustomStyling_parameters.docs,source:{originalSource:"{\n  args: {\n    className: 'max-w-4xl border-2 border-blue-200 dark:border-blue-800'\n  },\n  parameters: {\n    docs: {\n      description: {\n        story: `\nCustom styled variant demonstrating className prop usage for visual customization.\nShows how to apply custom borders, width constraints, and theme-aware styling.\n\n**Customizations:**\n- Maximum width constraint (4xl)\n- Custom border styling\n- Theme-aware border colors\n- Enhanced visual definition\n            `\n      }\n    }\n  }\n}",...null===(_CustomStyling_parameters1=CustomStyling.parameters)||void 0===_CustomStyling_parameters1||null===(_CustomStyling_parameters_docs=_CustomStyling_parameters1.docs)||void 0===_CustomStyling_parameters_docs?void 0:_CustomStyling_parameters_docs.source},description:{story:"Custom styled teams viewer with enhanced visual definition.\r\nDemonstrates className customization for different layouts.",...null===(_CustomStyling_parameters2=CustomStyling.parameters)||void 0===_CustomStyling_parameters2||null===(_CustomStyling_parameters_docs1=_CustomStyling_parameters2.docs)||void 0===_CustomStyling_parameters_docs1?void 0:_CustomStyling_parameters_docs1.description}}};const __namedExportsOrder=["Default","CustomStyling"]}}]);