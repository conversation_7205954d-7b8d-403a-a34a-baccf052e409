(self.webpackChunk_cloc_storybook=self.webpackChunk_cloc_storybook||[]).push([[7254],{"./node_modules/@storybook/test/dist sync recursive":module=>{function webpackEmptyContext(req){var e=new Error("Cannot find module '"+req+"'");throw e.code="MODULE_NOT_FOUND",e}webpackEmptyContext.keys=()=>[],webpackEmptyContext.resolve=webpackEmptyContext,webpackEmptyContext.id="./node_modules/@storybook/test/dist sync recursive",module.exports=webpackEmptyContext},"./src/stories/time-trackers/ClocBasic.stories.tsx":(__unused_webpack_module,__webpack_exports__,__webpack_require__)=>{"use strict";__webpack_require__.r(__webpack_exports__),__webpack_require__.d(__webpack_exports__,{BasicTimerBorder:()=>BasicTimerBorder,BasicTimerBorderFullRounded:()=>BasicTimerBorderFullRounded,BasicTimerBorderRounded:()=>BasicTimerBorderRounded,BasicTimerContained:()=>BasicTimerContained,BasicTimerContainedFullRounded:()=>BasicTimerContainedFullRounded,BasicTimerContainedRounded:()=>BasicTimerContainedRounded,BasicTimerGray:()=>BasicTimerGray,BasicTimerGrayFullRounded:()=>BasicTimerGrayFullRounded,BasicTimerGrayRounded:()=>BasicTimerGrayRounded,BasicTimerIcon:()=>BasicTimerIcon,BasicTimerIconBorder:()=>BasicTimerIconBorder,BasicTimerIconBorderFullRounded:()=>BasicTimerIconBorderFullRounded,BasicTimerIconBorderFullRoundedProgress:()=>BasicTimerIconBorderFullRoundedProgress,BasicTimerIconBorderFullRoundedProgressButton:()=>BasicTimerIconBorderFullRoundedProgressButton,BasicTimerIconBorderProgress:()=>BasicTimerIconBorderProgress,BasicTimerIconBorderProgressButton:()=>BasicTimerIconBorderProgressButton,BasicTimerIconBorderRounded:()=>BasicTimerIconBorderRounded,BasicTimerIconBorderRoundedProgress:()=>BasicTimerIconBorderRoundedProgress,BasicTimerIconBorderRoundedProgressButton:()=>BasicTimerIconBorderRoundedProgressButton,BasicTimerIconContained:()=>BasicTimerIconContained,BasicTimerIconContainedFullRounded:()=>BasicTimerIconContainedFullRounded,BasicTimerIconContainedFullRoundedProgress:()=>BasicTimerIconContainedFullRoundedProgress,BasicTimerIconContainedFullRoundedProgressButton:()=>BasicTimerIconContainedFullRoundedProgressButton,BasicTimerIconContainedProgress:()=>BasicTimerIconContainedProgress,BasicTimerIconContainedProgressButton:()=>BasicTimerIconContainedProgressButton,BasicTimerIconContainedRounded:()=>BasicTimerIconContainedRounded,BasicTimerIconContainedRoundedProgress:()=>BasicTimerIconContainedRoundedProgress,BasicTimerIconContainedRoundedProgressButton:()=>BasicTimerIconContainedRoundedProgressButton,BasicTimerIconGray:()=>BasicTimerIconGray,BasicTimerIconGrayFullRounded:()=>BasicTimerIconGrayFullRounded,BasicTimerIconGrayFullRoundedProgress:()=>BasicTimerIconGrayFullRoundedProgress,BasicTimerIconGrayFullRoundedProgressButton:()=>BasicTimerIconGrayFullRoundedProgressButton,BasicTimerIconGrayProgress:()=>BasicTimerIconGrayProgress,BasicTimerIconGrayProgressButton:()=>BasicTimerIconGrayProgressButton,BasicTimerIconGrayRounded:()=>BasicTimerIconGrayRounded,BasicTimerIconGrayRoundedProgress:()=>BasicTimerIconGrayRoundedProgress,BasicTimerIconGrayRoundedProgressButton:()=>BasicTimerIconGrayRoundedProgressButton,BasicTimerIconProgress:()=>BasicTimerIconProgress,BasicTimerIconProgressButton:()=>BasicTimerIconProgressButton,DefaultBasicTimer:()=>DefaultBasicTimer,__namedExportsOrder:()=>__namedExportsOrder,default:()=>__WEBPACK_DEFAULT_EXPORT__});var _DefaultBasicTimer_parameters,_DefaultBasicTimer_parameters_docs,_DefaultBasicTimer_parameters1,_BasicTimerBorder_parameters,_BasicTimerBorder_parameters_docs,_BasicTimerBorder_parameters1,_BasicTimerBorderRounded_parameters,_BasicTimerBorderRounded_parameters_docs,_BasicTimerBorderRounded_parameters1,_BasicTimerBorderFullRounded_parameters,_BasicTimerBorderFullRounded_parameters_docs,_BasicTimerBorderFullRounded_parameters1,_BasicTimerGray_parameters,_BasicTimerGray_parameters_docs,_BasicTimerGray_parameters1,_BasicTimerGrayRounded_parameters,_BasicTimerGrayRounded_parameters_docs,_BasicTimerGrayRounded_parameters1,_BasicTimerGrayFullRounded_parameters,_BasicTimerGrayFullRounded_parameters_docs,_BasicTimerGrayFullRounded_parameters1,_BasicTimerContained_parameters,_BasicTimerContained_parameters_docs,_BasicTimerContained_parameters1,_BasicTimerContainedRounded_parameters,_BasicTimerContainedRounded_parameters_docs,_BasicTimerContainedRounded_parameters1,_BasicTimerContainedFullRounded_parameters,_BasicTimerContainedFullRounded_parameters_docs,_BasicTimerContainedFullRounded_parameters1,_BasicTimerIcon_parameters,_BasicTimerIcon_parameters_docs,_BasicTimerIcon_parameters1,_BasicTimerIconBorder_parameters,_BasicTimerIconBorder_parameters_docs,_BasicTimerIconBorder_parameters1,_BasicTimerIconBorderRounded_parameters,_BasicTimerIconBorderRounded_parameters_docs,_BasicTimerIconBorderRounded_parameters1,_BasicTimerIconBorderFullRounded_parameters,_BasicTimerIconBorderFullRounded_parameters_docs,_BasicTimerIconBorderFullRounded_parameters1,_BasicTimerIconGray_parameters,_BasicTimerIconGray_parameters_docs,_BasicTimerIconGray_parameters1,_BasicTimerIconGrayRounded_parameters,_BasicTimerIconGrayRounded_parameters_docs,_BasicTimerIconGrayRounded_parameters1,_BasicTimerIconGrayFullRounded_parameters,_BasicTimerIconGrayFullRounded_parameters_docs,_BasicTimerIconGrayFullRounded_parameters1,_BasicTimerIconContained_parameters,_BasicTimerIconContained_parameters_docs,_BasicTimerIconContained_parameters1,_BasicTimerIconContainedRounded_parameters,_BasicTimerIconContainedRounded_parameters_docs,_BasicTimerIconContainedRounded_parameters1,_BasicTimerIconContainedFullRounded_parameters,_BasicTimerIconContainedFullRounded_parameters_docs,_BasicTimerIconContainedFullRounded_parameters1,_BasicTimerIconProgress_parameters,_BasicTimerIconProgress_parameters_docs,_BasicTimerIconProgress_parameters1,_BasicTimerIconBorderProgress_parameters,_BasicTimerIconBorderProgress_parameters_docs,_BasicTimerIconBorderProgress_parameters1,_BasicTimerIconBorderRoundedProgress_parameters,_BasicTimerIconBorderRoundedProgress_parameters_docs,_BasicTimerIconBorderRoundedProgress_parameters1,_BasicTimerIconBorderFullRoundedProgress_parameters,_BasicTimerIconBorderFullRoundedProgress_parameters_docs,_BasicTimerIconBorderFullRoundedProgress_parameters1,_BasicTimerIconGrayProgress_parameters,_BasicTimerIconGrayProgress_parameters_docs,_BasicTimerIconGrayProgress_parameters1,_BasicTimerIconGrayRoundedProgress_parameters,_BasicTimerIconGrayRoundedProgress_parameters_docs,_BasicTimerIconGrayRoundedProgress_parameters1,_BasicTimerIconGrayFullRoundedProgress_parameters,_BasicTimerIconGrayFullRoundedProgress_parameters_docs,_BasicTimerIconGrayFullRoundedProgress_parameters1,_BasicTimerIconContainedProgress_parameters,_BasicTimerIconContainedProgress_parameters_docs,_BasicTimerIconContainedProgress_parameters1,_BasicTimerIconContainedRoundedProgress_parameters,_BasicTimerIconContainedRoundedProgress_parameters_docs,_BasicTimerIconContainedRoundedProgress_parameters1,_BasicTimerIconContainedFullRoundedProgress_parameters,_BasicTimerIconContainedFullRoundedProgress_parameters_docs,_BasicTimerIconContainedFullRoundedProgress_parameters1,_BasicTimerIconProgressButton_parameters,_BasicTimerIconProgressButton_parameters_docs,_BasicTimerIconProgressButton_parameters1,_BasicTimerIconBorderProgressButton_parameters,_BasicTimerIconBorderProgressButton_parameters_docs,_BasicTimerIconBorderProgressButton_parameters1,_BasicTimerIconBorderRoundedProgressButton_parameters,_BasicTimerIconBorderRoundedProgressButton_parameters_docs,_BasicTimerIconBorderRoundedProgressButton_parameters1,_BasicTimerIconBorderFullRoundedProgressButton_parameters,_BasicTimerIconBorderFullRoundedProgressButton_parameters_docs,_BasicTimerIconBorderFullRoundedProgressButton_parameters1,_BasicTimerIconGrayProgressButton_parameters,_BasicTimerIconGrayProgressButton_parameters_docs,_BasicTimerIconGrayProgressButton_parameters1,_BasicTimerIconGrayRoundedProgressButton_parameters,_BasicTimerIconGrayRoundedProgressButton_parameters_docs,_BasicTimerIconGrayRoundedProgressButton_parameters1,_BasicTimerIconGrayFullRoundedProgressButton_parameters,_BasicTimerIconGrayFullRoundedProgressButton_parameters_docs,_BasicTimerIconGrayFullRoundedProgressButton_parameters1,_BasicTimerIconContainedProgressButton_parameters,_BasicTimerIconContainedProgressButton_parameters_docs,_BasicTimerIconContainedProgressButton_parameters1,_BasicTimerIconContainedRoundedProgressButton_parameters,_BasicTimerIconContainedRoundedProgressButton_parameters_docs,_BasicTimerIconContainedRoundedProgressButton_parameters1,_BasicTimerIconContainedFullRoundedProgressButton_parameters,_BasicTimerIconContainedFullRoundedProgressButton_parameters_docs,_BasicTimerIconContainedFullRoundedProgressButton_parameters1,_storybook_test__WEBPACK_IMPORTED_MODULE_0__=__webpack_require__("./node_modules/@storybook/test/dist/index.mjs");const __WEBPACK_DEFAULT_EXPORT__={title:"Time Trackers/Basic Cloc",component:__webpack_require__("../../packages/toolkit/atoms/dist/index.es.js").O9W,parameters:{layout:"centered"},args:{onClick:(0,_storybook_test__WEBPACK_IMPORTED_MODULE_0__.fn)()}},DefaultBasicTimer={args:{readonly:!0}},BasicTimerBorder={args:{readonly:!0,border:"thick"}},BasicTimerBorderRounded={args:{readonly:!0,border:"thick",rounded:"small"}},BasicTimerBorderFullRounded={args:{readonly:!0,border:"thick",rounded:"large"}},BasicTimerGray={args:{readonly:!0,background:"secondary"}},BasicTimerGrayRounded={args:{readonly:!0,background:"secondary",rounded:"small"}},BasicTimerGrayFullRounded={args:{readonly:!0,background:"secondary",rounded:"large"}},BasicTimerContained={args:{readonly:!0,background:"primary",color:"destructive"}},BasicTimerContainedRounded={args:{readonly:!0,background:"primary",color:"destructive",rounded:"small"}},BasicTimerContainedFullRounded={args:{readonly:!0,background:"primary",color:"destructive",rounded:"large"}},BasicTimerIcon={args:{readonly:!0,icon:!0}},BasicTimerIconBorder={args:{readonly:!0,icon:!0,border:"thick"}},BasicTimerIconBorderRounded={args:{readonly:!0,icon:!0,border:"thick",rounded:"small"}},BasicTimerIconBorderFullRounded={args:{readonly:!0,icon:!0,border:"thick",rounded:"large"}},BasicTimerIconGray={args:{readonly:!0,icon:!0,background:"secondary"}},BasicTimerIconGrayRounded={args:{readonly:!0,icon:!0,background:"secondary",rounded:"small"}},BasicTimerIconGrayFullRounded={args:{readonly:!0,icon:!0,background:"secondary",rounded:"large"}},BasicTimerIconContained={args:{readonly:!0,icon:!0,background:"primary",color:"destructive"}},BasicTimerIconContainedRounded={args:{readonly:!0,icon:!0,background:"primary",color:"destructive",rounded:"small"}},BasicTimerIconContainedFullRounded={args:{readonly:!0,icon:!0,background:"primary",color:"destructive",rounded:"large"}},BasicTimerIconProgress={args:{readonly:!0,icon:!0,progress:!0}},BasicTimerIconBorderProgress={args:{readonly:!0,icon:!0,border:"thick",progress:!0}},BasicTimerIconBorderRoundedProgress={args:{readonly:!0,icon:!0,border:"thick",progress:!0,rounded:"small"}},BasicTimerIconBorderFullRoundedProgress={args:{readonly:!0,icon:!0,border:"thick",progress:!0,rounded:"large"}},BasicTimerIconGrayProgress={args:{readonly:!0,icon:!0,background:"secondary",progress:!0}},BasicTimerIconGrayRoundedProgress={args:{readonly:!0,icon:!0,background:"secondary",progress:!0,rounded:"small"}},BasicTimerIconGrayFullRoundedProgress={args:{readonly:!0,icon:!0,background:"secondary",progress:!0,rounded:"large"}},BasicTimerIconContainedProgress={args:{readonly:!0,icon:!0,background:"primary",color:"destructive",progress:!0}},BasicTimerIconContainedRoundedProgress={args:{readonly:!0,icon:!0,background:"primary",color:"destructive",progress:!0,rounded:"small"}},BasicTimerIconContainedFullRoundedProgress={args:{readonly:!0,icon:!0,background:"primary",color:"destructive",progress:!0,rounded:"large"}},BasicTimerIconProgressButton={args:{icon:!0,progress:!0}},BasicTimerIconBorderProgressButton={args:{icon:!0,progress:!0,border:"thick"}},BasicTimerIconBorderRoundedProgressButton={args:{icon:!0,progress:!0,border:"thick",rounded:"small"}},BasicTimerIconBorderFullRoundedProgressButton={args:{icon:!0,progress:!0,border:"thick",rounded:"large"}},BasicTimerIconGrayProgressButton={args:{icon:!0,progress:!0,background:"secondary"}},BasicTimerIconGrayRoundedProgressButton={args:{icon:!0,progress:!0,background:"secondary",rounded:"small"}},BasicTimerIconGrayFullRoundedProgressButton={args:{icon:!0,progress:!0,background:"secondary",rounded:"large"}},BasicTimerIconContainedProgressButton={args:{icon:!0,progress:!0,background:"primary",color:"destructive"}},BasicTimerIconContainedRoundedProgressButton={args:{icon:!0,progress:!0,background:"primary",color:"destructive",rounded:"small"}},BasicTimerIconContainedFullRoundedProgressButton={args:{icon:!0,background:"primary",color:"destructive",rounded:"large",border:"thick"}};DefaultBasicTimer.parameters={...DefaultBasicTimer.parameters,docs:{...null===(_DefaultBasicTimer_parameters=DefaultBasicTimer.parameters)||void 0===_DefaultBasicTimer_parameters?void 0:_DefaultBasicTimer_parameters.docs,source:{originalSource:"{\n  args: {\n    readonly: true\n  }\n}",...null===(_DefaultBasicTimer_parameters1=DefaultBasicTimer.parameters)||void 0===_DefaultBasicTimer_parameters1||null===(_DefaultBasicTimer_parameters_docs=_DefaultBasicTimer_parameters1.docs)||void 0===_DefaultBasicTimer_parameters_docs?void 0:_DefaultBasicTimer_parameters_docs.source}}},BasicTimerBorder.parameters={...BasicTimerBorder.parameters,docs:{...null===(_BasicTimerBorder_parameters=BasicTimerBorder.parameters)||void 0===_BasicTimerBorder_parameters?void 0:_BasicTimerBorder_parameters.docs,source:{originalSource:"{\n  args: {\n    readonly: true,\n    border: 'thick'\n  }\n}",...null===(_BasicTimerBorder_parameters1=BasicTimerBorder.parameters)||void 0===_BasicTimerBorder_parameters1||null===(_BasicTimerBorder_parameters_docs=_BasicTimerBorder_parameters1.docs)||void 0===_BasicTimerBorder_parameters_docs?void 0:_BasicTimerBorder_parameters_docs.source}}},BasicTimerBorderRounded.parameters={...BasicTimerBorderRounded.parameters,docs:{...null===(_BasicTimerBorderRounded_parameters=BasicTimerBorderRounded.parameters)||void 0===_BasicTimerBorderRounded_parameters?void 0:_BasicTimerBorderRounded_parameters.docs,source:{originalSource:"{\n  args: {\n    readonly: true,\n    border: 'thick',\n    rounded: 'small'\n  }\n}",...null===(_BasicTimerBorderRounded_parameters1=BasicTimerBorderRounded.parameters)||void 0===_BasicTimerBorderRounded_parameters1||null===(_BasicTimerBorderRounded_parameters_docs=_BasicTimerBorderRounded_parameters1.docs)||void 0===_BasicTimerBorderRounded_parameters_docs?void 0:_BasicTimerBorderRounded_parameters_docs.source}}},BasicTimerBorderFullRounded.parameters={...BasicTimerBorderFullRounded.parameters,docs:{...null===(_BasicTimerBorderFullRounded_parameters=BasicTimerBorderFullRounded.parameters)||void 0===_BasicTimerBorderFullRounded_parameters?void 0:_BasicTimerBorderFullRounded_parameters.docs,source:{originalSource:"{\n  args: {\n    readonly: true,\n    border: 'thick',\n    rounded: 'large'\n  }\n}",...null===(_BasicTimerBorderFullRounded_parameters1=BasicTimerBorderFullRounded.parameters)||void 0===_BasicTimerBorderFullRounded_parameters1||null===(_BasicTimerBorderFullRounded_parameters_docs=_BasicTimerBorderFullRounded_parameters1.docs)||void 0===_BasicTimerBorderFullRounded_parameters_docs?void 0:_BasicTimerBorderFullRounded_parameters_docs.source}}},BasicTimerGray.parameters={...BasicTimerGray.parameters,docs:{...null===(_BasicTimerGray_parameters=BasicTimerGray.parameters)||void 0===_BasicTimerGray_parameters?void 0:_BasicTimerGray_parameters.docs,source:{originalSource:"{\n  args: {\n    readonly: true,\n    background: 'secondary'\n  }\n}",...null===(_BasicTimerGray_parameters1=BasicTimerGray.parameters)||void 0===_BasicTimerGray_parameters1||null===(_BasicTimerGray_parameters_docs=_BasicTimerGray_parameters1.docs)||void 0===_BasicTimerGray_parameters_docs?void 0:_BasicTimerGray_parameters_docs.source}}},BasicTimerGrayRounded.parameters={...BasicTimerGrayRounded.parameters,docs:{...null===(_BasicTimerGrayRounded_parameters=BasicTimerGrayRounded.parameters)||void 0===_BasicTimerGrayRounded_parameters?void 0:_BasicTimerGrayRounded_parameters.docs,source:{originalSource:"{\n  args: {\n    readonly: true,\n    background: 'secondary',\n    rounded: 'small'\n  }\n}",...null===(_BasicTimerGrayRounded_parameters1=BasicTimerGrayRounded.parameters)||void 0===_BasicTimerGrayRounded_parameters1||null===(_BasicTimerGrayRounded_parameters_docs=_BasicTimerGrayRounded_parameters1.docs)||void 0===_BasicTimerGrayRounded_parameters_docs?void 0:_BasicTimerGrayRounded_parameters_docs.source}}},BasicTimerGrayFullRounded.parameters={...BasicTimerGrayFullRounded.parameters,docs:{...null===(_BasicTimerGrayFullRounded_parameters=BasicTimerGrayFullRounded.parameters)||void 0===_BasicTimerGrayFullRounded_parameters?void 0:_BasicTimerGrayFullRounded_parameters.docs,source:{originalSource:"{\n  args: {\n    readonly: true,\n    background: 'secondary',\n    rounded: 'large'\n  }\n}",...null===(_BasicTimerGrayFullRounded_parameters1=BasicTimerGrayFullRounded.parameters)||void 0===_BasicTimerGrayFullRounded_parameters1||null===(_BasicTimerGrayFullRounded_parameters_docs=_BasicTimerGrayFullRounded_parameters1.docs)||void 0===_BasicTimerGrayFullRounded_parameters_docs?void 0:_BasicTimerGrayFullRounded_parameters_docs.source}}},BasicTimerContained.parameters={...BasicTimerContained.parameters,docs:{...null===(_BasicTimerContained_parameters=BasicTimerContained.parameters)||void 0===_BasicTimerContained_parameters?void 0:_BasicTimerContained_parameters.docs,source:{originalSource:"{\n  args: {\n    readonly: true,\n    background: 'primary',\n    color: 'destructive'\n  }\n}",...null===(_BasicTimerContained_parameters1=BasicTimerContained.parameters)||void 0===_BasicTimerContained_parameters1||null===(_BasicTimerContained_parameters_docs=_BasicTimerContained_parameters1.docs)||void 0===_BasicTimerContained_parameters_docs?void 0:_BasicTimerContained_parameters_docs.source}}},BasicTimerContainedRounded.parameters={...BasicTimerContainedRounded.parameters,docs:{...null===(_BasicTimerContainedRounded_parameters=BasicTimerContainedRounded.parameters)||void 0===_BasicTimerContainedRounded_parameters?void 0:_BasicTimerContainedRounded_parameters.docs,source:{originalSource:"{\n  args: {\n    readonly: true,\n    background: 'primary',\n    color: 'destructive',\n    rounded: 'small'\n  }\n}",...null===(_BasicTimerContainedRounded_parameters1=BasicTimerContainedRounded.parameters)||void 0===_BasicTimerContainedRounded_parameters1||null===(_BasicTimerContainedRounded_parameters_docs=_BasicTimerContainedRounded_parameters1.docs)||void 0===_BasicTimerContainedRounded_parameters_docs?void 0:_BasicTimerContainedRounded_parameters_docs.source}}},BasicTimerContainedFullRounded.parameters={...BasicTimerContainedFullRounded.parameters,docs:{...null===(_BasicTimerContainedFullRounded_parameters=BasicTimerContainedFullRounded.parameters)||void 0===_BasicTimerContainedFullRounded_parameters?void 0:_BasicTimerContainedFullRounded_parameters.docs,source:{originalSource:"{\n  args: {\n    readonly: true,\n    background: 'primary',\n    color: 'destructive',\n    rounded: 'large'\n  }\n}",...null===(_BasicTimerContainedFullRounded_parameters1=BasicTimerContainedFullRounded.parameters)||void 0===_BasicTimerContainedFullRounded_parameters1||null===(_BasicTimerContainedFullRounded_parameters_docs=_BasicTimerContainedFullRounded_parameters1.docs)||void 0===_BasicTimerContainedFullRounded_parameters_docs?void 0:_BasicTimerContainedFullRounded_parameters_docs.source}}},BasicTimerIcon.parameters={...BasicTimerIcon.parameters,docs:{...null===(_BasicTimerIcon_parameters=BasicTimerIcon.parameters)||void 0===_BasicTimerIcon_parameters?void 0:_BasicTimerIcon_parameters.docs,source:{originalSource:"{\n  args: {\n    readonly: true,\n    icon: true\n  }\n}",...null===(_BasicTimerIcon_parameters1=BasicTimerIcon.parameters)||void 0===_BasicTimerIcon_parameters1||null===(_BasicTimerIcon_parameters_docs=_BasicTimerIcon_parameters1.docs)||void 0===_BasicTimerIcon_parameters_docs?void 0:_BasicTimerIcon_parameters_docs.source}}},BasicTimerIconBorder.parameters={...BasicTimerIconBorder.parameters,docs:{...null===(_BasicTimerIconBorder_parameters=BasicTimerIconBorder.parameters)||void 0===_BasicTimerIconBorder_parameters?void 0:_BasicTimerIconBorder_parameters.docs,source:{originalSource:"{\n  args: {\n    readonly: true,\n    icon: true,\n    border: 'thick'\n  }\n}",...null===(_BasicTimerIconBorder_parameters1=BasicTimerIconBorder.parameters)||void 0===_BasicTimerIconBorder_parameters1||null===(_BasicTimerIconBorder_parameters_docs=_BasicTimerIconBorder_parameters1.docs)||void 0===_BasicTimerIconBorder_parameters_docs?void 0:_BasicTimerIconBorder_parameters_docs.source}}},BasicTimerIconBorderRounded.parameters={...BasicTimerIconBorderRounded.parameters,docs:{...null===(_BasicTimerIconBorderRounded_parameters=BasicTimerIconBorderRounded.parameters)||void 0===_BasicTimerIconBorderRounded_parameters?void 0:_BasicTimerIconBorderRounded_parameters.docs,source:{originalSource:"{\n  args: {\n    readonly: true,\n    icon: true,\n    border: 'thick',\n    rounded: 'small'\n  }\n}",...null===(_BasicTimerIconBorderRounded_parameters1=BasicTimerIconBorderRounded.parameters)||void 0===_BasicTimerIconBorderRounded_parameters1||null===(_BasicTimerIconBorderRounded_parameters_docs=_BasicTimerIconBorderRounded_parameters1.docs)||void 0===_BasicTimerIconBorderRounded_parameters_docs?void 0:_BasicTimerIconBorderRounded_parameters_docs.source}}},BasicTimerIconBorderFullRounded.parameters={...BasicTimerIconBorderFullRounded.parameters,docs:{...null===(_BasicTimerIconBorderFullRounded_parameters=BasicTimerIconBorderFullRounded.parameters)||void 0===_BasicTimerIconBorderFullRounded_parameters?void 0:_BasicTimerIconBorderFullRounded_parameters.docs,source:{originalSource:"{\n  args: {\n    readonly: true,\n    icon: true,\n    border: 'thick',\n    rounded: 'large'\n  }\n}",...null===(_BasicTimerIconBorderFullRounded_parameters1=BasicTimerIconBorderFullRounded.parameters)||void 0===_BasicTimerIconBorderFullRounded_parameters1||null===(_BasicTimerIconBorderFullRounded_parameters_docs=_BasicTimerIconBorderFullRounded_parameters1.docs)||void 0===_BasicTimerIconBorderFullRounded_parameters_docs?void 0:_BasicTimerIconBorderFullRounded_parameters_docs.source}}},BasicTimerIconGray.parameters={...BasicTimerIconGray.parameters,docs:{...null===(_BasicTimerIconGray_parameters=BasicTimerIconGray.parameters)||void 0===_BasicTimerIconGray_parameters?void 0:_BasicTimerIconGray_parameters.docs,source:{originalSource:"{\n  args: {\n    readonly: true,\n    icon: true,\n    background: 'secondary'\n  }\n}",...null===(_BasicTimerIconGray_parameters1=BasicTimerIconGray.parameters)||void 0===_BasicTimerIconGray_parameters1||null===(_BasicTimerIconGray_parameters_docs=_BasicTimerIconGray_parameters1.docs)||void 0===_BasicTimerIconGray_parameters_docs?void 0:_BasicTimerIconGray_parameters_docs.source}}},BasicTimerIconGrayRounded.parameters={...BasicTimerIconGrayRounded.parameters,docs:{...null===(_BasicTimerIconGrayRounded_parameters=BasicTimerIconGrayRounded.parameters)||void 0===_BasicTimerIconGrayRounded_parameters?void 0:_BasicTimerIconGrayRounded_parameters.docs,source:{originalSource:"{\n  args: {\n    readonly: true,\n    icon: true,\n    background: 'secondary',\n    rounded: 'small'\n  }\n}",...null===(_BasicTimerIconGrayRounded_parameters1=BasicTimerIconGrayRounded.parameters)||void 0===_BasicTimerIconGrayRounded_parameters1||null===(_BasicTimerIconGrayRounded_parameters_docs=_BasicTimerIconGrayRounded_parameters1.docs)||void 0===_BasicTimerIconGrayRounded_parameters_docs?void 0:_BasicTimerIconGrayRounded_parameters_docs.source}}},BasicTimerIconGrayFullRounded.parameters={...BasicTimerIconGrayFullRounded.parameters,docs:{...null===(_BasicTimerIconGrayFullRounded_parameters=BasicTimerIconGrayFullRounded.parameters)||void 0===_BasicTimerIconGrayFullRounded_parameters?void 0:_BasicTimerIconGrayFullRounded_parameters.docs,source:{originalSource:"{\n  args: {\n    readonly: true,\n    icon: true,\n    background: 'secondary',\n    rounded: 'large'\n  }\n}",...null===(_BasicTimerIconGrayFullRounded_parameters1=BasicTimerIconGrayFullRounded.parameters)||void 0===_BasicTimerIconGrayFullRounded_parameters1||null===(_BasicTimerIconGrayFullRounded_parameters_docs=_BasicTimerIconGrayFullRounded_parameters1.docs)||void 0===_BasicTimerIconGrayFullRounded_parameters_docs?void 0:_BasicTimerIconGrayFullRounded_parameters_docs.source}}},BasicTimerIconContained.parameters={...BasicTimerIconContained.parameters,docs:{...null===(_BasicTimerIconContained_parameters=BasicTimerIconContained.parameters)||void 0===_BasicTimerIconContained_parameters?void 0:_BasicTimerIconContained_parameters.docs,source:{originalSource:"{\n  args: {\n    readonly: true,\n    icon: true,\n    background: 'primary',\n    color: 'destructive'\n  }\n}",...null===(_BasicTimerIconContained_parameters1=BasicTimerIconContained.parameters)||void 0===_BasicTimerIconContained_parameters1||null===(_BasicTimerIconContained_parameters_docs=_BasicTimerIconContained_parameters1.docs)||void 0===_BasicTimerIconContained_parameters_docs?void 0:_BasicTimerIconContained_parameters_docs.source}}},BasicTimerIconContainedRounded.parameters={...BasicTimerIconContainedRounded.parameters,docs:{...null===(_BasicTimerIconContainedRounded_parameters=BasicTimerIconContainedRounded.parameters)||void 0===_BasicTimerIconContainedRounded_parameters?void 0:_BasicTimerIconContainedRounded_parameters.docs,source:{originalSource:"{\n  args: {\n    readonly: true,\n    icon: true,\n    background: 'primary',\n    color: 'destructive',\n    rounded: 'small'\n  }\n}",...null===(_BasicTimerIconContainedRounded_parameters1=BasicTimerIconContainedRounded.parameters)||void 0===_BasicTimerIconContainedRounded_parameters1||null===(_BasicTimerIconContainedRounded_parameters_docs=_BasicTimerIconContainedRounded_parameters1.docs)||void 0===_BasicTimerIconContainedRounded_parameters_docs?void 0:_BasicTimerIconContainedRounded_parameters_docs.source}}},BasicTimerIconContainedFullRounded.parameters={...BasicTimerIconContainedFullRounded.parameters,docs:{...null===(_BasicTimerIconContainedFullRounded_parameters=BasicTimerIconContainedFullRounded.parameters)||void 0===_BasicTimerIconContainedFullRounded_parameters?void 0:_BasicTimerIconContainedFullRounded_parameters.docs,source:{originalSource:"{\n  args: {\n    readonly: true,\n    icon: true,\n    background: 'primary',\n    color: 'destructive',\n    rounded: 'large'\n  }\n}",...null===(_BasicTimerIconContainedFullRounded_parameters1=BasicTimerIconContainedFullRounded.parameters)||void 0===_BasicTimerIconContainedFullRounded_parameters1||null===(_BasicTimerIconContainedFullRounded_parameters_docs=_BasicTimerIconContainedFullRounded_parameters1.docs)||void 0===_BasicTimerIconContainedFullRounded_parameters_docs?void 0:_BasicTimerIconContainedFullRounded_parameters_docs.source}}},BasicTimerIconProgress.parameters={...BasicTimerIconProgress.parameters,docs:{...null===(_BasicTimerIconProgress_parameters=BasicTimerIconProgress.parameters)||void 0===_BasicTimerIconProgress_parameters?void 0:_BasicTimerIconProgress_parameters.docs,source:{originalSource:"{\n  args: {\n    readonly: true,\n    icon: true,\n    progress: true\n  }\n}",...null===(_BasicTimerIconProgress_parameters1=BasicTimerIconProgress.parameters)||void 0===_BasicTimerIconProgress_parameters1||null===(_BasicTimerIconProgress_parameters_docs=_BasicTimerIconProgress_parameters1.docs)||void 0===_BasicTimerIconProgress_parameters_docs?void 0:_BasicTimerIconProgress_parameters_docs.source}}},BasicTimerIconBorderProgress.parameters={...BasicTimerIconBorderProgress.parameters,docs:{...null===(_BasicTimerIconBorderProgress_parameters=BasicTimerIconBorderProgress.parameters)||void 0===_BasicTimerIconBorderProgress_parameters?void 0:_BasicTimerIconBorderProgress_parameters.docs,source:{originalSource:"{\n  args: {\n    readonly: true,\n    icon: true,\n    border: 'thick',\n    progress: true\n  }\n}",...null===(_BasicTimerIconBorderProgress_parameters1=BasicTimerIconBorderProgress.parameters)||void 0===_BasicTimerIconBorderProgress_parameters1||null===(_BasicTimerIconBorderProgress_parameters_docs=_BasicTimerIconBorderProgress_parameters1.docs)||void 0===_BasicTimerIconBorderProgress_parameters_docs?void 0:_BasicTimerIconBorderProgress_parameters_docs.source}}},BasicTimerIconBorderRoundedProgress.parameters={...BasicTimerIconBorderRoundedProgress.parameters,docs:{...null===(_BasicTimerIconBorderRoundedProgress_parameters=BasicTimerIconBorderRoundedProgress.parameters)||void 0===_BasicTimerIconBorderRoundedProgress_parameters?void 0:_BasicTimerIconBorderRoundedProgress_parameters.docs,source:{originalSource:"{\n  args: {\n    readonly: true,\n    icon: true,\n    border: 'thick',\n    progress: true,\n    rounded: 'small'\n  }\n}",...null===(_BasicTimerIconBorderRoundedProgress_parameters1=BasicTimerIconBorderRoundedProgress.parameters)||void 0===_BasicTimerIconBorderRoundedProgress_parameters1||null===(_BasicTimerIconBorderRoundedProgress_parameters_docs=_BasicTimerIconBorderRoundedProgress_parameters1.docs)||void 0===_BasicTimerIconBorderRoundedProgress_parameters_docs?void 0:_BasicTimerIconBorderRoundedProgress_parameters_docs.source}}},BasicTimerIconBorderFullRoundedProgress.parameters={...BasicTimerIconBorderFullRoundedProgress.parameters,docs:{...null===(_BasicTimerIconBorderFullRoundedProgress_parameters=BasicTimerIconBorderFullRoundedProgress.parameters)||void 0===_BasicTimerIconBorderFullRoundedProgress_parameters?void 0:_BasicTimerIconBorderFullRoundedProgress_parameters.docs,source:{originalSource:"{\n  args: {\n    readonly: true,\n    icon: true,\n    border: 'thick',\n    progress: true,\n    rounded: 'large'\n  }\n}",...null===(_BasicTimerIconBorderFullRoundedProgress_parameters1=BasicTimerIconBorderFullRoundedProgress.parameters)||void 0===_BasicTimerIconBorderFullRoundedProgress_parameters1||null===(_BasicTimerIconBorderFullRoundedProgress_parameters_docs=_BasicTimerIconBorderFullRoundedProgress_parameters1.docs)||void 0===_BasicTimerIconBorderFullRoundedProgress_parameters_docs?void 0:_BasicTimerIconBorderFullRoundedProgress_parameters_docs.source}}},BasicTimerIconGrayProgress.parameters={...BasicTimerIconGrayProgress.parameters,docs:{...null===(_BasicTimerIconGrayProgress_parameters=BasicTimerIconGrayProgress.parameters)||void 0===_BasicTimerIconGrayProgress_parameters?void 0:_BasicTimerIconGrayProgress_parameters.docs,source:{originalSource:"{\n  args: {\n    readonly: true,\n    icon: true,\n    background: 'secondary',\n    progress: true\n  }\n}",...null===(_BasicTimerIconGrayProgress_parameters1=BasicTimerIconGrayProgress.parameters)||void 0===_BasicTimerIconGrayProgress_parameters1||null===(_BasicTimerIconGrayProgress_parameters_docs=_BasicTimerIconGrayProgress_parameters1.docs)||void 0===_BasicTimerIconGrayProgress_parameters_docs?void 0:_BasicTimerIconGrayProgress_parameters_docs.source}}},BasicTimerIconGrayRoundedProgress.parameters={...BasicTimerIconGrayRoundedProgress.parameters,docs:{...null===(_BasicTimerIconGrayRoundedProgress_parameters=BasicTimerIconGrayRoundedProgress.parameters)||void 0===_BasicTimerIconGrayRoundedProgress_parameters?void 0:_BasicTimerIconGrayRoundedProgress_parameters.docs,source:{originalSource:"{\n  args: {\n    readonly: true,\n    icon: true,\n    background: 'secondary',\n    progress: true,\n    rounded: 'small'\n  }\n}",...null===(_BasicTimerIconGrayRoundedProgress_parameters1=BasicTimerIconGrayRoundedProgress.parameters)||void 0===_BasicTimerIconGrayRoundedProgress_parameters1||null===(_BasicTimerIconGrayRoundedProgress_parameters_docs=_BasicTimerIconGrayRoundedProgress_parameters1.docs)||void 0===_BasicTimerIconGrayRoundedProgress_parameters_docs?void 0:_BasicTimerIconGrayRoundedProgress_parameters_docs.source}}},BasicTimerIconGrayFullRoundedProgress.parameters={...BasicTimerIconGrayFullRoundedProgress.parameters,docs:{...null===(_BasicTimerIconGrayFullRoundedProgress_parameters=BasicTimerIconGrayFullRoundedProgress.parameters)||void 0===_BasicTimerIconGrayFullRoundedProgress_parameters?void 0:_BasicTimerIconGrayFullRoundedProgress_parameters.docs,source:{originalSource:"{\n  args: {\n    readonly: true,\n    icon: true,\n    background: 'secondary',\n    progress: true,\n    rounded: 'large'\n  }\n}",...null===(_BasicTimerIconGrayFullRoundedProgress_parameters1=BasicTimerIconGrayFullRoundedProgress.parameters)||void 0===_BasicTimerIconGrayFullRoundedProgress_parameters1||null===(_BasicTimerIconGrayFullRoundedProgress_parameters_docs=_BasicTimerIconGrayFullRoundedProgress_parameters1.docs)||void 0===_BasicTimerIconGrayFullRoundedProgress_parameters_docs?void 0:_BasicTimerIconGrayFullRoundedProgress_parameters_docs.source}}},BasicTimerIconContainedProgress.parameters={...BasicTimerIconContainedProgress.parameters,docs:{...null===(_BasicTimerIconContainedProgress_parameters=BasicTimerIconContainedProgress.parameters)||void 0===_BasicTimerIconContainedProgress_parameters?void 0:_BasicTimerIconContainedProgress_parameters.docs,source:{originalSource:"{\n  args: {\n    readonly: true,\n    icon: true,\n    background: 'primary',\n    color: 'destructive',\n    progress: true\n  }\n}",...null===(_BasicTimerIconContainedProgress_parameters1=BasicTimerIconContainedProgress.parameters)||void 0===_BasicTimerIconContainedProgress_parameters1||null===(_BasicTimerIconContainedProgress_parameters_docs=_BasicTimerIconContainedProgress_parameters1.docs)||void 0===_BasicTimerIconContainedProgress_parameters_docs?void 0:_BasicTimerIconContainedProgress_parameters_docs.source}}},BasicTimerIconContainedRoundedProgress.parameters={...BasicTimerIconContainedRoundedProgress.parameters,docs:{...null===(_BasicTimerIconContainedRoundedProgress_parameters=BasicTimerIconContainedRoundedProgress.parameters)||void 0===_BasicTimerIconContainedRoundedProgress_parameters?void 0:_BasicTimerIconContainedRoundedProgress_parameters.docs,source:{originalSource:"{\n  args: {\n    readonly: true,\n    icon: true,\n    background: 'primary',\n    color: 'destructive',\n    progress: true,\n    rounded: 'small'\n  }\n}",...null===(_BasicTimerIconContainedRoundedProgress_parameters1=BasicTimerIconContainedRoundedProgress.parameters)||void 0===_BasicTimerIconContainedRoundedProgress_parameters1||null===(_BasicTimerIconContainedRoundedProgress_parameters_docs=_BasicTimerIconContainedRoundedProgress_parameters1.docs)||void 0===_BasicTimerIconContainedRoundedProgress_parameters_docs?void 0:_BasicTimerIconContainedRoundedProgress_parameters_docs.source}}},BasicTimerIconContainedFullRoundedProgress.parameters={...BasicTimerIconContainedFullRoundedProgress.parameters,docs:{...null===(_BasicTimerIconContainedFullRoundedProgress_parameters=BasicTimerIconContainedFullRoundedProgress.parameters)||void 0===_BasicTimerIconContainedFullRoundedProgress_parameters?void 0:_BasicTimerIconContainedFullRoundedProgress_parameters.docs,source:{originalSource:"{\n  args: {\n    readonly: true,\n    icon: true,\n    background: 'primary',\n    color: 'destructive',\n    progress: true,\n    rounded: 'large'\n  }\n}",...null===(_BasicTimerIconContainedFullRoundedProgress_parameters1=BasicTimerIconContainedFullRoundedProgress.parameters)||void 0===_BasicTimerIconContainedFullRoundedProgress_parameters1||null===(_BasicTimerIconContainedFullRoundedProgress_parameters_docs=_BasicTimerIconContainedFullRoundedProgress_parameters1.docs)||void 0===_BasicTimerIconContainedFullRoundedProgress_parameters_docs?void 0:_BasicTimerIconContainedFullRoundedProgress_parameters_docs.source}}},BasicTimerIconProgressButton.parameters={...BasicTimerIconProgressButton.parameters,docs:{...null===(_BasicTimerIconProgressButton_parameters=BasicTimerIconProgressButton.parameters)||void 0===_BasicTimerIconProgressButton_parameters?void 0:_BasicTimerIconProgressButton_parameters.docs,source:{originalSource:"{\n  args: {\n    icon: true,\n    progress: true\n  }\n}",...null===(_BasicTimerIconProgressButton_parameters1=BasicTimerIconProgressButton.parameters)||void 0===_BasicTimerIconProgressButton_parameters1||null===(_BasicTimerIconProgressButton_parameters_docs=_BasicTimerIconProgressButton_parameters1.docs)||void 0===_BasicTimerIconProgressButton_parameters_docs?void 0:_BasicTimerIconProgressButton_parameters_docs.source}}},BasicTimerIconBorderProgressButton.parameters={...BasicTimerIconBorderProgressButton.parameters,docs:{...null===(_BasicTimerIconBorderProgressButton_parameters=BasicTimerIconBorderProgressButton.parameters)||void 0===_BasicTimerIconBorderProgressButton_parameters?void 0:_BasicTimerIconBorderProgressButton_parameters.docs,source:{originalSource:"{\n  args: {\n    icon: true,\n    progress: true,\n    border: 'thick'\n  }\n}",...null===(_BasicTimerIconBorderProgressButton_parameters1=BasicTimerIconBorderProgressButton.parameters)||void 0===_BasicTimerIconBorderProgressButton_parameters1||null===(_BasicTimerIconBorderProgressButton_parameters_docs=_BasicTimerIconBorderProgressButton_parameters1.docs)||void 0===_BasicTimerIconBorderProgressButton_parameters_docs?void 0:_BasicTimerIconBorderProgressButton_parameters_docs.source}}},BasicTimerIconBorderRoundedProgressButton.parameters={...BasicTimerIconBorderRoundedProgressButton.parameters,docs:{...null===(_BasicTimerIconBorderRoundedProgressButton_parameters=BasicTimerIconBorderRoundedProgressButton.parameters)||void 0===_BasicTimerIconBorderRoundedProgressButton_parameters?void 0:_BasicTimerIconBorderRoundedProgressButton_parameters.docs,source:{originalSource:"{\n  args: {\n    icon: true,\n    progress: true,\n    border: 'thick',\n    rounded: 'small'\n  }\n}",...null===(_BasicTimerIconBorderRoundedProgressButton_parameters1=BasicTimerIconBorderRoundedProgressButton.parameters)||void 0===_BasicTimerIconBorderRoundedProgressButton_parameters1||null===(_BasicTimerIconBorderRoundedProgressButton_parameters_docs=_BasicTimerIconBorderRoundedProgressButton_parameters1.docs)||void 0===_BasicTimerIconBorderRoundedProgressButton_parameters_docs?void 0:_BasicTimerIconBorderRoundedProgressButton_parameters_docs.source}}},BasicTimerIconBorderFullRoundedProgressButton.parameters={...BasicTimerIconBorderFullRoundedProgressButton.parameters,docs:{...null===(_BasicTimerIconBorderFullRoundedProgressButton_parameters=BasicTimerIconBorderFullRoundedProgressButton.parameters)||void 0===_BasicTimerIconBorderFullRoundedProgressButton_parameters?void 0:_BasicTimerIconBorderFullRoundedProgressButton_parameters.docs,source:{originalSource:"{\n  args: {\n    icon: true,\n    progress: true,\n    border: 'thick',\n    rounded: 'large'\n  }\n}",...null===(_BasicTimerIconBorderFullRoundedProgressButton_parameters1=BasicTimerIconBorderFullRoundedProgressButton.parameters)||void 0===_BasicTimerIconBorderFullRoundedProgressButton_parameters1||null===(_BasicTimerIconBorderFullRoundedProgressButton_parameters_docs=_BasicTimerIconBorderFullRoundedProgressButton_parameters1.docs)||void 0===_BasicTimerIconBorderFullRoundedProgressButton_parameters_docs?void 0:_BasicTimerIconBorderFullRoundedProgressButton_parameters_docs.source}}},BasicTimerIconGrayProgressButton.parameters={...BasicTimerIconGrayProgressButton.parameters,docs:{...null===(_BasicTimerIconGrayProgressButton_parameters=BasicTimerIconGrayProgressButton.parameters)||void 0===_BasicTimerIconGrayProgressButton_parameters?void 0:_BasicTimerIconGrayProgressButton_parameters.docs,source:{originalSource:"{\n  args: {\n    icon: true,\n    progress: true,\n    background: 'secondary'\n  }\n}",...null===(_BasicTimerIconGrayProgressButton_parameters1=BasicTimerIconGrayProgressButton.parameters)||void 0===_BasicTimerIconGrayProgressButton_parameters1||null===(_BasicTimerIconGrayProgressButton_parameters_docs=_BasicTimerIconGrayProgressButton_parameters1.docs)||void 0===_BasicTimerIconGrayProgressButton_parameters_docs?void 0:_BasicTimerIconGrayProgressButton_parameters_docs.source}}},BasicTimerIconGrayRoundedProgressButton.parameters={...BasicTimerIconGrayRoundedProgressButton.parameters,docs:{...null===(_BasicTimerIconGrayRoundedProgressButton_parameters=BasicTimerIconGrayRoundedProgressButton.parameters)||void 0===_BasicTimerIconGrayRoundedProgressButton_parameters?void 0:_BasicTimerIconGrayRoundedProgressButton_parameters.docs,source:{originalSource:"{\n  args: {\n    icon: true,\n    progress: true,\n    background: 'secondary',\n    rounded: 'small'\n  }\n}",...null===(_BasicTimerIconGrayRoundedProgressButton_parameters1=BasicTimerIconGrayRoundedProgressButton.parameters)||void 0===_BasicTimerIconGrayRoundedProgressButton_parameters1||null===(_BasicTimerIconGrayRoundedProgressButton_parameters_docs=_BasicTimerIconGrayRoundedProgressButton_parameters1.docs)||void 0===_BasicTimerIconGrayRoundedProgressButton_parameters_docs?void 0:_BasicTimerIconGrayRoundedProgressButton_parameters_docs.source}}},BasicTimerIconGrayFullRoundedProgressButton.parameters={...BasicTimerIconGrayFullRoundedProgressButton.parameters,docs:{...null===(_BasicTimerIconGrayFullRoundedProgressButton_parameters=BasicTimerIconGrayFullRoundedProgressButton.parameters)||void 0===_BasicTimerIconGrayFullRoundedProgressButton_parameters?void 0:_BasicTimerIconGrayFullRoundedProgressButton_parameters.docs,source:{originalSource:"{\n  args: {\n    icon: true,\n    progress: true,\n    background: 'secondary',\n    rounded: 'large'\n  }\n}",...null===(_BasicTimerIconGrayFullRoundedProgressButton_parameters1=BasicTimerIconGrayFullRoundedProgressButton.parameters)||void 0===_BasicTimerIconGrayFullRoundedProgressButton_parameters1||null===(_BasicTimerIconGrayFullRoundedProgressButton_parameters_docs=_BasicTimerIconGrayFullRoundedProgressButton_parameters1.docs)||void 0===_BasicTimerIconGrayFullRoundedProgressButton_parameters_docs?void 0:_BasicTimerIconGrayFullRoundedProgressButton_parameters_docs.source}}},BasicTimerIconContainedProgressButton.parameters={...BasicTimerIconContainedProgressButton.parameters,docs:{...null===(_BasicTimerIconContainedProgressButton_parameters=BasicTimerIconContainedProgressButton.parameters)||void 0===_BasicTimerIconContainedProgressButton_parameters?void 0:_BasicTimerIconContainedProgressButton_parameters.docs,source:{originalSource:"{\n  args: {\n    icon: true,\n    progress: true,\n    background: 'primary',\n    color: 'destructive'\n  }\n}",...null===(_BasicTimerIconContainedProgressButton_parameters1=BasicTimerIconContainedProgressButton.parameters)||void 0===_BasicTimerIconContainedProgressButton_parameters1||null===(_BasicTimerIconContainedProgressButton_parameters_docs=_BasicTimerIconContainedProgressButton_parameters1.docs)||void 0===_BasicTimerIconContainedProgressButton_parameters_docs?void 0:_BasicTimerIconContainedProgressButton_parameters_docs.source}}},BasicTimerIconContainedRoundedProgressButton.parameters={...BasicTimerIconContainedRoundedProgressButton.parameters,docs:{...null===(_BasicTimerIconContainedRoundedProgressButton_parameters=BasicTimerIconContainedRoundedProgressButton.parameters)||void 0===_BasicTimerIconContainedRoundedProgressButton_parameters?void 0:_BasicTimerIconContainedRoundedProgressButton_parameters.docs,source:{originalSource:"{\n  args: {\n    icon: true,\n    progress: true,\n    background: 'primary',\n    color: 'destructive',\n    rounded: 'small'\n  }\n}",...null===(_BasicTimerIconContainedRoundedProgressButton_parameters1=BasicTimerIconContainedRoundedProgressButton.parameters)||void 0===_BasicTimerIconContainedRoundedProgressButton_parameters1||null===(_BasicTimerIconContainedRoundedProgressButton_parameters_docs=_BasicTimerIconContainedRoundedProgressButton_parameters1.docs)||void 0===_BasicTimerIconContainedRoundedProgressButton_parameters_docs?void 0:_BasicTimerIconContainedRoundedProgressButton_parameters_docs.source}}},BasicTimerIconContainedFullRoundedProgressButton.parameters={...BasicTimerIconContainedFullRoundedProgressButton.parameters,docs:{...null===(_BasicTimerIconContainedFullRoundedProgressButton_parameters=BasicTimerIconContainedFullRoundedProgressButton.parameters)||void 0===_BasicTimerIconContainedFullRoundedProgressButton_parameters?void 0:_BasicTimerIconContainedFullRoundedProgressButton_parameters.docs,source:{originalSource:"{\n  args: {\n    icon: true,\n    // progress: true,\n    background: 'primary',\n    color: 'destructive',\n    rounded: 'large',\n    border: 'thick'\n  }\n}",...null===(_BasicTimerIconContainedFullRoundedProgressButton_parameters1=BasicTimerIconContainedFullRoundedProgressButton.parameters)||void 0===_BasicTimerIconContainedFullRoundedProgressButton_parameters1||null===(_BasicTimerIconContainedFullRoundedProgressButton_parameters_docs=_BasicTimerIconContainedFullRoundedProgressButton_parameters1.docs)||void 0===_BasicTimerIconContainedFullRoundedProgressButton_parameters_docs?void 0:_BasicTimerIconContainedFullRoundedProgressButton_parameters_docs.source}}};const __namedExportsOrder=["DefaultBasicTimer","BasicTimerBorder","BasicTimerBorderRounded","BasicTimerBorderFullRounded","BasicTimerGray","BasicTimerGrayRounded","BasicTimerGrayFullRounded","BasicTimerContained","BasicTimerContainedRounded","BasicTimerContainedFullRounded","BasicTimerIcon","BasicTimerIconBorder","BasicTimerIconBorderRounded","BasicTimerIconBorderFullRounded","BasicTimerIconGray","BasicTimerIconGrayRounded","BasicTimerIconGrayFullRounded","BasicTimerIconContained","BasicTimerIconContainedRounded","BasicTimerIconContainedFullRounded","BasicTimerIconProgress","BasicTimerIconBorderProgress","BasicTimerIconBorderRoundedProgress","BasicTimerIconBorderFullRoundedProgress","BasicTimerIconGrayProgress","BasicTimerIconGrayRoundedProgress","BasicTimerIconGrayFullRoundedProgress","BasicTimerIconContainedProgress","BasicTimerIconContainedRoundedProgress","BasicTimerIconContainedFullRoundedProgress","BasicTimerIconProgressButton","BasicTimerIconBorderProgressButton","BasicTimerIconBorderRoundedProgressButton","BasicTimerIconBorderFullRoundedProgressButton","BasicTimerIconGrayProgressButton","BasicTimerIconGrayRoundedProgressButton","BasicTimerIconGrayFullRoundedProgressButton","BasicTimerIconContainedProgressButton","BasicTimerIconContainedRoundedProgressButton","BasicTimerIconContainedFullRoundedProgressButton"]}}]);