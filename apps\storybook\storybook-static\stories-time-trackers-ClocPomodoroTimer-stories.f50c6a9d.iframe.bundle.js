"use strict";(self.webpackChunk_cloc_storybook=self.webpackChunk_cloc_storybook||[]).push([[8704],{"./src/stories/time-trackers/ClocPomodoroTimer.stories.tsx":(__unused_webpack_module,__webpack_exports__,__webpack_require__)=>{__webpack_require__.r(__webpack_exports__),__webpack_require__.d(__webpack_exports__,{BorderedVariant:()=>BorderedVariant,CompactVariant:()=>CompactVariant,DarkTheme:()=>DarkTheme,Default:()=>Default,IntegrationShowcase:()=>IntegrationShowcase,LargeMinimal:()=>LargeMinimal,LargeMinimalBordered:()=>LargeMinimalBordered,LargeSize:()=>LargeSize,MinimalVariant:()=>MinimalVariant,MobileResponsive:()=>MobileResponsive,SmallCompactBordered:()=>SmallCompactBordered,SmallSize:()=>SmallSize,__namedExportsOrder:()=>__namedExportsOrder,default:()=>__WEBPACK_DEFAULT_EXPORT__});var _Default_parameters,_Default_parameters_docs,_Default_parameters1,_Default_parameters_docs1,_Default_parameters2,_SmallSize_parameters,_SmallSize_parameters_docs,_SmallSize_parameters1,_SmallSize_parameters_docs1,_SmallSize_parameters2,_LargeSize_parameters,_LargeSize_parameters_docs,_LargeSize_parameters1,_LargeSize_parameters_docs1,_LargeSize_parameters2,_CompactVariant_parameters,_CompactVariant_parameters_docs,_CompactVariant_parameters1,_CompactVariant_parameters_docs1,_CompactVariant_parameters2,_MinimalVariant_parameters,_MinimalVariant_parameters_docs,_MinimalVariant_parameters1,_MinimalVariant_parameters_docs1,_MinimalVariant_parameters2,_BorderedVariant_parameters,_BorderedVariant_parameters_docs,_BorderedVariant_parameters1,_BorderedVariant_parameters_docs1,_BorderedVariant_parameters2,_SmallCompactBordered_parameters,_SmallCompactBordered_parameters_docs,_SmallCompactBordered_parameters1,_SmallCompactBordered_parameters_docs1,_SmallCompactBordered_parameters2,_LargeMinimal_parameters,_LargeMinimal_parameters_docs,_LargeMinimal_parameters1,_LargeMinimal_parameters_docs1,_LargeMinimal_parameters2,_LargeMinimalBordered_parameters,_LargeMinimalBordered_parameters_docs,_LargeMinimalBordered_parameters1,_LargeMinimalBordered_parameters_docs1,_LargeMinimalBordered_parameters2,_DarkTheme_parameters,_DarkTheme_parameters_docs,_DarkTheme_parameters1,_DarkTheme_parameters_docs1,_DarkTheme_parameters2,_MobileResponsive_parameters,_MobileResponsive_parameters_docs,_MobileResponsive_parameters1,_MobileResponsive_parameters_docs1,_MobileResponsive_parameters2,_IntegrationShowcase_parameters,_IntegrationShowcase_parameters_docs,_IntegrationShowcase_parameters1,_IntegrationShowcase_parameters_docs1,_IntegrationShowcase_parameters2,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__=__webpack_require__("../../node_modules/next/dist/compiled/react/jsx-runtime.js");__webpack_require__("../../node_modules/next/dist/compiled/react/index.js");const __WEBPACK_DEFAULT_EXPORT__={title:"Time Trackers/Pomodoro Timer",component:__webpack_require__("../../packages/toolkit/atoms/dist/index.es.js").MRx,parameters:{layout:"centered",docs:{description:{component:"\nA comprehensive Pomodoro timer component that implements the Pomodoro Technique for time management.\n\n**Features:**\n- Multiple timer presets (Very Short: 15min, Short: 20min, Medium: 25min, Long: 30min, Custom)\n- Automatic cycling between work sessions and breaks\n- Long breaks every 4 pomodoros\n- Task management with add, edit, delete, and completion tracking\n- Timer customization with custom durations\n- Session counters for pomodoros, rests, and long rests\n- Local storage persistence for tasks and counters\n- Responsive design with smooth animations\n\n**Timer Flow:**\n1. Work session (Pomodoro) → Short break\n2. After 4 pomodoros → Long break\n3. Automatic progression between sessions\n\n**Customization:**\n- Custom work session duration\n- Custom short break duration\n- Custom long break duration\n- Task management for productivity tracking\n                "}}},argTypes:{size:{control:{type:"select"},options:["sm","md","lg"],description:"Size variant of the timer"},variant:{control:{type:"select"},options:["default","compact","minimal"],description:"Visual style variant"},border:{control:{type:"select"},options:["none","bordered"],description:"Border style variant"},className:{control:{type:"text"},description:"Additional CSS classes"}},decorators:[Story=>(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("div",{className:"w-[70vw]",children:(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(Story,{})})]},Default={parameters:{docs:{description:{story:"\nThe default Pomodoro timer starts with the medium preset (25-minute work sessions).\nUsers can start/pause the timer, manage tasks, and customize timer durations.\n\n**Default Settings:**\n- Work Session: 25 minutes\n- Short Break: 5 minutes\n- Long Break: 15 minutes (every 4th break)\n- Timer Type: Pomodoro (work session)\n                "}}}},SmallSize={args:{size:"sm"},parameters:{docs:{description:{story:"\nSmall size variant with compact dimensions and reduced padding.\nPerfect for dashboard widgets or sidebar placement.\n\n**Features:**\n- Smaller timer circle and text\n- Compact button sizing\n- Reduced spacing throughout\n                "}}}},LargeSize={args:{size:"lg"},parameters:{docs:{description:{story:"\nLarge size variant with expanded dimensions and generous padding.\nIdeal for main focus areas or full-screen timer applications.\n\n**Features:**\n- Larger timer circle and text\n- Expanded button sizing\n- Generous spacing throughout\n                "}}}},CompactVariant={args:{variant:"compact"},parameters:{docs:{description:{story:"\nCompact variant reduces spacing between elements while maintaining full functionality.\nUseful when vertical space is limited but all features are needed.\n\n**Features:**\n- Reduced spacing between sections\n- Same functionality as default\n- Includes task management\n                "}}}},MinimalVariant={args:{variant:"minimal"},parameters:{docs:{description:{story:"\nMinimal variant removes the task management section for a clean, focused experience.\nPerfect for users who prefer external task management or want minimal distractions.\n\n**Features:**\n- No task list\n- Clean, minimal interface\n- All timer functionality preserved\n- Timer customization still available\n                "}}}},BorderedVariant={args:{border:"bordered"},parameters:{docs:{description:{story:"\nBordered variant adds a prominent border around the timer for enhanced visual definition.\nUseful when the timer needs to stand out from surrounding content.\n\n**Features:**\n- Enhanced border styling\n- Better visual separation\n- Same functionality as default\n                "}}}},SmallCompactBordered={args:{size:"sm",variant:"compact",border:"bordered"},parameters:{docs:{description:{story:"\nCombines small size, compact spacing, and bordered styling for maximum space efficiency\nwhile maintaining clear visual boundaries.\n\n**Use Cases:**\n- Dashboard widgets\n- Sidebar components\n- Space-constrained layouts\n                "}}}},LargeMinimal={args:{size:"lg",variant:"minimal"},parameters:{docs:{description:{story:"\nLarge minimal variant provides a prominent, distraction-free timer experience.\nPerfect for full-screen timer applications or meditation sessions.\n\n**Use Cases:**\n- Full-screen timer mode\n- Meditation or focus sessions\n- Presentation mode\n                "}}}},LargeMinimalBordered={args:{size:"lg",variant:"minimal",border:"bordered"},parameters:{docs:{description:{story:"\nCombines large size, minimal interface, and bordered styling for maximum visual impact\nwhile maintaining a clean, focused experience.\n\n**Use Cases:**\n- Standalone timer applications\n- Kiosk mode displays\n- High-visibility timer needs\n                "}}}},DarkTheme={parameters:{docs:{description:{story:"\nDemonstrates the Pomodoro timer in dark theme mode.\nThe component automatically adapts to the current theme context.\n\n**Dark Theme Features:**\n- Inverted color scheme\n- Proper contrast ratios\n- Theme-aware animations\n- Consistent visual hierarchy\n                "}}},decorators:[Story=>(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("div",{className:"dark bg-black p-8 rounded-lg",children:(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(Story,{})})]},MobileResponsive={parameters:{docs:{description:{story:"\nDemonstrates the mobile-responsive behavior of the Pomodoro timer.\nThe component automatically adapts to smaller screen sizes and touch interfaces.\n\n**Mobile Optimizations:**\n- Touch-friendly button sizes\n- Responsive text scaling\n- Optimized spacing for small screens\n- Swipe-friendly task management\n- Portrait orientation support\n\n**Responsive Breakpoints:**\n- Mobile: < 640px\n- Tablet: 640px - 1024px\n- Desktop: > 1024px\n                "}}},decorators:[Story=>(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div",{className:"max-w-sm mx-auto border border-gray-300 rounded-lg overflow-hidden",children:[(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("div",{className:"bg-gray-100 px-4 py-2 text-sm font-medium text-center",children:"Mobile View (375px)"}),(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("div",{className:"p-4",children:(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(Story,{args:{size:"sm"}})})]})]},IntegrationShowcase={parameters:{docs:{description:{story:"\nDemonstrates how the Pomodoro timer integrates with other components and systems.\n\n**Integration Points:**\n- Theme system integration\n- Internationalization support\n- Local storage persistence\n- Animation system compatibility\n- Accessibility framework integration\n\n**Real-world Usage:**\n- Dashboard widgets\n- Productivity applications\n- Time tracking systems\n- Focus applications\n- Team collaboration tools\n                "}}},decorators:[Story=>(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("div",{className:"space-y-6",children:(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div",{className:"bg-gradient-to-r from-blue-50 to-purple-50 p-6 rounded-lg",children:[(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("h3",{className:"text-lg font-semibold mb-4",children:"Productivity Dashboard"}),(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-6",children:[(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("div",{children:(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(Story,{args:{size:"md",variant:"compact"}})}),(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div",{className:"space-y-4",children:[(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div",{className:"bg-white p-4 rounded border",children:[(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("h4",{className:"font-medium mb-2",children:"Today's Progress"}),(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div",{className:"text-sm text-gray-600",children:[(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("div",{children:"Completed Pomodoros: 3"}),(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("div",{children:"Total Focus Time: 1h 15m"}),(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("div",{children:"Tasks Completed: 5"})]})]}),(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div",{className:"bg-white p-4 rounded border",children:[(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("h4",{className:"font-medium mb-2",children:"Upcoming Tasks"}),(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div",{className:"text-sm text-gray-600 space-y-1",children:[(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("div",{children:"• Review project proposal"}),(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("div",{children:"• Update documentation"}),(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("div",{children:"• Team standup meeting"})]})]})]})]})]})})]};Default.parameters={...Default.parameters,docs:{...null===(_Default_parameters=Default.parameters)||void 0===_Default_parameters?void 0:_Default_parameters.docs,source:{originalSource:"{\n  parameters: {\n    docs: {\n      description: {\n        story: `\nThe default Pomodoro timer starts with the medium preset (25-minute work sessions).\nUsers can start/pause the timer, manage tasks, and customize timer durations.\n\n**Default Settings:**\n- Work Session: 25 minutes\n- Short Break: 5 minutes\n- Long Break: 15 minutes (every 4th break)\n- Timer Type: Pomodoro (work session)\n                `\n      }\n    }\n  }\n}",...null===(_Default_parameters1=Default.parameters)||void 0===_Default_parameters1||null===(_Default_parameters_docs=_Default_parameters1.docs)||void 0===_Default_parameters_docs?void 0:_Default_parameters_docs.source},description:{story:"Default Pomodoro timer with medium preset (25 minutes work, 5 minutes rest, 15 minutes long rest).\r\nIncludes task management, timer controls, and customization options.",...null===(_Default_parameters2=Default.parameters)||void 0===_Default_parameters2||null===(_Default_parameters_docs1=_Default_parameters2.docs)||void 0===_Default_parameters_docs1?void 0:_Default_parameters_docs1.description}}},SmallSize.parameters={...SmallSize.parameters,docs:{...null===(_SmallSize_parameters=SmallSize.parameters)||void 0===_SmallSize_parameters?void 0:_SmallSize_parameters.docs,source:{originalSource:"{\n  args: {\n    size: 'sm'\n  },\n  parameters: {\n    docs: {\n      description: {\n        story: `\nSmall size variant with compact dimensions and reduced padding.\nPerfect for dashboard widgets or sidebar placement.\n\n**Features:**\n- Smaller timer circle and text\n- Compact button sizing\n- Reduced spacing throughout\n                `\n      }\n    }\n  }\n}",...null===(_SmallSize_parameters1=SmallSize.parameters)||void 0===_SmallSize_parameters1||null===(_SmallSize_parameters_docs=_SmallSize_parameters1.docs)||void 0===_SmallSize_parameters_docs?void 0:_SmallSize_parameters_docs.source},description:{story:"Small size variant of the Pomodoro timer.\r\nCompact layout suitable for smaller spaces or sidebars.",...null===(_SmallSize_parameters2=SmallSize.parameters)||void 0===_SmallSize_parameters2||null===(_SmallSize_parameters_docs1=_SmallSize_parameters2.docs)||void 0===_SmallSize_parameters_docs1?void 0:_SmallSize_parameters_docs1.description}}},LargeSize.parameters={...LargeSize.parameters,docs:{...null===(_LargeSize_parameters=LargeSize.parameters)||void 0===_LargeSize_parameters?void 0:_LargeSize_parameters.docs,source:{originalSource:"{\n  args: {\n    size: 'lg'\n  },\n  parameters: {\n    docs: {\n      description: {\n        story: `\nLarge size variant with expanded dimensions and generous padding.\nIdeal for main focus areas or full-screen timer applications.\n\n**Features:**\n- Larger timer circle and text\n- Expanded button sizing\n- Generous spacing throughout\n                `\n      }\n    }\n  }\n}",...null===(_LargeSize_parameters1=LargeSize.parameters)||void 0===_LargeSize_parameters1||null===(_LargeSize_parameters_docs=_LargeSize_parameters1.docs)||void 0===_LargeSize_parameters_docs?void 0:_LargeSize_parameters_docs.source},description:{story:"Large size variant of the Pomodoro timer.\r\nExpanded layout for prominent display or full-screen usage.",...null===(_LargeSize_parameters2=LargeSize.parameters)||void 0===_LargeSize_parameters2||null===(_LargeSize_parameters_docs1=_LargeSize_parameters2.docs)||void 0===_LargeSize_parameters_docs1?void 0:_LargeSize_parameters_docs1.description}}},CompactVariant.parameters={...CompactVariant.parameters,docs:{...null===(_CompactVariant_parameters=CompactVariant.parameters)||void 0===_CompactVariant_parameters?void 0:_CompactVariant_parameters.docs,source:{originalSource:"{\n  args: {\n    variant: 'compact'\n  },\n  parameters: {\n    docs: {\n      description: {\n        story: `\nCompact variant reduces spacing between elements while maintaining full functionality.\nUseful when vertical space is limited but all features are needed.\n\n**Features:**\n- Reduced spacing between sections\n- Same functionality as default\n- Includes task management\n                `\n      }\n    }\n  }\n}",...null===(_CompactVariant_parameters1=CompactVariant.parameters)||void 0===_CompactVariant_parameters1||null===(_CompactVariant_parameters_docs=_CompactVariant_parameters1.docs)||void 0===_CompactVariant_parameters_docs?void 0:_CompactVariant_parameters_docs.source},description:{story:"Compact variant with reduced spacing between elements.\r\nMaintains all functionality while using less vertical space.",...null===(_CompactVariant_parameters2=CompactVariant.parameters)||void 0===_CompactVariant_parameters2||null===(_CompactVariant_parameters_docs1=_CompactVariant_parameters2.docs)||void 0===_CompactVariant_parameters_docs1?void 0:_CompactVariant_parameters_docs1.description}}},MinimalVariant.parameters={...MinimalVariant.parameters,docs:{...null===(_MinimalVariant_parameters=MinimalVariant.parameters)||void 0===_MinimalVariant_parameters?void 0:_MinimalVariant_parameters.docs,source:{originalSource:"{\n  args: {\n    variant: 'minimal'\n  },\n  parameters: {\n    docs: {\n      description: {\n        story: `\nMinimal variant removes the task management section for a clean, focused experience.\nPerfect for users who prefer external task management or want minimal distractions.\n\n**Features:**\n- No task list\n- Clean, minimal interface\n- All timer functionality preserved\n- Timer customization still available\n                `\n      }\n    }\n  }\n}",...null===(_MinimalVariant_parameters1=MinimalVariant.parameters)||void 0===_MinimalVariant_parameters1||null===(_MinimalVariant_parameters_docs=_MinimalVariant_parameters1.docs)||void 0===_MinimalVariant_parameters_docs?void 0:_MinimalVariant_parameters_docs.source},description:{story:"Minimal variant without task management.\r\nClean, focused timer interface for distraction-free sessions.",...null===(_MinimalVariant_parameters2=MinimalVariant.parameters)||void 0===_MinimalVariant_parameters2||null===(_MinimalVariant_parameters_docs1=_MinimalVariant_parameters2.docs)||void 0===_MinimalVariant_parameters_docs1?void 0:_MinimalVariant_parameters_docs1.description}}},BorderedVariant.parameters={...BorderedVariant.parameters,docs:{...null===(_BorderedVariant_parameters=BorderedVariant.parameters)||void 0===_BorderedVariant_parameters?void 0:_BorderedVariant_parameters.docs,source:{originalSource:"{\n  args: {\n    border: 'bordered'\n  },\n  parameters: {\n    docs: {\n      description: {\n        story: `\nBordered variant adds a prominent border around the timer for enhanced visual definition.\nUseful when the timer needs to stand out from surrounding content.\n\n**Features:**\n- Enhanced border styling\n- Better visual separation\n- Same functionality as default\n                `\n      }\n    }\n  }\n}",...null===(_BorderedVariant_parameters1=BorderedVariant.parameters)||void 0===_BorderedVariant_parameters1||null===(_BorderedVariant_parameters_docs=_BorderedVariant_parameters1.docs)||void 0===_BorderedVariant_parameters_docs?void 0:_BorderedVariant_parameters_docs.source},description:{story:"Bordered variant with enhanced visual definition.\r\nAdds a prominent border around the entire timer component.",...null===(_BorderedVariant_parameters2=BorderedVariant.parameters)||void 0===_BorderedVariant_parameters2||null===(_BorderedVariant_parameters_docs1=_BorderedVariant_parameters2.docs)||void 0===_BorderedVariant_parameters_docs1?void 0:_BorderedVariant_parameters_docs1.description}}},SmallCompactBordered.parameters={...SmallCompactBordered.parameters,docs:{...null===(_SmallCompactBordered_parameters=SmallCompactBordered.parameters)||void 0===_SmallCompactBordered_parameters?void 0:_SmallCompactBordered_parameters.docs,source:{originalSource:"{\n  args: {\n    size: 'sm',\n    variant: 'compact',\n    border: 'bordered'\n  },\n  parameters: {\n    docs: {\n      description: {\n        story: `\nCombines small size, compact spacing, and bordered styling for maximum space efficiency\nwhile maintaining clear visual boundaries.\n\n**Use Cases:**\n- Dashboard widgets\n- Sidebar components\n- Space-constrained layouts\n                `\n      }\n    }\n  }\n}",...null===(_SmallCompactBordered_parameters1=SmallCompactBordered.parameters)||void 0===_SmallCompactBordered_parameters1||null===(_SmallCompactBordered_parameters_docs=_SmallCompactBordered_parameters1.docs)||void 0===_SmallCompactBordered_parameters_docs?void 0:_SmallCompactBordered_parameters_docs.source},description:{story:"Small, compact, and bordered combination.\r\nMaximum space efficiency with clear visual boundaries.",...null===(_SmallCompactBordered_parameters2=SmallCompactBordered.parameters)||void 0===_SmallCompactBordered_parameters2||null===(_SmallCompactBordered_parameters_docs1=_SmallCompactBordered_parameters2.docs)||void 0===_SmallCompactBordered_parameters_docs1?void 0:_SmallCompactBordered_parameters_docs1.description}}},LargeMinimal.parameters={...LargeMinimal.parameters,docs:{...null===(_LargeMinimal_parameters=LargeMinimal.parameters)||void 0===_LargeMinimal_parameters?void 0:_LargeMinimal_parameters.docs,source:{originalSource:"{\n  args: {\n    size: 'lg',\n    variant: 'minimal'\n  },\n  parameters: {\n    docs: {\n      description: {\n        story: `\nLarge minimal variant provides a prominent, distraction-free timer experience.\nPerfect for full-screen timer applications or meditation sessions.\n\n**Use Cases:**\n- Full-screen timer mode\n- Meditation or focus sessions\n- Presentation mode\n                `\n      }\n    }\n  }\n}",...null===(_LargeMinimal_parameters1=LargeMinimal.parameters)||void 0===_LargeMinimal_parameters1||null===(_LargeMinimal_parameters_docs=_LargeMinimal_parameters1.docs)||void 0===_LargeMinimal_parameters_docs?void 0:_LargeMinimal_parameters_docs.source},description:{story:"Large minimal variant for focused timer sessions.\r\nProminent display without task management distractions.",...null===(_LargeMinimal_parameters2=LargeMinimal.parameters)||void 0===_LargeMinimal_parameters2||null===(_LargeMinimal_parameters_docs1=_LargeMinimal_parameters2.docs)||void 0===_LargeMinimal_parameters_docs1?void 0:_LargeMinimal_parameters_docs1.description}}},LargeMinimalBordered.parameters={...LargeMinimalBordered.parameters,docs:{...null===(_LargeMinimalBordered_parameters=LargeMinimalBordered.parameters)||void 0===_LargeMinimalBordered_parameters?void 0:_LargeMinimalBordered_parameters.docs,source:{originalSource:"{\n  args: {\n    size: 'lg',\n    variant: 'minimal',\n    border: 'bordered'\n  },\n  parameters: {\n    docs: {\n      description: {\n        story: `\nCombines large size, minimal interface, and bordered styling for maximum visual impact\nwhile maintaining a clean, focused experience.\n\n**Use Cases:**\n- Standalone timer applications\n- Kiosk mode displays\n- High-visibility timer needs\n                `\n      }\n    }\n  }\n}",...null===(_LargeMinimalBordered_parameters1=LargeMinimalBordered.parameters)||void 0===_LargeMinimalBordered_parameters1||null===(_LargeMinimalBordered_parameters_docs=_LargeMinimalBordered_parameters1.docs)||void 0===_LargeMinimalBordered_parameters_docs?void 0:_LargeMinimalBordered_parameters_docs.source},description:{story:"Large minimal bordered variant.\r\nMaximum prominence with clear boundaries and no distractions.",...null===(_LargeMinimalBordered_parameters2=LargeMinimalBordered.parameters)||void 0===_LargeMinimalBordered_parameters2||null===(_LargeMinimalBordered_parameters_docs1=_LargeMinimalBordered_parameters2.docs)||void 0===_LargeMinimalBordered_parameters_docs1?void 0:_LargeMinimalBordered_parameters_docs1.description}}},DarkTheme.parameters={...DarkTheme.parameters,docs:{...null===(_DarkTheme_parameters=DarkTheme.parameters)||void 0===_DarkTheme_parameters?void 0:_DarkTheme_parameters.docs,source:{originalSource:'{\n  parameters: {\n    docs: {\n      description: {\n        story: `\nDemonstrates the Pomodoro timer in dark theme mode.\nThe component automatically adapts to the current theme context.\n\n**Dark Theme Features:**\n- Inverted color scheme\n- Proper contrast ratios\n- Theme-aware animations\n- Consistent visual hierarchy\n                `\n      }\n    }\n  },\n  decorators: [Story => <div className="dark bg-black p-8 rounded-lg">\r\n                <Story />\r\n            </div>]\n}',...null===(_DarkTheme_parameters1=DarkTheme.parameters)||void 0===_DarkTheme_parameters1||null===(_DarkTheme_parameters_docs=_DarkTheme_parameters1.docs)||void 0===_DarkTheme_parameters_docs?void 0:_DarkTheme_parameters_docs.source},description:{story:"Dark theme variant demonstration.\r\nShows how the timer appears in dark mode.",...null===(_DarkTheme_parameters2=DarkTheme.parameters)||void 0===_DarkTheme_parameters2||null===(_DarkTheme_parameters_docs1=_DarkTheme_parameters2.docs)||void 0===_DarkTheme_parameters_docs1?void 0:_DarkTheme_parameters_docs1.description}}},MobileResponsive.parameters={...MobileResponsive.parameters,docs:{...null===(_MobileResponsive_parameters=MobileResponsive.parameters)||void 0===_MobileResponsive_parameters?void 0:_MobileResponsive_parameters.docs,source:{originalSource:'{\n  parameters: {\n    docs: {\n      description: {\n        story: `\nDemonstrates the mobile-responsive behavior of the Pomodoro timer.\nThe component automatically adapts to smaller screen sizes and touch interfaces.\n\n**Mobile Optimizations:**\n- Touch-friendly button sizes\n- Responsive text scaling\n- Optimized spacing for small screens\n- Swipe-friendly task management\n- Portrait orientation support\n\n**Responsive Breakpoints:**\n- Mobile: < 640px\n- Tablet: 640px - 1024px\n- Desktop: > 1024px\n                `\n      }\n    }\n  },\n  decorators: [Story => <div className="max-w-sm mx-auto border border-gray-300 rounded-lg overflow-hidden">\r\n                <div className="bg-gray-100 px-4 py-2 text-sm font-medium text-center">Mobile View (375px)</div>\r\n                <div className="p-4">\r\n                    <Story args={{\n        size: \'sm\'\n      }} />\r\n                </div>\r\n            </div>]\n}',...null===(_MobileResponsive_parameters1=MobileResponsive.parameters)||void 0===_MobileResponsive_parameters1||null===(_MobileResponsive_parameters_docs=_MobileResponsive_parameters1.docs)||void 0===_MobileResponsive_parameters_docs?void 0:_MobileResponsive_parameters_docs.source},description:{story:"Mobile responsive demonstration.\r\nShows how the timer adapts to mobile screen sizes.",...null===(_MobileResponsive_parameters2=MobileResponsive.parameters)||void 0===_MobileResponsive_parameters2||null===(_MobileResponsive_parameters_docs1=_MobileResponsive_parameters2.docs)||void 0===_MobileResponsive_parameters_docs1?void 0:_MobileResponsive_parameters_docs1.description}}},IntegrationShowcase.parameters={...IntegrationShowcase.parameters,docs:{...null===(_IntegrationShowcase_parameters=IntegrationShowcase.parameters)||void 0===_IntegrationShowcase_parameters?void 0:_IntegrationShowcase_parameters.docs,source:{originalSource:'{\n  parameters: {\n    docs: {\n      description: {\n        story: `\nDemonstrates how the Pomodoro timer integrates with other components and systems.\n\n**Integration Points:**\n- Theme system integration\n- Internationalization support\n- Local storage persistence\n- Animation system compatibility\n- Accessibility framework integration\n\n**Real-world Usage:**\n- Dashboard widgets\n- Productivity applications\n- Time tracking systems\n- Focus applications\n- Team collaboration tools\n                `\n      }\n    }\n  },\n  decorators: [Story => <div className="space-y-6">\r\n                <div className="bg-gradient-to-r from-blue-50 to-purple-50 p-6 rounded-lg">\r\n                    <h3 className="text-lg font-semibold mb-4">Productivity Dashboard</h3>\r\n                    <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">\r\n                        <div>\r\n                            <Story args={{\n            size: \'md\',\n            variant: \'compact\'\n          }} />\r\n                        </div>\r\n                        <div className="space-y-4">\r\n                            <div className="bg-white p-4 rounded border">\r\n                                <h4 className="font-medium mb-2">Today\'s Progress</h4>\r\n                                <div className="text-sm text-gray-600">\r\n                                    <div>Completed Pomodoros: 3</div>\r\n                                    <div>Total Focus Time: 1h 15m</div>\r\n                                    <div>Tasks Completed: 5</div>\r\n                                </div>\r\n                            </div>\r\n                            <div className="bg-white p-4 rounded border">\r\n                                <h4 className="font-medium mb-2">Upcoming Tasks</h4>\r\n                                <div className="text-sm text-gray-600 space-y-1">\r\n                                    <div>• Review project proposal</div>\r\n                                    <div>• Update documentation</div>\r\n                                    <div>• Team standup meeting</div>\r\n                                </div>\r\n                            </div>\r\n                        </div>\r\n                    </div>\r\n                </div>\r\n            </div>]\n}',...null===(_IntegrationShowcase_parameters1=IntegrationShowcase.parameters)||void 0===_IntegrationShowcase_parameters1||null===(_IntegrationShowcase_parameters_docs=_IntegrationShowcase_parameters1.docs)||void 0===_IntegrationShowcase_parameters_docs?void 0:_IntegrationShowcase_parameters_docs.source},description:{story:"Integration showcase.\r\nShows how the timer integrates with other components.",...null===(_IntegrationShowcase_parameters2=IntegrationShowcase.parameters)||void 0===_IntegrationShowcase_parameters2||null===(_IntegrationShowcase_parameters_docs1=_IntegrationShowcase_parameters2.docs)||void 0===_IntegrationShowcase_parameters_docs1?void 0:_IntegrationShowcase_parameters_docs1.description}}};const __namedExportsOrder=["Default","SmallSize","LargeSize","CompactVariant","MinimalVariant","BorderedVariant","SmallCompactBordered","LargeMinimal","LargeMinimalBordered","DarkTheme","MobileResponsive","IntegrationShowcase"]}}]);