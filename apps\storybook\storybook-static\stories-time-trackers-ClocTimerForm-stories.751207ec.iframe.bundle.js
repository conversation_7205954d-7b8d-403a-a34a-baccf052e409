"use strict";(self.webpackChunk_cloc_storybook=self.webpackChunk_cloc_storybook||[]).push([[4137],{"./src/stories/time-trackers/ClocTimerForm.stories.tsx":(__unused_webpack_module,__webpack_exports__,__webpack_require__)=>{__webpack_require__.r(__webpack_exports__),__webpack_require__.d(__webpack_exports__,{CompactLayout:()=>CompactLayout,Default:()=>Default,InSidebar:()=>InSidebar,InTimerWidget:()=>InTimerWidget,Large:()=>Large,MobileView:()=>MobileView,QuickStart:()=>QuickStart,Small:()=>Small,WithPresets:()=>WithPresets,WithTimer:()=>WithTimer,__namedExportsOrder:()=>__namedExportsOrder,default:()=>__WEBPACK_DEFAULT_EXPORT__});var _Default_parameters,_Default_parameters_docs,_Default_parameters1,_Small_parameters,_Small_parameters_docs,_Small_parameters1,_Large_parameters,_Large_parameters_docs,_Large_parameters1,_InTimerWidget_parameters,_InTimerWidget_parameters_docs,_InTimerWidget_parameters1,_InSidebar_parameters,_InSidebar_parameters_docs,_InSidebar_parameters1,_CompactLayout_parameters,_CompactLayout_parameters_docs,_CompactLayout_parameters1,_WithTimer_parameters,_WithTimer_parameters_docs,_WithTimer_parameters1,_MobileView_parameters,_MobileView_parameters_docs,_MobileView_parameters1,_QuickStart_parameters,_QuickStart_parameters_docs,_QuickStart_parameters1,_WithPresets_parameters,_WithPresets_parameters_docs,_WithPresets_parameters1,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__=__webpack_require__("../../node_modules/next/dist/compiled/react/jsx-runtime.js"),_cloc_atoms__WEBPACK_IMPORTED_MODULE_2__=(__webpack_require__("../../node_modules/next/dist/compiled/react/index.js"),__webpack_require__("../../packages/toolkit/atoms/dist/index.es.js"));const __WEBPACK_DEFAULT_EXPORT__={title:"Time Trackers/Timer Form",component:_cloc_atoms__WEBPACK_IMPORTED_MODULE_2__.VX$,parameters:{layout:"centered",docs:{description:{component:"A comprehensive timer form component that includes selectors for client, project, team, and task. Essential for setting up time tracking context before starting a timer."}}},argTypes:{size:{control:"select",options:["default","sm","lg"],description:"Size variant for all form controls"}}},Default={args:{size:"default"}},Small={args:{size:"sm"}},Large={args:{size:"lg"}},InTimerWidget={render:()=>(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div",{className:"bg-white dark:bg-gray-900 rounded-lg shadow-lg p-6 max-w-md",children:[(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("h2",{className:"text-xl font-semibold text-gray-900 dark:text-white mb-4",children:"Start Time Tracking"}),(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(_cloc_atoms__WEBPACK_IMPORTED_MODULE_2__.VX$,{size:"default"}),(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("div",{className:"mt-6 pt-4 border-t border-gray-200 dark:border-gray-700",children:(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("button",{className:"w-full bg-blue-600 hover:bg-blue-700 text-white font-medium py-3 px-4 rounded-lg transition-colors",children:"Start Timer"})})]})},InSidebar={render:()=>(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("div",{className:"w-80 bg-gray-50 dark:bg-gray-900 border-r border-gray-200 dark:border-gray-700 p-4",children:(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div",{className:"space-y-6",children:[(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div",{children:[(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("h3",{className:"text-lg font-semibold text-gray-900 dark:text-white mb-3",children:"Quick Timer Setup"}),(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(_cloc_atoms__WEBPACK_IMPORTED_MODULE_2__.VX$,{size:"sm"})]}),(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div",{className:"space-y-2",children:[(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("button",{className:"w-full bg-green-600 hover:bg-green-700 text-white font-medium py-2 px-4 rounded text-sm transition-colors",children:"▶ Start Timer"}),(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("button",{className:"w-full bg-gray-200 dark:bg-gray-700 hover:bg-gray-300 dark:hover:bg-gray-600 text-gray-700 dark:text-gray-300 font-medium py-2 px-4 rounded text-sm transition-colors",children:"⏸ Pause Timer"})]})]})})},CompactLayout={render:()=>(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("div",{className:"bg-white dark:bg-gray-900 border border-gray-200 dark:border-gray-700 rounded-lg p-4",children:(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div",{className:"flex items-center gap-4",children:[(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("div",{className:"flex-1",children:(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(_cloc_atoms__WEBPACK_IMPORTED_MODULE_2__.VX$,{size:"sm"})}),(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("button",{className:"bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded font-medium whitespace-nowrap",children:"Start"})]})})},WithTimer={render:()=>(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div",{className:"bg-white dark:bg-gray-900 rounded-xl shadow-lg p-6 max-w-lg",children:[(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div",{className:"text-center mb-6",children:[(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("div",{className:"text-4xl font-mono font-bold text-gray-900 dark:text-white mb-2",children:"00:00:00"}),(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("p",{className:"text-gray-500 dark:text-gray-400",children:"Ready to start tracking"})]}),(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div",{className:"space-y-4",children:[(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(_cloc_atoms__WEBPACK_IMPORTED_MODULE_2__.VX$,{size:"default"}),(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div",{className:"flex gap-3",children:[(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("button",{className:"flex-1 bg-green-600 hover:bg-green-700 text-white font-medium py-3 px-4 rounded-lg transition-colors",children:"▶ Start"}),(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("button",{className:"flex-1 bg-gray-200 dark:bg-gray-700 hover:bg-gray-300 dark:hover:bg-gray-600 text-gray-700 dark:text-gray-300 font-medium py-3 px-4 rounded-lg transition-colors",children:"⏸ Pause"}),(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("button",{className:"flex-1 bg-red-600 hover:bg-red-700 text-white font-medium py-3 px-4 rounded-lg transition-colors",children:"⏹ Stop"})]})]})]})},MobileView={render:()=>(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div",{className:"w-full max-w-sm mx-auto bg-white dark:bg-gray-900 rounded-lg shadow-lg",children:[(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("div",{className:"p-4 border-b border-gray-200 dark:border-gray-700",children:(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("h2",{className:"text-lg font-semibold text-gray-900 dark:text-white text-center",children:"Time Tracker"})}),(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div",{className:"p-4 space-y-4",children:[(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(_cloc_atoms__WEBPACK_IMPORTED_MODULE_2__.VX$,{size:"default"}),(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("button",{className:"w-full bg-blue-600 hover:bg-blue-700 text-white font-medium py-3 px-4 rounded-lg transition-colors",children:"Start Tracking"})]})]})},QuickStart={render:()=>(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("div",{className:"bg-gradient-to-r from-blue-50 to-indigo-50 dark:from-gray-800 dark:to-gray-900 p-6 rounded-lg",children:(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div",{className:"max-w-md mx-auto",children:[(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div",{className:"text-center mb-6",children:[(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("h2",{className:"text-2xl font-bold text-gray-900 dark:text-white mb-2",children:"Quick Start"}),(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("p",{className:"text-gray-600 dark:text-gray-400",children:"Set up your timer in seconds"})]}),(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div",{className:"bg-white dark:bg-gray-900 rounded-lg p-4 shadow-sm",children:[(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(_cloc_atoms__WEBPACK_IMPORTED_MODULE_2__.VX$,{size:"default"}),(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div",{className:"mt-4 flex gap-2",children:[(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("button",{className:"flex-1 bg-blue-600 hover:bg-blue-700 text-white font-medium py-2 px-4 rounded transition-colors",children:"Start Now"}),(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("button",{className:"px-4 py-2 text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white transition-colors",children:"Save as Template"})]})]})]})})},WithPresets={render:()=>(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div",{className:"bg-white dark:bg-gray-900 rounded-lg shadow-lg p-6 max-w-lg",children:[(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("h2",{className:"text-xl font-semibold text-gray-900 dark:text-white mb-4",children:"Timer Setup"}),(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div",{className:"space-y-4",children:[(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div",{children:[(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("h3",{className:"text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:"Quick Presets"}),(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div",{className:"grid grid-cols-2 gap-2",children:[(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("button",{className:"p-2 text-sm bg-gray-100 dark:bg-gray-800 hover:bg-gray-200 dark:hover:bg-gray-700 rounded transition-colors",children:"Development Work"}),(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("button",{className:"p-2 text-sm bg-gray-100 dark:bg-gray-800 hover:bg-gray-200 dark:hover:bg-gray-700 rounded transition-colors",children:"Client Meeting"}),(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("button",{className:"p-2 text-sm bg-gray-100 dark:bg-gray-800 hover:bg-gray-200 dark:hover:bg-gray-700 rounded transition-colors",children:"Code Review"}),(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("button",{className:"p-2 text-sm bg-gray-100 dark:bg-gray-800 hover:bg-gray-200 dark:hover:bg-gray-700 rounded transition-colors",children:"Documentation"})]})]}),(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div",{className:"border-t border-gray-200 dark:border-gray-700 pt-4",children:[(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("h3",{className:"text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:"Custom Setup"}),(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(_cloc_atoms__WEBPACK_IMPORTED_MODULE_2__.VX$,{size:"default"})]}),(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("button",{className:"w-full bg-blue-600 hover:bg-blue-700 text-white font-medium py-3 px-4 rounded-lg transition-colors",children:"Start Timer"})]})]})};Default.parameters={...Default.parameters,docs:{...null===(_Default_parameters=Default.parameters)||void 0===_Default_parameters?void 0:_Default_parameters.docs,source:{originalSource:"{\n  args: {\n    size: 'default'\n  }\n}",...null===(_Default_parameters1=Default.parameters)||void 0===_Default_parameters1||null===(_Default_parameters_docs=_Default_parameters1.docs)||void 0===_Default_parameters_docs?void 0:_Default_parameters_docs.source}}},Small.parameters={...Small.parameters,docs:{...null===(_Small_parameters=Small.parameters)||void 0===_Small_parameters?void 0:_Small_parameters.docs,source:{originalSource:"{\n  args: {\n    size: 'sm'\n  }\n}",...null===(_Small_parameters1=Small.parameters)||void 0===_Small_parameters1||null===(_Small_parameters_docs=_Small_parameters1.docs)||void 0===_Small_parameters_docs?void 0:_Small_parameters_docs.source}}},Large.parameters={...Large.parameters,docs:{...null===(_Large_parameters=Large.parameters)||void 0===_Large_parameters?void 0:_Large_parameters.docs,source:{originalSource:"{\n  args: {\n    size: 'lg'\n  }\n}",...null===(_Large_parameters1=Large.parameters)||void 0===_Large_parameters1||null===(_Large_parameters_docs=_Large_parameters1.docs)||void 0===_Large_parameters_docs?void 0:_Large_parameters_docs.source}}},InTimerWidget.parameters={...InTimerWidget.parameters,docs:{...null===(_InTimerWidget_parameters=InTimerWidget.parameters)||void 0===_InTimerWidget_parameters?void 0:_InTimerWidget_parameters.docs,source:{originalSource:'{\n  render: () => <div className="bg-white dark:bg-gray-900 rounded-lg shadow-lg p-6 max-w-md">\r\n            <h2 className="text-xl font-semibold text-gray-900 dark:text-white mb-4">Start Time Tracking</h2>\r\n            <ClocTimerForm size="default" />\r\n            <div className="mt-6 pt-4 border-t border-gray-200 dark:border-gray-700">\r\n                <button className="w-full bg-blue-600 hover:bg-blue-700 text-white font-medium py-3 px-4 rounded-lg transition-colors">\r\n                    Start Timer\r\n                </button>\r\n            </div>\r\n        </div>\n}',...null===(_InTimerWidget_parameters1=InTimerWidget.parameters)||void 0===_InTimerWidget_parameters1||null===(_InTimerWidget_parameters_docs=_InTimerWidget_parameters1.docs)||void 0===_InTimerWidget_parameters_docs?void 0:_InTimerWidget_parameters_docs.source}}},InSidebar.parameters={...InSidebar.parameters,docs:{...null===(_InSidebar_parameters=InSidebar.parameters)||void 0===_InSidebar_parameters?void 0:_InSidebar_parameters.docs,source:{originalSource:'{\n  render: () => <div className="w-80 bg-gray-50 dark:bg-gray-900 border-r border-gray-200 dark:border-gray-700 p-4">\r\n            <div className="space-y-6">\r\n                <div>\r\n                    <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-3">Quick Timer Setup</h3>\r\n                    <ClocTimerForm size="sm" />\r\n                </div>\r\n                <div className="space-y-2">\r\n                    <button className="w-full bg-green-600 hover:bg-green-700 text-white font-medium py-2 px-4 rounded text-sm transition-colors">\r\n                        ▶ Start Timer\r\n                    </button>\r\n                    <button className="w-full bg-gray-200 dark:bg-gray-700 hover:bg-gray-300 dark:hover:bg-gray-600 text-gray-700 dark:text-gray-300 font-medium py-2 px-4 rounded text-sm transition-colors">\r\n                        ⏸ Pause Timer\r\n                    </button>\r\n                </div>\r\n            </div>\r\n        </div>\n}',...null===(_InSidebar_parameters1=InSidebar.parameters)||void 0===_InSidebar_parameters1||null===(_InSidebar_parameters_docs=_InSidebar_parameters1.docs)||void 0===_InSidebar_parameters_docs?void 0:_InSidebar_parameters_docs.source}}},CompactLayout.parameters={...CompactLayout.parameters,docs:{...null===(_CompactLayout_parameters=CompactLayout.parameters)||void 0===_CompactLayout_parameters?void 0:_CompactLayout_parameters.docs,source:{originalSource:'{\n  render: () => <div className="bg-white dark:bg-gray-900 border border-gray-200 dark:border-gray-700 rounded-lg p-4">\r\n            <div className="flex items-center gap-4">\r\n                <div className="flex-1">\r\n                    <ClocTimerForm size="sm" />\r\n                </div>\r\n                <button className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded font-medium whitespace-nowrap">\r\n                    Start\r\n                </button>\r\n            </div>\r\n        </div>\n}',...null===(_CompactLayout_parameters1=CompactLayout.parameters)||void 0===_CompactLayout_parameters1||null===(_CompactLayout_parameters_docs=_CompactLayout_parameters1.docs)||void 0===_CompactLayout_parameters_docs?void 0:_CompactLayout_parameters_docs.source}}},WithTimer.parameters={...WithTimer.parameters,docs:{...null===(_WithTimer_parameters=WithTimer.parameters)||void 0===_WithTimer_parameters?void 0:_WithTimer_parameters.docs,source:{originalSource:'{\n  render: () => <div className="bg-white dark:bg-gray-900 rounded-xl shadow-lg p-6 max-w-lg">\r\n            <div className="text-center mb-6">\r\n                <div className="text-4xl font-mono font-bold text-gray-900 dark:text-white mb-2">00:00:00</div>\r\n                <p className="text-gray-500 dark:text-gray-400">Ready to start tracking</p>\r\n            </div>\r\n\r\n            <div className="space-y-4">\r\n                <ClocTimerForm size="default" />\r\n\r\n                <div className="flex gap-3">\r\n                    <button className="flex-1 bg-green-600 hover:bg-green-700 text-white font-medium py-3 px-4 rounded-lg transition-colors">\r\n                        ▶ Start\r\n                    </button>\r\n                    <button className="flex-1 bg-gray-200 dark:bg-gray-700 hover:bg-gray-300 dark:hover:bg-gray-600 text-gray-700 dark:text-gray-300 font-medium py-3 px-4 rounded-lg transition-colors">\r\n                        ⏸ Pause\r\n                    </button>\r\n                    <button className="flex-1 bg-red-600 hover:bg-red-700 text-white font-medium py-3 px-4 rounded-lg transition-colors">\r\n                        ⏹ Stop\r\n                    </button>\r\n                </div>\r\n            </div>\r\n        </div>\n}',...null===(_WithTimer_parameters1=WithTimer.parameters)||void 0===_WithTimer_parameters1||null===(_WithTimer_parameters_docs=_WithTimer_parameters1.docs)||void 0===_WithTimer_parameters_docs?void 0:_WithTimer_parameters_docs.source}}},MobileView.parameters={...MobileView.parameters,docs:{...null===(_MobileView_parameters=MobileView.parameters)||void 0===_MobileView_parameters?void 0:_MobileView_parameters.docs,source:{originalSource:'{\n  render: () => <div className="w-full max-w-sm mx-auto bg-white dark:bg-gray-900 rounded-lg shadow-lg">\r\n            <div className="p-4 border-b border-gray-200 dark:border-gray-700">\r\n                <h2 className="text-lg font-semibold text-gray-900 dark:text-white text-center">Time Tracker</h2>\r\n            </div>\r\n            <div className="p-4 space-y-4">\r\n                <ClocTimerForm size="default" />\r\n                <button className="w-full bg-blue-600 hover:bg-blue-700 text-white font-medium py-3 px-4 rounded-lg transition-colors">\r\n                    Start Tracking\r\n                </button>\r\n            </div>\r\n        </div>\n}',...null===(_MobileView_parameters1=MobileView.parameters)||void 0===_MobileView_parameters1||null===(_MobileView_parameters_docs=_MobileView_parameters1.docs)||void 0===_MobileView_parameters_docs?void 0:_MobileView_parameters_docs.source}}},QuickStart.parameters={...QuickStart.parameters,docs:{...null===(_QuickStart_parameters=QuickStart.parameters)||void 0===_QuickStart_parameters?void 0:_QuickStart_parameters.docs,source:{originalSource:'{\n  render: () => <div className="bg-gradient-to-r from-blue-50 to-indigo-50 dark:from-gray-800 dark:to-gray-900 p-6 rounded-lg">\r\n            <div className="max-w-md mx-auto">\r\n                <div className="text-center mb-6">\r\n                    <h2 className="text-2xl font-bold text-gray-900 dark:text-white mb-2">Quick Start</h2>\r\n                    <p className="text-gray-600 dark:text-gray-400">Set up your timer in seconds</p>\r\n                </div>\r\n\r\n                <div className="bg-white dark:bg-gray-900 rounded-lg p-4 shadow-sm">\r\n                    <ClocTimerForm size="default" />\r\n                    <div className="mt-4 flex gap-2">\r\n                        <button className="flex-1 bg-blue-600 hover:bg-blue-700 text-white font-medium py-2 px-4 rounded transition-colors">\r\n                            Start Now\r\n                        </button>\r\n                        <button className="px-4 py-2 text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white transition-colors">\r\n                            Save as Template\r\n                        </button>\r\n                    </div>\r\n                </div>\r\n            </div>\r\n        </div>\n}',...null===(_QuickStart_parameters1=QuickStart.parameters)||void 0===_QuickStart_parameters1||null===(_QuickStart_parameters_docs=_QuickStart_parameters1.docs)||void 0===_QuickStart_parameters_docs?void 0:_QuickStart_parameters_docs.source}}},WithPresets.parameters={...WithPresets.parameters,docs:{...null===(_WithPresets_parameters=WithPresets.parameters)||void 0===_WithPresets_parameters?void 0:_WithPresets_parameters.docs,source:{originalSource:'{\n  render: () => <div className="bg-white dark:bg-gray-900 rounded-lg shadow-lg p-6 max-w-lg">\r\n            <h2 className="text-xl font-semibold text-gray-900 dark:text-white mb-4">Timer Setup</h2>\r\n\r\n            <div className="space-y-4">\r\n                <div>\r\n                    <h3 className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Quick Presets</h3>\r\n                    <div className="grid grid-cols-2 gap-2">\r\n                        <button className="p-2 text-sm bg-gray-100 dark:bg-gray-800 hover:bg-gray-200 dark:hover:bg-gray-700 rounded transition-colors">\r\n                            Development Work\r\n                        </button>\r\n                        <button className="p-2 text-sm bg-gray-100 dark:bg-gray-800 hover:bg-gray-200 dark:hover:bg-gray-700 rounded transition-colors">\r\n                            Client Meeting\r\n                        </button>\r\n                        <button className="p-2 text-sm bg-gray-100 dark:bg-gray-800 hover:bg-gray-200 dark:hover:bg-gray-700 rounded transition-colors">\r\n                            Code Review\r\n                        </button>\r\n                        <button className="p-2 text-sm bg-gray-100 dark:bg-gray-800 hover:bg-gray-200 dark:hover:bg-gray-700 rounded transition-colors">\r\n                            Documentation\r\n                        </button>\r\n                    </div>\r\n                </div>\r\n\r\n                <div className="border-t border-gray-200 dark:border-gray-700 pt-4">\r\n                    <h3 className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Custom Setup</h3>\r\n                    <ClocTimerForm size="default" />\r\n                </div>\r\n\r\n                <button className="w-full bg-blue-600 hover:bg-blue-700 text-white font-medium py-3 px-4 rounded-lg transition-colors">\r\n                    Start Timer\r\n                </button>\r\n            </div>\r\n        </div>\n}',...null===(_WithPresets_parameters1=WithPresets.parameters)||void 0===_WithPresets_parameters1||null===(_WithPresets_parameters_docs=_WithPresets_parameters1.docs)||void 0===_WithPresets_parameters_docs?void 0:_WithPresets_parameters_docs.source}}};const __namedExportsOrder=["Default","Small","Large","InTimerWidget","InSidebar","CompactLayout","WithTimer","MobileView","QuickStart","WithPresets"]}}]);