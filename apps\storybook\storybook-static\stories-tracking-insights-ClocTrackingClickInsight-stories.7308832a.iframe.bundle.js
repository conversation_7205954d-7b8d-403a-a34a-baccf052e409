"use strict";(self.webpackChunk_cloc_storybook=self.webpackChunk_cloc_storybook||[]).push([[9463],{"./src/stories/tracking-insights/ClocTrackingClickInsight.stories.tsx":(__unused_webpack_module,__webpack_exports__,__webpack_require__)=>{__webpack_require__.r(__webpack_exports__),__webpack_require__.d(__webpack_exports__,{CustomStyling:()=>CustomStyling,Default:()=>Default,__namedExportsOrder:()=>__namedExportsOrder,default:()=>__WEBPACK_DEFAULT_EXPORT__});var _Default_parameters,_Default_parameters_docs,_Default_parameters1,_Default_parameters_docs1,_Default_parameters2,_CustomStyling_parameters,_CustomStyling_parameters_docs,_CustomStyling_parameters1,_CustomStyling_parameters_docs1,_CustomStyling_parameters2,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__=__webpack_require__("../../node_modules/next/dist/compiled/react/jsx-runtime.js");const __WEBPACK_DEFAULT_EXPORT__={title:"Tracking & Insights/Cloc Tracking Click Insight",component:__webpack_require__("../../packages/toolkit/atoms/dist/index.es.js").hi7,parameters:{layout:"centered",docs:{description:{component:"\nClocTrackingClickInsight is a comprehensive analytics component that provides detailed insights into user click behavior and interaction patterns. It processes session data from Microsoft Clarity to deliver actionable metrics about user engagement.\n\n### Key Capabilities\n\n- **Click Metrics**: Displays total clicks, click rate per session, and click density analysis\n- **Element Analysis**: Tracks unique elements clicked and provides spatial distribution insights\n- **Top Elements List**: Shows most clicked elements with percentage breakdown and engagement scores\n- **Real-time Data**: Automatically updates when new session data is available\n- **Error Handling**: Graceful error states with retry functionality\n- **Performance Optimized**: Efficient payload decoding and data processing\n\n### Technical Implementation\n\nThe component uses the TrackingProvider context to access session data and automatically decodes Clarity payloads to extract click events. It provides comprehensive analytics while maintaining optimal performance through memoized calculations.\n                "}}},argTypes:{className:{control:"text",description:"Additional CSS classes to apply to the component container",table:{type:{summary:"string"},defaultValue:{summary:"undefined"}}}},decorators:[Story=>(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("div",{style:{width:"800px",height:"600px"},children:(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(Story,{})})]},Default={args:{},parameters:{docs:{description:{story:"The default ClocTrackingClickInsight component with standard styling and full analytics display. Shows click metrics, unique element tracking, and top clicked elements list with percentage breakdowns."}}}},CustomStyling={args:{className:"shadow-xl border-2 border-blue-200 dark:border-blue-800 bg-gradient-to-br from-blue-50 to-indigo-50 dark:from-blue-950 dark:to-indigo-950"},parameters:{docs:{description:{story:"ClocTrackingClickInsight with custom styling applied through the className prop. Features enhanced visual design with gradient background, custom borders, and shadow effects while preserving all analytics functionality."}}}};Default.parameters={...Default.parameters,docs:{...null===(_Default_parameters=Default.parameters)||void 0===_Default_parameters?void 0:_Default_parameters.docs,source:{originalSource:"{\n  args: {},\n  parameters: {\n    docs: {\n      description: {\n        story: 'The default ClocTrackingClickInsight component with standard styling and full analytics display. Shows click metrics, unique element tracking, and top clicked elements list with percentage breakdowns.'\n      }\n    }\n  }\n}",...null===(_Default_parameters1=Default.parameters)||void 0===_Default_parameters1||null===(_Default_parameters_docs=_Default_parameters1.docs)||void 0===_Default_parameters_docs?void 0:_Default_parameters_docs.source},description:{story:"Default click insight component showing comprehensive analytics with standard styling.\r\nDisplays click metrics, element tracking, and top clicked elements in a clean interface.",...null===(_Default_parameters2=Default.parameters)||void 0===_Default_parameters2||null===(_Default_parameters_docs1=_Default_parameters2.docs)||void 0===_Default_parameters_docs1?void 0:_Default_parameters_docs1.description}}},CustomStyling.parameters={...CustomStyling.parameters,docs:{...null===(_CustomStyling_parameters=CustomStyling.parameters)||void 0===_CustomStyling_parameters?void 0:_CustomStyling_parameters.docs,source:{originalSource:"{\n  args: {\n    className: 'shadow-xl border-2 border-blue-200 dark:border-blue-800 bg-gradient-to-br from-blue-50 to-indigo-50 dark:from-blue-950 dark:to-indigo-950'\n  },\n  parameters: {\n    docs: {\n      description: {\n        story: 'ClocTrackingClickInsight with custom styling applied through the className prop. Features enhanced visual design with gradient background, custom borders, and shadow effects while preserving all analytics functionality.'\n      }\n    }\n  }\n}",...null===(_CustomStyling_parameters1=CustomStyling.parameters)||void 0===_CustomStyling_parameters1||null===(_CustomStyling_parameters_docs=_CustomStyling_parameters1.docs)||void 0===_CustomStyling_parameters_docs?void 0:_CustomStyling_parameters_docs.source},description:{story:"Click insight component with custom styling applied through className prop.\r\nDemonstrates how to customize the appearance while maintaining full functionality.",...null===(_CustomStyling_parameters2=CustomStyling.parameters)||void 0===_CustomStyling_parameters2||null===(_CustomStyling_parameters_docs1=_CustomStyling_parameters2.docs)||void 0===_CustomStyling_parameters_docs1?void 0:_CustomStyling_parameters_docs1.description}}};const __namedExportsOrder=["Default","CustomStyling"]}}]);