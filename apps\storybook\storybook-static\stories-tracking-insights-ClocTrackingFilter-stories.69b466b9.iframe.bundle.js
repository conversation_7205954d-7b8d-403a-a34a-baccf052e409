"use strict";(self.webpackChunk_cloc_storybook=self.webpackChunk_cloc_storybook||[]).push([[7365],{"./src/stories/tracking-insights/ClocTrackingFilter.stories.tsx":(__unused_webpack_module,__webpack_exports__,__webpack_require__)=>{__webpack_require__.r(__webpack_exports__),__webpack_require__.d(__webpack_exports__,{CustomRefreshInterval:()=>CustomRefreshInterval,CustomStyling:()=>CustomStyling,Default:()=>Default,WithAutoRefresh:()=>WithAutoRefresh,__namedExportsOrder:()=>__namedExportsOrder,default:()=>__WEBPACK_DEFAULT_EXPORT__});var _Default_parameters,_Default_parameters_docs,_Default_parameters1,_Default_parameters_docs1,_Default_parameters2,_WithAutoRefresh_parameters,_WithAutoRefresh_parameters_docs,_WithAutoRefresh_parameters1,_WithAutoRefresh_parameters_docs1,_WithAutoRefresh_parameters2,_CustomRefreshInterval_parameters,_CustomRefreshInterval_parameters_docs,_CustomRefreshInterval_parameters1,_CustomRefreshInterval_parameters_docs1,_CustomRefreshInterval_parameters2,_CustomStyling_parameters,_CustomStyling_parameters_docs,_CustomStyling_parameters1,_CustomStyling_parameters_docs1,_CustomStyling_parameters2,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__=__webpack_require__("../../node_modules/next/dist/compiled/react/jsx-runtime.js");const __WEBPACK_DEFAULT_EXPORT__={title:"Tracking & Insights/Cloc Tracking Filter",component:__webpack_require__("../../packages/toolkit/atoms/dist/index.es.js").BBW,parameters:{layout:"centered",docs:{description:{component:"\nClocTrackingFilter is a sophisticated filtering component that provides comprehensive controls for tracking session data. It offers timezone-aware date/time filtering, employee selection, and auto-refresh capabilities with intelligent permission handling.\n\n### Key Capabilities\n\n- **Timezone-Aware Filtering**: Intelligent timezone handling with 3-tier priority system\n- **Date/Time Controls**: Separate date picker and time inputs for precise range selection\n- **Employee Selection**: Permission-based employee selector for multi-user analytics\n- **Auto-refresh**: Configurable automatic refresh with customizable intervals\n- **Time Validation**: Real-time validation ensuring start time is before end time\n- **Permission Integration**: Adaptive UI based on user roles and permissions\n\n### Timezone Handling\n\nThe component implements a sophisticated timezone priority system:\n1. **User Timezone**: Uses user.timezone field if available\n2. **Browser Timezone**: Falls back to browser-detected timezone\n3. **UTC Fallback**: Uses UTC as final fallback for consistency\n\n### Technical Implementation\n\nThe component integrates with both TrackingProvider and ClocProvider contexts to access session data, user information, and permissions. It provides efficient state management and real-time validation while maintaining optimal performance.\n                "}}},argTypes:{className:{control:"text",description:"Additional CSS classes to apply to the component container",table:{type:{summary:"string"},defaultValue:{summary:"undefined"}}},autoRefresh:{control:"boolean",description:"Whether to enable automatic refresh of session data",table:{type:{summary:"boolean"},defaultValue:{summary:"false"}}},refreshInterval:{control:"number",description:"Refresh interval in milliseconds when auto-refresh is enabled",table:{type:{summary:"number"},defaultValue:{summary:"30000"}}}},decorators:[Story=>(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("div",{style:{width:"800px",height:"400px"},children:(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(Story,{})})]},Default={args:{autoRefresh:!1},parameters:{docs:{description:{story:"The default ClocTrackingFilter component without auto-refresh enabled. Provides timezone-aware date/time filtering and employee selection with manual refresh control."}}}},WithAutoRefresh={args:{autoRefresh:!0},parameters:{docs:{description:{story:"ClocTrackingFilter with auto-refresh enabled using the default 30-second interval. Automatically refreshes session data at regular intervals for real-time monitoring scenarios."}}}},CustomRefreshInterval={args:{autoRefresh:!0,refreshInterval:1e4},parameters:{docs:{description:{story:"ClocTrackingFilter with auto-refresh enabled and a custom 10-second refresh interval. Provides high-frequency data updates for real-time monitoring and analysis scenarios."}}}},CustomStyling={args:{autoRefresh:!1,className:"shadow-xl border-2 border-teal-200 dark:border-teal-800 bg-gradient-to-br from-teal-50 to-cyan-50 dark:from-teal-950 dark:to-cyan-950"},parameters:{docs:{description:{story:"ClocTrackingFilter with custom styling applied through the className prop. Features enhanced visual design with gradient background, custom borders, and shadow effects while preserving all filtering functionality."}}}};Default.parameters={...Default.parameters,docs:{...null===(_Default_parameters=Default.parameters)||void 0===_Default_parameters?void 0:_Default_parameters.docs,source:{originalSource:"{\n  args: {\n    autoRefresh: false\n  },\n  parameters: {\n    docs: {\n      description: {\n        story: 'The default ClocTrackingFilter component without auto-refresh enabled. Provides timezone-aware date/time filtering and employee selection with manual refresh control.'\n      }\n    }\n  }\n}",...null===(_Default_parameters1=Default.parameters)||void 0===_Default_parameters1||null===(_Default_parameters_docs=_Default_parameters1.docs)||void 0===_Default_parameters_docs?void 0:_Default_parameters_docs.source},description:{story:"Default filter component without auto-refresh functionality.\r\nProvides basic date/time filtering and employee selection capabilities.",...null===(_Default_parameters2=Default.parameters)||void 0===_Default_parameters2||null===(_Default_parameters_docs1=_Default_parameters2.docs)||void 0===_Default_parameters_docs1?void 0:_Default_parameters_docs1.description}}},WithAutoRefresh.parameters={...WithAutoRefresh.parameters,docs:{...null===(_WithAutoRefresh_parameters=WithAutoRefresh.parameters)||void 0===_WithAutoRefresh_parameters?void 0:_WithAutoRefresh_parameters.docs,source:{originalSource:"{\n  args: {\n    autoRefresh: true\n  },\n  parameters: {\n    docs: {\n      description: {\n        story: 'ClocTrackingFilter with auto-refresh enabled using the default 30-second interval. Automatically refreshes session data at regular intervals for real-time monitoring scenarios.'\n      }\n    }\n  }\n}",...null===(_WithAutoRefresh_parameters1=WithAutoRefresh.parameters)||void 0===_WithAutoRefresh_parameters1||null===(_WithAutoRefresh_parameters_docs=_WithAutoRefresh_parameters1.docs)||void 0===_WithAutoRefresh_parameters_docs?void 0:_WithAutoRefresh_parameters_docs.source},description:{story:"Filter component with auto-refresh enabled using default 30-second interval.\r\nAutomatically updates session data at regular intervals.",...null===(_WithAutoRefresh_parameters2=WithAutoRefresh.parameters)||void 0===_WithAutoRefresh_parameters2||null===(_WithAutoRefresh_parameters_docs1=_WithAutoRefresh_parameters2.docs)||void 0===_WithAutoRefresh_parameters_docs1?void 0:_WithAutoRefresh_parameters_docs1.description}}},CustomRefreshInterval.parameters={...CustomRefreshInterval.parameters,docs:{...null===(_CustomRefreshInterval_parameters=CustomRefreshInterval.parameters)||void 0===_CustomRefreshInterval_parameters?void 0:_CustomRefreshInterval_parameters.docs,source:{originalSource:"{\n  args: {\n    autoRefresh: true,\n    refreshInterval: 10000\n  },\n  parameters: {\n    docs: {\n      description: {\n        story: 'ClocTrackingFilter with auto-refresh enabled and a custom 10-second refresh interval. Provides high-frequency data updates for real-time monitoring and analysis scenarios.'\n      }\n    }\n  }\n}",...null===(_CustomRefreshInterval_parameters1=CustomRefreshInterval.parameters)||void 0===_CustomRefreshInterval_parameters1||null===(_CustomRefreshInterval_parameters_docs=_CustomRefreshInterval_parameters1.docs)||void 0===_CustomRefreshInterval_parameters_docs?void 0:_CustomRefreshInterval_parameters_docs.source},description:{story:"Filter component with custom refresh interval for high-frequency monitoring.\r\nUpdates session data every 10 seconds for real-time analysis.",...null===(_CustomRefreshInterval_parameters2=CustomRefreshInterval.parameters)||void 0===_CustomRefreshInterval_parameters2||null===(_CustomRefreshInterval_parameters_docs1=_CustomRefreshInterval_parameters2.docs)||void 0===_CustomRefreshInterval_parameters_docs1?void 0:_CustomRefreshInterval_parameters_docs1.description}}},CustomStyling.parameters={...CustomStyling.parameters,docs:{...null===(_CustomStyling_parameters=CustomStyling.parameters)||void 0===_CustomStyling_parameters?void 0:_CustomStyling_parameters.docs,source:{originalSource:"{\n  args: {\n    autoRefresh: false,\n    className: 'shadow-xl border-2 border-teal-200 dark:border-teal-800 bg-gradient-to-br from-teal-50 to-cyan-50 dark:from-teal-950 dark:to-cyan-950'\n  },\n  parameters: {\n    docs: {\n      description: {\n        story: 'ClocTrackingFilter with custom styling applied through the className prop. Features enhanced visual design with gradient background, custom borders, and shadow effects while preserving all filtering functionality.'\n      }\n    }\n  }\n}",...null===(_CustomStyling_parameters1=CustomStyling.parameters)||void 0===_CustomStyling_parameters1||null===(_CustomStyling_parameters_docs=_CustomStyling_parameters1.docs)||void 0===_CustomStyling_parameters_docs?void 0:_CustomStyling_parameters_docs.source},description:{story:"Filter component with custom styling applied through className prop.\r\nDemonstrates visual customization while maintaining full functionality.",...null===(_CustomStyling_parameters2=CustomStyling.parameters)||void 0===_CustomStyling_parameters2||null===(_CustomStyling_parameters_docs1=_CustomStyling_parameters2.docs)||void 0===_CustomStyling_parameters_docs1?void 0:_CustomStyling_parameters_docs1.description}}};const __namedExportsOrder=["Default","WithAutoRefresh","CustomRefreshInterval","CustomStyling"]}}]);