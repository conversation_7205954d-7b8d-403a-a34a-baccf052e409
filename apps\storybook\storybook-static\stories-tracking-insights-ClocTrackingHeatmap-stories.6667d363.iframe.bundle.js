"use strict";(self.webpackChunk_cloc_storybook=self.webpackChunk_cloc_storybook||[]).push([[8553],{"./src/stories/tracking-insights/ClocTrackingHeatmap.stories.tsx":(__unused_webpack_module,__webpack_exports__,__webpack_require__)=>{__webpack_require__.r(__webpack_exports__),__webpack_require__.d(__webpack_exports__,{CustomStyling:()=>CustomStyling,Default:()=>Default,WithoutControls:()=>WithoutControls,__namedExportsOrder:()=>__namedExportsOrder,default:()=>__WEBPACK_DEFAULT_EXPORT__});var _Default_parameters,_Default_parameters_docs,_Default_parameters1,_Default_parameters_docs1,_Default_parameters2,_WithoutControls_parameters,_WithoutControls_parameters_docs,_WithoutControls_parameters1,_WithoutControls_parameters_docs1,_WithoutControls_parameters2,_CustomStyling_parameters,_CustomStyling_parameters_docs,_CustomStyling_parameters1,_CustomStyling_parameters_docs1,_CustomStyling_parameters2,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__=__webpack_require__("../../node_modules/next/dist/compiled/react/jsx-runtime.js");const __WEBPACK_DEFAULT_EXPORT__={title:"Tracking & Insights/Cloc Tracking Heatmap",component:__webpack_require__("../../packages/toolkit/atoms/dist/index.es.js").vY6,parameters:{layout:"centered",docs:{description:{component:"\nClocTrackingHeatmap is a high-performance visualization component that provides interactive heatmap analysis of user behavior. It offers both click and scroll heatmaps with advanced controls and significant performance optimizations.\n\n### Key Capabilities\n\n- **Click Heatmaps**: Visualizes click patterns with color-coded intensity and aggregation\n- **Scroll Heatmaps**: Shows scroll behavior patterns with directional indicators\n- **Multiple Color Schemes**: Hot, cool, and blue color schemes for different visualization needs\n- **Interactive Controls**: Real-time adjustment of aggregation radius, color schemes, and display options\n- **Device Analytics**: Automatic device type detection with mobile/desktop statistics\n- **Performance Optimized**: Advanced optimizations including memoized components and debounced controls\n\n### Performance Features\n\n- **70-80% Fewer Re-renders**: Through custom context hooks and component memoization\n- **Debounced Controls**: 90% reduction in computation frequency during slider interactions\n- **Viewport Filtering**: 40-50% fewer canvas operations by rendering only visible elements\n- **RequestAnimationFrame**: Smooth scrolling with optimal frame rate synchronization\n\n### Technical Implementation\n\nThe component uses Microsoft Clarity's visualization engine with custom performance enhancements. It provides real-time heatmap generation while maintaining smooth user interactions through advanced optimization techniques.\n                "}}},argTypes:{className:{control:"text",description:"Additional CSS classes to apply to the component container",table:{type:{summary:"string"},defaultValue:{summary:"undefined"}}},showControl:{control:"boolean",description:"Whether to show the heatmap control panel for adjusting visualization settings",table:{type:{summary:"boolean"},defaultValue:{summary:"true"}}}},decorators:[Story=>(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("div",{style:{width:"1200px",height:"800px"},children:(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(Story,{})})]},Default={args:{showControl:!0},parameters:{docs:{description:{story:"The default ClocTrackingHeatmap component with controls enabled. Shows the full heatmap interface including the control panel for adjusting aggregation radius, color schemes, and heatmap modes."}}}},WithoutControls={args:{showControl:!1},parameters:{docs:{description:{story:"ClocTrackingHeatmap with controls disabled (showControl=false). Provides a clean heatmap visualization without the control panel, ideal for embedded use cases or when controls are managed externally."}}}},CustomStyling={args:{showControl:!0,className:"shadow-2xl border-2 border-orange-200 dark:border-orange-800 bg-gradient-to-br from-orange-50 to-red-50 dark:from-orange-950 dark:to-red-950"},parameters:{docs:{description:{story:"ClocTrackingHeatmap with custom styling applied through the className prop and controls enabled. Features enhanced visual design with gradient background, custom borders, and shadow effects while preserving all heatmap functionality."}}}};Default.parameters={...Default.parameters,docs:{...null===(_Default_parameters=Default.parameters)||void 0===_Default_parameters?void 0:_Default_parameters.docs,source:{originalSource:"{\n  args: {\n    showControl: true\n  },\n  parameters: {\n    docs: {\n      description: {\n        story: 'The default ClocTrackingHeatmap component with controls enabled. Shows the full heatmap interface including the control panel for adjusting aggregation radius, color schemes, and heatmap modes.'\n      }\n    }\n  }\n}",...null===(_Default_parameters1=Default.parameters)||void 0===_Default_parameters1||null===(_Default_parameters_docs=_Default_parameters1.docs)||void 0===_Default_parameters_docs?void 0:_Default_parameters_docs.source},description:{story:"Default heatmap component with controls enabled showing full functionality.\r\nDisplays interactive heatmap with control panel for customization.",...null===(_Default_parameters2=Default.parameters)||void 0===_Default_parameters2||null===(_Default_parameters_docs1=_Default_parameters2.docs)||void 0===_Default_parameters_docs1?void 0:_Default_parameters_docs1.description}}},WithoutControls.parameters={...WithoutControls.parameters,docs:{...null===(_WithoutControls_parameters=WithoutControls.parameters)||void 0===_WithoutControls_parameters?void 0:_WithoutControls_parameters.docs,source:{originalSource:"{\n  args: {\n    showControl: false\n  },\n  parameters: {\n    docs: {\n      description: {\n        story: 'ClocTrackingHeatmap with controls disabled (showControl=false). Provides a clean heatmap visualization without the control panel, ideal for embedded use cases or when controls are managed externally.'\n      }\n    }\n  }\n}",...null===(_WithoutControls_parameters1=WithoutControls.parameters)||void 0===_WithoutControls_parameters1||null===(_WithoutControls_parameters_docs=_WithoutControls_parameters1.docs)||void 0===_WithoutControls_parameters_docs?void 0:_WithoutControls_parameters_docs.source},description:{story:"Heatmap component without control panel for embedded or simplified use cases.\r\nProvides clean heatmap visualization without interactive controls.",...null===(_WithoutControls_parameters2=WithoutControls.parameters)||void 0===_WithoutControls_parameters2||null===(_WithoutControls_parameters_docs1=_WithoutControls_parameters2.docs)||void 0===_WithoutControls_parameters_docs1?void 0:_WithoutControls_parameters_docs1.description}}},CustomStyling.parameters={...CustomStyling.parameters,docs:{...null===(_CustomStyling_parameters=CustomStyling.parameters)||void 0===_CustomStyling_parameters?void 0:_CustomStyling_parameters.docs,source:{originalSource:"{\n  args: {\n    showControl: true,\n    className: 'shadow-2xl border-2 border-orange-200 dark:border-orange-800 bg-gradient-to-br from-orange-50 to-red-50 dark:from-orange-950 dark:to-red-950'\n  },\n  parameters: {\n    docs: {\n      description: {\n        story: 'ClocTrackingHeatmap with custom styling applied through the className prop and controls enabled. Features enhanced visual design with gradient background, custom borders, and shadow effects while preserving all heatmap functionality.'\n      }\n    }\n  }\n}",...null===(_CustomStyling_parameters1=CustomStyling.parameters)||void 0===_CustomStyling_parameters1||null===(_CustomStyling_parameters_docs=_CustomStyling_parameters1.docs)||void 0===_CustomStyling_parameters_docs?void 0:_CustomStyling_parameters_docs.source},description:{story:"Heatmap component with custom styling and controls enabled.\r\nDemonstrates visual customization while maintaining full functionality.",...null===(_CustomStyling_parameters2=CustomStyling.parameters)||void 0===_CustomStyling_parameters2||null===(_CustomStyling_parameters_docs1=_CustomStyling_parameters2.docs)||void 0===_CustomStyling_parameters_docs1?void 0:_CustomStyling_parameters_docs1.description}}};const __namedExportsOrder=["Default","WithoutControls","CustomStyling"]}}]);