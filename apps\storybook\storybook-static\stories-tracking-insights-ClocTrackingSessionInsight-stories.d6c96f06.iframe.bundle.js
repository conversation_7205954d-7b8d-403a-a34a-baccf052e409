"use strict";(self.webpackChunk_cloc_storybook=self.webpackChunk_cloc_storybook||[]).push([[6975],{"./src/stories/tracking-insights/ClocTrackingSessionInsight.stories.tsx":(__unused_webpack_module,__webpack_exports__,__webpack_require__)=>{__webpack_require__.r(__webpack_exports__),__webpack_require__.d(__webpack_exports__,{CustomStyling:()=>CustomStyling,Default:()=>Default,__namedExportsOrder:()=>__namedExportsOrder,default:()=>__WEBPACK_DEFAULT_EXPORT__});var _Default_parameters,_Default_parameters_docs,_Default_parameters1,_Default_parameters_docs1,_Default_parameters2,_CustomStyling_parameters,_CustomStyling_parameters_docs,_CustomStyling_parameters1,_CustomStyling_parameters_docs1,_CustomStyling_parameters2,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__=__webpack_require__("../../node_modules/next/dist/compiled/react/jsx-runtime.js");const __WEBPACK_DEFAULT_EXPORT__={title:"Tracking & Insights/Cloc Tracking Session Insight",component:__webpack_require__("../../packages/toolkit/atoms/dist/index.es.js").tc8,parameters:{layout:"centered",docs:{description:{component:"\nClocTrackingSessionInsight is a powerful analytics component that delivers comprehensive insights into user session behavior and engagement patterns. It processes Microsoft Clarity session data to provide actionable metrics about user interactions.\n\n### Key Capabilities\n\n- **Session Duration**: Tracks total session time, average duration, and time distribution patterns\n- **Engagement Scoring**: Calculates engagement scores based on interaction frequency and quality\n- **Interaction Analysis**: Measures interaction rates, event density, and user activity levels\n- **Device Intelligence**: Automatically detects and categorizes device types with usage statistics\n- **Event Tracking**: Comprehensive event count analysis with categorization and trends\n- **Quality Metrics**: Session quality assessment based on multiple engagement factors\n\n### Technical Implementation\n\nThe component leverages the TrackingProvider context to access session data and performs sophisticated analysis of decoded Clarity payloads. It provides real-time insights while maintaining optimal performance through efficient data processing and memoized calculations.\n                "}}},argTypes:{className:{control:"text",description:"Additional CSS classes to apply to the component container",table:{type:{summary:"string"},defaultValue:{summary:"undefined"}}}},decorators:[Story=>(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("div",{style:{width:"800px",height:"600px"},children:(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(Story,{})})]},Default={args:{},parameters:{docs:{description:{story:"The default ClocTrackingSessionInsight component with standard styling and full analytics display. Shows session duration metrics, engagement scoring, interaction rate analysis, and device type statistics."}}}},CustomStyling={args:{className:"shadow-xl border-2 border-green-200 dark:border-green-800 bg-gradient-to-br from-green-50 to-emerald-50 dark:from-green-950 dark:to-emerald-950"},parameters:{docs:{description:{story:"ClocTrackingSessionInsight with custom styling applied through the className prop. Features enhanced visual design with gradient background, custom borders, and shadow effects while preserving all session analytics functionality."}}}};Default.parameters={...Default.parameters,docs:{...null===(_Default_parameters=Default.parameters)||void 0===_Default_parameters?void 0:_Default_parameters.docs,source:{originalSource:"{\n  args: {},\n  parameters: {\n    docs: {\n      description: {\n        story: 'The default ClocTrackingSessionInsight component with standard styling and full analytics display. Shows session duration metrics, engagement scoring, interaction rate analysis, and device type statistics.'\n      }\n    }\n  }\n}",...null===(_Default_parameters1=Default.parameters)||void 0===_Default_parameters1||null===(_Default_parameters_docs=_Default_parameters1.docs)||void 0===_Default_parameters_docs?void 0:_Default_parameters_docs.source},description:{story:"Default session insight component showing comprehensive analytics with standard styling.\r\nDisplays session metrics, engagement scoring, and device analytics in a clean interface.",...null===(_Default_parameters2=Default.parameters)||void 0===_Default_parameters2||null===(_Default_parameters_docs1=_Default_parameters2.docs)||void 0===_Default_parameters_docs1?void 0:_Default_parameters_docs1.description}}},CustomStyling.parameters={...CustomStyling.parameters,docs:{...null===(_CustomStyling_parameters=CustomStyling.parameters)||void 0===_CustomStyling_parameters?void 0:_CustomStyling_parameters.docs,source:{originalSource:"{\n  args: {\n    className: 'shadow-xl border-2 border-green-200 dark:border-green-800 bg-gradient-to-br from-green-50 to-emerald-50 dark:from-green-950 dark:to-emerald-950'\n  },\n  parameters: {\n    docs: {\n      description: {\n        story: 'ClocTrackingSessionInsight with custom styling applied through the className prop. Features enhanced visual design with gradient background, custom borders, and shadow effects while preserving all session analytics functionality.'\n      }\n    }\n  }\n}",...null===(_CustomStyling_parameters1=CustomStyling.parameters)||void 0===_CustomStyling_parameters1||null===(_CustomStyling_parameters_docs=_CustomStyling_parameters1.docs)||void 0===_CustomStyling_parameters_docs?void 0:_CustomStyling_parameters_docs.source},description:{story:"Session insight component with custom styling applied through className prop.\r\nDemonstrates how to customize the appearance while maintaining full functionality.",...null===(_CustomStyling_parameters2=CustomStyling.parameters)||void 0===_CustomStyling_parameters2||null===(_CustomStyling_parameters_docs1=_CustomStyling_parameters2.docs)||void 0===_CustomStyling_parameters_docs1?void 0:_CustomStyling_parameters_docs1.description}}};const __namedExportsOrder=["Default","CustomStyling"]}}]);