"use strict";(self.webpackChunk_cloc_storybook=self.webpackChunk_cloc_storybook||[]).push([[9604],{"./src/stories/tracking-insights/ClocTrackingSessionReplay.stories.tsx":(__unused_webpack_module,__webpack_exports__,__webpack_require__)=>{__webpack_require__.r(__webpack_exports__),__webpack_require__.d(__webpack_exports__,{CustomStyling:()=>CustomStyling,Default:()=>Default,__namedExportsOrder:()=>__namedExportsOrder,default:()=>__WEBPACK_DEFAULT_EXPORT__});var _Default_parameters,_Default_parameters_docs,_Default_parameters1,_Default_parameters_docs1,_Default_parameters2,_CustomStyling_parameters,_CustomStyling_parameters_docs,_CustomStyling_parameters1,_CustomStyling_parameters_docs1,_CustomStyling_parameters2,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__=__webpack_require__("../../node_modules/next/dist/compiled/react/jsx-runtime.js");const __WEBPACK_DEFAULT_EXPORT__={title:"Tracking & Insights/Cloc Tracking Session Replay",component:__webpack_require__("../../packages/toolkit/atoms/dist/index.es.js").WSf,parameters:{layout:"centered",docs:{description:{component:"\nClocTrackingSessionReplay is a comprehensive session replay component that provides full Microsoft Clarity integration for watching and analyzing user sessions. It offers an intuitive interface for browsing, selecting, and replaying user interactions.\n\n### Key Capabilities\n\n- **Full Session Replay**: Complete Microsoft Clarity session playback with all user interactions\n- **Session Browser**: Interactive list of available sessions with key metadata\n- **Device Detection**: Automatic device type identification with visual indicators\n- **Session Metadata**: Duration, timestamps, URLs, and session IDs for each session\n- **Interactive Selection**: Click-to-select sessions with visual feedback and state management\n- **Responsive Design**: Adaptive layout that works across different screen sizes\n- **State Management**: Intelligent session clearing when filters change\n\n### Technical Implementation\n\nThe component integrates with the TrackingProvider context to access session data and uses Microsoft Clarity's replay functionality to render actual user sessions. It provides efficient session management and optimal performance through memoized device type detection.\n                "}}},argTypes:{className:{control:"text",description:"Additional CSS classes to apply to the component container",table:{type:{summary:"string"},defaultValue:{summary:"undefined"}}}},decorators:[Story=>(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("div",{style:{width:"1200px",height:"800px"},children:(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(Story,{})})]},Default={args:{},parameters:{docs:{description:{story:"The default ClocTrackingSessionReplay component with standard styling and full replay functionality. Shows the session browser on the left with session metadata and the replay area on the right for selected sessions."}}}},CustomStyling={args:{className:"shadow-2xl border-2 border-purple-200 dark:border-purple-800 bg-gradient-to-br from-purple-50 to-violet-50 dark:from-purple-950 dark:to-violet-950"},parameters:{docs:{description:{story:"ClocTrackingSessionReplay with custom styling applied through the className prop. Features enhanced visual design with gradient background, custom borders, and shadow effects while preserving all session replay functionality."}}}};Default.parameters={...Default.parameters,docs:{...null===(_Default_parameters=Default.parameters)||void 0===_Default_parameters?void 0:_Default_parameters.docs,source:{originalSource:"{\n  args: {},\n  parameters: {\n    docs: {\n      description: {\n        story: 'The default ClocTrackingSessionReplay component with standard styling and full replay functionality. Shows the session browser on the left with session metadata and the replay area on the right for selected sessions.'\n      }\n    }\n  }\n}",...null===(_Default_parameters1=Default.parameters)||void 0===_Default_parameters1||null===(_Default_parameters_docs=_Default_parameters1.docs)||void 0===_Default_parameters_docs?void 0:_Default_parameters_docs.source},description:{story:"Default session replay component showing the session browser and replay interface.\r\nDisplays session list with metadata and provides full replay functionality.",...null===(_Default_parameters2=Default.parameters)||void 0===_Default_parameters2||null===(_Default_parameters_docs1=_Default_parameters2.docs)||void 0===_Default_parameters_docs1?void 0:_Default_parameters_docs1.description}}},CustomStyling.parameters={...CustomStyling.parameters,docs:{...null===(_CustomStyling_parameters=CustomStyling.parameters)||void 0===_CustomStyling_parameters?void 0:_CustomStyling_parameters.docs,source:{originalSource:"{\n  args: {\n    className: 'shadow-2xl border-2 border-purple-200 dark:border-purple-800 bg-gradient-to-br from-purple-50 to-violet-50 dark:from-purple-950 dark:to-violet-950'\n  },\n  parameters: {\n    docs: {\n      description: {\n        story: 'ClocTrackingSessionReplay with custom styling applied through the className prop. Features enhanced visual design with gradient background, custom borders, and shadow effects while preserving all session replay functionality.'\n      }\n    }\n  }\n}",...null===(_CustomStyling_parameters1=CustomStyling.parameters)||void 0===_CustomStyling_parameters1||null===(_CustomStyling_parameters_docs=_CustomStyling_parameters1.docs)||void 0===_CustomStyling_parameters_docs?void 0:_CustomStyling_parameters_docs.source},description:{story:"Session replay component with custom styling applied through className prop.\r\nDemonstrates how to customize the appearance while maintaining full functionality.",...null===(_CustomStyling_parameters2=CustomStyling.parameters)||void 0===_CustomStyling_parameters2||null===(_CustomStyling_parameters_docs1=_CustomStyling_parameters2.docs)||void 0===_CustomStyling_parameters_docs1?void 0:_CustomStyling_parameters_docs1.description}}};const __namedExportsOrder=["Default","CustomStyling"]}}]);