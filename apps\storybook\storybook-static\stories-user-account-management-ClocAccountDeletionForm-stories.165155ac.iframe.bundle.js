"use strict";(self.webpackChunk_cloc_storybook=self.webpackChunk_cloc_storybook||[]).push([[811],{"./src/stories/user-account-management/ClocAccountDeletionForm.stories.tsx":(__unused_webpack_module,__webpack_exports__,__webpack_require__)=>{__webpack_require__.r(__webpack_exports__),__webpack_require__.d(__webpack_exports__,{CustomStyling:()=>CustomStyling,Default:()=>Default,InModal:()=>InModal,__namedExportsOrder:()=>__namedExportsOrder,default:()=>__WEBPACK_DEFAULT_EXPORT__});var _Default_parameters,_Default_parameters_docs,_Default_parameters1,_Default_parameters_docs1,_Default_parameters2,_InModal_parameters,_InModal_parameters_docs,_InModal_parameters1,_InModal_parameters_docs1,_InModal_parameters2,_CustomStyling_parameters,_CustomStyling_parameters_docs,_CustomStyling_parameters1,_CustomStyling_parameters_docs1,_CustomStyling_parameters2,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__=__webpack_require__("../../node_modules/next/dist/compiled/react/jsx-runtime.js"),_cloc_atoms__WEBPACK_IMPORTED_MODULE_2__=(__webpack_require__("../../node_modules/next/dist/compiled/react/index.js"),__webpack_require__("../../packages/toolkit/atoms/dist/index.es.js"));const __WEBPACK_DEFAULT_EXPORT__={title:"User Account Management/Account Deletion Form",component:_cloc_atoms__WEBPACK_IMPORTED_MODULE_2__.TKn,parameters:{layout:"centered",docs:{description:{component:"A critical account deletion form component with password confirmation and double-confirmation dialog. Features destructive action styling with red theming, loading states, error handling, and AlertDialog confirmation to prevent accidental deletions. Requires authenticated user context to function properly."}}},argTypes:{className:{control:"text",description:"Additional CSS classes for styling the form container"}}},Default={args:{}},InModal={render:()=>(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4",children:(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("div",{className:"bg-white dark:bg-gray-900 rounded-lg shadow-xl max-w-md w-full",children:(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("div",{className:"p-6",children:(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(_cloc_atoms__WEBPACK_IMPORTED_MODULE_2__.TKn,{className:"border-0 bg-transparent p-0 shadow-none"})})})}),parameters:{docs:{description:{story:"Account deletion form optimized for modal display with transparent background and removed borders."}}}},CustomStyling={args:{className:"border-2 border-red-300 dark:border-red-700 bg-red-50 dark:bg-red-950"},parameters:{docs:{description:{story:"Form with enhanced red-themed styling to emphasize the destructive nature of the action."}}}};Default.parameters={...Default.parameters,docs:{...null===(_Default_parameters=Default.parameters)||void 0===_Default_parameters?void 0:_Default_parameters.docs,source:{originalSource:"{\n  args: {}\n}",...null===(_Default_parameters1=Default.parameters)||void 0===_Default_parameters1||null===(_Default_parameters_docs=_Default_parameters1.docs)||void 0===_Default_parameters_docs?void 0:_Default_parameters_docs.source},description:{story:"Default account deletion form with standard destructive styling.\r\nShows password confirmation field and delete button with confirmation dialog.",...null===(_Default_parameters2=Default.parameters)||void 0===_Default_parameters2||null===(_Default_parameters_docs1=_Default_parameters2.docs)||void 0===_Default_parameters_docs1?void 0:_Default_parameters_docs1.description}}},InModal.parameters={...InModal.parameters,docs:{...null===(_InModal_parameters=InModal.parameters)||void 0===_InModal_parameters?void 0:_InModal_parameters.docs,source:{originalSource:'{\n  render: () => <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4">\r\n            <div className="bg-white dark:bg-gray-900 rounded-lg shadow-xl max-w-md w-full">\r\n                <div className="p-6">\r\n                    <ClocAccountDeletionForm className="border-0 bg-transparent p-0 shadow-none" />\r\n                </div>\r\n            </div>\r\n        </div>,\n  parameters: {\n    docs: {\n      description: {\n        story: \'Account deletion form optimized for modal display with transparent background and removed borders.\'\n      }\n    }\n  }\n}',...null===(_InModal_parameters1=InModal.parameters)||void 0===_InModal_parameters1||null===(_InModal_parameters_docs=_InModal_parameters1.docs)||void 0===_InModal_parameters_docs?void 0:_InModal_parameters_docs.source},description:{story:"Account deletion form displayed within a modal context.\r\nDemonstrates how the form appears in dialog overlays.",...null===(_InModal_parameters2=InModal.parameters)||void 0===_InModal_parameters2||null===(_InModal_parameters_docs1=_InModal_parameters2.docs)||void 0===_InModal_parameters_docs1?void 0:_InModal_parameters_docs1.description}}},CustomStyling.parameters={...CustomStyling.parameters,docs:{...null===(_CustomStyling_parameters=CustomStyling.parameters)||void 0===_CustomStyling_parameters?void 0:_CustomStyling_parameters.docs,source:{originalSource:"{\n  args: {\n    className: 'border-2 border-red-300 dark:border-red-700 bg-red-50 dark:bg-red-950'\n  },\n  parameters: {\n    docs: {\n      description: {\n        story: 'Form with enhanced red-themed styling to emphasize the destructive nature of the action.'\n      }\n    }\n  }\n}",...null===(_CustomStyling_parameters1=CustomStyling.parameters)||void 0===_CustomStyling_parameters1||null===(_CustomStyling_parameters_docs=_CustomStyling_parameters1.docs)||void 0===_CustomStyling_parameters_docs?void 0:_CustomStyling_parameters_docs.source},description:{story:"Account deletion form with custom styling applied via className prop.\r\nDemonstrates visual customization while maintaining destructive action context.",...null===(_CustomStyling_parameters2=CustomStyling.parameters)||void 0===_CustomStyling_parameters2||null===(_CustomStyling_parameters_docs1=_CustomStyling_parameters2.docs)||void 0===_CustomStyling_parameters_docs1?void 0:_CustomStyling_parameters_docs1.description}}};const __namedExportsOrder=["Default","InModal","CustomStyling"]}}]);