"use strict";(self.webpackChunk_cloc_storybook=self.webpackChunk_cloc_storybook||[]).push([[3928],{"./src/stories/user-account-management/ClocPasswordUpdateForm.stories.tsx":(__unused_webpack_module,__webpack_exports__,__webpack_require__)=>{__webpack_require__.r(__webpack_exports__),__webpack_require__.d(__webpack_exports__,{CustomStyling:()=>CustomStyling,Default:()=>Default,__namedExportsOrder:()=>__namedExportsOrder,default:()=>__WEBPACK_DEFAULT_EXPORT__});var _Default_parameters,_Default_parameters_docs,_Default_parameters1,_Default_parameters_docs1,_Default_parameters2,_CustomStyling_parameters,_CustomStyling_parameters_docs,_CustomStyling_parameters1,_CustomStyling_parameters_docs1,_CustomStyling_parameters2;__webpack_require__("../../node_modules/next/dist/compiled/react/index.js");const __WEBPACK_DEFAULT_EXPORT__={title:"User Account Management/Password Update Form",component:__webpack_require__("../../packages/toolkit/atoms/dist/index.es.js").eZn,parameters:{layout:"centered",docs:{description:{component:"A comprehensive password update form component with three-field validation (current password, new password, confirm new password). Features form validation, error handling, loading states with spinner animation, and orange-themed submit button. Requires authenticated user context to function properly."}}},argTypes:{className:{control:"text",description:"Additional CSS classes for styling the form container"}}},Default={args:{}},CustomStyling={args:{className:"border-2 border-orange-200 dark:border-orange-800 bg-orange-50 dark:bg-orange-950"},parameters:{docs:{description:{story:"Form with custom orange-themed styling applied through the className prop."}}}};Default.parameters={...Default.parameters,docs:{...null===(_Default_parameters=Default.parameters)||void 0===_Default_parameters?void 0:_Default_parameters.docs,source:{originalSource:"{\n  args: {}\n}",...null===(_Default_parameters1=Default.parameters)||void 0===_Default_parameters1||null===(_Default_parameters_docs=_Default_parameters1.docs)||void 0===_Default_parameters_docs?void 0:_Default_parameters_docs.source},description:{story:"Default password update form with standard styling and functionality.\r\nShows current password, new password, and confirm password fields with update button.",...null===(_Default_parameters2=Default.parameters)||void 0===_Default_parameters2||null===(_Default_parameters_docs1=_Default_parameters2.docs)||void 0===_Default_parameters_docs1?void 0:_Default_parameters_docs1.description}}},CustomStyling.parameters={...CustomStyling.parameters,docs:{...null===(_CustomStyling_parameters=CustomStyling.parameters)||void 0===_CustomStyling_parameters?void 0:_CustomStyling_parameters.docs,source:{originalSource:"{\n  args: {\n    className: 'border-2 border-orange-200 dark:border-orange-800 bg-orange-50 dark:bg-orange-950'\n  },\n  parameters: {\n    docs: {\n      description: {\n        story: 'Form with custom orange-themed styling applied through the className prop.'\n      }\n    }\n  }\n}",...null===(_CustomStyling_parameters1=CustomStyling.parameters)||void 0===_CustomStyling_parameters1||null===(_CustomStyling_parameters_docs=_CustomStyling_parameters1.docs)||void 0===_CustomStyling_parameters_docs?void 0:_CustomStyling_parameters_docs.source},description:{story:"Password update form with custom styling applied via className prop.\r\nDemonstrates visual customization capabilities.",...null===(_CustomStyling_parameters2=CustomStyling.parameters)||void 0===_CustomStyling_parameters2||null===(_CustomStyling_parameters_docs1=_CustomStyling_parameters2.docs)||void 0===_CustomStyling_parameters_docs1?void 0:_CustomStyling_parameters_docs1.description}}};const __namedExportsOrder=["Default","CustomStyling"]}}]);