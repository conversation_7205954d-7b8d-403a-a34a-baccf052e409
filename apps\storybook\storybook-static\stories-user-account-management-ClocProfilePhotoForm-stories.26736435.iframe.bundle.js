"use strict";(self.webpackChunk_cloc_storybook=self.webpackChunk_cloc_storybook||[]).push([[3173],{"./src/stories/user-account-management/ClocProfilePhotoForm.stories.tsx":(__unused_webpack_module,__webpack_exports__,__webpack_require__)=>{__webpack_require__.r(__webpack_exports__),__webpack_require__.d(__webpack_exports__,{Default:()=>Default,InProfileSettings:()=>InProfileSettings,InUserCard:()=>InUserCard,__namedExportsOrder:()=>__namedExportsOrder,default:()=>__WEBPACK_DEFAULT_EXPORT__});var _Default_parameters,_Default_parameters_docs,_Default_parameters1,_Default_parameters_docs1,_Default_parameters2,_InProfileSettings_parameters,_InProfileSettings_parameters_docs,_InProfileSettings_parameters1,_InProfileSettings_parameters_docs1,_InProfileSettings_parameters2,_InUserCard_parameters,_InUserCard_parameters_docs,_InUserCard_parameters1,_InUserCard_parameters_docs1,_InUserCard_parameters2,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__=__webpack_require__("../../node_modules/next/dist/compiled/react/jsx-runtime.js"),_cloc_atoms__WEBPACK_IMPORTED_MODULE_2__=(__webpack_require__("../../node_modules/next/dist/compiled/react/index.js"),__webpack_require__("../../packages/toolkit/atoms/dist/index.es.js"));const __WEBPACK_DEFAULT_EXPORT__={title:"User Account Management/Profile Photo Form",component:_cloc_atoms__WEBPACK_IMPORTED_MODULE_2__.N0j,parameters:{layout:"centered",docs:{description:{component:"A profile photo upload form component with avatar display, file upload functionality, and preview capabilities. Features a pencil edit icon overlay, loading overlay during upload, fallback initials display, and image file type validation. Requires authenticated user context to function properly."}}},argTypes:{className:{control:"text",description:"Additional CSS classes for styling the avatar container"}}},Default={args:{}},InProfileSettings={render:()=>(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("div",{className:"bg-white dark:bg-gray-900 rounded-lg shadow-lg p-6 max-w-md",children:(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div",{className:"text-center",children:[(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("h2",{className:"text-xl font-semibold text-gray-900 dark:text-white mb-4",children:"Profile Photo"}),(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("div",{className:"flex justify-center mb-4",children:(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(_cloc_atoms__WEBPACK_IMPORTED_MODULE_2__.N0j,{})}),(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("p",{className:"text-gray-600 dark:text-gray-400 text-sm",children:"Click the pencil icon to upload a new profile photo"})]})}),parameters:{docs:{description:{story:"Profile photo form within a dedicated profile settings card with instructions."}}}},InUserCard={render:()=>(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div",{className:"bg-white dark:bg-gray-900 rounded-lg shadow-lg p-6 max-w-sm",children:[(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(_cloc_atoms__WEBPACK_IMPORTED_MODULE_2__.N0j,{}),(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div",{className:"flex-1",children:[(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("h3",{className:"text-lg font-semibold text-gray-900 dark:text-white",children:"John Doe"}),(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("p",{className:"text-gray-600 dark:text-gray-400",children:"Software Developer"}),(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("p",{className:"text-gray-500 dark:text-gray-500 text-sm",children:"<EMAIL>"})]})]}),(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("div",{className:"mt-4 pt-4 border-t border-gray-200 dark:border-gray-700",children:(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("button",{className:"w-full bg-blue-600 hover:bg-blue-700 text-white font-medium py-2 px-4 rounded-lg transition-colors",children:"Edit Profile"})})]}),parameters:{docs:{description:{story:"Profile photo form integrated within a user profile card layout."}}}};Default.parameters={...Default.parameters,docs:{...null===(_Default_parameters=Default.parameters)||void 0===_Default_parameters?void 0:_Default_parameters.docs,source:{originalSource:"{\n  args: {}\n}",...null===(_Default_parameters1=Default.parameters)||void 0===_Default_parameters1||null===(_Default_parameters_docs=_Default_parameters1.docs)||void 0===_Default_parameters_docs?void 0:_Default_parameters_docs.source},description:{story:"Default profile photo form with standard avatar size and functionality.\r\nShows avatar with edit overlay and file upload capability.",...null===(_Default_parameters2=Default.parameters)||void 0===_Default_parameters2||null===(_Default_parameters_docs1=_Default_parameters2.docs)||void 0===_Default_parameters_docs1?void 0:_Default_parameters_docs1.description}}},InProfileSettings.parameters={...InProfileSettings.parameters,docs:{...null===(_InProfileSettings_parameters=InProfileSettings.parameters)||void 0===_InProfileSettings_parameters?void 0:_InProfileSettings_parameters.docs,source:{originalSource:'{\n  render: () => <div className="bg-white dark:bg-gray-900 rounded-lg shadow-lg p-6 max-w-md">\r\n            <div className="text-center">\r\n                <h2 className="text-xl font-semibold text-gray-900 dark:text-white mb-4">Profile Photo</h2>\r\n                <div className="flex justify-center mb-4">\r\n                    <ClocProfilePhotoForm />\r\n                </div>\r\n                <p className="text-gray-600 dark:text-gray-400 text-sm">\r\n                    Click the pencil icon to upload a new profile photo\r\n                </p>\r\n            </div>\r\n        </div>,\n  parameters: {\n    docs: {\n      description: {\n        story: \'Profile photo form within a dedicated profile settings card with instructions.\'\n      }\n    }\n  }\n}',...null===(_InProfileSettings_parameters1=InProfileSettings.parameters)||void 0===_InProfileSettings_parameters1||null===(_InProfileSettings_parameters_docs=_InProfileSettings_parameters1.docs)||void 0===_InProfileSettings_parameters_docs?void 0:_InProfileSettings_parameters_docs.source},description:{story:"Profile photo form in a profile settings context.\r\nShows how the component appears within a comprehensive profile interface.",...null===(_InProfileSettings_parameters2=InProfileSettings.parameters)||void 0===_InProfileSettings_parameters2||null===(_InProfileSettings_parameters_docs1=_InProfileSettings_parameters2.docs)||void 0===_InProfileSettings_parameters_docs1?void 0:_InProfileSettings_parameters_docs1.description}}},InUserCard.parameters={...InUserCard.parameters,docs:{...null===(_InUserCard_parameters=InUserCard.parameters)||void 0===_InUserCard_parameters?void 0:_InUserCard_parameters.docs,source:{originalSource:'{\n  render: () => <div className="bg-white dark:bg-gray-900 rounded-lg shadow-lg p-6 max-w-sm">\r\n            <div className="flex items-center space-x-4">\r\n                <ClocProfilePhotoForm />\r\n                <div className="flex-1">\r\n                    <h3 className="text-lg font-semibold text-gray-900 dark:text-white">John Doe</h3>\r\n                    <p className="text-gray-600 dark:text-gray-400">Software Developer</p>\r\n                    <p className="text-gray-500 dark:text-gray-500 text-sm"><EMAIL></p>\r\n                </div>\r\n            </div>\r\n            <div className="mt-4 pt-4 border-t border-gray-200 dark:border-gray-700">\r\n                <button className="w-full bg-blue-600 hover:bg-blue-700 text-white font-medium py-2 px-4 rounded-lg transition-colors">\r\n                    Edit Profile\r\n                </button>\r\n            </div>\r\n        </div>,\n  parameters: {\n    docs: {\n      description: {\n        story: \'Profile photo form integrated within a user profile card layout.\'\n      }\n    }\n  }\n}',...null===(_InUserCard_parameters1=InUserCard.parameters)||void 0===_InUserCard_parameters1||null===(_InUserCard_parameters_docs=_InUserCard_parameters1.docs)||void 0===_InUserCard_parameters_docs?void 0:_InUserCard_parameters_docs.source},description:{story:"Profile photo form in a user card layout.\r\nShows how the component appears in user profile cards or headers.",...null===(_InUserCard_parameters2=InUserCard.parameters)||void 0===_InUserCard_parameters2||null===(_InUserCard_parameters_docs1=_InUserCard_parameters2.docs)||void 0===_InUserCard_parameters_docs1?void 0:_InUserCard_parameters_docs1.description}}};const __namedExportsOrder=["Default","InProfileSettings","InUserCard"]}}]);