(self.webpackChunk_cloc_storybook=self.webpackChunk_cloc_storybook||[]).push([[2445],{"./node_modules/@storybook/test/dist sync recursive":module=>{function webpackEmptyContext(req){var e=new Error("Cannot find module '"+req+"'");throw e.code="MODULE_NOT_FOUND",e}webpackEmptyContext.keys=()=>[],webpackEmptyContext.resolve=webpackEmptyContext,webpackEmptyContext.id="./node_modules/@storybook/test/dist sync recursive",module.exports=webpackEmptyContext},"./src/stories/utilities/buttons/ClocButton.stories.tsx":(__unused_webpack_module,__webpack_exports__,__webpack_require__)=>{"use strict";__webpack_require__.r(__webpack_exports__),__webpack_require__.d(__webpack_exports__,{DefaultBorderedPauseButton:()=>DefaultBorderedPauseButton,DefaultClocButton:()=>DefaultClocButton,LargeBorderedPauseButton:()=>LargeBorderedPauseButton,LargeClocButton:()=>LargeClocButton,LargePauseButton:()=>LargePauseButton,LargeStopButton:()=>LargeStopButton,PauseButton:()=>PauseButton,SmallBorderedPauseButton:()=>SmallBorderedPauseButton,SmallClocButton:()=>SmallClocButton,SmallPauseButton:()=>SmallPauseButton,SmallStopButton:()=>SmallStopButton,StopButton:()=>StopButton,__namedExportsOrder:()=>__namedExportsOrder,default:()=>__WEBPACK_DEFAULT_EXPORT__});var _SmallClocButton_parameters,_SmallClocButton_parameters_docs,_SmallClocButton_parameters1,_SmallStopButton_parameters,_SmallStopButton_parameters_docs,_SmallStopButton_parameters1,_SmallPauseButton_parameters,_SmallPauseButton_parameters_docs,_SmallPauseButton_parameters1,_DefaultClocButton_parameters,_DefaultClocButton_parameters_docs,_DefaultClocButton_parameters1,_StopButton_parameters,_StopButton_parameters_docs,_StopButton_parameters1,_PauseButton_parameters,_PauseButton_parameters_docs,_PauseButton_parameters1,_LargeClocButton_parameters,_LargeClocButton_parameters_docs,_LargeClocButton_parameters1,_LargeStopButton_parameters,_LargeStopButton_parameters_docs,_LargeStopButton_parameters1,_LargePauseButton_parameters,_LargePauseButton_parameters_docs,_LargePauseButton_parameters1,_SmallBorderedPauseButton_parameters,_SmallBorderedPauseButton_parameters_docs,_SmallBorderedPauseButton_parameters1,_DefaultBorderedPauseButton_parameters,_DefaultBorderedPauseButton_parameters_docs,_DefaultBorderedPauseButton_parameters1,_LargeBorderedPauseButton_parameters,_LargeBorderedPauseButton_parameters_docs,_LargeBorderedPauseButton_parameters1,_storybook_test__WEBPACK_IMPORTED_MODULE_0__=__webpack_require__("./node_modules/@storybook/test/dist/index.mjs");const __WEBPACK_DEFAULT_EXPORT__={title:"Utilities/Buttons/Cloc Button",component:__webpack_require__("../../packages/toolkit/atoms/dist/index.es.js").iS7,parameters:{layout:"centered"},args:{onClick:(0,_storybook_test__WEBPACK_IMPORTED_MODULE_0__.fn)()}},SmallClocButton={args:{variant:"default",size:"sm"}},SmallStopButton={args:{variant:"default",size:"sm"}},SmallPauseButton={args:{variant:"default",size:"sm"}},DefaultClocButton={args:{variant:"default",size:"default"}},StopButton={args:{variant:"default",size:"default"}},PauseButton={args:{variant:"default",size:"default"}},LargeClocButton={args:{variant:"default",size:"lg"}},LargeStopButton={args:{variant:"default",size:"lg"}},LargePauseButton={args:{variant:"default",size:"lg"}},SmallBorderedPauseButton={args:{variant:"bordered",size:"sm"}},DefaultBorderedPauseButton={args:{variant:"bordered",size:"default"}},LargeBorderedPauseButton={args:{variant:"bordered",size:"lg"}};SmallClocButton.parameters={...SmallClocButton.parameters,docs:{...null===(_SmallClocButton_parameters=SmallClocButton.parameters)||void 0===_SmallClocButton_parameters?void 0:_SmallClocButton_parameters.docs,source:{originalSource:"{\n  args: {\n    variant: 'default',\n    size: 'sm'\n  }\n}",...null===(_SmallClocButton_parameters1=SmallClocButton.parameters)||void 0===_SmallClocButton_parameters1||null===(_SmallClocButton_parameters_docs=_SmallClocButton_parameters1.docs)||void 0===_SmallClocButton_parameters_docs?void 0:_SmallClocButton_parameters_docs.source}}},SmallStopButton.parameters={...SmallStopButton.parameters,docs:{...null===(_SmallStopButton_parameters=SmallStopButton.parameters)||void 0===_SmallStopButton_parameters?void 0:_SmallStopButton_parameters.docs,source:{originalSource:"{\n  args: {\n    variant: 'default',\n    size: 'sm'\n  }\n}",...null===(_SmallStopButton_parameters1=SmallStopButton.parameters)||void 0===_SmallStopButton_parameters1||null===(_SmallStopButton_parameters_docs=_SmallStopButton_parameters1.docs)||void 0===_SmallStopButton_parameters_docs?void 0:_SmallStopButton_parameters_docs.source}}},SmallPauseButton.parameters={...SmallPauseButton.parameters,docs:{...null===(_SmallPauseButton_parameters=SmallPauseButton.parameters)||void 0===_SmallPauseButton_parameters?void 0:_SmallPauseButton_parameters.docs,source:{originalSource:"{\n  args: {\n    variant: 'default',\n    size: 'sm'\n  }\n}",...null===(_SmallPauseButton_parameters1=SmallPauseButton.parameters)||void 0===_SmallPauseButton_parameters1||null===(_SmallPauseButton_parameters_docs=_SmallPauseButton_parameters1.docs)||void 0===_SmallPauseButton_parameters_docs?void 0:_SmallPauseButton_parameters_docs.source}}},DefaultClocButton.parameters={...DefaultClocButton.parameters,docs:{...null===(_DefaultClocButton_parameters=DefaultClocButton.parameters)||void 0===_DefaultClocButton_parameters?void 0:_DefaultClocButton_parameters.docs,source:{originalSource:"{\n  args: {\n    variant: 'default',\n    size: 'default'\n  }\n}",...null===(_DefaultClocButton_parameters1=DefaultClocButton.parameters)||void 0===_DefaultClocButton_parameters1||null===(_DefaultClocButton_parameters_docs=_DefaultClocButton_parameters1.docs)||void 0===_DefaultClocButton_parameters_docs?void 0:_DefaultClocButton_parameters_docs.source}}},StopButton.parameters={...StopButton.parameters,docs:{...null===(_StopButton_parameters=StopButton.parameters)||void 0===_StopButton_parameters?void 0:_StopButton_parameters.docs,source:{originalSource:"{\n  args: {\n    variant: 'default',\n    size: 'default'\n  }\n}",...null===(_StopButton_parameters1=StopButton.parameters)||void 0===_StopButton_parameters1||null===(_StopButton_parameters_docs=_StopButton_parameters1.docs)||void 0===_StopButton_parameters_docs?void 0:_StopButton_parameters_docs.source}}},PauseButton.parameters={...PauseButton.parameters,docs:{...null===(_PauseButton_parameters=PauseButton.parameters)||void 0===_PauseButton_parameters?void 0:_PauseButton_parameters.docs,source:{originalSource:"{\n  args: {\n    variant: 'default',\n    size: 'default'\n  }\n}",...null===(_PauseButton_parameters1=PauseButton.parameters)||void 0===_PauseButton_parameters1||null===(_PauseButton_parameters_docs=_PauseButton_parameters1.docs)||void 0===_PauseButton_parameters_docs?void 0:_PauseButton_parameters_docs.source}}},LargeClocButton.parameters={...LargeClocButton.parameters,docs:{...null===(_LargeClocButton_parameters=LargeClocButton.parameters)||void 0===_LargeClocButton_parameters?void 0:_LargeClocButton_parameters.docs,source:{originalSource:"{\n  args: {\n    variant: 'default',\n    size: 'lg'\n  }\n}",...null===(_LargeClocButton_parameters1=LargeClocButton.parameters)||void 0===_LargeClocButton_parameters1||null===(_LargeClocButton_parameters_docs=_LargeClocButton_parameters1.docs)||void 0===_LargeClocButton_parameters_docs?void 0:_LargeClocButton_parameters_docs.source}}},LargeStopButton.parameters={...LargeStopButton.parameters,docs:{...null===(_LargeStopButton_parameters=LargeStopButton.parameters)||void 0===_LargeStopButton_parameters?void 0:_LargeStopButton_parameters.docs,source:{originalSource:"{\n  args: {\n    variant: 'default',\n    size: 'lg'\n  }\n}",...null===(_LargeStopButton_parameters1=LargeStopButton.parameters)||void 0===_LargeStopButton_parameters1||null===(_LargeStopButton_parameters_docs=_LargeStopButton_parameters1.docs)||void 0===_LargeStopButton_parameters_docs?void 0:_LargeStopButton_parameters_docs.source}}},LargePauseButton.parameters={...LargePauseButton.parameters,docs:{...null===(_LargePauseButton_parameters=LargePauseButton.parameters)||void 0===_LargePauseButton_parameters?void 0:_LargePauseButton_parameters.docs,source:{originalSource:"{\n  args: {\n    variant: 'default',\n    size: 'lg'\n  }\n}",...null===(_LargePauseButton_parameters1=LargePauseButton.parameters)||void 0===_LargePauseButton_parameters1||null===(_LargePauseButton_parameters_docs=_LargePauseButton_parameters1.docs)||void 0===_LargePauseButton_parameters_docs?void 0:_LargePauseButton_parameters_docs.source}}},SmallBorderedPauseButton.parameters={...SmallBorderedPauseButton.parameters,docs:{...null===(_SmallBorderedPauseButton_parameters=SmallBorderedPauseButton.parameters)||void 0===_SmallBorderedPauseButton_parameters?void 0:_SmallBorderedPauseButton_parameters.docs,source:{originalSource:"{\n  args: {\n    variant: 'bordered',\n    size: 'sm'\n  }\n}",...null===(_SmallBorderedPauseButton_parameters1=SmallBorderedPauseButton.parameters)||void 0===_SmallBorderedPauseButton_parameters1||null===(_SmallBorderedPauseButton_parameters_docs=_SmallBorderedPauseButton_parameters1.docs)||void 0===_SmallBorderedPauseButton_parameters_docs?void 0:_SmallBorderedPauseButton_parameters_docs.source}}},DefaultBorderedPauseButton.parameters={...DefaultBorderedPauseButton.parameters,docs:{...null===(_DefaultBorderedPauseButton_parameters=DefaultBorderedPauseButton.parameters)||void 0===_DefaultBorderedPauseButton_parameters?void 0:_DefaultBorderedPauseButton_parameters.docs,source:{originalSource:"{\n  args: {\n    variant: 'bordered',\n    size: 'default'\n  }\n}",...null===(_DefaultBorderedPauseButton_parameters1=DefaultBorderedPauseButton.parameters)||void 0===_DefaultBorderedPauseButton_parameters1||null===(_DefaultBorderedPauseButton_parameters_docs=_DefaultBorderedPauseButton_parameters1.docs)||void 0===_DefaultBorderedPauseButton_parameters_docs?void 0:_DefaultBorderedPauseButton_parameters_docs.source}}},LargeBorderedPauseButton.parameters={...LargeBorderedPauseButton.parameters,docs:{...null===(_LargeBorderedPauseButton_parameters=LargeBorderedPauseButton.parameters)||void 0===_LargeBorderedPauseButton_parameters?void 0:_LargeBorderedPauseButton_parameters.docs,source:{originalSource:"{\n  args: {\n    variant: 'bordered',\n    size: 'lg'\n  }\n}",...null===(_LargeBorderedPauseButton_parameters1=LargeBorderedPauseButton.parameters)||void 0===_LargeBorderedPauseButton_parameters1||null===(_LargeBorderedPauseButton_parameters_docs=_LargeBorderedPauseButton_parameters1.docs)||void 0===_LargeBorderedPauseButton_parameters_docs?void 0:_LargeBorderedPauseButton_parameters_docs.source}}};const __namedExportsOrder=["SmallClocButton","SmallStopButton","SmallPauseButton","DefaultClocButton","StopButton","PauseButton","LargeClocButton","LargeStopButton","LargePauseButton","SmallBorderedPauseButton","DefaultBorderedPauseButton","LargeBorderedPauseButton"]}}]);