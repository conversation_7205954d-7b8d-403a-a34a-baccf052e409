"use strict";(self.webpackChunk_cloc_storybook=self.webpackChunk_cloc_storybook||[]).push([[448],{"./src/stories/utilities/date-time/ClocDatePicker.stories.tsx":(__unused_webpack_module,__webpack_exports__,__webpack_require__)=>{__webpack_require__.r(__webpack_exports__),__webpack_require__.d(__webpack_exports__,{CustomPlaceholder:()=>CustomPlaceholder,CustomStyling:()=>CustomStyling,DeadlineDate:()=>DeadlineDate,Default:()=>Default,DifferentContexts:()=>DifferentContexts,EndDate:()=>EndDate,FormExample:()=>FormExample,Interactive:()=>Interactive,StartDate:()=>StartDate,WithoutIcon:()=>WithoutIcon,__namedExportsOrder:()=>__namedExportsOrder,default:()=>__WEBPACK_DEFAULT_EXPORT__});var _Default_parameters,_Default_parameters_docs,_Default_parameters1,_WithoutIcon_parameters,_WithoutIcon_parameters_docs,_WithoutIcon_parameters1,_CustomPlaceholder_parameters,_CustomPlaceholder_parameters_docs,_CustomPlaceholder_parameters1,_StartDate_parameters,_StartDate_parameters_docs,_StartDate_parameters1,_EndDate_parameters,_EndDate_parameters_docs,_EndDate_parameters1,_DeadlineDate_parameters,_DeadlineDate_parameters_docs,_DeadlineDate_parameters1,_CustomStyling_parameters,_CustomStyling_parameters_docs,_CustomStyling_parameters1,_Interactive_parameters,_Interactive_parameters_docs,_Interactive_parameters1,_FormExample_parameters,_FormExample_parameters_docs,_FormExample_parameters1,_DifferentContexts_parameters,_DifferentContexts_parameters_docs,_DifferentContexts_parameters1,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__=__webpack_require__("../../node_modules/next/dist/compiled/react/jsx-runtime.js"),react__WEBPACK_IMPORTED_MODULE_1__=__webpack_require__("../../node_modules/next/dist/compiled/react/index.js"),_cloc_atoms__WEBPACK_IMPORTED_MODULE_2__=__webpack_require__("../../packages/toolkit/atoms/dist/index.es.js");const __WEBPACK_DEFAULT_EXPORT__={title:"Utilities/Date & Time/Date Picker",component:_cloc_atoms__WEBPACK_IMPORTED_MODULE_2__.uR2,parameters:{layout:"centered",docs:{description:{component:"A date picker component with calendar popup, integrated with Cloc theming system. Supports custom placeholder text and optional calendar icon."}}},argTypes:{placeholder:{control:"text",description:"Placeholder text when no date is selected"},icon:{control:"boolean",description:"Whether to show the calendar icon"},date:{control:"date",description:"Currently selected date"},className:{control:"text",description:"Additional CSS classes"}}},Default={args:{placeholder:"Pick a date",icon:!0}},WithoutIcon={args:{placeholder:"Select date",icon:!1}},CustomPlaceholder={args:{placeholder:"Choose your birthday",icon:!0}},StartDate={args:{placeholder:"Start date",icon:!0}},EndDate={args:{placeholder:"End date",icon:!0}},DeadlineDate={args:{placeholder:"Project deadline",icon:!0}},CustomStyling={args:{placeholder:"Custom styled picker",icon:!0,className:"w-[320px] border-blue-300"}},Interactive={render:()=>{const[selectedDate,setSelectedDate]=(0,react__WEBPACK_IMPORTED_MODULE_1__.useState)();return(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div",{className:"space-y-4",children:[(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(_cloc_atoms__WEBPACK_IMPORTED_MODULE_2__.uR2,{placeholder:"Select a date",icon:!0,date:selectedDate,setDate:setSelectedDate}),selectedDate&&(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("p",{className:"text-sm text-gray-600",children:["Selected: ",selectedDate.toLocaleDateString()]})]})}},FormExample={render:()=>(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div",{className:"space-y-4 p-4 border rounded-lg bg-white dark:bg-gray-800",children:[(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("h3",{className:"font-semibold text-gray-900 dark:text-white",children:"Event Details"}),(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div",{className:"space-y-2",children:[(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("label",{className:"text-sm font-medium text-gray-700 dark:text-gray-300",children:"Start Date"}),(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(_cloc_atoms__WEBPACK_IMPORTED_MODULE_2__.uR2,{placeholder:"Event start date",icon:!0})]}),(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div",{className:"space-y-2",children:[(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("label",{className:"text-sm font-medium text-gray-700 dark:text-gray-300",children:"End Date"}),(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(_cloc_atoms__WEBPACK_IMPORTED_MODULE_2__.uR2,{placeholder:"Event end date",icon:!0})]})]})]})},DifferentContexts={render:()=>(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div",{className:"space-y-6",children:[(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div",{className:"space-y-2",children:[(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("h4",{className:"font-medium",children:"Project Management"}),(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div",{className:"flex gap-2",children:[(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(_cloc_atoms__WEBPACK_IMPORTED_MODULE_2__.uR2,{placeholder:"Project start",icon:!0}),(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(_cloc_atoms__WEBPACK_IMPORTED_MODULE_2__.uR2,{placeholder:"Deadline",icon:!0})]})]}),(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div",{className:"space-y-2",children:[(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("h4",{className:"font-medium",children:"Time Tracking"}),(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div",{className:"flex gap-2",children:[(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(_cloc_atoms__WEBPACK_IMPORTED_MODULE_2__.uR2,{placeholder:"Report from",icon:!0}),(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(_cloc_atoms__WEBPACK_IMPORTED_MODULE_2__.uR2,{placeholder:"Report to",icon:!0})]})]}),(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div",{className:"space-y-2",children:[(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("h4",{className:"font-medium",children:"Personal"}),(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(_cloc_atoms__WEBPACK_IMPORTED_MODULE_2__.uR2,{placeholder:"Birthday",icon:!0})]})]})};Default.parameters={...Default.parameters,docs:{...null===(_Default_parameters=Default.parameters)||void 0===_Default_parameters?void 0:_Default_parameters.docs,source:{originalSource:"{\n  args: {\n    placeholder: 'Pick a date',\n    icon: true\n  }\n}",...null===(_Default_parameters1=Default.parameters)||void 0===_Default_parameters1||null===(_Default_parameters_docs=_Default_parameters1.docs)||void 0===_Default_parameters_docs?void 0:_Default_parameters_docs.source}}},WithoutIcon.parameters={...WithoutIcon.parameters,docs:{...null===(_WithoutIcon_parameters=WithoutIcon.parameters)||void 0===_WithoutIcon_parameters?void 0:_WithoutIcon_parameters.docs,source:{originalSource:"{\n  args: {\n    placeholder: 'Select date',\n    icon: false\n  }\n}",...null===(_WithoutIcon_parameters1=WithoutIcon.parameters)||void 0===_WithoutIcon_parameters1||null===(_WithoutIcon_parameters_docs=_WithoutIcon_parameters1.docs)||void 0===_WithoutIcon_parameters_docs?void 0:_WithoutIcon_parameters_docs.source}}},CustomPlaceholder.parameters={...CustomPlaceholder.parameters,docs:{...null===(_CustomPlaceholder_parameters=CustomPlaceholder.parameters)||void 0===_CustomPlaceholder_parameters?void 0:_CustomPlaceholder_parameters.docs,source:{originalSource:"{\n  args: {\n    placeholder: 'Choose your birthday',\n    icon: true\n  }\n}",...null===(_CustomPlaceholder_parameters1=CustomPlaceholder.parameters)||void 0===_CustomPlaceholder_parameters1||null===(_CustomPlaceholder_parameters_docs=_CustomPlaceholder_parameters1.docs)||void 0===_CustomPlaceholder_parameters_docs?void 0:_CustomPlaceholder_parameters_docs.source}}},StartDate.parameters={...StartDate.parameters,docs:{...null===(_StartDate_parameters=StartDate.parameters)||void 0===_StartDate_parameters?void 0:_StartDate_parameters.docs,source:{originalSource:"{\n  args: {\n    placeholder: 'Start date',\n    icon: true\n  }\n}",...null===(_StartDate_parameters1=StartDate.parameters)||void 0===_StartDate_parameters1||null===(_StartDate_parameters_docs=_StartDate_parameters1.docs)||void 0===_StartDate_parameters_docs?void 0:_StartDate_parameters_docs.source}}},EndDate.parameters={...EndDate.parameters,docs:{...null===(_EndDate_parameters=EndDate.parameters)||void 0===_EndDate_parameters?void 0:_EndDate_parameters.docs,source:{originalSource:"{\n  args: {\n    placeholder: 'End date',\n    icon: true\n  }\n}",...null===(_EndDate_parameters1=EndDate.parameters)||void 0===_EndDate_parameters1||null===(_EndDate_parameters_docs=_EndDate_parameters1.docs)||void 0===_EndDate_parameters_docs?void 0:_EndDate_parameters_docs.source}}},DeadlineDate.parameters={...DeadlineDate.parameters,docs:{...null===(_DeadlineDate_parameters=DeadlineDate.parameters)||void 0===_DeadlineDate_parameters?void 0:_DeadlineDate_parameters.docs,source:{originalSource:"{\n  args: {\n    placeholder: 'Project deadline',\n    icon: true\n  }\n}",...null===(_DeadlineDate_parameters1=DeadlineDate.parameters)||void 0===_DeadlineDate_parameters1||null===(_DeadlineDate_parameters_docs=_DeadlineDate_parameters1.docs)||void 0===_DeadlineDate_parameters_docs?void 0:_DeadlineDate_parameters_docs.source}}},CustomStyling.parameters={...CustomStyling.parameters,docs:{...null===(_CustomStyling_parameters=CustomStyling.parameters)||void 0===_CustomStyling_parameters?void 0:_CustomStyling_parameters.docs,source:{originalSource:"{\n  args: {\n    placeholder: 'Custom styled picker',\n    icon: true,\n    className: 'w-[320px] border-blue-300'\n  }\n}",...null===(_CustomStyling_parameters1=CustomStyling.parameters)||void 0===_CustomStyling_parameters1||null===(_CustomStyling_parameters_docs=_CustomStyling_parameters1.docs)||void 0===_CustomStyling_parameters_docs?void 0:_CustomStyling_parameters_docs.source}}},Interactive.parameters={...Interactive.parameters,docs:{...null===(_Interactive_parameters=Interactive.parameters)||void 0===_Interactive_parameters?void 0:_Interactive_parameters.docs,source:{originalSource:'{\n  render: () => {\n    const [selectedDate, setSelectedDate] = useState<Date>();\n    return <div className="space-y-4">\r\n                <ClocDatePicker placeholder="Select a date" icon={true} date={selectedDate} setDate={setSelectedDate} />\r\n                {selectedDate && <p className="text-sm text-gray-600">Selected: {selectedDate.toLocaleDateString()}</p>}\r\n            </div>;\n  }\n}',...null===(_Interactive_parameters1=Interactive.parameters)||void 0===_Interactive_parameters1||null===(_Interactive_parameters_docs=_Interactive_parameters1.docs)||void 0===_Interactive_parameters_docs?void 0:_Interactive_parameters_docs.source}}},FormExample.parameters={...FormExample.parameters,docs:{...null===(_FormExample_parameters=FormExample.parameters)||void 0===_FormExample_parameters?void 0:_FormExample_parameters.docs,source:{originalSource:'{\n  render: () => <div className="space-y-4 p-4 border rounded-lg bg-white dark:bg-gray-800">\r\n            <h3 className="font-semibold text-gray-900 dark:text-white">Event Details</h3>\r\n            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">\r\n                <div className="space-y-2">\r\n                    <label className="text-sm font-medium text-gray-700 dark:text-gray-300">Start Date</label>\r\n                    <ClocDatePicker placeholder="Event start date" icon={true} />\r\n                </div>\r\n                <div className="space-y-2">\r\n                    <label className="text-sm font-medium text-gray-700 dark:text-gray-300">End Date</label>\r\n                    <ClocDatePicker placeholder="Event end date" icon={true} />\r\n                </div>\r\n            </div>\r\n        </div>\n}',...null===(_FormExample_parameters1=FormExample.parameters)||void 0===_FormExample_parameters1||null===(_FormExample_parameters_docs=_FormExample_parameters1.docs)||void 0===_FormExample_parameters_docs?void 0:_FormExample_parameters_docs.source}}},DifferentContexts.parameters={...DifferentContexts.parameters,docs:{...null===(_DifferentContexts_parameters=DifferentContexts.parameters)||void 0===_DifferentContexts_parameters?void 0:_DifferentContexts_parameters.docs,source:{originalSource:'{\n  render: () => <div className="space-y-6">\r\n            <div className="space-y-2">\r\n                <h4 className="font-medium">Project Management</h4>\r\n                <div className="flex gap-2">\r\n                    <ClocDatePicker placeholder="Project start" icon={true} />\r\n                    <ClocDatePicker placeholder="Deadline" icon={true} />\r\n                </div>\r\n            </div>\r\n            <div className="space-y-2">\r\n                <h4 className="font-medium">Time Tracking</h4>\r\n                <div className="flex gap-2">\r\n                    <ClocDatePicker placeholder="Report from" icon={true} />\r\n                    <ClocDatePicker placeholder="Report to" icon={true} />\r\n                </div>\r\n            </div>\r\n            <div className="space-y-2">\r\n                <h4 className="font-medium">Personal</h4>\r\n                <ClocDatePicker placeholder="Birthday" icon={true} />\r\n            </div>\r\n        </div>\n}',...null===(_DifferentContexts_parameters1=DifferentContexts.parameters)||void 0===_DifferentContexts_parameters1||null===(_DifferentContexts_parameters_docs=_DifferentContexts_parameters1.docs)||void 0===_DifferentContexts_parameters_docs?void 0:_DifferentContexts_parameters_docs.source}}};const __namedExportsOrder=["Default","WithoutIcon","CustomPlaceholder","StartDate","EndDate","DeadlineDate","CustomStyling","Interactive","FormExample","DifferentContexts"]}}]);