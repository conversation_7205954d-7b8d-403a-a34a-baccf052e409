"use strict";(self.webpackChunk_cloc_storybook=self.webpackChunk_cloc_storybook||[]).push([[3827],{"./src/stories/utilities/date-time/ClocDateRangePicker.stories.tsx":(__unused_webpack_module,__webpack_exports__,__webpack_require__)=>{__webpack_require__.r(__webpack_exports__),__webpack_require__.d(__webpack_exports__,{CustomStyling:()=>CustomStyling,Default:()=>Default,IconSize:()=>IconSize,Interactive:()=>Interactive,Large:()=>Large,ProjectTimeframe:()=>ProjectTimeframe,ReportingForm:()=>ReportingForm,SizeComparison:()=>SizeComparison,Small:()=>Small,WithPreselectedRange:()=>WithPreselectedRange,__namedExportsOrder:()=>__namedExportsOrder,default:()=>__WEBPACK_DEFAULT_EXPORT__});var _Default_parameters,_Default_parameters_docs,_Default_parameters1,_Small_parameters,_Small_parameters_docs,_Small_parameters1,_Large_parameters,_Large_parameters_docs,_Large_parameters1,_IconSize_parameters,_IconSize_parameters_docs,_IconSize_parameters1,_WithPreselectedRange_parameters,_WithPreselectedRange_parameters_docs,_WithPreselectedRange_parameters1,_CustomStyling_parameters,_CustomStyling_parameters_docs,_CustomStyling_parameters1,_Interactive_parameters,_Interactive_parameters_docs,_Interactive_parameters1,_ReportingForm_parameters,_ReportingForm_parameters_docs,_ReportingForm_parameters1,_SizeComparison_parameters,_SizeComparison_parameters_docs,_SizeComparison_parameters1,_ProjectTimeframe_parameters,_ProjectTimeframe_parameters_docs,_ProjectTimeframe_parameters1,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__=__webpack_require__("../../node_modules/next/dist/compiled/react/jsx-runtime.js"),react__WEBPACK_IMPORTED_MODULE_1__=__webpack_require__("../../node_modules/next/dist/compiled/react/index.js"),_cloc_atoms__WEBPACK_IMPORTED_MODULE_2__=__webpack_require__("../../packages/toolkit/atoms/dist/index.es.js");const __WEBPACK_DEFAULT_EXPORT__={title:"Utilities/Date & Time/Date Range Picker",component:_cloc_atoms__WEBPACK_IMPORTED_MODULE_2__.JNB,parameters:{layout:"centered",docs:{description:{component:"A date range picker component with dual calendar popup for selecting start and end dates. Integrated with Cloc theming system and supports different sizes."}}},argTypes:{size:{control:"select",options:["default","sm","lg","icon"],description:"Size variant of the date range picker"},className:{control:"text",description:"Additional CSS classes"},date:{control:"object",description:"Selected date range with from and to dates"}}},Default={args:{size:"default"}},Small={args:{size:"sm"}},Large={args:{size:"lg"}},IconSize={args:{size:"icon"}},WithPreselectedRange={args:{size:"default",date:{from:new Date(2024,0,1),to:new Date(2024,0,7)}}},CustomStyling={args:{size:"default",className:"border-blue-300 bg-blue-50"}},Interactive={render:()=>{const[dateRange,setDateRange]=(0,react__WEBPACK_IMPORTED_MODULE_1__.useState)();return(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div",{className:"space-y-4",children:[(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(_cloc_atoms__WEBPACK_IMPORTED_MODULE_2__.JNB,{size:"default",date:dateRange,setDate:setDateRange}),(null==dateRange?void 0:dateRange.from)&&(null==dateRange?void 0:dateRange.to)&&(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div",{className:"text-sm text-gray-600 space-y-1",children:[(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("p",{children:["From: ",dateRange.from.toLocaleDateString()]}),(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("p",{children:["To: ",dateRange.to.toLocaleDateString()]}),(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("p",{children:["Duration:"," ",Math.ceil((dateRange.to.getTime()-dateRange.from.getTime())/864e5)," ","days"]})]})]})}},ReportingForm={render:()=>(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div",{className:"space-y-4 p-4 border rounded-lg bg-white dark:bg-gray-800",children:[(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("h3",{className:"font-semibold text-gray-900 dark:text-white",children:"Time Tracking Report"}),(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div",{className:"space-y-4",children:[(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div",{className:"space-y-2",children:[(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("label",{className:"text-sm font-medium text-gray-700 dark:text-gray-300",children:"Report Period"}),(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(_cloc_atoms__WEBPACK_IMPORTED_MODULE_2__.JNB,{size:"default"})]}),(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div",{className:"flex gap-2",children:[(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("button",{className:"px-4 py-2 bg-blue-600 text-white rounded-md text-sm hover:bg-blue-700",children:"Generate Report"}),(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("button",{className:"px-4 py-2 border border-gray-300 text-gray-700 rounded-md text-sm hover:bg-gray-50",children:"Clear Dates"})]})]})]})},SizeComparison={render:()=>(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div",{className:"space-y-6",children:[(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div",{className:"space-y-2",children:[(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("label",{className:"text-sm font-medium",children:"Small Size"}),(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(_cloc_atoms__WEBPACK_IMPORTED_MODULE_2__.JNB,{size:"sm"})]}),(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div",{className:"space-y-2",children:[(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("label",{className:"text-sm font-medium",children:"Default Size"}),(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(_cloc_atoms__WEBPACK_IMPORTED_MODULE_2__.JNB,{size:"default"})]}),(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div",{className:"space-y-2",children:[(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("label",{className:"text-sm font-medium",children:"Large Size"}),(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(_cloc_atoms__WEBPACK_IMPORTED_MODULE_2__.JNB,{size:"lg"})]}),(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div",{className:"space-y-2",children:[(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("label",{className:"text-sm font-medium",children:"Icon Size"}),(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(_cloc_atoms__WEBPACK_IMPORTED_MODULE_2__.JNB,{size:"icon"})]})]})},ProjectTimeframe={render:()=>(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div",{className:"space-y-4 p-4 border rounded-lg bg-white dark:bg-gray-800",children:[(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("h3",{className:"font-semibold text-gray-900 dark:text-white",children:"Project Timeframe"}),(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div",{className:"space-y-2",children:[(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("label",{className:"text-sm font-medium text-gray-700 dark:text-gray-300",children:"Development Phase"}),(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(_cloc_atoms__WEBPACK_IMPORTED_MODULE_2__.JNB,{size:"default"})]}),(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div",{className:"space-y-2",children:[(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("label",{className:"text-sm font-medium text-gray-700 dark:text-gray-300",children:"Testing Phase"}),(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(_cloc_atoms__WEBPACK_IMPORTED_MODULE_2__.JNB,{size:"default"})]})]})]})};Default.parameters={...Default.parameters,docs:{...null===(_Default_parameters=Default.parameters)||void 0===_Default_parameters?void 0:_Default_parameters.docs,source:{originalSource:"{\n  args: {\n    size: 'default'\n  }\n}",...null===(_Default_parameters1=Default.parameters)||void 0===_Default_parameters1||null===(_Default_parameters_docs=_Default_parameters1.docs)||void 0===_Default_parameters_docs?void 0:_Default_parameters_docs.source}}},Small.parameters={...Small.parameters,docs:{...null===(_Small_parameters=Small.parameters)||void 0===_Small_parameters?void 0:_Small_parameters.docs,source:{originalSource:"{\n  args: {\n    size: 'sm'\n  }\n}",...null===(_Small_parameters1=Small.parameters)||void 0===_Small_parameters1||null===(_Small_parameters_docs=_Small_parameters1.docs)||void 0===_Small_parameters_docs?void 0:_Small_parameters_docs.source}}},Large.parameters={...Large.parameters,docs:{...null===(_Large_parameters=Large.parameters)||void 0===_Large_parameters?void 0:_Large_parameters.docs,source:{originalSource:"{\n  args: {\n    size: 'lg'\n  }\n}",...null===(_Large_parameters1=Large.parameters)||void 0===_Large_parameters1||null===(_Large_parameters_docs=_Large_parameters1.docs)||void 0===_Large_parameters_docs?void 0:_Large_parameters_docs.source}}},IconSize.parameters={...IconSize.parameters,docs:{...null===(_IconSize_parameters=IconSize.parameters)||void 0===_IconSize_parameters?void 0:_IconSize_parameters.docs,source:{originalSource:"{\n  args: {\n    size: 'icon'\n  }\n}",...null===(_IconSize_parameters1=IconSize.parameters)||void 0===_IconSize_parameters1||null===(_IconSize_parameters_docs=_IconSize_parameters1.docs)||void 0===_IconSize_parameters_docs?void 0:_IconSize_parameters_docs.source}}},WithPreselectedRange.parameters={...WithPreselectedRange.parameters,docs:{...null===(_WithPreselectedRange_parameters=WithPreselectedRange.parameters)||void 0===_WithPreselectedRange_parameters?void 0:_WithPreselectedRange_parameters.docs,source:{originalSource:"{\n  args: {\n    size: 'default',\n    date: {\n      from: new Date(2024, 0, 1),\n      to: new Date(2024, 0, 7)\n    }\n  }\n}",...null===(_WithPreselectedRange_parameters1=WithPreselectedRange.parameters)||void 0===_WithPreselectedRange_parameters1||null===(_WithPreselectedRange_parameters_docs=_WithPreselectedRange_parameters1.docs)||void 0===_WithPreselectedRange_parameters_docs?void 0:_WithPreselectedRange_parameters_docs.source}}},CustomStyling.parameters={...CustomStyling.parameters,docs:{...null===(_CustomStyling_parameters=CustomStyling.parameters)||void 0===_CustomStyling_parameters?void 0:_CustomStyling_parameters.docs,source:{originalSource:"{\n  args: {\n    size: 'default',\n    className: 'border-blue-300 bg-blue-50'\n  }\n}",...null===(_CustomStyling_parameters1=CustomStyling.parameters)||void 0===_CustomStyling_parameters1||null===(_CustomStyling_parameters_docs=_CustomStyling_parameters1.docs)||void 0===_CustomStyling_parameters_docs?void 0:_CustomStyling_parameters_docs.source}}},Interactive.parameters={...Interactive.parameters,docs:{...null===(_Interactive_parameters=Interactive.parameters)||void 0===_Interactive_parameters?void 0:_Interactive_parameters.docs,source:{originalSource:'{\n  render: () => {\n    const [dateRange, setDateRange] = useState<DateRange | undefined>();\n    return <div className="space-y-4">\r\n                <ClocDateRangePicker size="default" date={dateRange} setDate={setDateRange} />\r\n                {dateRange?.from && dateRange?.to && <div className="text-sm text-gray-600 space-y-1">\r\n                        <p>From: {dateRange.from.toLocaleDateString()}</p>\r\n                        <p>To: {dateRange.to.toLocaleDateString()}</p>\r\n                        <p>\r\n                            Duration:{\' \'}\r\n                            {Math.ceil((dateRange.to.getTime() - dateRange.from.getTime()) / (1000 * 60 * 60 * 24))}{\' \'}\r\n                            days\r\n                        </p>\r\n                    </div>}\r\n            </div>;\n  }\n}',...null===(_Interactive_parameters1=Interactive.parameters)||void 0===_Interactive_parameters1||null===(_Interactive_parameters_docs=_Interactive_parameters1.docs)||void 0===_Interactive_parameters_docs?void 0:_Interactive_parameters_docs.source}}},ReportingForm.parameters={...ReportingForm.parameters,docs:{...null===(_ReportingForm_parameters=ReportingForm.parameters)||void 0===_ReportingForm_parameters?void 0:_ReportingForm_parameters.docs,source:{originalSource:'{\n  render: () => <div className="space-y-4 p-4 border rounded-lg bg-white dark:bg-gray-800">\r\n            <h3 className="font-semibold text-gray-900 dark:text-white">Time Tracking Report</h3>\r\n            <div className="space-y-4">\r\n                <div className="space-y-2">\r\n                    <label className="text-sm font-medium text-gray-700 dark:text-gray-300">Report Period</label>\r\n                    <ClocDateRangePicker size="default" />\r\n                </div>\r\n                <div className="flex gap-2">\r\n                    <button className="px-4 py-2 bg-blue-600 text-white rounded-md text-sm hover:bg-blue-700">\r\n                        Generate Report\r\n                    </button>\r\n                    <button className="px-4 py-2 border border-gray-300 text-gray-700 rounded-md text-sm hover:bg-gray-50">\r\n                        Clear Dates\r\n                    </button>\r\n                </div>\r\n            </div>\r\n        </div>\n}',...null===(_ReportingForm_parameters1=ReportingForm.parameters)||void 0===_ReportingForm_parameters1||null===(_ReportingForm_parameters_docs=_ReportingForm_parameters1.docs)||void 0===_ReportingForm_parameters_docs?void 0:_ReportingForm_parameters_docs.source}}},SizeComparison.parameters={...SizeComparison.parameters,docs:{...null===(_SizeComparison_parameters=SizeComparison.parameters)||void 0===_SizeComparison_parameters?void 0:_SizeComparison_parameters.docs,source:{originalSource:'{\n  render: () => <div className="space-y-6">\r\n            <div className="space-y-2">\r\n                <label className="text-sm font-medium">Small Size</label>\r\n                <ClocDateRangePicker size="sm" />\r\n            </div>\r\n            <div className="space-y-2">\r\n                <label className="text-sm font-medium">Default Size</label>\r\n                <ClocDateRangePicker size="default" />\r\n            </div>\r\n            <div className="space-y-2">\r\n                <label className="text-sm font-medium">Large Size</label>\r\n                <ClocDateRangePicker size="lg" />\r\n            </div>\r\n            <div className="space-y-2">\r\n                <label className="text-sm font-medium">Icon Size</label>\r\n                <ClocDateRangePicker size="icon" />\r\n            </div>\r\n        </div>\n}',...null===(_SizeComparison_parameters1=SizeComparison.parameters)||void 0===_SizeComparison_parameters1||null===(_SizeComparison_parameters_docs=_SizeComparison_parameters1.docs)||void 0===_SizeComparison_parameters_docs?void 0:_SizeComparison_parameters_docs.source}}},ProjectTimeframe.parameters={...ProjectTimeframe.parameters,docs:{...null===(_ProjectTimeframe_parameters=ProjectTimeframe.parameters)||void 0===_ProjectTimeframe_parameters?void 0:_ProjectTimeframe_parameters.docs,source:{originalSource:'{\n  render: () => <div className="space-y-4 p-4 border rounded-lg bg-white dark:bg-gray-800">\r\n            <h3 className="font-semibold text-gray-900 dark:text-white">Project Timeframe</h3>\r\n            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">\r\n                <div className="space-y-2">\r\n                    <label className="text-sm font-medium text-gray-700 dark:text-gray-300">Development Phase</label>\r\n                    <ClocDateRangePicker size="default" />\r\n                </div>\r\n                <div className="space-y-2">\r\n                    <label className="text-sm font-medium text-gray-700 dark:text-gray-300">Testing Phase</label>\r\n                    <ClocDateRangePicker size="default" />\r\n                </div>\r\n            </div>\r\n        </div>\n}',...null===(_ProjectTimeframe_parameters1=ProjectTimeframe.parameters)||void 0===_ProjectTimeframe_parameters1||null===(_ProjectTimeframe_parameters_docs=_ProjectTimeframe_parameters1.docs)||void 0===_ProjectTimeframe_parameters_docs?void 0:_ProjectTimeframe_parameters_docs.source}}};const __namedExportsOrder=["Default","Small","Large","IconSize","WithPreselectedRange","CustomStyling","Interactive","ReportingForm","SizeComparison","ProjectTimeframe"]}}]);