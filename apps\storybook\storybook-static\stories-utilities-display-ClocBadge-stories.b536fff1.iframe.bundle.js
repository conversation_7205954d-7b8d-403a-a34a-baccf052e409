"use strict";(self.webpackChunk_cloc_storybook=self.webpackChunk_cloc_storybook||[]).push([[8291],{"./src/stories/utilities/display/ClocBadge.stories.tsx":(__unused_webpack_module,__webpack_exports__,__webpack_require__)=>{__webpack_require__.r(__webpack_exports__),__webpack_require__.d(__webpack_exports__,{AllVariants:()=>AllVariants,CounterBadges:()=>CounterBadges,CustomStyling:()=>CustomStyling,Default:()=>Default,Destructive:()=>Destructive,LongText:()=>LongText,Outline:()=>Outline,Secondary:()=>Secondary,StatusBadges:()=>StatusBadges,WithNumbers:()=>WithNumbers,__namedExportsOrder:()=>__namedExportsOrder,default:()=>__WEBPACK_DEFAULT_EXPORT__});var _Default_parameters,_Default_parameters_docs,_Default_parameters1,_Secondary_parameters,_Secondary_parameters_docs,_Secondary_parameters1,_Destructive_parameters,_Destructive_parameters_docs,_Destructive_parameters1,_Outline_parameters,_Outline_parameters_docs,_Outline_parameters1,_WithNumbers_parameters,_WithNumbers_parameters_docs,_WithNumbers_parameters1,_LongText_parameters,_LongText_parameters_docs,_LongText_parameters1,_StatusBadges_parameters,_StatusBadges_parameters_docs,_StatusBadges_parameters1,_CounterBadges_parameters,_CounterBadges_parameters_docs,_CounterBadges_parameters1,_CustomStyling_parameters,_CustomStyling_parameters_docs,_CustomStyling_parameters1,_AllVariants_parameters,_AllVariants_parameters_docs,_AllVariants_parameters1,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__=__webpack_require__("../../node_modules/next/dist/compiled/react/jsx-runtime.js"),_cloc_atoms__WEBPACK_IMPORTED_MODULE_2__=(__webpack_require__("../../node_modules/next/dist/compiled/react/index.js"),__webpack_require__("../../packages/toolkit/atoms/dist/index.es.js"));const __WEBPACK_DEFAULT_EXPORT__={title:"Utilities/Display/Badge",component:_cloc_atoms__WEBPACK_IMPORTED_MODULE_2__.HsA,parameters:{layout:"centered"},argTypes:{variant:{control:"select",options:["default","secondary","destructive","outline"],description:"The visual style variant of the badge"},children:{control:"text",description:"The content to display inside the badge"},className:{control:"text",description:"Additional CSS classes to apply"}}},Default={args:{children:"Badge",variant:"default"}},Secondary={args:{children:"Secondary",variant:"secondary"}},Destructive={args:{children:"Destructive",variant:"destructive"}},Outline={args:{children:"Outline",variant:"outline"}},WithNumbers={args:{children:"42",variant:"default"}},LongText={args:{children:"Very Long Badge Text",variant:"secondary"}},StatusBadges={render:()=>(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div",{className:"flex gap-2 flex-wrap",children:[(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(_cloc_atoms__WEBPACK_IMPORTED_MODULE_2__.HsA,{variant:"default",children:"Active"}),(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(_cloc_atoms__WEBPACK_IMPORTED_MODULE_2__.HsA,{variant:"secondary",children:"Pending"}),(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(_cloc_atoms__WEBPACK_IMPORTED_MODULE_2__.HsA,{variant:"destructive",children:"Error"}),(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(_cloc_atoms__WEBPACK_IMPORTED_MODULE_2__.HsA,{variant:"outline",children:"Draft"})]})},CounterBadges={render:()=>(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div",{className:"flex gap-2 flex-wrap",children:[(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(_cloc_atoms__WEBPACK_IMPORTED_MODULE_2__.HsA,{variant:"default",children:"1"}),(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(_cloc_atoms__WEBPACK_IMPORTED_MODULE_2__.HsA,{variant:"secondary",children:"99+"}),(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(_cloc_atoms__WEBPACK_IMPORTED_MODULE_2__.HsA,{variant:"destructive",children:"!"}),(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(_cloc_atoms__WEBPACK_IMPORTED_MODULE_2__.HsA,{variant:"outline",children:"NEW"})]})},CustomStyling={args:{children:"Custom",variant:"default",className:"bg-blue-500 text-white border-blue-600"}},AllVariants={render:()=>(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("div",{className:"grid grid-cols-2 gap-4",children:(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div",{className:"space-y-2",children:[(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("h3",{className:"text-sm font-medium",children:"Light Theme"}),(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div",{className:"flex gap-2 flex-wrap",children:[(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(_cloc_atoms__WEBPACK_IMPORTED_MODULE_2__.HsA,{variant:"default",children:"Default"}),(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(_cloc_atoms__WEBPACK_IMPORTED_MODULE_2__.HsA,{variant:"secondary",children:"Secondary"}),(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(_cloc_atoms__WEBPACK_IMPORTED_MODULE_2__.HsA,{variant:"destructive",children:"Destructive"}),(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(_cloc_atoms__WEBPACK_IMPORTED_MODULE_2__.HsA,{variant:"outline",children:"Outline"})]})]})})};Default.parameters={...Default.parameters,docs:{...null===(_Default_parameters=Default.parameters)||void 0===_Default_parameters?void 0:_Default_parameters.docs,source:{originalSource:"{\n  args: {\n    children: 'Badge',\n    variant: 'default'\n  }\n}",...null===(_Default_parameters1=Default.parameters)||void 0===_Default_parameters1||null===(_Default_parameters_docs=_Default_parameters1.docs)||void 0===_Default_parameters_docs?void 0:_Default_parameters_docs.source}}},Secondary.parameters={...Secondary.parameters,docs:{...null===(_Secondary_parameters=Secondary.parameters)||void 0===_Secondary_parameters?void 0:_Secondary_parameters.docs,source:{originalSource:"{\n  args: {\n    children: 'Secondary',\n    variant: 'secondary'\n  }\n}",...null===(_Secondary_parameters1=Secondary.parameters)||void 0===_Secondary_parameters1||null===(_Secondary_parameters_docs=_Secondary_parameters1.docs)||void 0===_Secondary_parameters_docs?void 0:_Secondary_parameters_docs.source}}},Destructive.parameters={...Destructive.parameters,docs:{...null===(_Destructive_parameters=Destructive.parameters)||void 0===_Destructive_parameters?void 0:_Destructive_parameters.docs,source:{originalSource:"{\n  args: {\n    children: 'Destructive',\n    variant: 'destructive'\n  }\n}",...null===(_Destructive_parameters1=Destructive.parameters)||void 0===_Destructive_parameters1||null===(_Destructive_parameters_docs=_Destructive_parameters1.docs)||void 0===_Destructive_parameters_docs?void 0:_Destructive_parameters_docs.source}}},Outline.parameters={...Outline.parameters,docs:{...null===(_Outline_parameters=Outline.parameters)||void 0===_Outline_parameters?void 0:_Outline_parameters.docs,source:{originalSource:"{\n  args: {\n    children: 'Outline',\n    variant: 'outline'\n  }\n}",...null===(_Outline_parameters1=Outline.parameters)||void 0===_Outline_parameters1||null===(_Outline_parameters_docs=_Outline_parameters1.docs)||void 0===_Outline_parameters_docs?void 0:_Outline_parameters_docs.source}}},WithNumbers.parameters={...WithNumbers.parameters,docs:{...null===(_WithNumbers_parameters=WithNumbers.parameters)||void 0===_WithNumbers_parameters?void 0:_WithNumbers_parameters.docs,source:{originalSource:"{\n  args: {\n    children: '42',\n    variant: 'default'\n  }\n}",...null===(_WithNumbers_parameters1=WithNumbers.parameters)||void 0===_WithNumbers_parameters1||null===(_WithNumbers_parameters_docs=_WithNumbers_parameters1.docs)||void 0===_WithNumbers_parameters_docs?void 0:_WithNumbers_parameters_docs.source}}},LongText.parameters={...LongText.parameters,docs:{...null===(_LongText_parameters=LongText.parameters)||void 0===_LongText_parameters?void 0:_LongText_parameters.docs,source:{originalSource:"{\n  args: {\n    children: 'Very Long Badge Text',\n    variant: 'secondary'\n  }\n}",...null===(_LongText_parameters1=LongText.parameters)||void 0===_LongText_parameters1||null===(_LongText_parameters_docs=_LongText_parameters1.docs)||void 0===_LongText_parameters_docs?void 0:_LongText_parameters_docs.source}}},StatusBadges.parameters={...StatusBadges.parameters,docs:{...null===(_StatusBadges_parameters=StatusBadges.parameters)||void 0===_StatusBadges_parameters?void 0:_StatusBadges_parameters.docs,source:{originalSource:'{\n  render: () => <div className="flex gap-2 flex-wrap">\r\n            <ClocBadge variant="default">Active</ClocBadge>\r\n            <ClocBadge variant="secondary">Pending</ClocBadge>\r\n            <ClocBadge variant="destructive">Error</ClocBadge>\r\n            <ClocBadge variant="outline">Draft</ClocBadge>\r\n        </div>\n}',...null===(_StatusBadges_parameters1=StatusBadges.parameters)||void 0===_StatusBadges_parameters1||null===(_StatusBadges_parameters_docs=_StatusBadges_parameters1.docs)||void 0===_StatusBadges_parameters_docs?void 0:_StatusBadges_parameters_docs.source}}},CounterBadges.parameters={...CounterBadges.parameters,docs:{...null===(_CounterBadges_parameters=CounterBadges.parameters)||void 0===_CounterBadges_parameters?void 0:_CounterBadges_parameters.docs,source:{originalSource:'{\n  render: () => <div className="flex gap-2 flex-wrap">\r\n            <ClocBadge variant="default">1</ClocBadge>\r\n            <ClocBadge variant="secondary">99+</ClocBadge>\r\n            <ClocBadge variant="destructive">!</ClocBadge>\r\n            <ClocBadge variant="outline">NEW</ClocBadge>\r\n        </div>\n}',...null===(_CounterBadges_parameters1=CounterBadges.parameters)||void 0===_CounterBadges_parameters1||null===(_CounterBadges_parameters_docs=_CounterBadges_parameters1.docs)||void 0===_CounterBadges_parameters_docs?void 0:_CounterBadges_parameters_docs.source}}},CustomStyling.parameters={...CustomStyling.parameters,docs:{...null===(_CustomStyling_parameters=CustomStyling.parameters)||void 0===_CustomStyling_parameters?void 0:_CustomStyling_parameters.docs,source:{originalSource:"{\n  args: {\n    children: 'Custom',\n    variant: 'default',\n    className: 'bg-blue-500 text-white border-blue-600'\n  }\n}",...null===(_CustomStyling_parameters1=CustomStyling.parameters)||void 0===_CustomStyling_parameters1||null===(_CustomStyling_parameters_docs=_CustomStyling_parameters1.docs)||void 0===_CustomStyling_parameters_docs?void 0:_CustomStyling_parameters_docs.source}}},AllVariants.parameters={...AllVariants.parameters,docs:{...null===(_AllVariants_parameters=AllVariants.parameters)||void 0===_AllVariants_parameters?void 0:_AllVariants_parameters.docs,source:{originalSource:'{\n  render: () => <div className="grid grid-cols-2 gap-4">\r\n            <div className="space-y-2">\r\n                <h3 className="text-sm font-medium">Light Theme</h3>\r\n                <div className="flex gap-2 flex-wrap">\r\n                    <ClocBadge variant="default">Default</ClocBadge>\r\n                    <ClocBadge variant="secondary">Secondary</ClocBadge>\r\n                    <ClocBadge variant="destructive">Destructive</ClocBadge>\r\n                    <ClocBadge variant="outline">Outline</ClocBadge>\r\n                </div>\r\n            </div>\r\n        </div>\n}',...null===(_AllVariants_parameters1=AllVariants.parameters)||void 0===_AllVariants_parameters1||null===(_AllVariants_parameters_docs=_AllVariants_parameters1.docs)||void 0===_AllVariants_parameters_docs?void 0:_AllVariants_parameters_docs.source}}};const __namedExportsOrder=["Default","Secondary","Destructive","Outline","WithNumbers","LongText","StatusBadges","CounterBadges","CustomStyling","AllVariants"]}}]);