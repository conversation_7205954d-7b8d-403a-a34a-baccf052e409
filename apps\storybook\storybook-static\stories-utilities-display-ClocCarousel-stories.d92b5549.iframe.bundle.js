"use strict";(self.webpackChunk_cloc_storybook=self.webpackChunk_cloc_storybook||[]).push([[896],{"./src/stories/utilities/display/ClocCarousel.stories.tsx":(__unused_webpack_module,__webpack_exports__,__webpack_require__)=>{__webpack_require__.r(__webpack_exports__),__webpack_require__.d(__webpack_exports__,{CustomItemWidth:()=>CustomItemWidth,Default:()=>Default,ManyItems:()=>ManyItems,SmallItems:()=>SmallItems,TimerCards:()=>TimerCards,Vertical:()=>Vertical,WithCustomRender:()=>WithCustomRender,WithNumbers:()=>WithNumbers,__namedExportsOrder:()=>__namedExportsOrder,default:()=>__WEBPACK_DEFAULT_EXPORT__});var _Default_parameters,_Default_parameters_docs,_Default_parameters1,_WithNumbers_parameters,_WithNumbers_parameters_docs,_WithNumbers_parameters1,_Vertical_parameters,_Vertical_parameters_docs,_Vertical_parameters1,_CustomItemWidth_parameters,_CustomItemWidth_parameters_docs,_CustomItemWidth_parameters1,_ManyItems_parameters,_ManyItems_parameters_docs,_ManyItems_parameters1,_WithCustomRender_parameters,_WithCustomRender_parameters_docs,_WithCustomRender_parameters1,_TimerCards_parameters,_TimerCards_parameters_docs,_TimerCards_parameters1,_SmallItems_parameters,_SmallItems_parameters_docs,_SmallItems_parameters1,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__=__webpack_require__("../../node_modules/next/dist/compiled/react/jsx-runtime.js");__webpack_require__("../../node_modules/next/dist/compiled/react/index.js");const __WEBPACK_DEFAULT_EXPORT__={title:"Utilities/Display/Carousel",component:__webpack_require__("../../packages/toolkit/atoms/dist/index.es.js").Qn0,parameters:{layout:"centered"},argTypes:{orientation:{control:"select",options:["horizontal","vertical"],description:"The orientation of the carousel"},itemWidth:{control:"text",description:'CSS class for item width (e.g., "basis-1/2", "basis-1/3")'},items:{control:"object",description:"Array of items to display in the carousel"}}},Default={args:{items:["Item 1","Item 2","Item 3","Item 4","Item 5"],orientation:"horizontal",itemWidth:"basis-1/2"}},WithNumbers={args:{items:[1,2,3,4,5,6,7,8,9,10],orientation:"horizontal",itemWidth:"basis-1/3"}},Vertical={args:{items:["Vertical 1","Vertical 2","Vertical 3","Vertical 4"],orientation:"vertical",itemWidth:"basis-1/2"}},CustomItemWidth={args:{items:["Wide 1","Wide 2","Wide 3"],orientation:"horizontal",itemWidth:"basis-2/3"}},ManyItems={args:{items:Array.from({length:20},(_,i)=>"Item ".concat(i+1)),orientation:"horizontal",itemWidth:"basis-1/4"}},WithCustomRender={args:{items:["Task 1: completed","Task 2: pending","Task 3: in-progress","Task 4: completed"],orientation:"horizontal",itemWidth:"basis-1/2",renderItem:item=>{const[title,status]=item.split(": ");return(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div",{className:"text-center",children:[(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("div",{className:"font-semibold",children:title}),(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("div",{className:"text-sm ".concat("completed"===status?"text-green-600":"pending"===status?"text-yellow-600":"text-blue-600"),children:status})]})}}},TimerCards={args:{items:["02:30:45|Project A","01:15:20|Project B","03:45:10|Project C","00:45:30|Project D"],orientation:"horizontal",itemWidth:"basis-1/2",renderItem:item=>{const[time,project]=item.split("|");return(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div",{className:"text-center space-y-2",children:[(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("div",{className:"text-2xl font-mono font-bold text-blue-600",children:time}),(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("div",{className:"text-sm text-gray-600",children:project})]})}}},SmallItems={args:{items:["A","B","C","D","E","F","G","H"],orientation:"horizontal",itemWidth:"basis-1/6"}};Default.parameters={...Default.parameters,docs:{...null===(_Default_parameters=Default.parameters)||void 0===_Default_parameters?void 0:_Default_parameters.docs,source:{originalSource:"{\n  args: {\n    items: ['Item 1', 'Item 2', 'Item 3', 'Item 4', 'Item 5'],\n    orientation: 'horizontal',\n    itemWidth: 'basis-1/2'\n  }\n}",...null===(_Default_parameters1=Default.parameters)||void 0===_Default_parameters1||null===(_Default_parameters_docs=_Default_parameters1.docs)||void 0===_Default_parameters_docs?void 0:_Default_parameters_docs.source}}},WithNumbers.parameters={...WithNumbers.parameters,docs:{...null===(_WithNumbers_parameters=WithNumbers.parameters)||void 0===_WithNumbers_parameters?void 0:_WithNumbers_parameters.docs,source:{originalSource:"{\n  args: {\n    items: [1, 2, 3, 4, 5, 6, 7, 8, 9, 10],\n    orientation: 'horizontal',\n    itemWidth: 'basis-1/3'\n  }\n}",...null===(_WithNumbers_parameters1=WithNumbers.parameters)||void 0===_WithNumbers_parameters1||null===(_WithNumbers_parameters_docs=_WithNumbers_parameters1.docs)||void 0===_WithNumbers_parameters_docs?void 0:_WithNumbers_parameters_docs.source}}},Vertical.parameters={...Vertical.parameters,docs:{...null===(_Vertical_parameters=Vertical.parameters)||void 0===_Vertical_parameters?void 0:_Vertical_parameters.docs,source:{originalSource:"{\n  args: {\n    items: ['Vertical 1', 'Vertical 2', 'Vertical 3', 'Vertical 4'],\n    orientation: 'vertical',\n    itemWidth: 'basis-1/2'\n  }\n}",...null===(_Vertical_parameters1=Vertical.parameters)||void 0===_Vertical_parameters1||null===(_Vertical_parameters_docs=_Vertical_parameters1.docs)||void 0===_Vertical_parameters_docs?void 0:_Vertical_parameters_docs.source}}},CustomItemWidth.parameters={...CustomItemWidth.parameters,docs:{...null===(_CustomItemWidth_parameters=CustomItemWidth.parameters)||void 0===_CustomItemWidth_parameters?void 0:_CustomItemWidth_parameters.docs,source:{originalSource:"{\n  args: {\n    items: ['Wide 1', 'Wide 2', 'Wide 3'],\n    orientation: 'horizontal',\n    itemWidth: 'basis-2/3'\n  }\n}",...null===(_CustomItemWidth_parameters1=CustomItemWidth.parameters)||void 0===_CustomItemWidth_parameters1||null===(_CustomItemWidth_parameters_docs=_CustomItemWidth_parameters1.docs)||void 0===_CustomItemWidth_parameters_docs?void 0:_CustomItemWidth_parameters_docs.source}}},ManyItems.parameters={...ManyItems.parameters,docs:{...null===(_ManyItems_parameters=ManyItems.parameters)||void 0===_ManyItems_parameters?void 0:_ManyItems_parameters.docs,source:{originalSource:"{\n  args: {\n    items: Array.from({\n      length: 20\n    }, (_, i) => `Item ${i + 1}`),\n    orientation: 'horizontal',\n    itemWidth: 'basis-1/4'\n  }\n}",...null===(_ManyItems_parameters1=ManyItems.parameters)||void 0===_ManyItems_parameters1||null===(_ManyItems_parameters_docs=_ManyItems_parameters1.docs)||void 0===_ManyItems_parameters_docs?void 0:_ManyItems_parameters_docs.source}}},WithCustomRender.parameters={...WithCustomRender.parameters,docs:{...null===(_WithCustomRender_parameters=WithCustomRender.parameters)||void 0===_WithCustomRender_parameters?void 0:_WithCustomRender_parameters.docs,source:{originalSource:"{\n  args: {\n    items: ['Task 1: completed', 'Task 2: pending', 'Task 3: in-progress', 'Task 4: completed'],\n    orientation: 'horizontal',\n    itemWidth: 'basis-1/2',\n    renderItem: (item: any) => {\n      const [title, status] = item.split(': ');\n      return <div className=\"text-center\">\r\n                    <div className=\"font-semibold\">{title}</div>\r\n                    <div className={`text-sm ${status === 'completed' ? 'text-green-600' : status === 'pending' ? 'text-yellow-600' : 'text-blue-600'}`}>\r\n                        {status}\r\n                    </div>\r\n                </div>;\n    }\n  }\n}",...null===(_WithCustomRender_parameters1=WithCustomRender.parameters)||void 0===_WithCustomRender_parameters1||null===(_WithCustomRender_parameters_docs=_WithCustomRender_parameters1.docs)||void 0===_WithCustomRender_parameters_docs?void 0:_WithCustomRender_parameters_docs.source}}},TimerCards.parameters={...TimerCards.parameters,docs:{...null===(_TimerCards_parameters=TimerCards.parameters)||void 0===_TimerCards_parameters?void 0:_TimerCards_parameters.docs,source:{originalSource:"{\n  args: {\n    items: ['02:30:45|Project A', '01:15:20|Project B', '03:45:10|Project C', '00:45:30|Project D'],\n    orientation: 'horizontal',\n    itemWidth: 'basis-1/2',\n    renderItem: (item: any) => {\n      const [time, project] = item.split('|');\n      return <div className=\"text-center space-y-2\">\r\n                    <div className=\"text-2xl font-mono font-bold text-blue-600\">{time}</div>\r\n                    <div className=\"text-sm text-gray-600\">{project}</div>\r\n                </div>;\n    }\n  }\n}",...null===(_TimerCards_parameters1=TimerCards.parameters)||void 0===_TimerCards_parameters1||null===(_TimerCards_parameters_docs=_TimerCards_parameters1.docs)||void 0===_TimerCards_parameters_docs?void 0:_TimerCards_parameters_docs.source}}},SmallItems.parameters={...SmallItems.parameters,docs:{...null===(_SmallItems_parameters=SmallItems.parameters)||void 0===_SmallItems_parameters?void 0:_SmallItems_parameters.docs,source:{originalSource:"{\n  args: {\n    items: ['A', 'B', 'C', 'D', 'E', 'F', 'G', 'H'],\n    orientation: 'horizontal',\n    itemWidth: 'basis-1/6'\n  }\n}",...null===(_SmallItems_parameters1=SmallItems.parameters)||void 0===_SmallItems_parameters1||null===(_SmallItems_parameters_docs=_SmallItems_parameters1.docs)||void 0===_SmallItems_parameters_docs?void 0:_SmallItems_parameters_docs.source}}};const __namedExportsOrder=["Default","WithNumbers","Vertical","CustomItemWidth","ManyItems","WithCustomRender","TimerCards","SmallItems"]}}]);