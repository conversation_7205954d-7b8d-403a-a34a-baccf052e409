"use strict";(self.webpackChunk_cloc_storybook=self.webpackChunk_cloc_storybook||[]).push([[4823],{"./src/stories/utilities/display/ClocProgress.stories.tsx":(__unused_webpack_module,__webpack_exports__,__webpack_require__)=>{__webpack_require__.r(__webpack_exports__),__webpack_require__.d(__webpack_exports__,{CustomStyling:()=>CustomStyling,Default:()=>Default,DifferentSizes:()=>DifferentSizes,FullWidth:()=>FullWidth,Large:()=>Large,MultipleProgress:()=>MultipleProgress,ProgressCard:()=>ProgressCard,Small:()=>Small,WithLabel:()=>WithLabel,__namedExportsOrder:()=>__namedExportsOrder,default:()=>__WEBPACK_DEFAULT_EXPORT__});var _Default_parameters,_Default_parameters_docs,_Default_parameters1,_FullWidth_parameters,_FullWidth_parameters_docs,_FullWidth_parameters1,_Small_parameters,_Small_parameters_docs,_Small_parameters1,_Large_parameters,_Large_parameters_docs,_Large_parameters1,_CustomStyling_parameters,_CustomStyling_parameters_docs,_CustomStyling_parameters1,_WithLabel_parameters,_WithLabel_parameters_docs,_WithLabel_parameters1,_ProgressCard_parameters,_ProgressCard_parameters_docs,_ProgressCard_parameters1,_MultipleProgress_parameters,_MultipleProgress_parameters_docs,_MultipleProgress_parameters1,_DifferentSizes_parameters,_DifferentSizes_parameters_docs,_DifferentSizes_parameters1,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__=__webpack_require__("../../node_modules/next/dist/compiled/react/jsx-runtime.js"),_cloc_atoms__WEBPACK_IMPORTED_MODULE_2__=(__webpack_require__("../../node_modules/next/dist/compiled/react/index.js"),__webpack_require__("../../packages/toolkit/atoms/dist/index.es.js"));const __WEBPACK_DEFAULT_EXPORT__={title:"Utilities/Display/Progress",component:_cloc_atoms__WEBPACK_IMPORTED_MODULE_2__.bIQ,parameters:{layout:"centered",docs:{description:{component:"A progress bar component that automatically calculates progress based on today's tracked time from the Cloc context. Shows progress towards an 8-hour daily goal."}}},argTypes:{className:{control:"text",description:"Additional CSS classes to apply to the progress bar"}}},Default={args:{className:"w-64"}},FullWidth={args:{className:"w-full"}},Small={args:{className:"w-32 h-2"}},Large={args:{className:"w-96 h-4"}},CustomStyling={args:{className:"w-64 h-3 rounded-full"}},WithLabel={render:args=>(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div",{className:"space-y-2",children:[(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div",{className:"flex justify-between text-sm text-gray-600",children:[(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("span",{children:"Daily Progress"}),(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("span",{children:"Today's tracked time"})]}),(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(_cloc_atoms__WEBPACK_IMPORTED_MODULE_2__.bIQ,{...args})]}),args:{className:"w-80"}},ProgressCard={render:args=>(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div",{className:"p-4 border rounded-lg bg-white dark:bg-gray-800 shadow-sm space-y-3",children:[(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("h3",{className:"font-semibold text-gray-900 dark:text-white",children:"Today's Work Progress"}),(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div",{className:"space-y-2",children:[(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div",{className:"flex justify-between text-sm text-gray-600 dark:text-gray-400",children:[(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("span",{children:"Progress towards 8-hour goal"}),(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("span",{children:"Auto-calculated"})]}),(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(_cloc_atoms__WEBPACK_IMPORTED_MODULE_2__.bIQ,{...args}),(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("p",{className:"text-xs text-gray-500 dark:text-gray-400",children:"Progress is automatically calculated based on your tracked time today"})]})]}),args:{className:"w-full"}},MultipleProgress={render:()=>(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div",{className:"space-y-4 w-80",children:[(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div",{className:"space-y-2",children:[(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("label",{className:"text-sm font-medium",children:"Daily Goal (8 hours)"}),(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(_cloc_atoms__WEBPACK_IMPORTED_MODULE_2__.bIQ,{className:"w-full"})]}),(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div",{className:"space-y-2",children:[(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("label",{className:"text-sm font-medium",children:"Weekly Goal (40 hours)"}),(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(_cloc_atoms__WEBPACK_IMPORTED_MODULE_2__.bIQ,{className:"w-full h-2"})]}),(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div",{className:"space-y-2",children:[(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("label",{className:"text-sm font-medium",children:"Monthly Goal (160 hours)"}),(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(_cloc_atoms__WEBPACK_IMPORTED_MODULE_2__.bIQ,{className:"w-full h-1"})]})]})},DifferentSizes={render:()=>(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div",{className:"space-y-4",children:[(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div",{className:"space-y-2",children:[(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("label",{className:"text-sm",children:"Extra Small (h-1)"}),(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(_cloc_atoms__WEBPACK_IMPORTED_MODULE_2__.bIQ,{className:"w-64 h-1"})]}),(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div",{className:"space-y-2",children:[(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("label",{className:"text-sm",children:"Small (h-2)"}),(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(_cloc_atoms__WEBPACK_IMPORTED_MODULE_2__.bIQ,{className:"w-64 h-2"})]}),(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div",{className:"space-y-2",children:[(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("label",{className:"text-sm",children:"Default (h-3)"}),(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(_cloc_atoms__WEBPACK_IMPORTED_MODULE_2__.bIQ,{className:"w-64"})]}),(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div",{className:"space-y-2",children:[(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("label",{className:"text-sm",children:"Large (h-4)"}),(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(_cloc_atoms__WEBPACK_IMPORTED_MODULE_2__.bIQ,{className:"w-64 h-4"})]}),(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div",{className:"space-y-2",children:[(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("label",{className:"text-sm",children:"Extra Large (h-6)"}),(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(_cloc_atoms__WEBPACK_IMPORTED_MODULE_2__.bIQ,{className:"w-64 h-6"})]})]})};Default.parameters={...Default.parameters,docs:{...null===(_Default_parameters=Default.parameters)||void 0===_Default_parameters?void 0:_Default_parameters.docs,source:{originalSource:"{\n  args: {\n    className: 'w-64'\n  }\n}",...null===(_Default_parameters1=Default.parameters)||void 0===_Default_parameters1||null===(_Default_parameters_docs=_Default_parameters1.docs)||void 0===_Default_parameters_docs?void 0:_Default_parameters_docs.source}}},FullWidth.parameters={...FullWidth.parameters,docs:{...null===(_FullWidth_parameters=FullWidth.parameters)||void 0===_FullWidth_parameters?void 0:_FullWidth_parameters.docs,source:{originalSource:"{\n  args: {\n    className: 'w-full'\n  }\n}",...null===(_FullWidth_parameters1=FullWidth.parameters)||void 0===_FullWidth_parameters1||null===(_FullWidth_parameters_docs=_FullWidth_parameters1.docs)||void 0===_FullWidth_parameters_docs?void 0:_FullWidth_parameters_docs.source}}},Small.parameters={...Small.parameters,docs:{...null===(_Small_parameters=Small.parameters)||void 0===_Small_parameters?void 0:_Small_parameters.docs,source:{originalSource:"{\n  args: {\n    className: 'w-32 h-2'\n  }\n}",...null===(_Small_parameters1=Small.parameters)||void 0===_Small_parameters1||null===(_Small_parameters_docs=_Small_parameters1.docs)||void 0===_Small_parameters_docs?void 0:_Small_parameters_docs.source}}},Large.parameters={...Large.parameters,docs:{...null===(_Large_parameters=Large.parameters)||void 0===_Large_parameters?void 0:_Large_parameters.docs,source:{originalSource:"{\n  args: {\n    className: 'w-96 h-4'\n  }\n}",...null===(_Large_parameters1=Large.parameters)||void 0===_Large_parameters1||null===(_Large_parameters_docs=_Large_parameters1.docs)||void 0===_Large_parameters_docs?void 0:_Large_parameters_docs.source}}},CustomStyling.parameters={...CustomStyling.parameters,docs:{...null===(_CustomStyling_parameters=CustomStyling.parameters)||void 0===_CustomStyling_parameters?void 0:_CustomStyling_parameters.docs,source:{originalSource:"{\n  args: {\n    className: 'w-64 h-3 rounded-full'\n  }\n}",...null===(_CustomStyling_parameters1=CustomStyling.parameters)||void 0===_CustomStyling_parameters1||null===(_CustomStyling_parameters_docs=_CustomStyling_parameters1.docs)||void 0===_CustomStyling_parameters_docs?void 0:_CustomStyling_parameters_docs.source}}},WithLabel.parameters={...WithLabel.parameters,docs:{...null===(_WithLabel_parameters=WithLabel.parameters)||void 0===_WithLabel_parameters?void 0:_WithLabel_parameters.docs,source:{originalSource:'{\n  render: args => <div className="space-y-2">\r\n            <div className="flex justify-between text-sm text-gray-600">\r\n                <span>Daily Progress</span>\r\n                <span>Today\'s tracked time</span>\r\n            </div>\r\n            <ClocProgress {...args} />\r\n        </div>,\n  args: {\n    className: \'w-80\'\n  }\n}',...null===(_WithLabel_parameters1=WithLabel.parameters)||void 0===_WithLabel_parameters1||null===(_WithLabel_parameters_docs=_WithLabel_parameters1.docs)||void 0===_WithLabel_parameters_docs?void 0:_WithLabel_parameters_docs.source}}},ProgressCard.parameters={...ProgressCard.parameters,docs:{...null===(_ProgressCard_parameters=ProgressCard.parameters)||void 0===_ProgressCard_parameters?void 0:_ProgressCard_parameters.docs,source:{originalSource:'{\n  render: args => <div className="p-4 border rounded-lg bg-white dark:bg-gray-800 shadow-sm space-y-3">\r\n            <h3 className="font-semibold text-gray-900 dark:text-white">Today\'s Work Progress</h3>\r\n            <div className="space-y-2">\r\n                <div className="flex justify-between text-sm text-gray-600 dark:text-gray-400">\r\n                    <span>Progress towards 8-hour goal</span>\r\n                    <span>Auto-calculated</span>\r\n                </div>\r\n                <ClocProgress {...args} />\r\n                <p className="text-xs text-gray-500 dark:text-gray-400">\r\n                    Progress is automatically calculated based on your tracked time today\r\n                </p>\r\n            </div>\r\n        </div>,\n  args: {\n    className: \'w-full\'\n  }\n}',...null===(_ProgressCard_parameters1=ProgressCard.parameters)||void 0===_ProgressCard_parameters1||null===(_ProgressCard_parameters_docs=_ProgressCard_parameters1.docs)||void 0===_ProgressCard_parameters_docs?void 0:_ProgressCard_parameters_docs.source}}},MultipleProgress.parameters={...MultipleProgress.parameters,docs:{...null===(_MultipleProgress_parameters=MultipleProgress.parameters)||void 0===_MultipleProgress_parameters?void 0:_MultipleProgress_parameters.docs,source:{originalSource:'{\n  render: () => <div className="space-y-4 w-80">\r\n            <div className="space-y-2">\r\n                <label className="text-sm font-medium">Daily Goal (8 hours)</label>\r\n                <ClocProgress className="w-full" />\r\n            </div>\r\n            <div className="space-y-2">\r\n                <label className="text-sm font-medium">Weekly Goal (40 hours)</label>\r\n                <ClocProgress className="w-full h-2" />\r\n            </div>\r\n            <div className="space-y-2">\r\n                <label className="text-sm font-medium">Monthly Goal (160 hours)</label>\r\n                <ClocProgress className="w-full h-1" />\r\n            </div>\r\n        </div>\n}',...null===(_MultipleProgress_parameters1=MultipleProgress.parameters)||void 0===_MultipleProgress_parameters1||null===(_MultipleProgress_parameters_docs=_MultipleProgress_parameters1.docs)||void 0===_MultipleProgress_parameters_docs?void 0:_MultipleProgress_parameters_docs.source}}},DifferentSizes.parameters={...DifferentSizes.parameters,docs:{...null===(_DifferentSizes_parameters=DifferentSizes.parameters)||void 0===_DifferentSizes_parameters?void 0:_DifferentSizes_parameters.docs,source:{originalSource:'{\n  render: () => <div className="space-y-4">\r\n            <div className="space-y-2">\r\n                <label className="text-sm">Extra Small (h-1)</label>\r\n                <ClocProgress className="w-64 h-1" />\r\n            </div>\r\n            <div className="space-y-2">\r\n                <label className="text-sm">Small (h-2)</label>\r\n                <ClocProgress className="w-64 h-2" />\r\n            </div>\r\n            <div className="space-y-2">\r\n                <label className="text-sm">Default (h-3)</label>\r\n                <ClocProgress className="w-64" />\r\n            </div>\r\n            <div className="space-y-2">\r\n                <label className="text-sm">Large (h-4)</label>\r\n                <ClocProgress className="w-64 h-4" />\r\n            </div>\r\n            <div className="space-y-2">\r\n                <label className="text-sm">Extra Large (h-6)</label>\r\n                <ClocProgress className="w-64 h-6" />\r\n            </div>\r\n        </div>\n}',...null===(_DifferentSizes_parameters1=DifferentSizes.parameters)||void 0===_DifferentSizes_parameters1||null===(_DifferentSizes_parameters_docs=_DifferentSizes_parameters1.docs)||void 0===_DifferentSizes_parameters_docs?void 0:_DifferentSizes_parameters_docs.source}}};const __namedExportsOrder=["Default","FullWidth","Small","Large","CustomStyling","WithLabel","ProgressCard","MultipleProgress","DifferentSizes"]}}]);