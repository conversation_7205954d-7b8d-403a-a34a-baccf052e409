"use strict";(self.webpackChunk_cloc_storybook=self.webpackChunk_cloc_storybook||[]).push([[8883],{"./src/stories/utilities/display/ClocProgressCircle.stories.tsx":(__unused_webpack_module,__webpack_exports__,__webpack_require__)=>{__webpack_require__.r(__webpack_exports__),__webpack_require__.d(__webpack_exports__,{BlueTheme:()=>BlueTheme,CustomColors:()=>CustomColors,CustomPercentage:()=>CustomPercentage,Default:()=>Default,FastAnimation:()=>FastAnimation,LargeSize:()=>LargeSize,ProgressStates:()=>ProgressStates,RedTheme:()=>RedTheme,SizeVariations:()=>SizeVariations,SlowAnimation:()=>SlowAnimation,SmallSize:()=>SmallSize,ThickStroke:()=>ThickStroke,ThinStroke:()=>ThinStroke,__namedExportsOrder:()=>__namedExportsOrder,default:()=>__WEBPACK_DEFAULT_EXPORT__});var _Default_parameters,_Default_parameters_docs,_Default_parameters1,_CustomPercentage_parameters,_CustomPercentage_parameters_docs,_CustomPercentage_parameters1,_SmallSize_parameters,_SmallSize_parameters_docs,_SmallSize_parameters1,_LargeSize_parameters,_LargeSize_parameters_docs,_LargeSize_parameters1,_ThinStroke_parameters,_ThinStroke_parameters_docs,_ThinStroke_parameters1,_ThickStroke_parameters,_ThickStroke_parameters_docs,_ThickStroke_parameters1,_FastAnimation_parameters,_FastAnimation_parameters_docs,_FastAnimation_parameters1,_SlowAnimation_parameters,_SlowAnimation_parameters_docs,_SlowAnimation_parameters1,_CustomColors_parameters,_CustomColors_parameters_docs,_CustomColors_parameters1,_RedTheme_parameters,_RedTheme_parameters_docs,_RedTheme_parameters1,_BlueTheme_parameters,_BlueTheme_parameters_docs,_BlueTheme_parameters1,_ProgressStates_parameters,_ProgressStates_parameters_docs,_ProgressStates_parameters1,_SizeVariations_parameters,_SizeVariations_parameters_docs,_SizeVariations_parameters1,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__=__webpack_require__("../../node_modules/next/dist/compiled/react/jsx-runtime.js"),_cloc_atoms__WEBPACK_IMPORTED_MODULE_2__=(__webpack_require__("../../node_modules/next/dist/compiled/react/index.js"),__webpack_require__("../../packages/toolkit/atoms/dist/index.es.js"));const __WEBPACK_DEFAULT_EXPORT__={title:"Utilities/Display/Progress Circle",component:_cloc_atoms__WEBPACK_IMPORTED_MODULE_2__.RNo,parameters:{layout:"centered",docs:{description:{component:"A circular progress indicator that automatically calculates progress based on today's tracked time from the Cloc context. Shows progress towards an 8-hour daily goal with customizable appearance."}}},argTypes:{percentage:{control:{type:"range",min:0,max:100,step:1},description:"Progress percentage (0-100). Auto-calculated from context if not provided."},radius:{control:{type:"range",min:20,max:100,step:5},description:"Radius of the progress circle"},strokeWidth:{control:{type:"range",min:2,max:20,step:1},description:"Width of the progress stroke"},size:{control:{type:"range",min:40,max:200,step:10},description:"Overall size of the component"},duration:{control:{type:"range",min:100,max:2e3,step:100},description:"Animation duration in milliseconds"},colors:{control:"object",description:"Custom color scheme for the progress circle"}}},Default={args:{}},CustomPercentage={args:{percentage:75}},SmallSize={args:{size:60,radius:25,strokeWidth:6}},LargeSize={args:{size:120,radius:55,strokeWidth:12}},ThinStroke={args:{strokeWidth:4,percentage:60}},ThickStroke={args:{strokeWidth:16,percentage:80}},FastAnimation={args:{duration:200,percentage:90}},SlowAnimation={args:{duration:1500,percentage:45}},CustomColors={args:{percentage:65,colors:{primary:"#10b981",secondary:"#34d399",background:"#d1fae5"}}},RedTheme={args:{percentage:85,colors:{primary:"#ef4444",secondary:"#f87171",background:"#fecaca"}}},BlueTheme={args:{percentage:50,colors:{primary:"#3b82f6",secondary:"#60a5fa",background:"#bfdbfe"}}},ProgressStates={render:()=>(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div",{className:"grid grid-cols-2 md:grid-cols-4 gap-6",children:[(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div",{className:"text-center space-y-2",children:[(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(_cloc_atoms__WEBPACK_IMPORTED_MODULE_2__.RNo,{percentage:0}),(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("p",{className:"text-sm text-gray-600",children:"Not Started"})]}),(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div",{className:"text-center space-y-2",children:[(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(_cloc_atoms__WEBPACK_IMPORTED_MODULE_2__.RNo,{percentage:25}),(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("p",{className:"text-sm text-gray-600",children:"Quarter Done"})]}),(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div",{className:"text-center space-y-2",children:[(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(_cloc_atoms__WEBPACK_IMPORTED_MODULE_2__.RNo,{percentage:50}),(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("p",{className:"text-sm text-gray-600",children:"Half Way"})]}),(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div",{className:"text-center space-y-2",children:[(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(_cloc_atoms__WEBPACK_IMPORTED_MODULE_2__.RNo,{percentage:100}),(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("p",{className:"text-sm text-gray-600",children:"Complete"})]})]})},SizeVariations={render:()=>(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div",{className:"flex items-center gap-6",children:[(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div",{className:"text-center space-y-2",children:[(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(_cloc_atoms__WEBPACK_IMPORTED_MODULE_2__.RNo,{size:40,radius:18,strokeWidth:4,percentage:75}),(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("p",{className:"text-xs text-gray-600",children:"Small"})]}),(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div",{className:"text-center space-y-2",children:[(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(_cloc_atoms__WEBPACK_IMPORTED_MODULE_2__.RNo,{size:60,radius:25,strokeWidth:6,percentage:75}),(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("p",{className:"text-xs text-gray-600",children:"Medium"})]}),(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div",{className:"text-center space-y-2",children:[(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(_cloc_atoms__WEBPACK_IMPORTED_MODULE_2__.RNo,{size:80,radius:35,strokeWidth:8,percentage:75}),(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("p",{className:"text-xs text-gray-600",children:"Large"})]}),(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div",{className:"text-center space-y-2",children:[(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(_cloc_atoms__WEBPACK_IMPORTED_MODULE_2__.RNo,{size:100,radius:45,strokeWidth:10,percentage:75}),(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("p",{className:"text-xs text-gray-600",children:"Extra Large"})]})]})};Default.parameters={...Default.parameters,docs:{...null===(_Default_parameters=Default.parameters)||void 0===_Default_parameters?void 0:_Default_parameters.docs,source:{originalSource:"{\n  args: {}\n}",...null===(_Default_parameters1=Default.parameters)||void 0===_Default_parameters1||null===(_Default_parameters_docs=_Default_parameters1.docs)||void 0===_Default_parameters_docs?void 0:_Default_parameters_docs.source}}},CustomPercentage.parameters={...CustomPercentage.parameters,docs:{...null===(_CustomPercentage_parameters=CustomPercentage.parameters)||void 0===_CustomPercentage_parameters?void 0:_CustomPercentage_parameters.docs,source:{originalSource:"{\n  args: {\n    percentage: 75\n  }\n}",...null===(_CustomPercentage_parameters1=CustomPercentage.parameters)||void 0===_CustomPercentage_parameters1||null===(_CustomPercentage_parameters_docs=_CustomPercentage_parameters1.docs)||void 0===_CustomPercentage_parameters_docs?void 0:_CustomPercentage_parameters_docs.source}}},SmallSize.parameters={...SmallSize.parameters,docs:{...null===(_SmallSize_parameters=SmallSize.parameters)||void 0===_SmallSize_parameters?void 0:_SmallSize_parameters.docs,source:{originalSource:"{\n  args: {\n    size: 60,\n    radius: 25,\n    strokeWidth: 6\n  }\n}",...null===(_SmallSize_parameters1=SmallSize.parameters)||void 0===_SmallSize_parameters1||null===(_SmallSize_parameters_docs=_SmallSize_parameters1.docs)||void 0===_SmallSize_parameters_docs?void 0:_SmallSize_parameters_docs.source}}},LargeSize.parameters={...LargeSize.parameters,docs:{...null===(_LargeSize_parameters=LargeSize.parameters)||void 0===_LargeSize_parameters?void 0:_LargeSize_parameters.docs,source:{originalSource:"{\n  args: {\n    size: 120,\n    radius: 55,\n    strokeWidth: 12\n  }\n}",...null===(_LargeSize_parameters1=LargeSize.parameters)||void 0===_LargeSize_parameters1||null===(_LargeSize_parameters_docs=_LargeSize_parameters1.docs)||void 0===_LargeSize_parameters_docs?void 0:_LargeSize_parameters_docs.source}}},ThinStroke.parameters={...ThinStroke.parameters,docs:{...null===(_ThinStroke_parameters=ThinStroke.parameters)||void 0===_ThinStroke_parameters?void 0:_ThinStroke_parameters.docs,source:{originalSource:"{\n  args: {\n    strokeWidth: 4,\n    percentage: 60\n  }\n}",...null===(_ThinStroke_parameters1=ThinStroke.parameters)||void 0===_ThinStroke_parameters1||null===(_ThinStroke_parameters_docs=_ThinStroke_parameters1.docs)||void 0===_ThinStroke_parameters_docs?void 0:_ThinStroke_parameters_docs.source}}},ThickStroke.parameters={...ThickStroke.parameters,docs:{...null===(_ThickStroke_parameters=ThickStroke.parameters)||void 0===_ThickStroke_parameters?void 0:_ThickStroke_parameters.docs,source:{originalSource:"{\n  args: {\n    strokeWidth: 16,\n    percentage: 80\n  }\n}",...null===(_ThickStroke_parameters1=ThickStroke.parameters)||void 0===_ThickStroke_parameters1||null===(_ThickStroke_parameters_docs=_ThickStroke_parameters1.docs)||void 0===_ThickStroke_parameters_docs?void 0:_ThickStroke_parameters_docs.source}}},FastAnimation.parameters={...FastAnimation.parameters,docs:{...null===(_FastAnimation_parameters=FastAnimation.parameters)||void 0===_FastAnimation_parameters?void 0:_FastAnimation_parameters.docs,source:{originalSource:"{\n  args: {\n    duration: 200,\n    percentage: 90\n  }\n}",...null===(_FastAnimation_parameters1=FastAnimation.parameters)||void 0===_FastAnimation_parameters1||null===(_FastAnimation_parameters_docs=_FastAnimation_parameters1.docs)||void 0===_FastAnimation_parameters_docs?void 0:_FastAnimation_parameters_docs.source}}},SlowAnimation.parameters={...SlowAnimation.parameters,docs:{...null===(_SlowAnimation_parameters=SlowAnimation.parameters)||void 0===_SlowAnimation_parameters?void 0:_SlowAnimation_parameters.docs,source:{originalSource:"{\n  args: {\n    duration: 1500,\n    percentage: 45\n  }\n}",...null===(_SlowAnimation_parameters1=SlowAnimation.parameters)||void 0===_SlowAnimation_parameters1||null===(_SlowAnimation_parameters_docs=_SlowAnimation_parameters1.docs)||void 0===_SlowAnimation_parameters_docs?void 0:_SlowAnimation_parameters_docs.source}}},CustomColors.parameters={...CustomColors.parameters,docs:{...null===(_CustomColors_parameters=CustomColors.parameters)||void 0===_CustomColors_parameters?void 0:_CustomColors_parameters.docs,source:{originalSource:"{\n  args: {\n    percentage: 65,\n    colors: {\n      primary: '#10b981',\n      secondary: '#34d399',\n      background: '#d1fae5'\n    }\n  }\n}",...null===(_CustomColors_parameters1=CustomColors.parameters)||void 0===_CustomColors_parameters1||null===(_CustomColors_parameters_docs=_CustomColors_parameters1.docs)||void 0===_CustomColors_parameters_docs?void 0:_CustomColors_parameters_docs.source}}},RedTheme.parameters={...RedTheme.parameters,docs:{...null===(_RedTheme_parameters=RedTheme.parameters)||void 0===_RedTheme_parameters?void 0:_RedTheme_parameters.docs,source:{originalSource:"{\n  args: {\n    percentage: 85,\n    colors: {\n      primary: '#ef4444',\n      secondary: '#f87171',\n      background: '#fecaca'\n    }\n  }\n}",...null===(_RedTheme_parameters1=RedTheme.parameters)||void 0===_RedTheme_parameters1||null===(_RedTheme_parameters_docs=_RedTheme_parameters1.docs)||void 0===_RedTheme_parameters_docs?void 0:_RedTheme_parameters_docs.source}}},BlueTheme.parameters={...BlueTheme.parameters,docs:{...null===(_BlueTheme_parameters=BlueTheme.parameters)||void 0===_BlueTheme_parameters?void 0:_BlueTheme_parameters.docs,source:{originalSource:"{\n  args: {\n    percentage: 50,\n    colors: {\n      primary: '#3b82f6',\n      secondary: '#60a5fa',\n      background: '#bfdbfe'\n    }\n  }\n}",...null===(_BlueTheme_parameters1=BlueTheme.parameters)||void 0===_BlueTheme_parameters1||null===(_BlueTheme_parameters_docs=_BlueTheme_parameters1.docs)||void 0===_BlueTheme_parameters_docs?void 0:_BlueTheme_parameters_docs.source}}},ProgressStates.parameters={...ProgressStates.parameters,docs:{...null===(_ProgressStates_parameters=ProgressStates.parameters)||void 0===_ProgressStates_parameters?void 0:_ProgressStates_parameters.docs,source:{originalSource:'{\n  render: () => <div className="grid grid-cols-2 md:grid-cols-4 gap-6">\r\n            <div className="text-center space-y-2">\r\n                <ClocProgressCircle percentage={0} />\r\n                <p className="text-sm text-gray-600">Not Started</p>\r\n            </div>\r\n            <div className="text-center space-y-2">\r\n                <ClocProgressCircle percentage={25} />\r\n                <p className="text-sm text-gray-600">Quarter Done</p>\r\n            </div>\r\n            <div className="text-center space-y-2">\r\n                <ClocProgressCircle percentage={50} />\r\n                <p className="text-sm text-gray-600">Half Way</p>\r\n            </div>\r\n            <div className="text-center space-y-2">\r\n                <ClocProgressCircle percentage={100} />\r\n                <p className="text-sm text-gray-600">Complete</p>\r\n            </div>\r\n        </div>\n}',...null===(_ProgressStates_parameters1=ProgressStates.parameters)||void 0===_ProgressStates_parameters1||null===(_ProgressStates_parameters_docs=_ProgressStates_parameters1.docs)||void 0===_ProgressStates_parameters_docs?void 0:_ProgressStates_parameters_docs.source}}},SizeVariations.parameters={...SizeVariations.parameters,docs:{...null===(_SizeVariations_parameters=SizeVariations.parameters)||void 0===_SizeVariations_parameters?void 0:_SizeVariations_parameters.docs,source:{originalSource:'{\n  render: () => <div className="flex items-center gap-6">\r\n            <div className="text-center space-y-2">\r\n                <ClocProgressCircle size={40} radius={18} strokeWidth={4} percentage={75} />\r\n                <p className="text-xs text-gray-600">Small</p>\r\n            </div>\r\n            <div className="text-center space-y-2">\r\n                <ClocProgressCircle size={60} radius={25} strokeWidth={6} percentage={75} />\r\n                <p className="text-xs text-gray-600">Medium</p>\r\n            </div>\r\n            <div className="text-center space-y-2">\r\n                <ClocProgressCircle size={80} radius={35} strokeWidth={8} percentage={75} />\r\n                <p className="text-xs text-gray-600">Large</p>\r\n            </div>\r\n            <div className="text-center space-y-2">\r\n                <ClocProgressCircle size={100} radius={45} strokeWidth={10} percentage={75} />\r\n                <p className="text-xs text-gray-600">Extra Large</p>\r\n            </div>\r\n        </div>\n}',...null===(_SizeVariations_parameters1=SizeVariations.parameters)||void 0===_SizeVariations_parameters1||null===(_SizeVariations_parameters_docs=_SizeVariations_parameters1.docs)||void 0===_SizeVariations_parameters_docs?void 0:_SizeVariations_parameters_docs.source}}};const __namedExportsOrder=["Default","CustomPercentage","SmallSize","LargeSize","ThinStroke","ThickStroke","FastAnimation","SlowAnimation","CustomColors","RedTheme","BlueTheme","ProgressStates","SizeVariations"]}}]);