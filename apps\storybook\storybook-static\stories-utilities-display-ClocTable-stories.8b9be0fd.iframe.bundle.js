"use strict";(self.webpackChunk_cloc_storybook=self.webpackChunk_cloc_storybook||[]).push([[8334],{"./src/stories/utilities/display/ClocTable.stories.tsx":(__unused_webpack_module,__webpack_exports__,__webpack_require__)=>{__webpack_require__.r(__webpack_exports__),__webpack_require__.d(__webpack_exports__,{CustomCells:()=>CustomCells,CustomHeaders:()=>CustomHeaders,CustomStyling:()=>CustomStyling,Default:()=>Default,EmptyTable:()=>EmptyTable,SingleRow:()=>SingleRow,TimeTrackingData:()=>TimeTrackingData,WithCaption:()=>WithCaption,WithFooter:()=>WithFooter,__namedExportsOrder:()=>__namedExportsOrder,default:()=>__WEBPACK_DEFAULT_EXPORT__});var _Default_parameters,_Default_parameters_docs,_Default_parameters1,_WithCaption_parameters,_WithCaption_parameters_docs,_WithCaption_parameters1,_WithFooter_parameters,_WithFooter_parameters_docs,_WithFooter_parameters1,_CustomHeaders_parameters,_CustomHeaders_parameters_docs,_CustomHeaders_parameters1,_CustomCells_parameters,_CustomCells_parameters_docs,_CustomCells_parameters1,_TimeTrackingData_parameters,_TimeTrackingData_parameters_docs,_TimeTrackingData_parameters1,_EmptyTable_parameters,_EmptyTable_parameters_docs,_EmptyTable_parameters1,_SingleRow_parameters,_SingleRow_parameters_docs,_SingleRow_parameters1,_CustomStyling_parameters,_CustomStyling_parameters_docs,_CustomStyling_parameters1,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__=__webpack_require__("../../node_modules/next/dist/compiled/react/jsx-runtime.js"),_cloc_atoms__WEBPACK_IMPORTED_MODULE_2__=(__webpack_require__("../../node_modules/next/dist/compiled/react/index.js"),__webpack_require__("../../packages/toolkit/atoms/dist/index.es.js"));const __WEBPACK_DEFAULT_EXPORT__={title:"Utilities/Display/Table",component:_cloc_atoms__WEBPACK_IMPORTED_MODULE_2__.CAG,parameters:{layout:"centered",docs:{description:{component:"A flexible table component with customizable headers, cells, and footer. Supports custom rendering functions and styling."}}},argTypes:{data:{control:"object",description:"Array of data objects to display in the table"},caption:{control:"text",description:"Table caption displayed at the top"},footerData:{control:"object",description:"Footer data with label and value"},tableClassName:{control:"text",description:"CSS classes for the table element"},headerClassName:{control:"text",description:"CSS classes for table headers"},rowClassName:{control:"text",description:"CSS classes for table rows"},cellClassName:{control:"text",description:"CSS classes for table cells"}}},Default={args:{data:_cloc_atoms__WEBPACK_IMPORTED_MODULE_2__.uIT.slice(0,3)}},WithCaption={args:{data:_cloc_atoms__WEBPACK_IMPORTED_MODULE_2__.uIT.slice(0,4),caption:"Invoice Summary Report"}},WithFooter={args:{data:_cloc_atoms__WEBPACK_IMPORTED_MODULE_2__.uIT.slice(0,5),caption:"Monthly Invoices",footerData:{label:"Total Amount",value:"$1,750.00"}}},CustomHeaders={args:{data:_cloc_atoms__WEBPACK_IMPORTED_MODULE_2__.uIT.slice(0,3),renderHeader:column=>({invoice:"📄 Invoice ID",paymentStatus:"💳 Payment Status",totalAmount:"💰 Total Amount",paymentMethod:"🏦 Payment Method"}[column]||column)}},CustomCells={args:{data:_cloc_atoms__WEBPACK_IMPORTED_MODULE_2__.uIT.slice(0,4),renderCell:(row,column)=>{if("paymentStatus"===column){const colorClass={Paid:"bg-green-100 text-green-800",Pending:"bg-yellow-100 text-yellow-800",Unpaid:"bg-red-100 text-red-800"}[row[column]]||"";return(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("span",{className:"px-2 py-1 rounded-full text-xs font-medium ".concat(colorClass),children:row[column]})}return"totalAmount"===column?(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("span",{className:"font-mono font-semibold",children:row[column]}):row[column]}}},TimeTrackingData={args:{data:[{project:"Website Redesign",task:"UI Design",hours:"4.5",status:"In Progress"},{project:"Mobile App",task:"Backend API",hours:"6.2",status:"Completed"},{project:"Dashboard",task:"Data Visualization",hours:"3.8",status:"In Progress"},{project:"E-commerce",task:"Payment Integration",hours:"5.1",status:"Completed"}],caption:"Weekly Time Tracking Report",footerData:{label:"Total Hours Worked",value:"19.6 hours"},renderHeader:column=>({project:"Project",task:"Task",hours:"Hours",status:"Status"}[column]||column),renderCell:(row,column)=>{if("status"===column){const isCompleted="Completed"===row[column];return(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("span",{className:"px-2 py-1 rounded-full text-xs font-medium ".concat(isCompleted?"bg-green-100 text-green-800":"bg-blue-100 text-blue-800"),children:row[column]})}return"hours"===column?(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("span",{className:"font-mono",children:[row[column],"h"]}):row[column]}}},EmptyTable={args:{data:[],caption:"No Data Available"}},SingleRow={args:{data:[_cloc_atoms__WEBPACK_IMPORTED_MODULE_2__.uIT[0]],caption:"Single Invoice Record"}},CustomStyling={args:{data:_cloc_atoms__WEBPACK_IMPORTED_MODULE_2__.uIT.slice(0,3),tableClassName:"border-blue-200 bg-blue-50",headerClassName:"text-blue-900 font-bold",rowClassName:"hover:bg-blue-100",cellClassName:"text-blue-800"}};Default.parameters={...Default.parameters,docs:{...null===(_Default_parameters=Default.parameters)||void 0===_Default_parameters?void 0:_Default_parameters.docs,source:{originalSource:"{\n  args: {\n    data: fakedataTable.slice(0, 3)\n  }\n}",...null===(_Default_parameters1=Default.parameters)||void 0===_Default_parameters1||null===(_Default_parameters_docs=_Default_parameters1.docs)||void 0===_Default_parameters_docs?void 0:_Default_parameters_docs.source}}},WithCaption.parameters={...WithCaption.parameters,docs:{...null===(_WithCaption_parameters=WithCaption.parameters)||void 0===_WithCaption_parameters?void 0:_WithCaption_parameters.docs,source:{originalSource:"{\n  args: {\n    data: fakedataTable.slice(0, 4),\n    caption: 'Invoice Summary Report'\n  }\n}",...null===(_WithCaption_parameters1=WithCaption.parameters)||void 0===_WithCaption_parameters1||null===(_WithCaption_parameters_docs=_WithCaption_parameters1.docs)||void 0===_WithCaption_parameters_docs?void 0:_WithCaption_parameters_docs.source}}},WithFooter.parameters={...WithFooter.parameters,docs:{...null===(_WithFooter_parameters=WithFooter.parameters)||void 0===_WithFooter_parameters?void 0:_WithFooter_parameters.docs,source:{originalSource:"{\n  args: {\n    data: fakedataTable.slice(0, 5),\n    caption: 'Monthly Invoices',\n    footerData: {\n      label: 'Total Amount',\n      value: '$1,750.00'\n    }\n  }\n}",...null===(_WithFooter_parameters1=WithFooter.parameters)||void 0===_WithFooter_parameters1||null===(_WithFooter_parameters_docs=_WithFooter_parameters1.docs)||void 0===_WithFooter_parameters_docs?void 0:_WithFooter_parameters_docs.source}}},CustomHeaders.parameters={...CustomHeaders.parameters,docs:{...null===(_CustomHeaders_parameters=CustomHeaders.parameters)||void 0===_CustomHeaders_parameters?void 0:_CustomHeaders_parameters.docs,source:{originalSource:"{\n  args: {\n    data: fakedataTable.slice(0, 3),\n    renderHeader: (column: string) => {\n      const headerMap: Record<string, string> = {\n        invoice: '📄 Invoice ID',\n        paymentStatus: '💳 Payment Status',\n        totalAmount: '💰 Total Amount',\n        paymentMethod: '🏦 Payment Method'\n      };\n      return headerMap[column] || column;\n    }\n  }\n}",...null===(_CustomHeaders_parameters1=CustomHeaders.parameters)||void 0===_CustomHeaders_parameters1||null===(_CustomHeaders_parameters_docs=_CustomHeaders_parameters1.docs)||void 0===_CustomHeaders_parameters_docs?void 0:_CustomHeaders_parameters_docs.source}}},CustomCells.parameters={...CustomCells.parameters,docs:{...null===(_CustomCells_parameters=CustomCells.parameters)||void 0===_CustomCells_parameters?void 0:_CustomCells_parameters.docs,source:{originalSource:"{\n  args: {\n    data: fakedataTable.slice(0, 4),\n    renderCell: (row: any, column: string) => {\n      if (column === 'paymentStatus') {\n        const statusColors = {\n          Paid: 'bg-green-100 text-green-800',\n          Pending: 'bg-yellow-100 text-yellow-800',\n          Unpaid: 'bg-red-100 text-red-800'\n        };\n        const colorClass = statusColors[row[column] as keyof typeof statusColors] || '';\n        return <span className={`px-2 py-1 rounded-full text-xs font-medium ${colorClass}`}>{row[column]}</span>;\n      }\n      if (column === 'totalAmount') {\n        return <span className=\"font-mono font-semibold\">{row[column]}</span>;\n      }\n      return row[column];\n    }\n  }\n}",...null===(_CustomCells_parameters1=CustomCells.parameters)||void 0===_CustomCells_parameters1||null===(_CustomCells_parameters_docs=_CustomCells_parameters1.docs)||void 0===_CustomCells_parameters_docs?void 0:_CustomCells_parameters_docs.source}}},TimeTrackingData.parameters={...TimeTrackingData.parameters,docs:{...null===(_TimeTrackingData_parameters=TimeTrackingData.parameters)||void 0===_TimeTrackingData_parameters?void 0:_TimeTrackingData_parameters.docs,source:{originalSource:"{\n  args: {\n    data: [{\n      project: 'Website Redesign',\n      task: 'UI Design',\n      hours: '4.5',\n      status: 'In Progress'\n    }, {\n      project: 'Mobile App',\n      task: 'Backend API',\n      hours: '6.2',\n      status: 'Completed'\n    }, {\n      project: 'Dashboard',\n      task: 'Data Visualization',\n      hours: '3.8',\n      status: 'In Progress'\n    }, {\n      project: 'E-commerce',\n      task: 'Payment Integration',\n      hours: '5.1',\n      status: 'Completed'\n    }],\n    caption: 'Weekly Time Tracking Report',\n    footerData: {\n      label: 'Total Hours Worked',\n      value: '19.6 hours'\n    },\n    renderHeader: (column: string) => {\n      const headers = {\n        project: 'Project',\n        task: 'Task',\n        hours: 'Hours',\n        status: 'Status'\n      };\n      return headers[column as keyof typeof headers] || column;\n    },\n    renderCell: (row: any, column: string) => {\n      if (column === 'status') {\n        const isCompleted = row[column] === 'Completed';\n        return <span className={`px-2 py-1 rounded-full text-xs font-medium ${isCompleted ? 'bg-green-100 text-green-800' : 'bg-blue-100 text-blue-800'}`}>\r\n                        {row[column]}\r\n                    </span>;\n      }\n      if (column === 'hours') {\n        return <span className=\"font-mono\">{row[column]}h</span>;\n      }\n      return row[column];\n    }\n  }\n}",...null===(_TimeTrackingData_parameters1=TimeTrackingData.parameters)||void 0===_TimeTrackingData_parameters1||null===(_TimeTrackingData_parameters_docs=_TimeTrackingData_parameters1.docs)||void 0===_TimeTrackingData_parameters_docs?void 0:_TimeTrackingData_parameters_docs.source}}},EmptyTable.parameters={...EmptyTable.parameters,docs:{...null===(_EmptyTable_parameters=EmptyTable.parameters)||void 0===_EmptyTable_parameters?void 0:_EmptyTable_parameters.docs,source:{originalSource:"{\n  args: {\n    data: [],\n    caption: 'No Data Available'\n  }\n}",...null===(_EmptyTable_parameters1=EmptyTable.parameters)||void 0===_EmptyTable_parameters1||null===(_EmptyTable_parameters_docs=_EmptyTable_parameters1.docs)||void 0===_EmptyTable_parameters_docs?void 0:_EmptyTable_parameters_docs.source}}},SingleRow.parameters={...SingleRow.parameters,docs:{...null===(_SingleRow_parameters=SingleRow.parameters)||void 0===_SingleRow_parameters?void 0:_SingleRow_parameters.docs,source:{originalSource:"{\n  args: {\n    data: [fakedataTable[0]],\n    caption: 'Single Invoice Record'\n  }\n}",...null===(_SingleRow_parameters1=SingleRow.parameters)||void 0===_SingleRow_parameters1||null===(_SingleRow_parameters_docs=_SingleRow_parameters1.docs)||void 0===_SingleRow_parameters_docs?void 0:_SingleRow_parameters_docs.source}}},CustomStyling.parameters={...CustomStyling.parameters,docs:{...null===(_CustomStyling_parameters=CustomStyling.parameters)||void 0===_CustomStyling_parameters?void 0:_CustomStyling_parameters.docs,source:{originalSource:"{\n  args: {\n    data: fakedataTable.slice(0, 3),\n    tableClassName: 'border-blue-200 bg-blue-50',\n    headerClassName: 'text-blue-900 font-bold',\n    rowClassName: 'hover:bg-blue-100',\n    cellClassName: 'text-blue-800'\n  }\n}",...null===(_CustomStyling_parameters1=CustomStyling.parameters)||void 0===_CustomStyling_parameters1||null===(_CustomStyling_parameters_docs=_CustomStyling_parameters1.docs)||void 0===_CustomStyling_parameters_docs?void 0:_CustomStyling_parameters_docs.source}}};const __namedExportsOrder=["Default","WithCaption","WithFooter","CustomHeaders","CustomCells","TimeTrackingData","EmptyTable","SingleRow","CustomStyling"]}}]);