"use strict";(self.webpackChunk_cloc_storybook=self.webpackChunk_cloc_storybook||[]).push([[9050],{"./src/stories/utilities/settings/ClocFontToggle.stories.tsx":(__unused_webpack_module,__webpack_exports__,__webpack_require__)=>{__webpack_require__.r(__webpack_exports__),__webpack_require__.d(__webpack_exports__,{AccessibilityExample:()=>AccessibilityExample,CompactLayout:()=>CompactLayout,Default:()=>Default,InSidebar:()=>InSidebar,InToolbar:()=>InToolbar,SettingsPanel:()=>SettingsPanel,WithPreview:()=>WithPreview,__namedExportsOrder:()=>__namedExportsOrder,default:()=>__WEBPACK_DEFAULT_EXPORT__});var _Default_parameters,_Default_parameters_docs,_Default_parameters1,_InToolbar_parameters,_InToolbar_parameters_docs,_InToolbar_parameters1,_InSidebar_parameters,_InSidebar_parameters_docs,_InSidebar_parameters1,_WithPreview_parameters,_WithPreview_parameters_docs,_WithPreview_parameters1,_SettingsPanel_parameters,_SettingsPanel_parameters_docs,_SettingsPanel_parameters1,_AccessibilityExample_parameters,_AccessibilityExample_parameters_docs,_AccessibilityExample_parameters1,_CompactLayout_parameters,_CompactLayout_parameters_docs,_CompactLayout_parameters1,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__=__webpack_require__("../../node_modules/next/dist/compiled/react/jsx-runtime.js"),_cloc_atoms__WEBPACK_IMPORTED_MODULE_2__=(__webpack_require__("../../node_modules/next/dist/compiled/react/index.js"),__webpack_require__("../../packages/toolkit/atoms/dist/index.es.js"));const __WEBPACK_DEFAULT_EXPORT__={title:"Utilities/Settings/Font Toggle",component:_cloc_atoms__WEBPACK_IMPORTED_MODULE_2__.jF8,parameters:{layout:"centered",docs:{description:{component:"A font selector component that allows users to switch between different font families for the time tracking interface. Integrates with the Cloc font system and provides various typography options."}}}},Default={args:{}},InToolbar={render:()=>(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div",{className:"flex items-center gap-4 p-4 bg-gray-100 dark:bg-gray-800 rounded-lg",children:[(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("h3",{className:"font-semibold text-gray-900 dark:text-white",children:"Typography"}),(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(_cloc_atoms__WEBPACK_IMPORTED_MODULE_2__.jF8,{}),(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("button",{className:"px-3 py-1 bg-blue-600 text-white rounded text-sm",children:"Apply"})]})},InSidebar={render:()=>(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div",{className:"w-64 p-4 bg-white dark:bg-gray-900 border-r border-gray-200 dark:border-gray-700 space-y-4",children:[(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("h3",{className:"font-semibold text-gray-900 dark:text-white",children:"Typography Settings"}),(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div",{className:"space-y-3",children:[(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div",{className:"space-y-2",children:[(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("label",{className:"text-sm font-medium text-gray-700 dark:text-gray-300",children:"Font Family"}),(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(_cloc_atoms__WEBPACK_IMPORTED_MODULE_2__.jF8,{})]}),(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div",{className:"space-y-2",children:[(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("label",{className:"text-sm font-medium text-gray-700 dark:text-gray-300",children:"Font Size"}),(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("select",{className:"w-full p-2 border border-gray-300 rounded-md text-sm",children:[(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("option",{children:"Small"}),(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("option",{children:"Medium"}),(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("option",{children:"Large"})]})]})]})]})},WithPreview={render:()=>(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div",{className:"space-y-4 p-4 border rounded-lg bg-white dark:bg-gray-800",children:[(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("h3",{className:"font-semibold text-gray-900 dark:text-white",children:"Font Customization"}),(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div",{className:"space-y-4",children:[(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div",{className:"space-y-2",children:[(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("label",{className:"text-sm font-medium text-gray-700 dark:text-gray-300",children:"Select Font"}),(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(_cloc_atoms__WEBPACK_IMPORTED_MODULE_2__.jF8,{})]}),(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div",{className:"p-4 border border-gray-200 dark:border-gray-600 rounded-md",children:[(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("h4",{className:"text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:"Preview"}),(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div",{className:"space-y-2",children:[(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("p",{className:"text-lg",children:"Timer Display: 02:45:30"}),(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("p",{className:"text-sm text-gray-600 dark:text-gray-400",children:"This font will be applied to timers and time displays"})]})]})]})]})},SettingsPanel={render:()=>(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div",{className:"max-w-md p-6 bg-white dark:bg-gray-900 border border-gray-200 dark:border-gray-700 rounded-lg shadow-lg",children:[(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("h2",{className:"text-lg font-semibold text-gray-900 dark:text-white mb-4",children:"Display Preferences"}),(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div",{className:"space-y-4",children:[(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div",{className:"space-y-2",children:[(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("label",{className:"text-sm font-medium text-gray-700 dark:text-gray-300",children:"Timer Font"}),(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(_cloc_atoms__WEBPACK_IMPORTED_MODULE_2__.jF8,{}),(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("p",{className:"text-xs text-gray-500 dark:text-gray-400",children:"Choose a font for timer displays and time-related text"})]}),(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div",{className:"space-y-2",children:[(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("label",{className:"text-sm font-medium text-gray-700 dark:text-gray-300",children:"Preview"}),(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div",{className:"p-3 bg-gray-50 dark:bg-gray-800 rounded border",children:[(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("div",{className:"text-2xl font-mono",children:"08:45:23"}),(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("div",{className:"text-sm text-gray-600 dark:text-gray-400",children:"Working on Project Alpha"})]})]}),(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("div",{className:"pt-4 border-t border-gray-200 dark:border-gray-600",children:(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("button",{className:"w-full px-4 py-2 bg-blue-600 text-white rounded-md text-sm hover:bg-blue-700",children:"Save Preferences"})})]})]})},AccessibilityExample={render:()=>(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div",{className:"space-y-4 p-4 border rounded-lg bg-white dark:bg-gray-800",children:[(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("h3",{className:"font-semibold text-gray-900 dark:text-white",children:"Accessibility Options"}),(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div",{className:"space-y-4",children:[(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div",{className:"space-y-2",children:[(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("label",{className:"text-sm font-medium text-gray-700 dark:text-gray-300",children:"Font Family"}),(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(_cloc_atoms__WEBPACK_IMPORTED_MODULE_2__.jF8,{}),(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("p",{className:"text-xs text-gray-500 dark:text-gray-400",children:"Some fonts may be easier to read for users with dyslexia or visual impairments"})]}),(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div",{className:"grid grid-cols-2 gap-4 text-sm",children:[(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div",{className:"space-y-1",children:[(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("p",{className:"font-medium",children:"Recommended for readability:"}),(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("ul",{className:"text-gray-600 dark:text-gray-400 space-y-1",children:[(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("li",{children:"• Inter"}),(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("li",{children:"• Open Sans"}),(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("li",{children:"• Roboto"})]})]}),(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div",{className:"space-y-1",children:[(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("p",{className:"font-medium",children:"Monospace for timers:"}),(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("ul",{className:"text-gray-600 dark:text-gray-400 space-y-1",children:[(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("li",{children:"• Source Code Pro"}),(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("li",{children:"• Digital-7"})]})]})]})]})]})},CompactLayout={render:()=>(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div",{className:"flex items-center gap-2 p-2 bg-gray-50 dark:bg-gray-800 rounded",children:[(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("span",{className:"text-sm text-gray-600 dark:text-gray-400",children:"Font:"}),(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(_cloc_atoms__WEBPACK_IMPORTED_MODULE_2__.jF8,{})]})};Default.parameters={...Default.parameters,docs:{...null===(_Default_parameters=Default.parameters)||void 0===_Default_parameters?void 0:_Default_parameters.docs,source:{originalSource:"{\n  args: {}\n}",...null===(_Default_parameters1=Default.parameters)||void 0===_Default_parameters1||null===(_Default_parameters_docs=_Default_parameters1.docs)||void 0===_Default_parameters_docs?void 0:_Default_parameters_docs.source}}},InToolbar.parameters={...InToolbar.parameters,docs:{...null===(_InToolbar_parameters=InToolbar.parameters)||void 0===_InToolbar_parameters?void 0:_InToolbar_parameters.docs,source:{originalSource:'{\n  render: () => <div className="flex items-center gap-4 p-4 bg-gray-100 dark:bg-gray-800 rounded-lg">\r\n            <h3 className="font-semibold text-gray-900 dark:text-white">Typography</h3>\r\n            <ClocFontToggle />\r\n            <button className="px-3 py-1 bg-blue-600 text-white rounded text-sm">Apply</button>\r\n        </div>\n}',...null===(_InToolbar_parameters1=InToolbar.parameters)||void 0===_InToolbar_parameters1||null===(_InToolbar_parameters_docs=_InToolbar_parameters1.docs)||void 0===_InToolbar_parameters_docs?void 0:_InToolbar_parameters_docs.source}}},InSidebar.parameters={...InSidebar.parameters,docs:{...null===(_InSidebar_parameters=InSidebar.parameters)||void 0===_InSidebar_parameters?void 0:_InSidebar_parameters.docs,source:{originalSource:'{\n  render: () => <div className="w-64 p-4 bg-white dark:bg-gray-900 border-r border-gray-200 dark:border-gray-700 space-y-4">\r\n            <h3 className="font-semibold text-gray-900 dark:text-white">Typography Settings</h3>\r\n            <div className="space-y-3">\r\n                <div className="space-y-2">\r\n                    <label className="text-sm font-medium text-gray-700 dark:text-gray-300">Font Family</label>\r\n                    <ClocFontToggle />\r\n                </div>\r\n                <div className="space-y-2">\r\n                    <label className="text-sm font-medium text-gray-700 dark:text-gray-300">Font Size</label>\r\n                    <select className="w-full p-2 border border-gray-300 rounded-md text-sm">\r\n                        <option>Small</option>\r\n                        <option>Medium</option>\r\n                        <option>Large</option>\r\n                    </select>\r\n                </div>\r\n            </div>\r\n        </div>\n}',...null===(_InSidebar_parameters1=InSidebar.parameters)||void 0===_InSidebar_parameters1||null===(_InSidebar_parameters_docs=_InSidebar_parameters1.docs)||void 0===_InSidebar_parameters_docs?void 0:_InSidebar_parameters_docs.source}}},WithPreview.parameters={...WithPreview.parameters,docs:{...null===(_WithPreview_parameters=WithPreview.parameters)||void 0===_WithPreview_parameters?void 0:_WithPreview_parameters.docs,source:{originalSource:'{\n  render: () => <div className="space-y-4 p-4 border rounded-lg bg-white dark:bg-gray-800">\r\n            <h3 className="font-semibold text-gray-900 dark:text-white">Font Customization</h3>\r\n            <div className="space-y-4">\r\n                <div className="space-y-2">\r\n                    <label className="text-sm font-medium text-gray-700 dark:text-gray-300">Select Font</label>\r\n                    <ClocFontToggle />\r\n                </div>\r\n                <div className="p-4 border border-gray-200 dark:border-gray-600 rounded-md">\r\n                    <h4 className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Preview</h4>\r\n                    <div className="space-y-2">\r\n                        <p className="text-lg">Timer Display: 02:45:30</p>\r\n                        <p className="text-sm text-gray-600 dark:text-gray-400">\r\n                            This font will be applied to timers and time displays\r\n                        </p>\r\n                    </div>\r\n                </div>\r\n            </div>\r\n        </div>\n}',...null===(_WithPreview_parameters1=WithPreview.parameters)||void 0===_WithPreview_parameters1||null===(_WithPreview_parameters_docs=_WithPreview_parameters1.docs)||void 0===_WithPreview_parameters_docs?void 0:_WithPreview_parameters_docs.source}}},SettingsPanel.parameters={...SettingsPanel.parameters,docs:{...null===(_SettingsPanel_parameters=SettingsPanel.parameters)||void 0===_SettingsPanel_parameters?void 0:_SettingsPanel_parameters.docs,source:{originalSource:'{\n  render: () => <div className="max-w-md p-6 bg-white dark:bg-gray-900 border border-gray-200 dark:border-gray-700 rounded-lg shadow-lg">\r\n            <h2 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">Display Preferences</h2>\r\n            <div className="space-y-4">\r\n                <div className="space-y-2">\r\n                    <label className="text-sm font-medium text-gray-700 dark:text-gray-300">Timer Font</label>\r\n                    <ClocFontToggle />\r\n                    <p className="text-xs text-gray-500 dark:text-gray-400">\r\n                        Choose a font for timer displays and time-related text\r\n                    </p>\r\n                </div>\r\n                <div className="space-y-2">\r\n                    <label className="text-sm font-medium text-gray-700 dark:text-gray-300">Preview</label>\r\n                    <div className="p-3 bg-gray-50 dark:bg-gray-800 rounded border">\r\n                        <div className="text-2xl font-mono">08:45:23</div>\r\n                        <div className="text-sm text-gray-600 dark:text-gray-400">Working on Project Alpha</div>\r\n                    </div>\r\n                </div>\r\n                <div className="pt-4 border-t border-gray-200 dark:border-gray-600">\r\n                    <button className="w-full px-4 py-2 bg-blue-600 text-white rounded-md text-sm hover:bg-blue-700">\r\n                        Save Preferences\r\n                    </button>\r\n                </div>\r\n            </div>\r\n        </div>\n}',...null===(_SettingsPanel_parameters1=SettingsPanel.parameters)||void 0===_SettingsPanel_parameters1||null===(_SettingsPanel_parameters_docs=_SettingsPanel_parameters1.docs)||void 0===_SettingsPanel_parameters_docs?void 0:_SettingsPanel_parameters_docs.source}}},AccessibilityExample.parameters={...AccessibilityExample.parameters,docs:{...null===(_AccessibilityExample_parameters=AccessibilityExample.parameters)||void 0===_AccessibilityExample_parameters?void 0:_AccessibilityExample_parameters.docs,source:{originalSource:'{\n  render: () => <div className="space-y-4 p-4 border rounded-lg bg-white dark:bg-gray-800">\r\n            <h3 className="font-semibold text-gray-900 dark:text-white">Accessibility Options</h3>\r\n            <div className="space-y-4">\r\n                <div className="space-y-2">\r\n                    <label className="text-sm font-medium text-gray-700 dark:text-gray-300">Font Family</label>\r\n                    <ClocFontToggle />\r\n                    <p className="text-xs text-gray-500 dark:text-gray-400">\r\n                        Some fonts may be easier to read for users with dyslexia or visual impairments\r\n                    </p>\r\n                </div>\r\n                <div className="grid grid-cols-2 gap-4 text-sm">\r\n                    <div className="space-y-1">\r\n                        <p className="font-medium">Recommended for readability:</p>\r\n                        <ul className="text-gray-600 dark:text-gray-400 space-y-1">\r\n                            <li>• Inter</li>\r\n                            <li>• Open Sans</li>\r\n                            <li>• Roboto</li>\r\n                        </ul>\r\n                    </div>\r\n                    <div className="space-y-1">\r\n                        <p className="font-medium">Monospace for timers:</p>\r\n                        <ul className="text-gray-600 dark:text-gray-400 space-y-1">\r\n                            <li>• Source Code Pro</li>\r\n                            <li>• Digital-7</li>\r\n                        </ul>\r\n                    </div>\r\n                </div>\r\n            </div>\r\n        </div>\n}',...null===(_AccessibilityExample_parameters1=AccessibilityExample.parameters)||void 0===_AccessibilityExample_parameters1||null===(_AccessibilityExample_parameters_docs=_AccessibilityExample_parameters1.docs)||void 0===_AccessibilityExample_parameters_docs?void 0:_AccessibilityExample_parameters_docs.source}}},CompactLayout.parameters={...CompactLayout.parameters,docs:{...null===(_CompactLayout_parameters=CompactLayout.parameters)||void 0===_CompactLayout_parameters?void 0:_CompactLayout_parameters.docs,source:{originalSource:'{\n  render: () => <div className="flex items-center gap-2 p-2 bg-gray-50 dark:bg-gray-800 rounded">\r\n            <span className="text-sm text-gray-600 dark:text-gray-400">Font:</span>\r\n            <ClocFontToggle />\r\n        </div>\n}',...null===(_CompactLayout_parameters1=CompactLayout.parameters)||void 0===_CompactLayout_parameters1||null===(_CompactLayout_parameters_docs=_CompactLayout_parameters1.docs)||void 0===_CompactLayout_parameters_docs?void 0:_CompactLayout_parameters_docs.source}}};const __namedExportsOrder=["Default","InToolbar","InSidebar","WithPreview","SettingsPanel","AccessibilityExample","CompactLayout"]}}]);