"use strict";(self.webpackChunk_cloc_storybook=self.webpackChunk_cloc_storybook||[]).push([[2819],{"./src/stories/utilities/settings/ClocLanguageSwitch.stories.tsx":(__unused_webpack_module,__webpack_exports__,__webpack_require__)=>{var _Default_parameters,_Default_parameters_docs,_Default_parameters1,_Default_parameters_docs1,_Default_parameters2,_WithLabel_parameters,_WithLabel_parameters_docs,_WithLabel_parameters1,_WithLabel_parameters_docs1,_WithLabel_parameters2,_Small_parameters,_Small_parameters_docs,_Small_parameters1,_Small_parameters_docs1,_Small_parameters2,_Large_parameters,_Large_parameters_docs,_Large_parameters1,_Large_parameters_docs1,_Large_parameters2,_SmallWithLabel_parameters,_SmallWithLabel_parameters_docs,_SmallWithLabel_parameters1,_SmallWithLabel_parameters_docs1,_SmallWithLabel_parameters2;__webpack_require__.r(__webpack_exports__),__webpack_require__.d(__webpack_exports__,{Default:()=>Default,Large:()=>Large,Small:()=>Small,SmallWithLabel:()=>SmallWithLabel,WithLabel:()=>WithLabel,__namedExportsOrder:()=>__namedExportsOrder,default:()=>__WEBPACK_DEFAULT_EXPORT__});const __WEBPACK_DEFAULT_EXPORT__={title:"Utilities/Settings/Language Switch",component:__webpack_require__("../../packages/toolkit/atoms/dist/index.es.js").i$z,parameters:{layout:"centered",docs:{description:{component:"\nClocLanguageSwitch is a specialized select component that enables internationalization (i18n) for Cloc components. It provides seamless language switching with persistent storage and real-time updates across all components within the same ClocProvider context.\n\n### Key Capabilities\n\n- **Multi-language Support**: Currently supports English and French with extensible architecture\n- **Persistent Preferences**: Automatically saves selected language to localStorage for future sessions\n- **Context Integration**: Updates all Cloc components simultaneously through the shared i18n context\n- **Visual Identification**: Country flag icons for intuitive language recognition\n- **Size Flexibility**: Three size variants (sm, default, lg) for different interface contexts\n- **Optional Labeling**: Configurable label display for enhanced user experience\n\n### Language Management\n\nThe component integrates with react-i18next and provides:\n- Automatic language detection from browser settings\n- Fallback to stored preferences in localStorage\n- Real-time language switching without page reload\n- Consistent translation updates across all Cloc components\n\n### Technical Implementation\n\nUses the `changeClocLanguage()` utility function to:\n1. Update the i18next language setting\n2. Store the preference in localStorage\n3. Trigger re-rendering of all connected components\n\n### Integration Requirements\n\n- Must be used within a ClocProvider context\n- Requires i18next configuration with supported locales\n- Translation files must be available for all supported languages\n\n### Best Practices\n\n- Place in global navigation or settings areas for easy access\n- Use appropriate size variant for the interface context\n- Include label when the purpose isn't immediately clear\n- Test with all supported languages to ensure proper functionality\n                "}}},argTypes:{size:{control:"select",options:["sm","default","lg"],description:"Size variant of the language switch component",table:{type:{summary:"string"},defaultValue:{summary:"default"}}},label:{control:"boolean",description:'Whether to show the "Language Switch :" label',table:{type:{summary:"boolean"},defaultValue:{summary:"false"}}}}},Default={args:{size:"default",label:!1},parameters:{docs:{description:{story:"The default language switch component without label. Ideal for navigation bars and compact interface areas where space is limited."}}}},WithLabel={args:{size:"default",label:!0},parameters:{docs:{description:{story:"Language switch with descriptive label for enhanced user experience. Use in settings pages or configuration panels where clarity is important."}}}},Small={args:{size:"sm",label:!1},parameters:{docs:{description:{story:"Small size language switch for compact interfaces, toolbars, and dense layouts where space conservation is important."}}}},Large={args:{size:"lg",label:!0},parameters:{docs:{description:{story:"Large size language switch for prominent placement in settings pages, configuration panels, or onboarding flows."}}}},SmallWithLabel={args:{size:"sm",label:!0},parameters:{docs:{description:{story:"Small size language switch with label for compact but descriptive interface. Balances space efficiency with user clarity."}}}};Default.parameters={...Default.parameters,docs:{...null===(_Default_parameters=Default.parameters)||void 0===_Default_parameters?void 0:_Default_parameters.docs,source:{originalSource:"{\n  args: {\n    size: 'default',\n    label: false\n  },\n  parameters: {\n    docs: {\n      description: {\n        story: 'The default language switch component without label. Ideal for navigation bars and compact interface areas where space is limited.'\n      }\n    }\n  }\n}",...null===(_Default_parameters1=Default.parameters)||void 0===_Default_parameters1||null===(_Default_parameters_docs=_Default_parameters1.docs)||void 0===_Default_parameters_docs?void 0:_Default_parameters_docs.source},description:{story:"Default language switch without label for clean, minimal appearance.\r\nUse in navigation bars or compact interface areas.",...null===(_Default_parameters2=Default.parameters)||void 0===_Default_parameters2||null===(_Default_parameters_docs1=_Default_parameters2.docs)||void 0===_Default_parameters_docs1?void 0:_Default_parameters_docs1.description}}},WithLabel.parameters={...WithLabel.parameters,docs:{...null===(_WithLabel_parameters=WithLabel.parameters)||void 0===_WithLabel_parameters?void 0:_WithLabel_parameters.docs,source:{originalSource:"{\n  args: {\n    size: 'default',\n    label: true\n  },\n  parameters: {\n    docs: {\n      description: {\n        story: 'Language switch with descriptive label for enhanced user experience. Use in settings pages or configuration panels where clarity is important.'\n      }\n    }\n  }\n}",...null===(_WithLabel_parameters1=WithLabel.parameters)||void 0===_WithLabel_parameters1||null===(_WithLabel_parameters_docs=_WithLabel_parameters1.docs)||void 0===_WithLabel_parameters_docs?void 0:_WithLabel_parameters_docs.source},description:{story:"Language switch with descriptive label for better user understanding.\r\nUse in settings pages or when the purpose isn't immediately clear.",...null===(_WithLabel_parameters2=WithLabel.parameters)||void 0===_WithLabel_parameters2||null===(_WithLabel_parameters_docs1=_WithLabel_parameters2.docs)||void 0===_WithLabel_parameters_docs1?void 0:_WithLabel_parameters_docs1.description}}},Small.parameters={...Small.parameters,docs:{...null===(_Small_parameters=Small.parameters)||void 0===_Small_parameters?void 0:_Small_parameters.docs,source:{originalSource:"{\n  args: {\n    size: 'sm',\n    label: false\n  },\n  parameters: {\n    docs: {\n      description: {\n        story: 'Small size language switch for compact interfaces, toolbars, and dense layouts where space conservation is important.'\n      }\n    }\n  }\n}",...null===(_Small_parameters1=Small.parameters)||void 0===_Small_parameters1||null===(_Small_parameters_docs=_Small_parameters1.docs)||void 0===_Small_parameters_docs?void 0:_Small_parameters_docs.source},description:{story:"Small size language switch for compact interfaces and toolbars.\r\nUse in dense layouts or secondary navigation areas.",...null===(_Small_parameters2=Small.parameters)||void 0===_Small_parameters2||null===(_Small_parameters_docs1=_Small_parameters2.docs)||void 0===_Small_parameters_docs1?void 0:_Small_parameters_docs1.description}}},Large.parameters={...Large.parameters,docs:{...null===(_Large_parameters=Large.parameters)||void 0===_Large_parameters?void 0:_Large_parameters.docs,source:{originalSource:"{\n  args: {\n    size: 'lg',\n    label: true\n  },\n  parameters: {\n    docs: {\n      description: {\n        story: 'Large size language switch for prominent placement in settings pages, configuration panels, or onboarding flows.'\n      }\n    }\n  }\n}",...null===(_Large_parameters1=Large.parameters)||void 0===_Large_parameters1||null===(_Large_parameters_docs=_Large_parameters1.docs)||void 0===_Large_parameters_docs?void 0:_Large_parameters_docs.source},description:{story:"Large size language switch for prominent placement in settings.\r\nUse in configuration pages or onboarding flows.",...null===(_Large_parameters2=Large.parameters)||void 0===_Large_parameters2||null===(_Large_parameters_docs1=_Large_parameters2.docs)||void 0===_Large_parameters_docs1?void 0:_Large_parameters_docs1.description}}},SmallWithLabel.parameters={...SmallWithLabel.parameters,docs:{...null===(_SmallWithLabel_parameters=SmallWithLabel.parameters)||void 0===_SmallWithLabel_parameters?void 0:_SmallWithLabel_parameters.docs,source:{originalSource:"{\n  args: {\n    size: 'sm',\n    label: true\n  },\n  parameters: {\n    docs: {\n      description: {\n        story: 'Small size language switch with label for compact but descriptive interface. Balances space efficiency with user clarity.'\n      }\n    }\n  }\n}",...null===(_SmallWithLabel_parameters1=SmallWithLabel.parameters)||void 0===_SmallWithLabel_parameters1||null===(_SmallWithLabel_parameters_docs=_SmallWithLabel_parameters1.docs)||void 0===_SmallWithLabel_parameters_docs?void 0:_SmallWithLabel_parameters_docs.source},description:{story:"Small size with label for compact but descriptive interface.\r\nUse when space is limited but clarity is still important.",...null===(_SmallWithLabel_parameters2=SmallWithLabel.parameters)||void 0===_SmallWithLabel_parameters2||null===(_SmallWithLabel_parameters_docs1=_SmallWithLabel_parameters2.docs)||void 0===_SmallWithLabel_parameters_docs1?void 0:_SmallWithLabel_parameters_docs1.description}}};const __namedExportsOrder=["Default","WithLabel","Small","Large","SmallWithLabel"]}}]);