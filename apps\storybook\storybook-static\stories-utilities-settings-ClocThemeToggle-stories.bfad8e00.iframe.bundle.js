"use strict";(self.webpackChunk_cloc_storybook=self.webpackChunk_cloc_storybook||[]).push([[4140],{"./src/stories/utilities/settings/ClocThemeToggle.stories.tsx":(__unused_webpack_module,__webpack_exports__,__webpack_require__)=>{__webpack_require__.r(__webpack_exports__),__webpack_require__.d(__webpack_exports__,{CustomStyling:()=>CustomStyling,Default:()=>Default,InSidebar:()=>InSidebar,InToolbar:()=>InToolbar,Large:()=>Large,SettingsPanel:()=>SettingsPanel,SizeComparison:()=>SizeComparison,Small:()=>Small,WithPreview:()=>WithPreview,__namedExportsOrder:()=>__namedExportsOrder,default:()=>__WEBPACK_DEFAULT_EXPORT__});var _Default_parameters,_Default_parameters_docs,_Default_parameters1,_Small_parameters,_Small_parameters_docs,_Small_parameters1,_Large_parameters,_Large_parameters_docs,_Large_parameters1,_CustomStyling_parameters,_CustomStyling_parameters_docs,_CustomStyling_parameters1,_InToolbar_parameters,_InToolbar_parameters_docs,_InToolbar_parameters1,_InSidebar_parameters,_InSidebar_parameters_docs,_InSidebar_parameters1,_WithPreview_parameters,_WithPreview_parameters_docs,_WithPreview_parameters1,_SizeComparison_parameters,_SizeComparison_parameters_docs,_SizeComparison_parameters1,_SettingsPanel_parameters,_SettingsPanel_parameters_docs,_SettingsPanel_parameters1,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__=__webpack_require__("../../node_modules/next/dist/compiled/react/jsx-runtime.js"),_cloc_atoms__WEBPACK_IMPORTED_MODULE_2__=(__webpack_require__("../../node_modules/next/dist/compiled/react/index.js"),__webpack_require__("../../packages/toolkit/atoms/dist/index.es.js")),_cloc_ui__WEBPACK_IMPORTED_MODULE_3__=__webpack_require__("../../packages/ui/dist/index.es.js");const __WEBPACK_DEFAULT_EXPORT__={title:"Utilities/Settings/Theme Toggle",component:_cloc_atoms__WEBPACK_IMPORTED_MODULE_2__.Xd,parameters:{layout:"centered",docs:{description:{component:"A theme selector component that allows users to switch between different Cloc themes. Includes various color schemes and styling options for the time tracking interface."}}},argTypes:{size:{control:"select",options:["default","sm","lg"],description:"Size variant of the theme toggle"},className:{control:"text",description:"Additional CSS classes"}}},Default={args:{size:"default"}},Small={args:{size:"sm"}},Large={args:{size:"lg"}},CustomStyling={args:{size:"default",className:"border-2 border-blue-300 rounded-lg"}},InToolbar={render:()=>(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div",{className:"flex items-center gap-4 p-4 bg-gray-100 dark:bg-gray-800 rounded-lg",children:[(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("h3",{className:"font-semibold text-gray-900 dark:text-white",children:"Settings"}),(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(_cloc_atoms__WEBPACK_IMPORTED_MODULE_2__.Xd,{size:"default"})]})},InSidebar={render:()=>(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div",{className:"w-64 p-4 bg-white rounded-lg dark:bg-gray-900 border border-gray-200 dark:border-gray-700 space-y-4",children:[(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("h3",{className:"font-semibold text-gray-900 dark:text-white",children:"Preferences"}),(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("div",{className:"space-y-3",children:(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div",{className:"space-y-2",children:[(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("label",{className:"text-sm font-medium text-gray-700 dark:text-gray-300",children:"Theme"}),(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(_cloc_atoms__WEBPACK_IMPORTED_MODULE_2__.Xd,{className:"w-[90%]"})]})})]})},WithPreview={render:()=>(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div",{className:"space-y-4 p-4 border rounded-lg bg-white dark:bg-gray-800",children:[(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("h3",{className:"font-semibold text-gray-900 dark:text-white",children:"Theme Customization"}),(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div",{className:"space-y-4",children:[(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div",{className:"space-y-2",children:[(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("label",{className:"text-sm font-medium text-gray-700 dark:text-gray-300",children:"Select Theme"}),(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(_cloc_atoms__WEBPACK_IMPORTED_MODULE_2__.Xd,{size:"default"})]}),(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div",{className:"p-4 border border-gray-200 dark:border-gray-600 rounded-md",children:[(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("h4",{className:"text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:"Preview"}),(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div",{className:"flex items-center gap-2",children:[(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(_cloc_ui__WEBPACK_IMPORTED_MODULE_3__.cc,{className:"w-8 h-8 rounded-full "}),(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("span",{className:"text-sm text-gray-600 dark:text-gray-400",children:"Theme colors will be applied to timers and charts"})]})]})]})]})},SizeComparison={render:()=>(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div",{className:"space-y-6",children:[(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div",{className:"space-y-2",children:[(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("label",{className:"text-sm font-medium",children:"Small Size"}),(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(_cloc_atoms__WEBPACK_IMPORTED_MODULE_2__.Xd,{size:"sm"})]}),(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div",{className:"space-y-2",children:[(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("label",{className:"text-sm font-medium",children:"Default Size"}),(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(_cloc_atoms__WEBPACK_IMPORTED_MODULE_2__.Xd,{size:"default"})]}),(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div",{className:"space-y-2",children:[(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("label",{className:"text-sm font-medium",children:"Large Size"}),(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(_cloc_atoms__WEBPACK_IMPORTED_MODULE_2__.Xd,{size:"lg"})]})]})},SettingsPanel={render:()=>(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div",{className:"max-w-md p-6 bg-white dark:bg-gray-900 border border-gray-200 dark:border-gray-700 rounded-lg shadow-lg",children:[(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("h2",{className:"text-lg font-semibold text-gray-900 dark:text-white mb-4",children:"Appearance Settings"}),(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div",{className:"space-y-4",children:[(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div",{className:"space-y-2",children:[(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("label",{className:"text-sm font-medium text-gray-700 dark:text-gray-300",children:"Color Theme"}),(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(_cloc_atoms__WEBPACK_IMPORTED_MODULE_2__.Xd,{size:"default"}),(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("p",{className:"text-xs text-gray-500 dark:text-gray-400",children:"Choose a color scheme for your time tracking interface"})]}),(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("div",{className:"pt-4 border-t border-gray-200 dark:border-gray-600",children:(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(_cloc_ui__WEBPACK_IMPORTED_MODULE_3__.cc,{className:"w-full px-4 py-2 bg-blue-600 text-white rounded-md text-sm hover:bg-blue-700",children:"Apply Changes"})})]})]})};Default.parameters={...Default.parameters,docs:{...null===(_Default_parameters=Default.parameters)||void 0===_Default_parameters?void 0:_Default_parameters.docs,source:{originalSource:"{\n  args: {\n    size: 'default'\n  }\n}",...null===(_Default_parameters1=Default.parameters)||void 0===_Default_parameters1||null===(_Default_parameters_docs=_Default_parameters1.docs)||void 0===_Default_parameters_docs?void 0:_Default_parameters_docs.source}}},Small.parameters={...Small.parameters,docs:{...null===(_Small_parameters=Small.parameters)||void 0===_Small_parameters?void 0:_Small_parameters.docs,source:{originalSource:"{\n  args: {\n    size: 'sm'\n  }\n}",...null===(_Small_parameters1=Small.parameters)||void 0===_Small_parameters1||null===(_Small_parameters_docs=_Small_parameters1.docs)||void 0===_Small_parameters_docs?void 0:_Small_parameters_docs.source}}},Large.parameters={...Large.parameters,docs:{...null===(_Large_parameters=Large.parameters)||void 0===_Large_parameters?void 0:_Large_parameters.docs,source:{originalSource:"{\n  args: {\n    size: 'lg'\n  }\n}",...null===(_Large_parameters1=Large.parameters)||void 0===_Large_parameters1||null===(_Large_parameters_docs=_Large_parameters1.docs)||void 0===_Large_parameters_docs?void 0:_Large_parameters_docs.source}}},CustomStyling.parameters={...CustomStyling.parameters,docs:{...null===(_CustomStyling_parameters=CustomStyling.parameters)||void 0===_CustomStyling_parameters?void 0:_CustomStyling_parameters.docs,source:{originalSource:"{\n  args: {\n    size: 'default',\n    className: 'border-2 border-blue-300 rounded-lg'\n  }\n}",...null===(_CustomStyling_parameters1=CustomStyling.parameters)||void 0===_CustomStyling_parameters1||null===(_CustomStyling_parameters_docs=_CustomStyling_parameters1.docs)||void 0===_CustomStyling_parameters_docs?void 0:_CustomStyling_parameters_docs.source}}},InToolbar.parameters={...InToolbar.parameters,docs:{...null===(_InToolbar_parameters=InToolbar.parameters)||void 0===_InToolbar_parameters?void 0:_InToolbar_parameters.docs,source:{originalSource:'{\n  render: () => <div className="flex items-center gap-4 p-4 bg-gray-100 dark:bg-gray-800 rounded-lg">\r\n            <h3 className="font-semibold text-gray-900 dark:text-white">Settings</h3>\r\n            <ClocThemeToggle size="default" />\r\n        </div>\n}',...null===(_InToolbar_parameters1=InToolbar.parameters)||void 0===_InToolbar_parameters1||null===(_InToolbar_parameters_docs=_InToolbar_parameters1.docs)||void 0===_InToolbar_parameters_docs?void 0:_InToolbar_parameters_docs.source}}},InSidebar.parameters={...InSidebar.parameters,docs:{...null===(_InSidebar_parameters=InSidebar.parameters)||void 0===_InSidebar_parameters?void 0:_InSidebar_parameters.docs,source:{originalSource:'{\n  render: () => <div className="w-64 p-4 bg-white rounded-lg dark:bg-gray-900 border border-gray-200 dark:border-gray-700 space-y-4">\r\n            <h3 className="font-semibold text-gray-900 dark:text-white">Preferences</h3>\r\n            <div className="space-y-3">\r\n                <div className="space-y-2">\r\n                    <label className="text-sm font-medium text-gray-700 dark:text-gray-300">Theme</label>\r\n                    <ClocThemeToggle className="w-[90%]" />\r\n                </div>\r\n            </div>\r\n        </div>\n}',...null===(_InSidebar_parameters1=InSidebar.parameters)||void 0===_InSidebar_parameters1||null===(_InSidebar_parameters_docs=_InSidebar_parameters1.docs)||void 0===_InSidebar_parameters_docs?void 0:_InSidebar_parameters_docs.source}}},WithPreview.parameters={...WithPreview.parameters,docs:{...null===(_WithPreview_parameters=WithPreview.parameters)||void 0===_WithPreview_parameters?void 0:_WithPreview_parameters.docs,source:{originalSource:'{\n  render: () => <div className="space-y-4 p-4 border rounded-lg bg-white dark:bg-gray-800">\r\n            <h3 className="font-semibold text-gray-900 dark:text-white">Theme Customization</h3>\r\n            <div className="space-y-4">\r\n                <div className="space-y-2">\r\n                    <label className="text-sm font-medium text-gray-700 dark:text-gray-300">Select Theme</label>\r\n                    <ClocThemeToggle size="default" />\r\n                </div>\r\n                <div className="p-4 border border-gray-200 dark:border-gray-600 rounded-md">\r\n                    <h4 className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Preview</h4>\r\n                    <div className="flex items-center gap-2">\r\n                        <ThemedButton className="w-8 h-8 rounded-full "></ThemedButton>\r\n                        <span className="text-sm text-gray-600 dark:text-gray-400">\r\n                            Theme colors will be applied to timers and charts\r\n                        </span>\r\n                    </div>\r\n                </div>\r\n            </div>\r\n        </div>\n}',...null===(_WithPreview_parameters1=WithPreview.parameters)||void 0===_WithPreview_parameters1||null===(_WithPreview_parameters_docs=_WithPreview_parameters1.docs)||void 0===_WithPreview_parameters_docs?void 0:_WithPreview_parameters_docs.source}}},SizeComparison.parameters={...SizeComparison.parameters,docs:{...null===(_SizeComparison_parameters=SizeComparison.parameters)||void 0===_SizeComparison_parameters?void 0:_SizeComparison_parameters.docs,source:{originalSource:'{\n  render: () => <div className="space-y-6">\r\n            <div className="space-y-2">\r\n                <label className="text-sm font-medium">Small Size</label>\r\n                <ClocThemeToggle size="sm" />\r\n            </div>\r\n            <div className="space-y-2">\r\n                <label className="text-sm font-medium">Default Size</label>\r\n                <ClocThemeToggle size="default" />\r\n            </div>\r\n            <div className="space-y-2">\r\n                <label className="text-sm font-medium">Large Size</label>\r\n                <ClocThemeToggle size="lg" />\r\n            </div>\r\n        </div>\n}',...null===(_SizeComparison_parameters1=SizeComparison.parameters)||void 0===_SizeComparison_parameters1||null===(_SizeComparison_parameters_docs=_SizeComparison_parameters1.docs)||void 0===_SizeComparison_parameters_docs?void 0:_SizeComparison_parameters_docs.source}}},SettingsPanel.parameters={...SettingsPanel.parameters,docs:{...null===(_SettingsPanel_parameters=SettingsPanel.parameters)||void 0===_SettingsPanel_parameters?void 0:_SettingsPanel_parameters.docs,source:{originalSource:'{\n  render: () => <div className="max-w-md p-6 bg-white dark:bg-gray-900 border border-gray-200 dark:border-gray-700 rounded-lg shadow-lg">\r\n            <h2 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">Appearance Settings</h2>\r\n            <div className="space-y-4">\r\n                <div className="space-y-2">\r\n                    <label className="text-sm font-medium text-gray-700 dark:text-gray-300">Color Theme</label>\r\n                    <ClocThemeToggle size="default" />\r\n                    <p className="text-xs text-gray-500 dark:text-gray-400">\r\n                        Choose a color scheme for your time tracking interface\r\n                    </p>\r\n                </div>\r\n                <div className="pt-4 border-t border-gray-200 dark:border-gray-600">\r\n                    <ThemedButton className="w-full px-4 py-2 bg-blue-600 text-white rounded-md text-sm hover:bg-blue-700">\r\n                        Apply Changes\r\n                    </ThemedButton>\r\n                </div>\r\n            </div>\r\n        </div>\n}',...null===(_SettingsPanel_parameters1=SettingsPanel.parameters)||void 0===_SettingsPanel_parameters1||null===(_SettingsPanel_parameters_docs=_SettingsPanel_parameters1.docs)||void 0===_SettingsPanel_parameters_docs?void 0:_SettingsPanel_parameters_docs.source}}};const __namedExportsOrder=["Default","Small","Large","CustomStyling","InToolbar","InSidebar","WithPreview","SizeComparison","SettingsPanel"]}}]);