/** @jsxImportSource theme-ui */

import { useEffect, useState } from 'react';
import { cva, VariantProps } from 'class-variance-authority';
import { Card, cn, transformData, generateChartConfig } from '@cloc/ui';
import { ChartData } from '@cloc/types';
import { SpinOverlayLoader } from 'src/lib/components/loaders/spin-overlay-loader';
import { useTranslation } from 'react-i18next';
import { useClocContext } from '@lib/context/cloc-context';
import { ClocActiveEmployeeSelector } from '@components/cloc-ui-components/inputs/cloc-active-employee-selector';
import { ClocReportDatesRangePicker } from '@components/cloc-ui-components/inputs/cloc-report-dates-range-picker';
import { ClocReportDisplayer } from '@components/cloc-ui-components/report-displayer';
import { BarChart } from '@components/cloc-ui-components/cloc-ui-charts/bar-chart/bar-chart';
import { Line<PERSON>hart } from '@components/cloc-ui-components/cloc-ui-charts/line-chart/line-chart';
import { AreaChart } from '@components/cloc-ui-components/cloc-ui-charts/area-chart/area-chart';
import { TooltipChart } from '@components/cloc-ui-components/cloc-ui-charts/tooltip-chart/tooltip-chart';
import { RadialChart } from '@components/cloc-ui-components/cloc-ui-charts/radial-chart/radial-chart';
import { RadarChart } from '@components/cloc-ui-components/cloc-ui-charts/radar-chart/radar-chart';
import { ClocTimerFooter } from '@components/layouts/footers/component-footer';

export type ChartType = 'bar' | 'bar-vertical' | 'area' | 'pie' | 'line' | 'radar' | 'radial' | 'tooltip';
interface IBasicClocReportProps extends VariantProps<typeof basicReportVariants> {
	type?: ChartType;
	className?: string;
	draggable?: boolean;
}

const basicReportVariants = cva(
	'dark:text-white h-[600px] flex p-5 flex-col justify-between gap-3 rounded-xl  bg-white dark:bg-black shadow-2xl dark:shadow-white/10 ',
	{
		variants: {
			variant: {
				default: '',
				bordered: 'border-2 border-secondaryColor'
			},
			size: {
				default: 'w-[700px]',
				sm: 'w-[600px] text-sm p-4',
				lg: 'min-w-[1200px] w-full px-10 max-w-screen'
			}
		},
		defaultVariants: {
			variant: 'default',
			size: 'default'
		}
	}
);

/**
 * @description
 * A component that displays worked time of connected employee in the current week by default but user can change dates range,
 * If an admin is connected, it displays worked time of employees in selected team.
 *
 * @param {IBasicClocReportProps} props
 * @param {ChartType} [props.type='bar'] - The type of the chart to display
 * @param {string} [props.variant='default'] - The variant of the component
 * @param {string} [props.className] - The className of the component
 * @param {boolean} [props.draggable=false] - Whether the component is draggable or not
 * @param {string} [props.size='default'] - The size of the component
 *
 * @return {ReactElement} The component
 */
const BasicClocReport = ({ type = 'bar', variant, className, draggable = false, size }: IBasicClocReportProps) => {
	const {
		config,
		setConfig,
		appliedTheme,
		report,
		loadings: { reportLoading, userLoading }
	} = useClocContext();

	const [data, setData] = useState<ChartData[]>([]);

	const { t } = useTranslation();

	useEffect(() => {
		setData(transformData(report));
	}, [report]);

	useEffect(() => {
		setConfig(generateChartConfig(data));
	}, [data]);

	return (
		<Card
			className={cn(basicReportVariants({ variant, size, className }), ' relative')}
			sx={{ borderColor: 'borderColor' }}
		>
			{draggable && <div className="handle w-full h-8 absolute top-0 left-0 cursor-grab"></div>}

			{(reportLoading || userLoading) && <SpinOverlayLoader />}

			<div className=" flex flex-col gap-2 ">
				<div className=" flex w-full justify-between items-center">
					<h1 className="text-lg font-medium">{t('REPORT.title')}</h1>
					<div className=" flex justify-end items-center gap-2 ">
						<ClocActiveEmployeeSelector labeled={false} />
						<ClocReportDatesRangePicker />
					</div>
				</div>
				<div className="flex gap-3 overflow-scroll custom-scroll pb-2">
					{report && report[0] ? (
						report.map((elt, index) => {
							return (
								<ClocReportDisplayer
									workedTime={elt.sum}
									label={elt.employee.fullName.split(' ')[0]}
									maxWorkHours={8}
									key={index}
								/>
							);
						})
					) : (
						// <ClocReportDisplayerLoader />
						<ClocReportDisplayer
							workedTime={0}
							maxWorkHours={8}
							label={t('COMMON.time') + ' ' + t('COMMON.worked')}
						/>
					)}
				</div>
			</div>

			<>
				{!(report && report[0]) ? (
					<div className="w-full text-slate-400 dark:text-slate-600 flex top-1/2 left-1/2 justify-center items-center absolute -translate-x-2/4 -translate-y-2/4 ">
						{t('NO_DATA.no_data_available')}
					</div>
				) : (
					// <div className="flex justify-center items-center h-[250px]">No data available</div>
					<>
						{type == 'bar' && (
							<BarChart color={appliedTheme.colors?.borderColor as string} config={config} data={data} />
						)}
						{type == 'bar-vertical' && (
							<BarChart
								color={appliedTheme.colors?.borderColor as string}
								config={config}
								data={data}
								layout="vertical"
							/>
						)}
						{type == 'line' && (
							<LineChart color={appliedTheme.colors?.borderColor as string} config={config} data={data} />
						)}
						{type == 'area' && (
							<AreaChart color={appliedTheme.colors?.borderColor as string} config={config} data={data} />
						)}
						{type == 'tooltip' && (
							<TooltipChart
								color={appliedTheme.colors?.borderColor as string}
								config={config}
								data={data}
							/>
						)}
						{type == 'radial' && (
							<RadialChart
								color={appliedTheme.colors?.borderColor as string}
								config={config}
								data={data}
							/>
						)}
						{type == 'radar' && (
							<RadarChart
								color={appliedTheme.colors?.borderColor as string}
								config={config}
								data={data}
							/>
						)}
						{type == 'pie' && (
							// <PieChart color={appliedTheme.colors?.borderColor as string} config={config} data={data} />
							<div className="flex justify-center items-center h-[300px]">
								Pie Chart Under development
							</div>
						)}
					</>
				)}
			</>

			<ClocTimerFooter />
		</Card>
	);
};

export { BasicClocReport, basicReportVariants };
