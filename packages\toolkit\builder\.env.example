# https://www.builder.io/c/docs/using-your-api-key
NEXT_PUBLIC_BUILDER_API_KEY=250a11df53a34361b2c1bb0035393df4

# https://studio.plasmic.app/projects
# Unique identifier for the Plasmic project (used to link your Plasmic project to your Next.js application)
NEXT_PUBLIC_PLASMIC_PROJECT_ID=

# https://studio.plasmic.app/settings
# Plasmic API token (required to access resources and data for your Plasmic project through the API)
NEXT_PUBLIC_PLASMIC_PROJECT_API_TOKEN=

# You need to choose which one you want to use between B<PERSON>LDER | PLASMIC
NEXT_PUBLIC_TYPE=PLASMIC

# Authentication token used for API requests
NEXT_PUBLIC_TOKEN=

# Main public API URL
NEXT_PUBLIC_API_URL=

# Public API URL prefix (useful for constructing dynamic routes)
NEXT_PUBLIC_API_URL_PREFIX=

NEXT_PUBLIC_COOKIE_DOMAINS=

# Cloc API url (e.g. https://api.cloc.ai/api)
NEXT_PUBLIC_CLOC_API_URL=
