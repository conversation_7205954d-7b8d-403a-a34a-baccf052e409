'use client';

import { ClocProvider, theme5 } from '@cloc/atoms';
import { ReactElement, ReactNode } from 'react';

const clocConfig = {
	apiUrl: process.env.NEXT_PUBLIC_CLOC_API_URL
};

const ClientLayout = ({ children, lang }: { children: ReactNode; lang?: string }): ReactElement => {
	return (
		<ClocProvider config={clocConfig} lang={lang} theme={theme5}>
			{children}
		</ClocProvider>
	);
};

export default ClientLayout;
